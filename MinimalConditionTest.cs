using System;
using System.Collections.Generic;
using System.Text;
using System.Net;
using System.Text.Json;

namespace SnapAnalyser
{
    // Simple test class to verify condition expression extraction
    class MinimalConditionTest
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("=== Testing Condition Expression Extraction ===\n");
            
            // Create a test snap with condition properties
            var conditionSnap = new SnapNode
            {
                Id = "test-condition",
                Label = "Test Condition",
                Type = "com-snaplogic-snaps-transform-conditional",
                Properties = new Dictionary<string, string>
                {
                    { "expression_0", "$value > 10" },
                    { "expression_1", "$type == 'order'" },
                    { "filterConfig", "{\"expression\": \"$data.field != null\", \"type\": \"complex\"}" },
                    { "output_0", "Success" },
                    { "output_1", "Orders" },
                    { "output_2", "Default" }
                }
            };
            
            // Create our extraction helper methods
            var conditionExpressions = ExtractConditionExpressions(conditionSnap);
            
            // Display the extracted expressions
            Console.WriteLine("Extracted condition expressions:");
            foreach (var cond in conditionExpressions)
            {
                Console.WriteLine($"- Expression: {cond.Expression}");
                Console.WriteLine($"  Description: {cond.Description}");
                Console.WriteLine();
            }
            
            // Test the HTML generation
            var htmlOutput = GenerateConditionHtml(conditionSnap);
            Console.WriteLine("Generated HTML for condition snap:");
            Console.WriteLine(htmlOutput);
        }
        
        // Helper class for storing condition info
        class ConditionInfo
        {
            public string Expression { get; set; }
            public string Description { get; set; }
        }
        
        // Extract condition expressions from snap properties
        static List<ConditionInfo> ExtractConditionExpressions(SnapNode snap)
        {
            var conditions = new List<ConditionInfo>();
            
            // Look for properties with expression in the key
            foreach (var prop in snap.Properties)
            {
                if (prop.Key.Contains("expression_") && !string.IsNullOrEmpty(prop.Value))
                {
                    var condition = new ConditionInfo
                    {
                        Expression = prop.Value,
                        Description = AnalyzeConditionPattern(prop.Value)
                    };
                    conditions.Add(condition);
                }
            }
            
            // Check for JSON format conditions
            foreach (var prop in snap.Properties)
            {
                if (prop.Key.Contains("filter") || 
                    prop.Key.Contains("condition") || 
                    prop.Key.Contains("evaluate"))
                {
                    try
                    {
                        // Try to parse as JSON
                        if (prop.Value.StartsWith("{"))
                        {
                            var jsonDoc = JsonDocument.Parse(prop.Value);
                            if (jsonDoc.RootElement.TryGetProperty("expression", out var expr))
                            {
                                var expressionValue = expr.GetString();
                                if (!string.IsNullOrEmpty(expressionValue))
                                {
                                    var condition = new ConditionInfo
                                    {
                                        Expression = expressionValue,
                                        Description = AnalyzeConditionPattern(expressionValue)
                                    };
                                    conditions.Add(condition);
                                }
                            }
                        }
                    }
                    catch (JsonException)
                    {
                        // Not valid JSON, check if it's a simple expression string
                        if (!string.IsNullOrEmpty(prop.Value) && !conditions.Any(c => c.Expression == prop.Value))
                        {
                            var condition = new ConditionInfo
                            {
                                Expression = prop.Value,
                                Description = AnalyzeConditionPattern(prop.Value)
                            };
                            conditions.Add(condition);
                        }
                    }
                }
            }
            
            return conditions;
        }
        
        // Analyze condition pattern to generate description
        static string AnalyzeConditionPattern(string expression)
        {
            // Check for common patterns
            if (expression.Contains("==") || expression.Contains("!=") || 
                expression.Contains(">") || expression.Contains("<"))
            {
                return "Comparison: Tests if values are equal, not equal, greater than, or less than.";
            }
            else if (expression.Contains("null"))
            {
                return "Null check: Tests if a value exists or is null.";
            }
            else if (expression.Contains("&&") || expression.Contains("||"))
            {
                return "Logical operation: Combines multiple conditions with AND/OR logic.";
            }
            else if (expression.Contains("$."))
            {
                return "JSONPath: Extracts values from a JSON structure.";
            }
            else
            {
                return "Custom expression";
            }
        }
        
        // Generate HTML for condition details
        static string GenerateConditionHtml(SnapNode snap)
        {
            var details = new StringBuilder();
            
            details.AppendLine("<p><strong>Condition Configuration:</strong></p>");
            
            var conditions = ExtractConditionExpressions(snap);
            
            if (conditions.Any())
            {
                details.AppendLine("<p>This condition snap evaluates the following expression(s):</p>");
                details.AppendLine("<table style='width: 100%; border-collapse: collapse;'>");
                details.AppendLine("<tr><th style='text-align:left; border: 1px solid #ddd; padding: 8px;'>Expression</th><th style='text-align:left; border: 1px solid #ddd; padding: 8px;'>Description</th></tr>");
                
                foreach (var condition in conditions)
                {
                    details.AppendLine($"<tr><td style='border: 1px solid #ddd; padding: 8px;'><code>{WebUtility.HtmlEncode(condition.Expression)}</code></td><td style='border: 1px solid #ddd; padding: 8px;'>{WebUtility.HtmlEncode(condition.Description)}</td></tr>");
                }
                
                details.AppendLine("</table>");
            }
            else
            {
                details.AppendLine("<p>No specific condition expressions were found.</p>");
            }
            
            // Extract output routing properties
            var outputProps = snap.Properties.Where(p => 
                p.Key.Contains("output") || 
                p.Key.Contains("true") || 
                p.Key.Contains("false") || 
                p.Key.Contains("path") || 
                p.Key.Contains("route")).ToList();
                
            if (outputProps.Any())
            {
                details.AppendLine("<p><strong>Output Routing:</strong></p>");
                details.AppendLine("<ul>");
                foreach (var output in outputProps)
                {
                    details.AppendLine($"<li><strong>{WebUtility.HtmlEncode(output.Key)}:</strong> <code>{WebUtility.HtmlEncode(output.Value)}</code></li>");
                }
                details.AppendLine("</ul>");
            }
            
            return details.ToString();
        }
    }
    
    // Minimal mock classes to support our test
    public class SnapNode
    {
        public string Id { get; set; }
        public string Label { get; set; }
        public string Type { get; set; }
        public Dictionary<string, string> Properties { get; set; } = new Dictionary<string, string>();
    }
}
