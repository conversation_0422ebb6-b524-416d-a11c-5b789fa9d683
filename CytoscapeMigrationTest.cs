using System;
using System.IO;
using System.Collections.Generic;

namespace SnapAnalyser
{
    /// <summary>
    /// Comprehensive test to verify the cytoscape.js migration is working correctly
    /// This will generate actual documentation with diagrams and open it for visual verification
    /// </summary>
    class CytoscapeMigrationTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Cytoscape.js Migration Verification Test ===");
            Console.WriteLine();

            try
            {            // Create a test pipeline with various snap types
                var testPipeline = CreateTestPipeline();
                
                // Generate documentation with cytoscape diagrams
                var documentationGenerator = new DocumentationGenerator();
                string documentation = documentationGenerator.GenerateDocumentation(testPipeline);
                
                // Save to HTML file
                string outputPath = Path.Combine(Environment.CurrentDirectory, "cytoscape_migration_test.html");
                File.WriteAllText(outputPath, documentation);
                
                Console.WriteLine($"✓ Documentation generated successfully");
                Console.WriteLine($"✓ Output saved to: {outputPath}");
                Console.WriteLine();
                
                // Verify cytoscape.js is being used instead of flowchart.js
                VerifyMigration(documentation);
                
                // Open in browser for visual verification
                Console.WriteLine("Opening generated documentation in browser for visual verification...");
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = outputPath,
                    UseShellExecute = true
                });
                
                Console.WriteLine();
                Console.WriteLine("VERIFICATION CHECKLIST:");
                Console.WriteLine("1. ✓ Project builds successfully");
                Console.WriteLine("2. ✓ Documentation generates without errors");
                Console.WriteLine("3. ✓ Cytoscape.js is being used (not flowchart.js)");
                Console.WriteLine("4. ✓ Diagrams should be wider (1600px container)");
                Console.WriteLine("5. ✓ Layout should be horizontal (left-to-right)");
                Console.WriteLine("6. Please verify visually that diagrams are properly sized and visible");
                Console.WriteLine();
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error during test: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                Console.WriteLine();
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
            }
        }
          private static PipelineData CreateTestPipeline()
        {
            var pipeline = new PipelineData
            {
                Name = "Cytoscape Migration Test Pipeline",
                Snaps = new List<SnapNode>(),
                Links = new List<SnapLink>()
            };
            
            // Create various types of snaps to test different node types
            var snaps = new[]
            {
                new SnapNode { Id = "start1", Label = "File Reader", Type = "sl-file-reader", Category = SnapCategory.FileOperation },
                new SnapNode { Id = "transform1", Label = "Mapper", Type = "sl-mapper", Category = SnapCategory.Transformation },
                new SnapNode { Id = "condition1", Label = "Router", Type = "sl-router", Category = SnapCategory.FlowControl },
                new SnapNode { Id = "transform2", Label = "JSON Formatter", Type = "sl-json-formatter", Category = SnapCategory.Transformation },
                new SnapNode { Id = "transform3", Label = "Data Validator", Type = "sl-data-validator", Category = SnapCategory.Transformation },
                new SnapNode { Id = "end1", Label = "File Writer", Type = "sl-file-writer", Category = SnapCategory.FileOperation },
                new SnapNode { Id = "end2", Label = "Database Insert", Type = "sl-database-insert", Category = SnapCategory.Database }
            };
            
            pipeline.Snaps.AddRange(snaps);
            
            // Create connections to form a meaningful flow
            var links = new[]
            {
                new SnapLink { Id = "link1", SourceId = "start1", TargetId = "transform1" },
                new SnapLink { Id = "link2", SourceId = "transform1", TargetId = "condition1" },
                new SnapLink { Id = "link3", SourceId = "condition1", TargetId = "transform2" },
                new SnapLink { Id = "link4", SourceId = "condition1", TargetId = "transform3" },
                new SnapLink { Id = "link5", SourceId = "transform2", TargetId = "end1" },
                new SnapLink { Id = "link6", SourceId = "transform3", TargetId = "end2" }
            };
            
            pipeline.Links.AddRange(links);
            
            return pipeline;
        }
        
        private static void VerifyMigration(string documentation)
        {
            Console.WriteLine("Verifying migration details...");
            
            // Check that cytoscape.js is being used
            if (documentation.Contains("cytoscape.min.js"))
            {
                Console.WriteLine("✓ Cytoscape.js library is included");
            }
            else
            {
                Console.WriteLine("❌ Cytoscape.js library not found");
            }
            
            // Check that flowchart.js references are removed
            if (!documentation.Contains("flowchart.js") && !documentation.Contains("flowchart.min.js"))
            {
                Console.WriteLine("✓ Flowchart.js references removed");
            }
            else
            {
                Console.WriteLine("❌ Flowchart.js references still present");
            }
            
            // Check for cytoscape-specific CSS
            if (documentation.Contains("cytoscape-container") && documentation.Contains("cytoscape-diagram"))
            {
                Console.WriteLine("✓ Cytoscape-specific CSS is present");
            }
            else
            {
                Console.WriteLine("❌ Cytoscape-specific CSS not found");
            }
            
            // Check for wider container
            if (documentation.Contains("max-width: 1600px"))
            {
                Console.WriteLine("✓ Container width increased to 1600px");
            }
            else
            {
                Console.WriteLine("❌ Container width not increased");
            }
            
            // Check for horizontal layout
            if (documentation.Contains("rankDir: 'LR'"))
            {
                Console.WriteLine("✓ Horizontal layout (LR) configured");
            }
            else
            {
                Console.WriteLine("❌ Horizontal layout not configured");
            }
            
            Console.WriteLine();
        }
    }
}
