# Enhanced account debugging to see why account is empty

$filePath = "DocumentationGenerator.cs"

Write-Host "Enhancing account debug..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Replace the account debug with more detailed investigation
$oldAccountDebug = @'
var account = propMap["account"] as JObject;
                                                if (account != null)
                                                {
                                                    Console.WriteLine($"[ACCOUNT-DEBUG] Complete account JSON:");
                                                    Console.WriteLine(account.ToString(Newtonsoft.Json.Formatting.Indented));
                                                }
                                                else
                                                {
                                                    Console.WriteLine($"[ACCOUNT-DEBUG] No account object found in property_map");
                                                }
'@

$newAccountDebug = @'
var account = propMap["account"];
                                                Console.WriteLine($"[ACCOUNT-DEBUG] Account token type: {account?.Type}");
                                                Console.WriteLine($"[ACCOUNT-DEBUG] Account has value: {account?.HasValues}");
                                                
                                                if (account is JObject accountObj)
                                                {
                                                    Console.WriteLine($"[ACCOUNT-DEBUG] Account is JObject with {accountObj.Count} properties");
                                                    Console.WriteLine($"[ACCOUNT-DEBUG] Complete account JSON:");
                                                    Console.WriteLine(accountObj.ToString(Newtonsoft.Json.Formatting.Indented));
                                                    
                                                    // Check for account_ref specifically
                                                    var accountRef = accountObj["account_ref"];
                                                    if (accountRef != null)
                                                    {
                                                        Console.WriteLine($"[ACCOUNT-DEBUG] Found account_ref: {accountRef.ToString(Newtonsoft.Json.Formatting.Indented)}");
                                                    }
                                                    else
                                                    {
                                                        Console.WriteLine($"[ACCOUNT-DEBUG] No account_ref in account object");
                                                    }
                                                }
                                                else
                                                {
                                                    Console.WriteLine($"[ACCOUNT-DEBUG] Account is not JObject, raw value: {account?.ToString()}");
                                                }
'@

if ($content.Contains($oldAccountDebug)) {
    $content = $content.Replace($oldAccountDebug, $newAccountDebug)
    Write-Host "Enhanced account debugging" -ForegroundColor Yellow
} else {
    Write-Host "Account debug code not found" -ForegroundColor Red
}

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Enhanced account debugging!" -ForegroundColor Green
