using System;
using System.IO;
using System.Collections.Generic;

class TestCytoscapeIntegration
{
    static void Main()
    {
        Console.WriteLine("Testing Cytoscape.js Integration");
        Console.WriteLine("================================");
        
        try
        {
            // Create instances
            var cytoscapeGen = new SnapAnalyser.CytoscapeJsGenerator();
            var diagramGen = new SnapAnalyser.DiagramGenerator();
            
            // Create test pipeline
            var pipeline = new SnapAnalyser.PipelineData
            {
                Name = "Test Cytoscape Pipeline",
                Snaps = new List<SnapAnalyser.SnapNode>
                {
                    new SnapAnalyser.SnapNode 
                    { 
                        Id = "start-node", 
                        Label = "Start Process", 
                        Type = "com.snaplogic.snap.api.InputSetting",
                        Category = SnapAnalyser.SnapCategory.ExternalSystem
                    },
                    new SnapAnalyser.SnapNode 
                    { 
                        Id = "transform-node", 
                        Label = "Transform Data", 
                        Type = "com.snaplogic.snap.api.Transformer",
                        Category = SnapAnalyser.SnapCategory.Transformation
                    },
                    new SnapAnalyser.SnapNode 
                    { 
                        Id = "end-node", 
                        Label = "Save Results", 
                        Type = "com.snaplogic.snap.api.OutputSetting",
                        Category = SnapAnalyser.SnapCategory.Database
                    }
                },
                Links = new List<SnapAnalyser.SnapLink>
                {
                    new SnapAnalyser.SnapLink { SourceId = "start-node", TargetId = "transform-node" },
                    new SnapAnalyser.SnapLink { SourceId = "transform-node", TargetId = "end-node" }
                }
            };
            
            // Generate diagram
            string diagram = diagramGen.GenerateDiagram(pipeline);
            
            // Check content
            bool hasCytoscape = diagram.Contains("cytoscape");
            bool hasFlowchart = diagram.Contains("flowchart");
            
            Console.WriteLine($"Generated diagram: {diagram.Length} characters");
            Console.WriteLine($"Contains cytoscape.js: {hasCytoscape}");
            Console.WriteLine($"Contains flowchart.js: {hasFlowchart}");
            
            // Save to file
            string fileName = "cytoscape_integration_test.html";
            File.WriteAllText(fileName, $@"<!DOCTYPE html>
<html>
<head>
    <title>Cytoscape Integration Test</title>
    <script src='https://unpkg.com/cytoscape@3.30.3/dist/cytoscape.min.js'></script>
    <script src='https://unpkg.com/dagre@0.8.5/dist/dagre.min.js'></script>
    <script src='https://unpkg.com/cytoscape-dagre@2.5.0/cytoscape-dagre.js'></script>
</head>
<body>
    <h1>Cytoscape.js Integration Test</h1>
    <p>Status: {(hasCytoscape ? "✅ SUCCESS" : "❌ FAILED")}</p>
    <p>Cytoscape.js found: {hasCytoscape}</p>
    <p>Flowchart.js found: {hasFlowchart}</p>
    {diagram}
</body>
</html>");
            
            Console.WriteLine($"Test results saved to: {fileName}");
            
            if (hasCytoscape && !hasFlowchart)
            {
                Console.WriteLine("🎉 SUCCESS: Cytoscape.js integration is working!");
            }
            else
            {
                Console.WriteLine("❌ ISSUE: Expected cytoscape.js, but got different content");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"ERROR: {ex.Message}");
            Console.WriteLine($"Stack: {ex.StackTrace}");
        }
        
        Console.WriteLine("Press any key to continue...");
        Console.ReadKey();
    }
}
