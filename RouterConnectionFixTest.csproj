<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="SelectPdf" Version="23.1.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="DocumentationGenerator.cs" />
    <Compile Include="AIDescriptionGenerator.cs" />
    <Compile Include="DescriptionCache.cs" />
    <Compile Include="PipelinePatternAnalyzer.cs" />
    <Compile Include="CytoscapeJsGenerator.cs" />
    <Compile Include="FlowControlConfigurationGenerator.cs" />
    <Compile Include="RouterConnectionFixTest.cs" />
  </ItemGroup>

</Project>
