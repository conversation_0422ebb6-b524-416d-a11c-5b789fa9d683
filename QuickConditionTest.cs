using System;
using System.Collections.Generic;
using SnapAnalyser;

// Simple standalone test to verify condition expression extraction and display
class QuickConditionTest
{
    static void Main()
    {
        // Create a test condition snap similar to the one in the user's example
        var conditionSnap = new SnapNode
        {
            Id = "test-condition",
            Label = "BlockTypes Condition",
            Type = "com-snaplogic-snaps-transform-conditional",
            Properties = new Dictionary<string, string>
            {
                { "expression_0", "$blocktypes.blockId == null || $blocktypes.blockId == ''" },
                { "expression_1", "$blocktypes.parentBlock == null || $blocktypes.parentBlock == ''" },
                { "expression_2", "$blocktypes.blocktype == 'District'" },
                { "output_0", "Remove District" },
                { "output_1", "Collapse Parent" },
                { "output_2", "Keep District" },
                { "settings.evaluateAll.value", "False" },
                { "settings.execution_mode.value", "Validate & Execute" },
                { "settings.nullSafeAccess.value", "False" }
            }
        };

        Console.WriteLine("======= TESTING CONDITION SNAP EXTRACTION =======\n");
        
        // Extract condition expressions using the public method
        var conditions = SnapBestPractices.ExtractConditions(conditionSnap);
        
        Console.WriteLine($"Found {conditions.Count} condition expressions:");
        foreach (var cond in conditions)
        {
            Console.WriteLine($"- {cond.Description}: {cond.Expression}");
        }
        
        Console.WriteLine("\n======= TEST COMPLETED =======");
    }
}
