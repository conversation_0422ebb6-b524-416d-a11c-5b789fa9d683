        private string GetDatabaseConnectionInfo(SnapNode snap)
        {
            if (snap.Category != SnapCategory.Database)
                return null;

            Console.WriteLine($"[DB-CONNECTION] Checking connection info for snap: {snap.Label}, Properties count: {snap.Properties.Count}");
            
            // Log all properties for debugging
            foreach (var prop in snap.Properties)
            {
                Console.WriteLine($"[DB-CONNECTION] Property: {prop.Key} = {prop.Value}");
            }

            // First, look for the nested account structure
            if (snap.Properties.ContainsKey("account"))
            {
                try
                {
                    string accountJson = snap.Properties["account"];
                    Console.WriteLine($"[DB-CONNECTION] Found account property, parsing JSON: {accountJson}");
                    
                    var accountObj = JObject.Parse(accountJson);
                    
                    // Navigate through the nested structure: account -> account_ref -> value -> label -> value
                    var accountRef = accountObj["account_ref"];
                    if (accountRef != null)
                    {
                        var valueObj = accountRef["value"];
                        if (valueObj != null)
                        {
                            var labelObj = valueObj["label"];
                            if (labelObj != null)
                            {
                                var accountName = labelObj["value"]?.ToString();
                                if (!string.IsNullOrEmpty(accountName))
                                {
                                    Console.WriteLine($"[DB-CONNECTION] Found account name from nested JSON: {accountName}");
                                    return accountName;
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[DB-CONNECTION] Error parsing account JSON: {ex.Message}");
                }
            }

            // Look for account/connection properties, but filter out boolean values
            var connectionProps = snap.Properties.Where(p =>
                (p.Key.ToLower().Contains("account") ||
                p.Key.ToLower().Contains("connect") ||
                p.Key.ToLower().Contains("datasource") ||
                p.Key.ToLower().Contains("database") ||
                p.Key.ToLower().Contains("server") ||
                p.Key.ToLower().Contains("sqlserver") ||
                p.Key.ToLower().Contains("sql_server") ||
                p.Key.ToLower().Contains("dbaccount") ||
                p.Key.ToLower().Contains("db_account") ||
                p.Key.ToLower().Contains("connectionstring") ||
                p.Key.ToLower().Contains("connection_string") ||
                p.Key.ToLower().Contains("host") ||
                p.Key.ToLower().Contains("instance") ||
                p.Key.ToLower().Contains("servername") ||
                p.Key.ToLower().Contains("server_name")) &&
                !string.IsNullOrEmpty(p.Value) &&
                !p.Value.Equals("true", StringComparison.OrdinalIgnoreCase) &&
                !p.Value.Equals("false", StringComparison.OrdinalIgnoreCase) &&
                !bool.TryParse(p.Value, out _))
                .ToList();

            Console.WriteLine($"[DB-CONNECTION] Found {connectionProps.Count} connection properties after filtering");

            foreach (var prop in connectionProps)
            {
                Console.WriteLine($"[DB-CONNECTION] Found valid connection property: {prop.Key} = {prop.Value}");
                
                // Clean up the account name for display
                string accountName = prop.Value;
                
                // Remove common prefixes/suffixes that might not be user-friendly
                if (accountName.StartsWith("$") && accountName.Contains("."))
                {
                    // Handle parameter references like "$account.database_account"
                    accountName = accountName.Substring(accountName.LastIndexOf('.') + 1);
                }
                
                return accountName;
            }

            // Look for database name in properties (also filter out boolean values)
            var dbNameProps = snap.Properties.Where(p =>
                (p.Key.ToLower().Contains("dbname") ||
                p.Key.ToLower().Contains("database_name") ||
                p.Key.ToLower().Contains("schema") ||
                p.Key.ToLower().Contains("databasename") ||
                p.Key.ToLower().Contains("catalog") ||
                p.Key.ToLower().Contains("initialcatalog") ||
                p.Key.ToLower().Contains("initial_catalog") ||
                p.Key.ToLower().Contains("db") ||
                p.Key.ToLower().Contains("table") ||
                p.Key.ToLower().Contains("tablename") ||
                p.Key.ToLower().Contains("table_name")) &&
                !string.IsNullOrEmpty(p.Value) &&
                !p.Value.Equals("true", StringComparison.OrdinalIgnoreCase) &&
                !p.Value.Equals("false", StringComparison.OrdinalIgnoreCase) &&
                !bool.TryParse(p.Value, out _))
                .ToList();

            Console.WriteLine($"[DB-CONNECTION] Found {dbNameProps.Count} database name properties after filtering");

            foreach (var prop in dbNameProps)
            {
                Console.WriteLine($"[DB-CONNECTION] Found database name property: {prop.Key} = {prop.Value}");
                return prop.Value;
            }

            Console.WriteLine($"[DB-CONNECTION] No connection info found for snap: {snap.Label}");
            return null;
        }
