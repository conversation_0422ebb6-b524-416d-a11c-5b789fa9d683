<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="AIDescriptionGenerator.cs" />
    <Compile Include="ConfigManager.cs" />
    <Compile Include="DescriptionCache.cs" />
    <Compile Include="DiagramGenerator.cs" />
    <Compile Include="DocumentationGenerator.cs" />
    <Compile Include="PathAnalysis.cs" />
    <Compile Include="PipelinePatternAnalyzer.cs" />
    <Compile Include="SlpAnalyzer.cs" />
    <Compile Include="SnapBestPractices.cs" />
    <Compile Include="SnapLogicClient.cs" />
    <Compile Include="TestBatchWithLogging.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
  </ItemGroup>

</Project>
