using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Runtime.Versioning;
using System.Diagnostics;

[assembly: SupportedOSPlatform("windows")]

namespace SnapAnalyser
{
    public partial class BatchDocumentationForm : Form
    {        private readonly SlpAnalyzer _analyzer;
        private readonly DiagramGenerator _diagramGenerator;
        private DocumentationGenerator _docGenerator;
        private ListBox lstFiles;
        private Button btnAddFiles;
        private Button btnRemoveFiles;
        private TextBox txtOutputFolder;
        private Button btnBrowseOutput;
        private Button btnOpenSourceFolder;
        private Button btnOpenOutputFolder;
        private CheckBox chkHtml;
        private CheckBox chkPdf;
        private CheckBox chkUseAI;
        private CheckBox chkUseCachedDescriptions;
        private Button btnProcess;
        private Button btnClose;
        private ProgressBar progressBar;
        private Label lblStatus;
          // Project management controls
        private Label lblCurrentProject;
        private Button btnNewProject;
        private Button btnLoadProject;
        private Button btnSaveProject;
        private Button btnEditProject;
        private ProjectData _currentProject;
        
        // Logging support
        private string _logFilePath;
        private bool _isLoggingEnabled = false;
          public BatchDocumentationForm()
        {
            // Initialize business logic objects
            _analyzer = new SlpAnalyzer();
            _diagramGenerator = new DiagramGenerator();
            _docGenerator = new DocumentationGenerator();

            // Initialize UI controls
            lstFiles = new ListBox();
            btnAddFiles = new Button();
            btnRemoveFiles = new Button();
            txtOutputFolder = new TextBox();
            btnBrowseOutput = new Button();
            btnOpenSourceFolder = new Button();
            btnOpenOutputFolder = new Button();
            chkHtml = new CheckBox();
            chkPdf = new CheckBox();
            chkUseAI = new CheckBox();
            chkUseCachedDescriptions = new CheckBox();
            btnProcess = new Button();
            btnClose = new Button();
            progressBar = new ProgressBar();
            lblStatus = new Label();
              // Initialize project management controls
            lblCurrentProject = new Label();
            btnNewProject = new Button();
            btnLoadProject = new Button();
            btnSaveProject = new Button();
            btnEditProject = new Button();

            // Set up the UI layout and properties
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.progressBar = new ProgressBar();
            this.lblStatus = new Label();

            // Form settings
            this.Text = "Batch SLP File Processor";
            this.Size = new System.Drawing.Size(850, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            // Files ListBox
            this.lstFiles.Location = new System.Drawing.Point(12, 12);
            this.lstFiles.Size = new System.Drawing.Size(650, 250);
            this.lstFiles.SelectionMode = SelectionMode.MultiExtended;

            // Add Files button
            this.btnAddFiles.Location = new System.Drawing.Point(668, 12);
            this.btnAddFiles.Size = new System.Drawing.Size(100, 30);
            this.btnAddFiles.Text = "Add Files...";
            this.btnAddFiles.Click += new EventHandler(btnAddFiles_Click);

            // Remove Files button
            this.btnRemoveFiles.Location = new System.Drawing.Point(668, 48);
            this.btnRemoveFiles.Size = new System.Drawing.Size(100, 30);
            this.btnRemoveFiles.Text = "Remove";
            this.btnRemoveFiles.Click += new EventHandler(btnRemoveFiles_Click);

            // Open Source Folder button
            this.btnOpenSourceFolder.Location = new System.Drawing.Point(668, 84);
            this.btnOpenSourceFolder.Size = new System.Drawing.Size(100, 30);
            this.btnOpenSourceFolder.Text = "Open Source";
            this.btnOpenSourceFolder.Click += new EventHandler(btnOpenSourceFolder_Click);

            // Output folder label
            var lblOutputFolder = new Label();
            lblOutputFolder.Text = "Output Folder:";
            lblOutputFolder.Location = new System.Drawing.Point(12, 275);
            lblOutputFolder.AutoSize = true;

            // Output folder textbox
            this.txtOutputFolder.Location = new System.Drawing.Point(12, 295);
            this.txtOutputFolder.Size = new System.Drawing.Size(650, 23);
            this.txtOutputFolder.Text = ConfigManager.LastOutputFolderLocation ?? 
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "SnapAnalyzer");

            // Browse Output button
            this.btnBrowseOutput.Location = new System.Drawing.Point(668, 295);
            this.btnBrowseOutput.Size = new System.Drawing.Size(100, 23);
            this.btnBrowseOutput.Text = "Browse...";
            this.btnBrowseOutput.Click += new EventHandler(btnBrowseOutput_Click);

            // Open Output Folder button
            this.btnOpenOutputFolder.Location = new System.Drawing.Point(668, 324);
            this.btnOpenOutputFolder.Size = new System.Drawing.Size(100, 23);
            this.btnOpenOutputFolder.Text = "Open Output";
            this.btnOpenOutputFolder.Click += new EventHandler(btnOpenOutputFolder_Click);

            // Checkboxes
            this.chkHtml.Location = new System.Drawing.Point(12, 330);
            this.chkHtml.Text = "Generate HTML Documentation";
            this.chkHtml.AutoSize = true;
            this.chkHtml.Checked = true;

            this.chkPdf.Location = new System.Drawing.Point(200, 330);
            this.chkPdf.Text = "Generate PDF Documentation";
            this.chkPdf.AutoSize = true;

            this.chkUseAI.Location = new System.Drawing.Point(12, 360);
            this.chkUseAI.Text = "Use AI for Pipeline Overview (Falls back to free service if needed)";
            this.chkUseAI.AutoSize = true;            
            this.chkUseCachedDescriptions.Location = new System.Drawing.Point(12, 390);
            this.chkUseCachedDescriptions.Text = "Use cached AI descriptions (faster but may use outdated content)";
            this.chkUseCachedDescriptions.AutoSize = true;
            this.chkUseCachedDescriptions.Checked = true;

            // Current project status label
            this.lblCurrentProject.Location = new System.Drawing.Point(12, 405);
            this.lblCurrentProject.AutoSize = true;
            this.lblCurrentProject.Text = "No project loaded";
            this.lblCurrentProject.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Italic);

            // Process button
            this.btnProcess.Location = new System.Drawing.Point(12, 430);
            this.btnProcess.Size = new System.Drawing.Size(100, 30);
            this.btnProcess.Text = "Process Files";
            this.btnProcess.Click += new EventHandler(btnProcess_Click);

            // Project management buttons on same row
            this.btnNewProject.Location = new System.Drawing.Point(125, 430);
            this.btnNewProject.Size = new System.Drawing.Size(90, 30);
            this.btnNewProject.Text = "New Project";
            this.btnNewProject.Click += new EventHandler(btnNewProject_Click);

            this.btnLoadProject.Location = new System.Drawing.Point(225, 430);
            this.btnLoadProject.Size = new System.Drawing.Size(90, 30);
            this.btnLoadProject.Text = "Load Project";
            this.btnLoadProject.Click += new EventHandler(btnLoadProject_Click);

            this.btnSaveProject.Location = new System.Drawing.Point(325, 430);
            this.btnSaveProject.Size = new System.Drawing.Size(90, 30);
            this.btnSaveProject.Text = "Save Project";
            this.btnSaveProject.Click += new EventHandler(btnSaveProject_Click);

            this.btnEditProject.Location = new System.Drawing.Point(425, 430);
            this.btnEditProject.Size = new System.Drawing.Size(90, 30);
            this.btnEditProject.Text = "Edit Project";
            this.btnEditProject.Click += new EventHandler(btnEditProject_Click);

            // Close button
            this.btnClose.Location = new System.Drawing.Point(668, 430);
            this.btnClose.Size = new System.Drawing.Size(100, 30);
            this.btnClose.Text = "Close";
            this.btnClose.Click += new EventHandler(btnClose_Click);            // Progress bar
            this.progressBar.Location = new System.Drawing.Point(12, 480);
            this.progressBar.Size = new System.Drawing.Size(756, 23);

            // Status label
            this.lblStatus.Location = new System.Drawing.Point(12, 510);
            this.lblStatus.AutoSize = true;
            this.lblStatus.Text = "Ready";

            // Add controls to form
            this.Controls.AddRange(new Control[] {
                this.lstFiles,
                this.btnAddFiles,
                this.btnRemoveFiles,
                this.btnOpenSourceFolder,
                lblOutputFolder,
                this.txtOutputFolder,
                this.btnBrowseOutput,
                this.btnOpenOutputFolder,
                this.chkHtml,
                this.chkPdf,
                this.chkUseAI,
                this.chkUseCachedDescriptions,
                this.lblCurrentProject,
                this.btnProcess,
                this.btnNewProject,
                this.btnLoadProject,
                this.btnSaveProject,
                this.btnEditProject,
                this.btnClose,
                this.progressBar,
                this.lblStatus
            });
        }

        private void btnAddFiles_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "SnapLogic Pipeline Files (*.slp)|*.slp|All files (*.*)|*.*";
                openFileDialog.Multiselect = true;
                openFileDialog.RestoreDirectory = true;

                if (!string.IsNullOrEmpty(ConfigManager.LastSlpFolderLocation))
                {
                    string folder = Path.GetDirectoryName(ConfigManager.LastSlpFolderLocation);
                    if (Directory.Exists(folder))
                    {
                        openFileDialog.InitialDirectory = folder;
                    }
                }

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    foreach (string file in openFileDialog.FileNames)
                    {
                        if (!lstFiles.Items.Contains(file))
                        {
                            lstFiles.Items.Add(file);
                        }
                    }

                    if (openFileDialog.FileNames.Length > 0)
                    {
                        ConfigManager.LastSlpFolderLocation = openFileDialog.FileNames[0];
                    }
                }
            }
        }

        private void btnRemoveFiles_Click(object sender, EventArgs e)
        {
            while (lstFiles.SelectedItems.Count > 0)
            {
                lstFiles.Items.Remove(lstFiles.SelectedItems[0]);
            }
        }

        private void btnBrowseOutput_Click(object sender, EventArgs e)
        {
            using (FolderBrowserDialog folderDialog = new FolderBrowserDialog())
            {
                folderDialog.Description = "Select output folder for documentation";
                folderDialog.SelectedPath = txtOutputFolder.Text;

                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    txtOutputFolder.Text = folderDialog.SelectedPath;
                    ConfigManager.LastOutputFolderLocation = folderDialog.SelectedPath;
                }
            }
        }

        private void btnOpenSourceFolder_Click(object sender, EventArgs e)
        {
            if (lstFiles.Items.Count == 0)
            {
                MessageBox.Show("Please add at least one SLP file to process.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // Open the folder of the first selected file
            string firstFile = lstFiles.SelectedItem?.ToString() ?? lstFiles.Items[0].ToString();
            string folderPath = Path.GetDirectoryName(firstFile);

            if (Directory.Exists(folderPath))
            {
                try
                {
                    Process.Start(new ProcessStartInfo("explorer.exe", folderPath) { UseShellExecute = true });
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error opening source folder: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
            {
                MessageBox.Show("Source folder does not exist.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnOpenOutputFolder_Click(object sender, EventArgs e)
        {
            if (!Directory.Exists(txtOutputFolder.Text))
            {
                MessageBox.Show("Output folder does not exist. Please set a valid output folder.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            try
            {
                Process.Start(new ProcessStartInfo("explorer.exe", txtOutputFolder.Text) { UseShellExecute = true });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening output folder: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnProcess_Click(object sender, EventArgs e)
        {
            if (lstFiles.Items.Count == 0)
            {
                MessageBox.Show("Please add at least one SLP file to process.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (!chkHtml.Checked && !chkPdf.Checked)
            {
                MessageBox.Show("Please select at least one output format.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (!Directory.Exists(txtOutputFolder.Text))
            {
                try
                {
                    Directory.CreateDirectory(txtOutputFolder.Text);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error creating output directory: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
            }            DisableUI();
            progressBar.Minimum = 0;
            progressBar.Maximum = 100;
            progressBar.Value = 0;            try
            {
                // Enable logging for batch processing
                EnableLogging(txtOutputFolder.Text);
                LogMessage($"[BATCH] Starting batch processing session with {lstFiles.Items.Count} files");
                LogMessage($"[BATCH] AI Enabled: {chkUseAI.Checked}, HTML: {chkHtml.Checked}, PDF: {chkPdf.Checked}");
                  // Initialize AI generator if enabled
                AIDescriptionGenerator aiGenerator = null;
                if (chkUseAI.Checked)
                {
                    UpdateStatus("Initializing AI engine...");
                    LogMessage("[BATCH] Initializing AI engine");
                    aiGenerator = new AIDescriptionGenerator(null);
                    aiGenerator.UseCachedDescriptions = chkUseCachedDescriptions.Checked;
                    aiGenerator.EnableLogging(txtOutputFolder.Text);
                    _docGenerator = new DocumentationGenerator(aiGenerator);
                    
                    // Enable logging on the DocumentationGenerator as well
                    _docGenerator.EnableLogging(Path.Combine(txtOutputFolder.Text, $"doc_generation_log_{DateTime.Now:yyyy-MM-dd_HHmmss}.log"));
                    LogMessage($"[BATCH] AI engine and DocumentationGenerator logging initialized with cached descriptions: {chkUseCachedDescriptions.Checked}");
                }

                int totalFiles = lstFiles.Items.Count;
                int processedFiles = 0;
                UpdateStatus($"Starting batch processing of {totalFiles} file(s)...");
                LogMessage($"[BATCH] Processing {totalFiles} files with 30-minute overall timeout");using (var cancellationTokenSource = new CancellationTokenSource())
                {
                    cancellationTokenSource.CancelAfter(TimeSpan.FromMinutes(30)); // Overall timeout
                    var cancellationToken = cancellationTokenSource.Token;                    foreach (string filePath in lstFiles.Items)
                    {
                        try
                        {
                            if (cancellationToken.IsCancellationRequested)
                            {
                                LogMessage("[BATCH] Overall cancellation token was requested - stopping processing");
                                UpdateStatus("Processing timeout reached. Some files were not processed.");
                                break;
                            }

                            string fileName = Path.GetFileName(filePath);
                            LogMessage($"[BATCH] Starting processing of file: {fileName}");
                            UpdateStatus($"Processing {fileName}...");
                            var processTask = ProcessSingleFileAsync(filePath, cancellationToken);
                            
                            // Add a timeout for each file
                            LogMessage($"[BATCH] Waiting for {fileName} with 5-minute file timeout");
                            if (await Task.WhenAny(processTask, Task.Delay(TimeSpan.FromMinutes(5), cancellationToken)) != processTask)
                            {
                                LogMessage($"[BATCH] File processing timeout reached for {fileName}");
                                throw new TimeoutException($"Processing timed out for {fileName}");                            }
                            
                            await processTask; // Will throw if the task failed
                            LogMessage($"[BATCH] Successfully completed processing of {Path.GetFileName(filePath)}");
                            processedFiles++;
                            UpdateProgress((int)((float)processedFiles / totalFiles * 100));
                        }
                        catch (OperationCanceledException ex)
                        {
                            string fileName = Path.GetFileName(filePath);
                            LogMessage($"[BATCH] Processing cancelled for {fileName}: {ex.Message}");
                            UpdateStatus($"Processing of {fileName} was cancelled.");
                        }
                        catch (TimeoutException tex)
                        {
                            LogMessage($"[BATCH] Timeout exception for {Path.GetFileName(filePath)}: {tex.Message}");
                            UpdateStatus($"Processing timeout: {tex.Message}");
                            MessageBox.Show($"Processing timed out for {Path.GetFileName(filePath)}. Moving to next file.", 
                                "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);                        }
                        catch (Exception ex)
                        {
                            string fileName = Path.GetFileName(filePath);
                            LogMessage($"[BATCH] Error processing {fileName}: {ex.Message}");
                            LogMessage($"[BATCH] Exception details: {ex}");
                            UpdateStatus($"Error processing {fileName}: {ex.Message}");
                            MessageBox.Show($"Error processing {fileName}: {ex.Message}", 
                                "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                }

                LogMessage($"[BATCH] Batch processing completed. Processed {processedFiles} of {totalFiles} files");

                string completionMessage = processedFiles == totalFiles 
                    ? $"Successfully processed all {totalFiles} files."
                    : $"Processed {processedFiles} of {totalFiles} files. Some files had errors.";
                
                UpdateStatus(completionMessage);
                MessageBox.Show(completionMessage, "Batch Processing Complete", 
                    MessageBoxButtons.OK, processedFiles == totalFiles ? MessageBoxIcon.Information : MessageBoxIcon.Warning);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error during batch processing: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                EnableUI();
            }
        }        private async Task ProcessSingleFileAsync(string filePath, CancellationToken cancellationToken = default)
        {
            string fileName = Path.GetFileNameWithoutExtension(filePath);            LogMessage($"[FILE] Starting ProcessSingleFileAsync for {fileName}");
              UpdateStatus($"Reading file: {fileName}...");
            LogMessage($"[FILE] Reading file content for {fileName}");
            string fileContent = await File.ReadAllTextAsync(filePath, cancellationToken).ConfigureAwait(false);
            LogMessage($"[FILE] File content read successfully, length: {fileContent.Length} characters");

            UpdateStatus($"Analyzing pipeline: {fileName}...");
            LogMessage($"[FILE] Starting pipeline analysis for {fileName}");
            var pipelineData = _analyzer.AnalyzePipeline(fileContent);
            LogMessage($"[FILE] Pipeline analysis completed for {fileName}");
            cancellationToken.ThrowIfCancellationRequested();
            
            UpdateStatus($"Generating diagram: {fileName}...");
            LogMessage($"[FILE] Starting diagram generation for {fileName}");
            string diagramSvg = _diagramGenerator.GenerateDiagram(pipelineData);
            LogMessage($"[FILE] Diagram generation completed for {fileName}");
            cancellationToken.ThrowIfCancellationRequested();            if (chkHtml.Checked)
            {
                UpdateStatus($"Generating HTML documentation for {fileName}...");
                LogMessage($"[FILE] Starting HTML generation for {fileName} - AI Enabled: {_docGenerator.HasAICapability}");
                  string htmlDoc;
                if (_docGenerator.HasAICapability)
                {
                    LogMessage($"[FILE] Creating timeout cancellation token for {fileName} (2 minute timeout)");
                    using (var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken))
                    {                        timeoutCts.CancelAfter(TimeSpan.FromMinutes(2)); // Timeout for AI generation
                        LogMessage($"[FILE] Calling GenerateHtmlDocumentationAsync for {fileName}");
                          try
                        {
                            htmlDoc = await _docGenerator.GenerateHtmlDocumentationAsync(pipelineData, diagramSvg, _currentProject, timeoutCts.Token).ConfigureAwait(false);
                            LogMessage($"[FILE] Successfully generated HTML documentation for {fileName}");
                        }
                        catch (OperationCanceledException ex)
                        {
                            LogMessage($"[FILE] HTML generation cancelled for {fileName}: {ex.Message}");
                            throw;
                        }
                        catch (Exception ex)
                        {
                            LogMessage($"[FILE] Error generating HTML documentation for {fileName}: {ex.Message}");
                            throw;
                        }
                    }
                }                else
                {
                    LogMessage($"[FILE] Using non-AI HTML generation for {fileName}");
                    htmlDoc = _docGenerator.GenerateHtmlDocumentation(pipelineData, diagramSvg, _currentProject);
                    LogMessage($"[FILE] Completed non-AI HTML generation for {fileName}");
                }cancellationToken.ThrowIfCancellationRequested();

                string htmlPath = Path.Combine(txtOutputFolder.Text, $"{fileName}_documentation.html");                UpdateStatus($"Saving HTML documentation for {fileName}...");
                LogMessage($"[FILE] Saving HTML documentation to: {htmlPath}");
                await File.WriteAllTextAsync(htmlPath, htmlDoc, System.Text.Encoding.UTF8, cancellationToken).ConfigureAwait(false);
                LogMessage($"[FILE] HTML documentation saved successfully for {fileName}");
            }

            if (chkPdf.Checked)
            {
                UpdateStatus($"Generating PDF documentation for {fileName}...");
                LogMessage($"[FILE] Starting PDF generation for {fileName}");
                string pdfPath = Path.Combine(txtOutputFolder.Text, $"{fileName}_documentation.pdf");
                _docGenerator.GeneratePdfDocumentation(pipelineData, diagramSvg, pdfPath);
                LogMessage($"[FILE] PDF documentation generated successfully for {fileName}");
                cancellationToken.ThrowIfCancellationRequested();
            }
            
            LogMessage($"[FILE] Completed ProcessSingleFileAsync for {fileName}");
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void UpdateStatus(string status)
        {
            if (lblStatus.InvokeRequired)
            {
                lblStatus.Invoke(new Action(() => lblStatus.Text = status));
            }
            else
            {
                lblStatus.Text = status;
            }
        }

        private void UpdateProgress(int value)
        {
            if (progressBar.InvokeRequired)
            {
                progressBar.Invoke(new Action(() => progressBar.Value = value));
            }
            else
            {
                progressBar.Value = value;
            }
        }        private void DisableUI()
        {
            void DisableControls()
            {
                btnAddFiles.Enabled = false;
                btnRemoveFiles.Enabled = false;
                btnBrowseOutput.Enabled = false;
                btnOpenSourceFolder.Enabled = false;
                btnOpenOutputFolder.Enabled = false;
                btnProcess.Enabled = false;
                chkHtml.Enabled = false;
                chkPdf.Enabled = false;
                chkUseAI.Enabled = false;
                chkUseCachedDescriptions.Enabled = false;
                progressBar.Value = 0;
            }

            if (InvokeRequired)
            {
                Invoke(new Action(DisableControls));
            }
            else
            {
                DisableControls();
            }
        }        private void EnableUI()
        {
            void EnableControls()
            {
                btnAddFiles.Enabled = true;
                btnRemoveFiles.Enabled = true;
                btnBrowseOutput.Enabled = true;
                btnOpenSourceFolder.Enabled = true;
                btnOpenOutputFolder.Enabled = true;
                btnProcess.Enabled = true;
                chkHtml.Enabled = true;
                chkPdf.Enabled = true;
                chkUseAI.Enabled = true;
                chkUseCachedDescriptions.Enabled = true;
            }

            if (InvokeRequired)
            {
                Invoke(new Action(EnableControls));
            }
            else            {
                EnableControls();
            }
        }

        // Method to enable logging
        private void EnableLogging(string outputFolder)
        {
            try
            {
                // Create logs directory if it doesn't exist
                Directory.CreateDirectory(outputFolder);

                // Set the log file path
                _logFilePath = Path.Combine(outputFolder, $"batch_processing_log_{DateTime.Now:yyyy-MM-dd_HHmmss}.log");
                _isLoggingEnabled = true;

                // Create the log file with a header
                File.WriteAllText(_logFilePath, $"Batch Processing Log - Started at {DateTime.Now}\r\n");
                LogMessage("Batch processing logging initialized");
            }
            catch (Exception ex)
            {
                // Can't use LogMessage here since logging isn't set up yet
                Console.WriteLine($"Error setting up batch logging: {ex.Message}");
                _isLoggingEnabled = false;
            }
        }

        // Method to log messages both to console and file
        private void LogMessage(string message)
        {
            // Always log to console
            Console.WriteLine(message);

            // Log to file if enabled
            if (_isLoggingEnabled)
            {
                try
                {
                    File.AppendAllText(_logFilePath, $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}\r\n");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error writing to batch log file: {ex.Message}");
                }
            }
        }

        // Project management event handlers
        private void btnNewProject_Click(object sender, EventArgs e)
        {
            using (var projectForm = new ProjectForm())
            {
                if (projectForm.ShowDialog() == DialogResult.OK)
                {
                    _currentProject = projectForm.Project;
                    UpdateProjectUI();
                    
                    // Sync project settings with form controls
                    chkHtml.Checked = _currentProject.GenerateHtml;
                    chkPdf.Checked = _currentProject.GeneratePdf;
                    chkUseAI.Checked = _currentProject.UseAI;
                    chkUseCachedDescriptions.Checked = _currentProject.UseCachedDescriptions;
                    
                    if (!string.IsNullOrEmpty(_currentProject.OutputFolder))
                    {
                        txtOutputFolder.Text = _currentProject.OutputFolder;
                    }
                    
                    btnSaveProject.Enabled = true;
                    btnEditProject.Enabled = true;
                }
            }
        }

        private void btnLoadProject_Click(object sender, EventArgs e)
        {
            using (var openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "Project Files (*.json)|*.json|All files (*.*)|*.*";
                openFileDialog.Title = "Load Project";
                
                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        _currentProject = ProjectData.LoadFromFile(openFileDialog.FileName);
                        UpdateProjectUI();
                        
                        // Load project files into the list
                        lstFiles.Items.Clear();
                        foreach (var file in _currentProject.SlpFiles)
                        {
                            if (File.Exists(file))
                            {
                                lstFiles.Items.Add(file);
                            }
                        }
                        
                        // Sync project settings with form controls
                        chkHtml.Checked = _currentProject.GenerateHtml;
                        chkPdf.Checked = _currentProject.GeneratePdf;
                        chkUseAI.Checked = _currentProject.UseAI;
                        chkUseCachedDescriptions.Checked = _currentProject.UseCachedDescriptions;
                        
                        if (!string.IsNullOrEmpty(_currentProject.OutputFolder))
                        {
                            txtOutputFolder.Text = _currentProject.OutputFolder;
                        }
                        
                        btnSaveProject.Enabled = true;
                        btnEditProject.Enabled = true;
                        
                        MessageBox.Show($"Project '{_currentProject.Name}' loaded successfully.", "Project Loaded", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error loading project: {ex.Message}", "Error", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void btnSaveProject_Click(object sender, EventArgs e)
        {
            if (_currentProject == null)
            {
                MessageBox.Show("No project to save.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            
            // Update project with current form state
            _currentProject.SlpFiles.Clear();
            foreach (string file in lstFiles.Items)
            {
                _currentProject.SlpFiles.Add(file);
            }
            
            _currentProject.OutputFolder = txtOutputFolder.Text;
            _currentProject.GenerateHtml = chkHtml.Checked;
            _currentProject.GeneratePdf = chkPdf.Checked;
            _currentProject.UseAI = chkUseAI.Checked;
            _currentProject.UseCachedDescriptions = chkUseCachedDescriptions.Checked;
            _currentProject.LastModified = DateTime.Now;
            
            using (var saveFileDialog = new SaveFileDialog())
            {
                saveFileDialog.Filter = "Project Files (*.json)|*.json|All files (*.*)|*.*";
                saveFileDialog.Title = "Save Project";
                saveFileDialog.FileName = $"{_currentProject.Name}.json";
                
                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        _currentProject.SaveToFile(saveFileDialog.FileName);
                        MessageBox.Show($"Project '{_currentProject.Name}' saved successfully.", "Project Saved", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error saving project: {ex.Message}", "Error", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void btnEditProject_Click(object sender, EventArgs e)
        {
            if (_currentProject == null)
            {
                MessageBox.Show("No project to edit.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            
            using (var projectForm = new ProjectForm(_currentProject))
            {
                if (projectForm.ShowDialog() == DialogResult.OK)
                {
                    _currentProject = projectForm.Project;
                    UpdateProjectUI();
                }
            }
        }

        private void UpdateProjectUI()
        {
            if (_currentProject != null)
            {
                lblCurrentProject.Text = $"Project: {_currentProject.Name}";
                btnSaveProject.Enabled = true;
                btnEditProject.Enabled = true;
            }
            else
            {
                lblCurrentProject.Text = "No project loaded";
                btnSaveProject.Enabled = false;
                btnEditProject.Enabled = false;
            }
        }
    }
}