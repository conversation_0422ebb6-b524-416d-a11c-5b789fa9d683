using System;

namespace SnapAnalyser
{
    public class TestRouterLogic
    {
        public static void Main()
        {
            // Create a mock Router snap
            var routerSnap = new SnapNode
            {
                Label = "Route Unit Count",
                Type = "com-snaplogic-snaps-flow-router",
                Category = SnapCategory.FlowControl
            };

            // Test the logic
            string snapType = routerSnap.Type.ToLowerInvariant();
            Console.WriteLine($"Snap Type: '{routerSnap.Type}' -> Lower: '{snapType}'");
            Console.WriteLine($"Snap Category: {routerSnap.Category}");
            
            bool categoryCheck = routerSnap.Category == SnapCategory.FlowControl;
            bool routerCheck = snapType.Contains("router");
            bool conditionCheck = snapType.Contains("condition");
            bool filterCheck = snapType.Contains("filter");
            
            Console.WriteLine($"Category == FlowControl: {categoryCheck}");
            Console.WriteLine($"Contains 'router': {routerCheck}");
            Console.WriteLine($"Contains 'condition': {conditionCheck}");
            Console.WriteLine($"Contains 'filter': {filterCheck}");
            
            bool isFlowControl = categoryCheck && (routerCheck || conditionCheck || filterCheck);
            Console.WriteLine($"Final IsFlowControl result: {isFlowControl}");
            
            bool isTransformation = routerSnap.Category == SnapCategory.Transformation && 
                   (snapType.Contains("map") || snapType.Contains("transform") || snapType.Contains("datatransform"));
            Console.WriteLine($"IsTransformation result: {isTransformation}");
            
            bool finalResult = isTransformation || isFlowControl;
            Console.WriteLine($"Final IsMapperOrConditionSnap result: {finalResult}");
        }
    }
}
