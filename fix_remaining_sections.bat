@echo off
echo Making final changes to SnapBestPractices.cs...

REM First, make a backup
copy "SnapBestPractices.cs" "SnapBestPractices.cs.final_backup"

REM For Copy snap
powershell -Command "(Get-Content -Path 'SnapBestPractices.cs' -Raw) -replace '(?s)details.AppendLine\(\"<p><strong>Best Practices for Copy Snaps:</strong></p>\"\);\r?\ndetails.AppendLine\(\"<ul><li>Use Copy snaps to duplicate document streams for parallel processing paths\.</li><li>Be aware that this creates full duplicates of documents, which can increase memory usage\.</li></ul>\"\);\r?\ndetails.AppendLine\(\"<p><strong>Example Usage:</strong></p>\"\);\r?\ndetails.AppendLine\(\"<p>This Copy snap duplicates each incoming document to its multiple output views, allowing identical data to flow through different processing branches simultaneously\.</p>\"\);', 'details.AppendLine(\"<p><strong>Best Practices for Copy Snaps:</strong></p>\");\r\ndetails.AppendLine(\"<ul><li>Use Copy snaps to duplicate document streams for parallel processing paths.</li><li>Be aware that this creates full duplicates of documents, which can increase memory usage.</li></ul>\");\r\n// No Example Usage section for Copy snap types as per requirements;'" > "temp1.cs"

REM For Pipeline Execute snap
powershell -Command "(Get-Content -Path 'temp1.cs' -Raw) -replace '(?s)details.AppendLine\(\"<p><strong>Best Practices for Pipeline Execute Snaps:</strong></p>\"\);\r?\ndetails.AppendLine\(\"<ul><li>Pass only necessary parameters to the child pipeline\.</li><li>Ensure the child pipeline is designed to handle the inputs and produce expected outputs\.</li><li>Manage error handling for child pipeline failures\.</li></ul>\"\);\r?\ndetails.AppendLine\(\"<p><strong>Example Usage:</strong></p>\"\);', 'details.AppendLine(\"<p><strong>Best Practices for Pipeline Execute Snaps:</strong></p>\");\r\ndetails.AppendLine(\"<ul><li>Pass only necessary parameters to the child pipeline.</li><li>Ensure the child pipeline is designed to handle the inputs and produce expected outputs.</li><li>Manage error handling for child pipeline failures.</li></ul>\");\r\n// No Example Usage section for Pipeline Execute snap types as per requirements;'" > "temp2.cs"

REM For Generic Transformation snap
powershell -Command "(Get-Content -Path 'temp2.cs' -Raw) -replace '(?s)details.AppendLine\(\"<p><strong>Best Practices for this Transformation Snap:</strong></p>\"\);\r?\ndetails.AppendLine\(\"<ul><li>Understand the specific transformation logic this snap applies\.</li><li>Validate output against expected schema and values\.</li></ul>\"\);\r?\ndetails.AppendLine\(\"<p><strong>Example Usage:</strong></p>\"\);', 'details.AppendLine(\"<p><strong>Best Practices for this Transformation Snap:</strong></p>\");\r\ndetails.AppendLine(\"<ul><li>Understand the specific transformation logic this snap applies.</li><li>Validate output against expected schema and values.</li></ul>\");\r\n// No Example Usage section for Generic Transformation snap types as per requirements;'" > "SnapBestPractices.cs"

REM Clean up temp files
del temp1.cs
del temp2.cs

echo Final changes completed.
