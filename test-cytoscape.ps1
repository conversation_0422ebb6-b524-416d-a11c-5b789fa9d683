# Test Cytoscape.js Integration
Write-Host "Testing Cytoscape.js Integration" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

try {
    # Build the project first
    Write-Host "Building project..." -ForegroundColor Yellow
    dotnet build SnapAnalyzer.csproj
    
    # Run the cytoscape test
    Write-Host "Running cytoscape test..." -ForegroundColor Yellow
    dotnet run --project SnapAnalyzer.csproj -- --test-cytoscape-js
    
    Write-Host "Test completed!" -ForegroundColor Green
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
