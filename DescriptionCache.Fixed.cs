using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Linq;

namespace SnapAnalyser
{
    public class DescriptionCache
    {
        private readonly string _cacheFilePath;
        private Dictionary<string, CacheItem> _cache;
        private readonly object _cacheLock = new object();
        private bool _isInitialized = false;

        public DescriptionCache()
        {
            // Create a cache file in the user's local app data
            string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            string appFolder = Path.Combine(appDataPath, "SnapDocumenter");
            Directory.CreateDirectory(appFolder);
            _cacheFilePath = Path.Combine(appFolder, "description_cache.json");
            _cache = new Dictionary<string, CacheItem>();
        }

        public async Task InitializeAsync()
        {
            if (_isInitialized) return;

            try
            {
                if (File.Exists(_cacheFilePath))
                {
                    string jsonContent = await File.ReadAllTextAsync(_cacheFilePath).ConfigureAwait(false);
                    if (!string.IsNullOrEmpty(jsonContent))
                    {
                        var cacheData = JsonSerializer.Deserialize<Dictionary<string, CacheItem>>(jsonContent);
                        lock (_cacheLock)
                        {
                            _cache = cacheData ?? new Dictionary<string, CacheItem>();
                        }
                    }
                }

                // Clean up expired entries after loading
                lock (_cacheLock)
                {
                    var expiredKeys = _cache.Where(kvp => IsExpired(kvp.Value)).Select(kvp => kvp.Key).ToList();
                    foreach (var key in expiredKeys)
                    {
                        _cache.Remove(key);
                    }
                }

                if (_cache.Count > 0)
                {
                    await SaveAsync().ConfigureAwait(false);
                }
                
                await CleanupExpiredEntriesAsync().ConfigureAwait(false);
                _isInitialized = true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing cache: {ex.Message}");
                lock (_cacheLock)
                {
                    _cache = new Dictionary<string, CacheItem>();
                }
                _isInitialized = true;
            }
        }

        private async Task SaveAsync()
        {
            try
            {
                Dictionary<string, CacheItem> cacheToSave;
                lock (_cacheLock)
                {
                    cacheToSave = new Dictionary<string, CacheItem>(_cache);
                }

                string jsonContent = JsonSerializer.Serialize(cacheToSave, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(_cacheFilePath, jsonContent).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving cache: {ex.Message}");
            }
        }

        // FIXED: Synchronous methods now use async/await properly without blocking
        public bool TryGetDescription(SnapNode snap, out string description)
        {
            description = null;
            
            if (!_isInitialized)
            {
                // Use Task.Run to avoid deadlock in UI context
                Task.Run(async () => await InitializeAsync().ConfigureAwait(false)).GetAwaiter().GetResult();
            }

            string cacheKey = GenerateCacheKey(snap);

            lock (_cacheLock)
            {
                if (_cache.TryGetValue(cacheKey, out var cachedItem))
                {
                    // Check if the cached item has expired
                    if (IsExpired(cachedItem))
                    {
                        // Remove expired item
                        _cache.Remove(cacheKey);
                        // Save changes asynchronously without blocking
                        Task.Run(async () => await SaveAsync().ConfigureAwait(false));
                        return false;
                    }

                    description = cachedItem.Description;
                    return true;
                }
            }

            return false;
        }

        public bool TryGetPseudocode(SnapNode snap, out string pseudocode)
        {
            pseudocode = null;
            
            if (!_isInitialized)
            {
                // Use Task.Run to avoid deadlock in UI context
                Task.Run(async () => await InitializeAsync().ConfigureAwait(false)).GetAwaiter().GetResult();
            }

            string cacheKey = GenerateCacheKey(snap);

            lock (_cacheLock)
            {
                if (_cache.TryGetValue(cacheKey, out var cachedItem))
                {
                    // Check if the cached item has expired
                    if (IsExpired(cachedItem))
                    {
                        // Remove expired item
                        _cache.Remove(cacheKey);
                        // Save changes asynchronously without blocking
                        Task.Run(async () => await SaveAsync().ConfigureAwait(false));
                        return false;
                    }

                    pseudocode = cachedItem.Pseudocode;
                    return true;
                }
            }

            return false;
        }

        public void StoreDescription(SnapNode snap, string description, string pseudocode)
        {
            if (!_isInitialized)
            {
                // Use Task.Run to avoid deadlock in UI context
                Task.Run(async () => await InitializeAsync().ConfigureAwait(false)).GetAwaiter().GetResult();
            }

            string cacheKey = GenerateCacheKey(snap);

            lock (_cacheLock)
            {
                if (_cache.TryGetValue(cacheKey, out var existingItem))
                {
                    // Update existing item, preserving non-null values
                    if (!string.IsNullOrEmpty(description))
                        existingItem.Description = description;
                    if (!string.IsNullOrEmpty(pseudocode))
                        existingItem.Pseudocode = pseudocode;
                    existingItem.Timestamp = DateTime.Now;
                }
                else
                {
                    // Create new item
                    _cache[cacheKey] = new CacheItem
                    {
                        Description = description,
                        Pseudocode = pseudocode,
                        Timestamp = DateTime.Now
                    };
                }
            }

            // Save changes asynchronously without blocking
            Task.Run(async () => await SaveAsync().ConfigureAwait(false));
        }

        public async Task SaveDescriptionAsync(SnapNode snap, string description)
        {
            if (!_isInitialized)
            {
                await InitializeAsync().ConfigureAwait(false);
            }

            string cacheKey = GenerateCacheKey(snap);

            lock (_cacheLock)
            {
                if (_cache.TryGetValue(cacheKey, out var existingItem))
                {
                    existingItem.Description = description;
                    existingItem.Timestamp = DateTime.Now;
                }
                else
                {
                    _cache[cacheKey] = new CacheItem
                    {
                        Description = description,
                        Timestamp = DateTime.Now
                    };
                }
            }

            await SaveAsync().ConfigureAwait(false);
        }

        public async Task SavePseudocodeAsync(SnapNode snap, string pseudocode)
        {
            if (!_isInitialized)
            {
                await InitializeAsync().ConfigureAwait(false);
            }

            string cacheKey = GenerateCacheKey(snap);

            lock (_cacheLock)
            {
                if (_cache.TryGetValue(cacheKey, out var existingItem))
                {
                    existingItem.Pseudocode = pseudocode;
                    existingItem.Timestamp = DateTime.Now;
                }
                else
                {
                    _cache[cacheKey] = new CacheItem
                    {
                        Pseudocode = pseudocode,
                        Timestamp = DateTime.Now
                    };
                }
            }

            await SaveAsync().ConfigureAwait(false);
        }

        // FIXED: Proper async implementation
        public void ClearExpiredEntries()
        {
            // Use Task.Run to avoid deadlock in UI context
            Task.Run(async () => 
            {
                await CleanupExpiredEntriesAsync().ConfigureAwait(false);
                await SaveAsync().ConfigureAwait(false);
            });
        }

        public async Task CleanupExpiredEntriesAsync()
        {
            if (!_isInitialized)
            {
                await InitializeAsync().ConfigureAwait(false);
            }

            List<string> expiredKeys;
            lock (_cacheLock)
            {
                expiredKeys = _cache.Where(kvp => IsExpired(kvp.Value)).Select(kvp => kvp.Key).ToList();
                foreach (var key in expiredKeys)
                {
                    _cache.Remove(key);
                }
            }

            if (expiredKeys.Count > 0)
            {
                await SaveAsync().ConfigureAwait(false);
            }
        }

        private string GenerateCacheKey(SnapNode snap)
        {
            // Create a unique key based on snap type and key properties
            var keyProperties = new List<string> { snap.Type, snap.Label };
            
            // Add significant properties to the key
            if (snap.Properties.TryGetValue("settings", out string settings))
            {
                keyProperties.Add(settings.GetHashCode().ToString());
            }
            
            return string.Join("|", keyProperties);
        }

        private bool IsExpired(CacheItem item)
        {
            // Cache expires after 24 hours
            return DateTime.Now - item.Timestamp > TimeSpan.FromHours(24);
        }

        public class CacheItem
        {
            public string Description { get; set; }
            public string Pseudocode { get; set; }
            public DateTime Timestamp { get; set; }
        }
    }
}
