using System;
using System.IO;
using System.Text;
using System.Reflection;

namespace SnapAnalyser
{
    public class TestMarkdownConversion
    {
        public static void RunTest()
        {
            Console.WriteLine("Testing Markdown to HTML conversion...");

            // Get private method using reflection
            var docGenType = typeof(DocumentationGenerator);
            var docGen = new DocumentationGenerator();
            var methodInfo = docGenType.GetMethod("ConvertMarkdownToHtml", 
                BindingFlags.NonPublic | BindingFlags.Instance);

            if (methodInfo == null)
            {
                Console.WriteLine("ERROR: Could not find ConvertMarkdownToHtml method");
                return;
            }            // Test samples
            var testCases = new[]
            {
                new { Name = "Basic Formatting", 
                    Input = "This is **bold** and this is *italic* text.",
                    Expected = "<p>This is <strong>bold</strong> and this is <em>italic</em> text.</p>" },
                
                new { Name = "Heading", 
                    Input = "# Main Heading\nThis is regular text.",
                    Expected = "<h3>Main Heading</h3>\n<p>This is regular text.</p>" },
                
                new { Name = "Nested Lists", 
                    Input = "- Item 1\n- Item 2\n- Item 3",
                    Expected = "<ul>\n<li>Item 1</li>\n<li>Item 2</li>\n<li>Item 3</li>\n</ul>" },
                
                new { Name = "Ordered Lists", 
                    Input = "1. First\n2. Second\n3. Third",
                    Expected = "<ol>\n<li>First</li>\n<li>Second</li>\n<li>Third</li>\n</ol>" },
                
                new { Name = "Code Block", 
                    Input = "```\nvar x = 10;\nconsole.log(x);\n```",
                    Expected = "<pre><code>\nvar x = 10;\nconsole.log(x);\n</code></pre>" },
                
                new { Name = "Inline Code", 
                    Input = "Use the `console.log()` function.",
                    Expected = "<p>Use the <code>console.log()</code> function.</p>" },
                
                new { Name = "Links", 
                    Input = "Check [this link](https://example.com) for more info.",
                    Expected = "<p>Check <a href=\"https://example.com\">this link</a> for more info.</p>" },
                
                new { Name = "Paragraphs", 
                    Input = "Paragraph 1.\n\nParagraph 2.",
                    Expected = "<p>Paragraph 1.</p>\n<p>Paragraph 2.</p>" },
                
                new { Name = "Mixed Content", 
                    Input = "# Title\n\n**Introduction**\n\n- Item 1\n- Item 2\n\nSome `code` here.",
                    Expected = "<h3>Title</h3>\n<p><strong>Introduction</strong></p>\n<ul>\n<li>Item 1</li>\n<li>Item 2</li>\n</ul>\n<p>Some <code>code</code> here.</p>" },
                
                new { Name = "Complex AI Description", 
                    Input = "# Union Snap\n\nThe Union snap merges multiple document streams into a single output stream without changing the document content.\n\n## Key Features\n\n- Combines documents from multiple inputs\n- Preserves document structure and content\n- Simple configuration with no mapping required\n\nThis component is particularly useful for:\n\n1. Combining data from multiple sources\n2. Merging parallel processing results\n3. Implementing fan-in patterns\n\n```\nfunction process(inputs) {\n  // Simply pass all documents to output\n  for each input in inputs {\n    for each document in input {\n      output.write(document)\n    }\n  }\n}\n```",
                    Expected = "<h3>Union Snap</h3>\n<p>The Union snap merges multiple document streams into a single output stream without changing the document content.</p>\n<h4>Key Features</h4>\n<ul>\n<li>Combines documents from multiple inputs</li>\n<li>Preserves document structure and content</li>\n<li>Simple configuration with no mapping required</li>\n</ul>\n<p>This component is particularly useful for:</p>\n<ol>\n<li>Combining data from multiple sources</li>\n<li>Merging parallel processing results</li>\n<li>Implementing fan-in patterns</li>\n</ol>\n<pre><code>\nfunction process(inputs) {\n  // Simply pass all documents to output\n  for each input in inputs {\n    for each document in input {\n      output.write(document)\n    }\n  }\n}\n</code></pre>" },
                
                // Test cases specifically for asterisks issues
                new { Name = "Single Asterisks for Emphasis",
                    Input = "Configuration: *passThrough: True*",
                    Expected = "<p>Configuration: <em>passThrough: True</em></p>" },
                
                new { Name = "Double Asterisks for Strong Emphasis",
                    Input = "**Output snap properties:**",
                    Expected = "<p><strong>Output snap properties:</strong></p>" },
                    
                new { Name = "Mixed Asterisks Usage",
                    Input = "**Bold Text** with some *italic text* in the same line.",
                    Expected = "<p><strong>Bold Text</strong> with some <em>italic text</em> in the same line.</p>" },
                    
                new { Name = "Asterisks in Lists",
                    Input = "- Item with *italic* text\n- Item with **bold** text",
                    Expected = "<ul>\n<li>Item with <em>italic</em> text</li>\n<li>Item with <strong>bold</strong> text</li>\n</ul>" },
                
                // New test cases for inline code in property descriptions
                new { Name = "Inline Code in Technical Descriptions",
                    Input = "With `passThrough` set to `false`, only mapped fields are included.",
                    Expected = "<p>With <code>passThrough</code> set to <code>false</code>, only mapped fields are included.</p>" },
                
                new { Name = "Multiple Inline Code in a Sentence",
                    Input = "No Pass-through: With `autoCommit` set to `True`, the mapper snap will automatically commit documents.",
                    Expected = "<p>No Pass-through: With <code>autoCommit</code> set to <code>True</code>, the mapper snap will automatically commit documents.</p>" },
            };

            int passed = 0;
            int failed = 0;

            // Test each case
            foreach (var testCase in testCases)
            {
                // Invoke the method
                var result = (string)methodInfo.Invoke(docGen, new object[] { testCase.Input });

                // Normalize whitespace for comparison
                var normalizedResult = NormalizeWhitespace(result);
                var normalizedExpected = NormalizeWhitespace(testCase.Expected);

                // Compare and display results
                if (normalizedResult == normalizedExpected)
                {
                    Console.WriteLine($"✓ PASS: {testCase.Name}");
                    passed++;
                }
                else
                {
                    Console.WriteLine($"✗ FAIL: {testCase.Name}");
                    Console.WriteLine("  Expected:");
                    Console.WriteLine($"  {normalizedExpected}");
                    Console.WriteLine("  Actual:");
                    Console.WriteLine($"  {normalizedResult}");
                    failed++;
                }
            }

            Console.WriteLine($"\nTest summary: {passed} passed, {failed} failed");

            // Also write output to file for easier comparison
            WriteTestResultsToFile(testCases, methodInfo, docGen);
        }

        private static string NormalizeWhitespace(string input)
        {
            // Replace all whitespace sequences with a single space
            return System.Text.RegularExpressions.Regex.Replace(input, @"\s+", " ").Trim();
        }

        private static void WriteTestResultsToFile(dynamic testCases, MethodInfo methodInfo, DocumentationGenerator docGen)
        {
            var outputFolder = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.Desktop), 
                "MarkdownTests");
                
            Directory.CreateDirectory(outputFolder);
            
            var outputFile = Path.Combine(outputFolder, "test_results.html");
            
            var html = new StringBuilder();
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html>");
            html.AppendLine("<head>");
            html.AppendLine("  <title>Markdown Conversion Test Results</title>");
            html.AppendLine("  <style>");
            html.AppendLine("    body { font-family: Arial, sans-serif; margin: 20px; }");
            html.AppendLine("    .test-case { margin-bottom: 30px; border: 1px solid #ccc; padding: 15px; }");
            html.AppendLine("    .pass { background-color: #e6ffe6; }");
            html.AppendLine("    .fail { background-color: #ffe6e6; }");
            html.AppendLine("    .input, .expected, .actual { margin: 10px 0; padding: 10px; border: 1px solid #eee; }");
            html.AppendLine("    pre { background-color: #f5f5f5; padding: 10px; overflow: auto; }");
            html.AppendLine("    .label { font-weight: bold; margin-top: 10px; }");
            html.AppendLine("    h2 { margin-top: 0; }");
            html.AppendLine("  </style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");
            html.AppendLine("  <h1>Markdown to HTML Conversion Test Results</h1>");
            
            int passed = 0;
            foreach (var testCase in testCases)
            {
                // Invoke the method
                var result = (string)methodInfo.Invoke(docGen, new object[] { testCase.Input });
                
                // Compare and determine if passed
                var normalizedResult = NormalizeWhitespace(result);
                var normalizedExpected = NormalizeWhitespace(testCase.Expected);
                bool isPassed = normalizedResult == normalizedExpected;
                
                if (isPassed) passed++;
                
                // Add test case to HTML
                html.AppendLine($"  <div class=\"test-case {(isPassed ? "pass" : "fail")}\">");
                html.AppendLine($"    <h2>{testCase.Name} {(isPassed ? "✓" : "✗")}</h2>");
                html.AppendLine("    <div class=\"input\">");
                html.AppendLine("      <div class=\"label\">Input Markdown:</div>");
                html.AppendLine($"      <pre>{System.Web.HttpUtility.HtmlEncode(testCase.Input)}</pre>");
                html.AppendLine("    </div>");
                html.AppendLine("    <div class=\"expected\">");
                html.AppendLine("      <div class=\"label\">Expected HTML:</div>");
                html.AppendLine($"      <pre>{System.Web.HttpUtility.HtmlEncode(testCase.Expected)}</pre>");
                html.AppendLine("    </div>");
                html.AppendLine("    <div class=\"actual\">");
                html.AppendLine("      <div class=\"label\">Actual HTML:</div>");
                html.AppendLine($"      <pre>{System.Web.HttpUtility.HtmlEncode(result)}</pre>");
                html.AppendLine("    </div>");
                html.AppendLine("    <div class=\"rendered\">");
                html.AppendLine("      <div class=\"label\">Rendered Result:</div>");
                html.AppendLine($"      {result}");
                html.AppendLine("    </div>");
                html.AppendLine("  </div>");
            }
            
            html.AppendLine($"  <h2>Summary: {passed}/{testCases.Length} tests passed</h2>");
            html.AppendLine("</body>");
            html.AppendLine("</html>");
            
            File.WriteAllText(outputFile, html.ToString());
            Console.WriteLine($"Detailed results written to: {outputFile}");
        }

        public static void TestInlineCodePlaceholders()
        {
            Console.WriteLine("Testing inline code placeholders...");

            // Get private method using reflection
            var docGenType = typeof(DocumentationGenerator);
            var docGen = new DocumentationGenerator();
            var methodInfo = docGenType.GetMethod("ConvertMarkdownToHtml", 
                BindingFlags.NonPublic | BindingFlags.Instance);

            if (methodInfo == null)
            {
                Console.WriteLine("ERROR: Could not find ConvertMarkdownToHtml method");
                return;
            }
            
            // Test for the specific issue with placeholders
            var problematicMarkdown = "No Pass-through: With `passThrough` set to `false`, only mapped fields are included.";
            Console.WriteLine($"Testing problematic markdown: {problematicMarkdown}");
            
            var html = (string)methodInfo.Invoke(docGen, new object[] { problematicMarkdown });
            Console.WriteLine($"Converted HTML: {html}");
            
            // Check for the presence of placeholders
            if (html.Contains("INLINE") || html.Contains("CODE"))
            {
                Console.WriteLine("FAILED: Output contains placeholder text");
            }
            else
            {
                Console.WriteLine("SUCCESS: No placeholders found in output");
                
                // Check if code tags are present
                if (html.Contains("<code>passThrough</code>") && html.Contains("<code>false</code>"))
                {
                    Console.WriteLine("Code tags were correctly inserted");
                }
                else
                {
                    Console.WriteLine("WARNING: Code tags may be missing or incorrect");
                }
            }
            
            // Test with the specific example found in the HTML
            var realExampleMarkdown = "No Pass-through: With `autoCommit` set to `True`, only the explicitly mapped fields are included in the output.";
            Console.WriteLine($"\nTesting real example: {realExampleMarkdown}");
            
            html = (string)methodInfo.Invoke(docGen, new object[] { realExampleMarkdown });
            Console.WriteLine($"Converted HTML: {html}");
            
            // Check for the presence of placeholders
            if (html.Contains("INLINE") || (html.Contains("CODE") && !html.Contains("<code>")))
            {
                Console.WriteLine("FAILED: Real example output contains placeholder text");
            }
            else
            {
                Console.WriteLine("SUCCESS: Real example contains no placeholders");
            }
        }
    }
}
