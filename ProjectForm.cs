using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

namespace SnapAnalyser
{
    public partial class ProjectForm : Form
    {
        private TextBox txtProjectName;
        private TextBox txtProjectDescription;
        private TextBox txtProjectPurpose;        private Button btnOK;
        private Button btnCancel;
        
        [System.ComponentModel.DesignerSerializationVisibility(System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public ProjectData Project { get; private set; }

        public ProjectForm(ProjectData existingProject = null)
        {
            Project = existingProject ?? new ProjectData();
            InitializeComponent();
            
            if (existingProject != null)
            {
                txtProjectName.Text = existingProject.Name;
                txtProjectDescription.Text = existingProject.Description;
                txtProjectPurpose.Text = existingProject.Purpose;
                this.Text = "Edit Project";
            }
            else
            {
                this.Text = "New Project";
            }
        }

        private void InitializeComponent()
        {
            this.txtProjectName = new TextBox();
            this.txtProjectDescription = new TextBox();
            this.txtProjectPurpose = new TextBox();
            this.btnOK = new Button();
            this.btnCancel = new Button();
            
            this.SuspendLayout();

            // Form
            this.ClientSize = new Size(500, 400);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "Project Details";

            // Project Name Label
            var lblProjectName = new Label();
            lblProjectName.Text = "Project Name:";
            lblProjectName.Location = new Point(12, 15);
            lblProjectName.Size = new Size(100, 23);
            lblProjectName.AutoSize = true;

            // Project Name TextBox
            this.txtProjectName.Location = new Point(12, 35);
            this.txtProjectName.Size = new Size(460, 23);
            this.txtProjectName.TabIndex = 0;

            // Project Description Label
            var lblProjectDescription = new Label();
            lblProjectDescription.Text = "Project Description:";
            lblProjectDescription.Location = new Point(12, 70);
            lblProjectDescription.Size = new Size(120, 23);
            lblProjectDescription.AutoSize = true;

            // Project Description TextBox
            this.txtProjectDescription.Location = new Point(12, 90);
            this.txtProjectDescription.Size = new Size(460, 80);
            this.txtProjectDescription.TabIndex = 1;
            this.txtProjectDescription.Multiline = true;
            this.txtProjectDescription.ScrollBars = ScrollBars.Vertical;

            // Project Purpose Label
            var lblProjectPurpose = new Label();
            lblProjectPurpose.Text = "Project Purpose (for AI context):";
            lblProjectPurpose.Location = new Point(12, 180);
            lblProjectPurpose.Size = new Size(180, 23);
            lblProjectPurpose.AutoSize = true;

            // Project Purpose TextBox
            this.txtProjectPurpose.Location = new Point(12, 200);
            this.txtProjectPurpose.Size = new Size(460, 120);
            this.txtProjectPurpose.TabIndex = 2;
            this.txtProjectPurpose.Multiline = true;
            this.txtProjectPurpose.ScrollBars = ScrollBars.Vertical;

            // Info Label
            var lblInfo = new Label();
            lblInfo.Text = "Note: Pipelines starting with 'P' are treated as parent pipelines, those starting with 'CH' as child pipelines.";
            lblInfo.Location = new Point(12, 330);
            lblInfo.Size = new Size(460, 30);
            lblInfo.ForeColor = Color.Gray;
            lblInfo.Font = new Font(lblInfo.Font, FontStyle.Italic);

            // OK Button
            this.btnOK.Location = new Point(316, 365);
            this.btnOK.Size = new Size(75, 23);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "OK";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += BtnOK_Click;

            // Cancel Button
            this.btnCancel.Location = new Point(397, 365);
            this.btnCancel.Size = new Size(75, 23);
            this.btnCancel.TabIndex = 4;
            this.btnCancel.Text = "Cancel";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += BtnCancel_Click;

            // Add controls to form
            this.Controls.Add(lblProjectName);
            this.Controls.Add(this.txtProjectName);
            this.Controls.Add(lblProjectDescription);
            this.Controls.Add(this.txtProjectDescription);
            this.Controls.Add(lblProjectPurpose);
            this.Controls.Add(this.txtProjectPurpose);
            this.Controls.Add(lblInfo);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnCancel);

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtProjectName.Text))
            {
                MessageBox.Show("Please enter a project name.", "Validation Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtProjectName.Focus();
                return;
            }

            Project.Name = txtProjectName.Text.Trim();
            Project.Description = txtProjectDescription.Text.Trim();
            Project.Purpose = txtProjectPurpose.Text.Trim();
            Project.LastModified = DateTime.Now;

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
