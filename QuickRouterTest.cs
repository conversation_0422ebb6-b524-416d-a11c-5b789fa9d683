using System;
using System.Collections.Generic;
using SnapAnalyser;

namespace SnapAnalyser
{
    class QuickRouterTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Quick Router Fix Verification ===");
            Console.WriteLine();

            // Create a test router snap with empty SourceViewId connections
            var routerSnap = new SnapNode
            {
                Id = "router-test-001",
                Label = "Route by Value",
                Type = "com-snaplogic-snaps-flow-router",
                Category = SnapCategory.FlowControl,
                Properties = new Dictionary<string, string>
                {
                    ["settings.routes.value[0].expression.value"] = "$value > 50",
                    ["settings.routes.value[0].outputViewName.value"] = "high",
                    ["settings.routes.value[1].expression.value"] = "$value <= 50", 
                    ["settings.routes.value[1].outputViewName.value"] = "low"
                },
                OutputConnections = new List<SnapLink>
                {
                    new SnapLink { SourceId = "router-test-001", TargetId = "snap-high-001", SourceViewId = "" }, // Empty SourceViewId
                    new SnapLink { SourceId = "router-test-001", TargetId = "snap-low-001", SourceViewId = "" }   // Empty SourceViewId
                }
            };

            // Create target snaps
            var allSnaps = new List<SnapNode>
            {
                routerSnap,
                new SnapNode { Id = "snap-high-001", Label = "Process High Values", Type = "com-snaplogic-snaps-transform-mapper" },
                new SnapNode { Id = "snap-low-001", Label = "Process Low Values", Type = "com-snaplogic-snaps-transform-mapper" }
            };

            // Test the router configuration generation
            var generator = new FlowControlConfigurationGenerator();
            
            try
            {
                string config = generator.GenerateRouterConfiguration(routerSnap, allSnaps);
                
                Console.WriteLine("Generated Router Configuration:");
                Console.WriteLine("===============================");
                Console.WriteLine(config);
                
                // Verify the fix worked
                if (config.Contains("Process High Values") && config.Contains("Process Low Values"))
                {
                    Console.WriteLine();
                    Console.WriteLine("✅ SUCCESS: Router fix verified - connected snaps properly displayed!");
                    
                    if (config.Contains("output0") && config.Contains("output1"))
                    {
                        Console.WriteLine("✅ OUTPUT NAMING: Fallback output view names generated correctly");
                    }
                }
                else
                {
                    Console.WriteLine();
                    Console.WriteLine("❌ ISSUE: Router connections not properly displayed");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: {ex.Message}");
            }
            
            Console.WriteLine();
            Console.WriteLine("Test complete. Press any key to exit...");
            Console.ReadKey();
        }
    }
}
