using System;
using SnapAnalyser;

Console.WriteLine("Testing configuration loading...");
Console.WriteLine($"API Key loaded: {!string.IsNullOrEmpty(ConfigManager.OpenAIApiKey)}");
Console.WriteLine($"API Key length: {ConfigManager.OpenAIApiKey?.Length ?? 0}");
Console.WriteLine($"Endpoint: {ConfigManager.AzureOpenAIEndpoint}");
Console.WriteLine($"Deployment: {ConfigManager.AzureOpenAIDeploymentName}");
Console.WriteLine($"Timeout: {ConfigManager.AzureOpenAITimeoutSeconds}");

// Test if Azure AI will be enabled
string apiKey = ConfigManager.OpenAIApiKey;
string endpoint = ConfigManager.AzureOpenAIEndpoint;
bool useAzureAI = !string.IsNullOrEmpty(apiKey) && !string.IsNullOrEmpty(endpoint);

Console.WriteLine($"Azure AI will be enabled: {useAzureAI}");

if (!useAzureAI)
{
    Console.WriteLine("Azure AI is disabled because:");
    if (string.IsNullOrEmpty(apiKey))
        Console.WriteLine("  - API key is null or empty");
    if (string.IsNullOrEmpty(endpoint))
        Console.WriteLine("  - Endpoint is null or empty");
}
