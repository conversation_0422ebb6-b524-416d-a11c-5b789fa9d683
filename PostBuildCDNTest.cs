using System;
using System.Collections.Generic;
using System.IO;

namespace SnapAnalyser
{
    class PostBuildCDNTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== POST-BUILD CDN VERIFICATION ===");
            Console.WriteLine("Testing if rebuilt application includes CDN fixes...");
            Console.WriteLine();

            try
            {
                // Create minimal test pipeline
                var pipeline = new PipelineData
                {
                    Name = "Post-Build Test Pipeline",
                    Author = "Test",
                    Snaps = new List<SnapNode>
                    {
                        new SnapNode
                        {
                            Id = "test-start",
                            Label = "Start",
                            Type = "read",
                            Category = SnapCategory.ExternalSystem,
                            IsStartPoint = true
                        },
                        new SnapNode
                        {
                            Id = "test-end",
                            Label = "End",
                            Type = "write",
                            Category = SnapCategory.ExternalSystem,
                            IsEndPoint = true
                        }
                    },
                    Links = new List<Link>
                    {
                        new Link { FromSnapId = "test-start", ToSnapId = "test-end" }
                    }
                };

                // Generate diagram
                var diagramGen = new DiagramGenerator();
                string diagram = diagramGen.GenerateDiagram(pipeline);
                Console.WriteLine("✓ Diagram generated");

                // Generate documentation
                var docGen = new DocumentationGenerator();
                string documentation = docGen.GenerateHtmlDocumentationAsync(pipeline, diagram, new ProjectData()).Result;
                
                // Check for CDN presence
                bool hasRaphaelCDN = documentation.Contains("raphael.min.js");
                bool hasFlowchartCDN = documentation.Contains("flowchart.min.js");
                bool hasFlowchartStyles = documentation.Contains(".flowchart-container");
                
                // Check placement (CDN before </body>)
                int bodyCloseIndex = documentation.IndexOf("</body>");
                int raphaelIndex = documentation.IndexOf("raphael.min.js");
                bool correctPlacement = raphaelIndex > 0 && raphaelIndex < bodyCloseIndex;

                Console.WriteLine();
                Console.WriteLine("=== VERIFICATION RESULTS ===");
                Console.WriteLine($"✓ Raphael CDN included: {hasRaphaelCDN}");
                Console.WriteLine($"✓ Flowchart CDN included: {hasFlowchartCDN}");
                Console.WriteLine($"✓ Flowchart styles included: {hasFlowchartStyles}");
                Console.WriteLine($"✓ Correct CDN placement: {correctPlacement}");

                // Save test output
                string outputFile = "post_build_test_output.html";
                File.WriteAllText(outputFile, documentation);
                Console.WriteLine($"✓ Test output saved: {outputFile}");

                Console.WriteLine();
                if (hasRaphaelCDN && hasFlowchartCDN && hasFlowchartStyles && correctPlacement)
                {
                    Console.WriteLine("🎉 SUCCESS! All CDN fixes are properly compiled!");
                    Console.WriteLine("The application should now generate diagrams correctly.");
                }
                else
                {
                    Console.WriteLine("❌ ISSUE: CDN fixes not properly applied:");
                    if (!hasRaphaelCDN) Console.WriteLine("  - Missing Raphael CDN");
                    if (!hasFlowchartCDN) Console.WriteLine("  - Missing Flowchart CDN");
                    if (!hasFlowchartStyles) Console.WriteLine("  - Missing styles");
                    if (!correctPlacement) Console.WriteLine("  - Wrong CDN placement");
                    
                    Console.WriteLine();
                    Console.WriteLine("DEBUGGING INFO:");
                    Console.WriteLine($"Documentation length: {documentation.Length}");
                    Console.WriteLine($"Contains 'raphael': {documentation.Contains("raphael")}");
                    Console.WriteLine($"Contains 'flowchart': {documentation.Contains("flowchart")}");
                    
                    // Show around the </body> area
                    if (bodyCloseIndex > 0)
                    {
                        int start = Math.Max(0, bodyCloseIndex - 200);
                        int length = Math.Min(400, documentation.Length - start);
                        Console.WriteLine("Last 200 chars before </body>:");
                        Console.WriteLine(documentation.Substring(start, length));
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
