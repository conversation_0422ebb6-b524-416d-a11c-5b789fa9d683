<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  
  <ItemGroup>
    <Compile Include="TestConnectionProgram.cs" />
    <Compile Include="ConfigManager.cs" />
    <Compile Include="AzureOpenAITester.cs" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="System.Text.Json" Version="9.0.0" />
  </ItemGroup>
</Project>
