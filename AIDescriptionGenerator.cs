using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace SnapAnalyser
{
    public class AIDescriptionGenerator
    {
        private string _logFilePath;
        private bool _isLoggingEnabled = false; private readonly string _apiKey;
        private string _endpoint;
        private string _deploymentName;
        private int _timeoutSeconds; private readonly HttpClient _httpClient;
        private bool _useAzureAI; // Not readonly so we can disable it if connection test fails
        private readonly DescriptionCache _cache;
        private bool _useCachedDescriptions = true; // Default to using cached descriptions

        public bool UseCachedDescriptions
        {
            get { return _useCachedDescriptions; }
            set { _useCachedDescriptions = value; }
        }
        public AIDescriptionGenerator(string apiKey = null)
        {
            try
            {
                // Use the provided API key if given, otherwise try to get it from ConfigManager
                _apiKey = !string.IsNullOrEmpty(apiKey) ? apiKey : ConfigManager.OpenAIApiKey;
                _endpoint = ConfigManager.AzureOpenAIEndpoint;
                _deploymentName = ConfigManager.AzureOpenAIDeploymentName;
                _timeoutSeconds = ConfigManager.AzureOpenAITimeoutSeconds;
                _useAzureAI = !string.IsNullOrEmpty(_apiKey) && !string.IsNullOrEmpty(_endpoint);

                // Initialize the cache
                _cache = new DescriptionCache();
                // Initialize HttpClient first to ensure it's never null
                _httpClient = new HttpClient();
                // Set HttpClient timeout to 5 minutes as a safety net while relying on CancellationToken
                _httpClient.Timeout = TimeSpan.FromMinutes(5);Console.WriteLine("----- Azure OpenAI Configuration -----");
                Console.WriteLine($"API Key provided: {!string.IsNullOrEmpty(_apiKey)}");
                Console.WriteLine($"API Key length: {_apiKey?.Length ?? 0}");
                Console.WriteLine($"Endpoint: {_endpoint}");
                Console.WriteLine($"Deployment Name: {_deploymentName}");
                Console.WriteLine($"Timeout: {_timeoutSeconds} seconds");
                Console.WriteLine($"Azure OpenAI will be used: {_useAzureAI}");
                Console.WriteLine($"Using cached descriptions: {_useCachedDescriptions}");

                if (!_useAzureAI)
                {
                    if (string.IsNullOrEmpty(_apiKey))
                    {
                        Console.WriteLine("WARNING: No Azure OpenAI API key provided. To use Azure OpenAI, please update the OpenAIApiKey in config.json.");
                    }
                    if (string.IsNullOrEmpty(_endpoint))
                    {
                        Console.WriteLine("WARNING: No Azure OpenAI endpoint provided. To use Azure OpenAI, please update the AzureOpenAIEndpoint in config.json.");
                    }
                    Console.WriteLine("Falling back to local implementation for text generation.");
                }
                else
                {
                    Console.WriteLine("AIDescriptionGenerator initialized with Azure OpenAI");
                    // Log partial API key for debugging (showing just the first few characters)
                    if (!string.IsNullOrEmpty(_apiKey) && _apiKey.Length > 8)
                    {
                        Console.WriteLine($"Using API key starting with: {_apiKey.Substring(0, 8)}...");
                    }                    // DETAILED CONNECTION TEST WITH ENHANCED LOGGING
                    Console.WriteLine("🔍 STARTING Azure OpenAI connection test...");

                    // Verify the connection to Azure OpenAI
                    if (_useAzureAI)
                    {
                        Task.Run(async () =>
                        {
                            try
                            {
                                Console.WriteLine("📞 Testing Azure OpenAI connection...");
                                bool isConnected = await TestAzureOpenAIConnection();
                                if (isConnected)
                                {
                                    Console.WriteLine("✅ Successfully connected to Azure OpenAI API");
                                }
                                else
                                {
                                    Console.WriteLine("❌ Failed to connect to Azure OpenAI API - will fall back to local implementation");
                                    _useAzureAI = false;
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"❌ Error testing Azure OpenAI connection: {ex.Message}");
                                if (ex.InnerException != null)
                                {
                                    Console.WriteLine($"❌ Inner exception: {ex.InnerException.Message}");
                                }
                                Console.WriteLine($"❌ Stack trace: {ex.StackTrace}");
                                _useAzureAI = false;
                            }
                        });

                        Console.WriteLine($"🏁 FINAL Azure AI status after connection test: {_useAzureAI}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR initializing AIDescriptionGenerator: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                // Ensure HttpClient is always initialized
                if (_httpClient == null)
                {
                    _httpClient = new HttpClient();
                }

                // Disable Azure AI if there was an error
                _useAzureAI = false;
            }
        }

        // Enable logging with a specific path for the log file
        public void EnableLogging(string outputFolder)
        {
            try
            {
                // Create logs directory if it doesn't exist
                Directory.CreateDirectory(outputFolder);

                // Set the log file path
                _logFilePath = Path.Combine(outputFolder, $"ai_processing_log_{DateTime.Now:yyyy-MM-dd_HHmmss}.log");
                _isLoggingEnabled = true;

                // Create the log file with a header
                File.WriteAllText(_logFilePath, $"AI Processing Log - Started at {DateTime.Now}\r\n");
                LogMessage("Logging initialized");
            }
            catch (Exception ex)
            {
                // Can't use LogMessage here since logging isn't set up yet
                Console.WriteLine($"Error setting up logging: {ex.Message}");
                _isLoggingEnabled = false;
            }
        }

        // Method to log messages both to console and file
        public void LogMessage(string message)
        {
            // Always log to console
            Console.WriteLine(message);

            // Log to file if enabled
            if (_isLoggingEnabled)
            {
                try
                {
                    File.AppendAllText(_logFilePath, $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}\r\n");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error writing to log file: {ex.Message}");
                }
            }
        }
        public async Task<string> GeneratePipelineDescription(PipelineData pipeline, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.Now;
            var requestId = Guid.NewGuid().ToString("N")[0..8];

            LogMessage($"[PIPELINE-DESC-{requestId}] Starting pipeline description generation for '{pipeline?.Name ?? "Unknown"}'");
            LogMessage($"[PIPELINE-DESC-{requestId}] Pipeline contains {pipeline?.Snaps?.Count ?? 0} snaps");
            LogMessage($"[PIPELINE-DESC-{requestId}] Using Azure AI: {_useAzureAI}");

            try
            {
                // Create a summary of the pipeline for analysis
                LogMessage($"[PIPELINE-DESC-{requestId}] Creating pipeline summary");
                var pipelineSummary = CreatePipelineSummary(pipeline);
                LogMessage($"[PIPELINE-DESC-{requestId}] Pipeline summary created, length: {pipelineSummary?.Length ?? 0} characters");
                // Enhanced prompt to focus on the pipeline's business purpose and high-level functionality
                string prompt = $"Create a concise intro (max 150 words) for this SnapLogic pipeline:\n\n{pipelineSummary}\n\n" +
                               "In 1-2 short paragraphs, cover only:\n" +
                               "1. Business purpose and problem solved\n" +
                               "2. Key data sources/destinations\n" +
                               "3. Main transformations\n" +
                               "4. Business outcome\n\n" +
                               "Be extremely brief. Use direct statements. Avoid lengthy explanations.";

                if (_useAzureAI)
                {
                    try
                    {
                        LogMessage($"[PIPELINE-DESC-{requestId}] Sending to Azure OpenAI, prompt length: {prompt?.Length ?? 0} characters");
                        var result = await SendPromptToAzureAI(prompt, cancellationToken);

                        var duration = DateTime.Now - startTime;
                        LogMessage($"[PIPELINE-DESC-{requestId}] SUCCESS: Azure OpenAI completed in {duration.TotalMilliseconds:F0}ms, result length: {result?.Length ?? 0} characters");
                        return result;
                    }
                    catch (OperationCanceledException)
                    {
                        var duration = DateTime.Now - startTime;
                        LogMessage($"[PIPELINE-DESC-{requestId}] CANCELLED: Azure OpenAI request cancelled after {duration.TotalMilliseconds:F0}ms");
                        throw;
                    }
                    catch (Exception ex)
                    {
                        var duration = DateTime.Now - startTime;
                        LogMessage($"[PIPELINE-DESC-{requestId}] ERROR: Azure OpenAI failed after {duration.TotalMilliseconds:F0}ms - {ex.Message}");
                        LogMessage($"[PIPELINE-DESC-{requestId}] Falling back to local implementation");
                        var fallbackResult = GetGenericDescription(pipeline);
                        LogMessage($"[PIPELINE-DESC-{requestId}] Fallback completed, result length: {fallbackResult?.Length ?? 0} characters");
                        return fallbackResult;
                    }
                }
                else
                {
                    LogMessage($"[PIPELINE-DESC-{requestId}] Using local analyzer (Azure AI disabled)");
                    var result = GetGenericDescription(pipeline);
                    var duration = DateTime.Now - startTime;
                    LogMessage($"[PIPELINE-DESC-{requestId}] Local analyzer completed in {duration.TotalMilliseconds:F0}ms, result length: {result?.Length ?? 0} characters");
                    return result;
                }
            }
            catch (Exception ex)
            {
                var duration = DateTime.Now - startTime;
                LogMessage($"[PIPELINE-DESC-{requestId}] FATAL ERROR: Pipeline description generation failed after {duration.TotalMilliseconds:F0}ms - {ex.Message}");
                LogMessage($"[PIPELINE-DESC-{requestId}] Stack trace: {ex.StackTrace}");
                throw;
            }
        }
        public async Task<string> GenerateSnapDescription(SnapNode snap, PipelineData pipeline, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.Now;
            var requestId = Guid.NewGuid().ToString("N")[0..8];

            LogMessage($"[SNAP-DESC-{requestId}] Starting snap description generation for '{snap?.Label ?? "Unknown"}' ({snap?.Type ?? "Unknown"})");
            LogMessage($"[SNAP-DESC-{requestId}] Using cached descriptions: {_useCachedDescriptions}");
            LogMessage($"[SNAP-DESC-{requestId}] Using Azure AI: {_useAzureAI}");

            try
            {
                // Check if we should use cached descriptions and if we have a cached version
                if (_useCachedDescriptions && _cache.TryGetDescription(snap, out string cachedDescription))
                {
                    var duration = DateTime.Now - startTime;
                    LogMessage($"[SNAP-DESC-{requestId}] CACHE HIT: Found cached description in {duration.TotalMilliseconds:F0}ms, length: {cachedDescription?.Length ?? 0} characters");
                    return cachedDescription;
                }
                LogMessage($"[SNAP-DESC-{requestId}] CACHE MISS: No cached description found, generating new description");
                // Check if we should skip AI description for this snap type
                if (ShouldSkipAIDescription(snap))
                {
                    string basicDescription = GetSimplifiedDescription(snap);
                    var duration = DateTime.Now - startTime;
                    LogMessage($"[SNAP-DESC-{requestId}] SKIPPED: Using simplified description for basic snap type in {duration.TotalMilliseconds:F0}ms");
                    return basicDescription;
                }

                // Create a description of the snap and its context
                LogMessage($"[SNAP-DESC-{requestId}] Creating snap context");
                var snapContext = CreateSnapContext(snap, pipeline);
                LogMessage($"[SNAP-DESC-{requestId}] Snap context created, length: {snapContext?.Length ?? 0} characters");

                // The prompt to send to the AI
                string prompt = $"Write a brief technical description of this SnapLogic snap component:\n\n{snapContext}\n\n" +
                               "Concisely explain (max 100 words):\n" +
                               "1. What this snap does in the pipeline\n" +
                               "2. Key configuration settings\n" +
                               "3. Input/output behavior\n\n" +
                               "Be direct and specific. Avoid technical jargon unless necessary.";

                LogMessage($"[SNAP-DESC-{requestId}] Prompt created, length: {prompt?.Length ?? 0} characters");

                string description;
                if (_useAzureAI)
                {
                    try
                    {
                        LogMessage($"[SNAP-DESC-{requestId}] Sending to Azure OpenAI");
                        description = await SendPromptToAzureAI(prompt, cancellationToken);
                        LogMessage($"[SNAP-DESC-{requestId}] Azure OpenAI response received, length: {description?.Length ?? 0} characters");
                    }
                    catch (OperationCanceledException)
                    {
                        var duration = DateTime.Now - startTime;
                        LogMessage($"[SNAP-DESC-{requestId}] CANCELLED: Azure OpenAI request cancelled after {duration.TotalMilliseconds:F0}ms");
                        throw;
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"[SNAP-DESC-{requestId}] ERROR: Azure OpenAI failed - {ex.Message}");
                        LogMessage($"[SNAP-DESC-{requestId}] Falling back to local implementation");
                        description = GetGenericSnapDescription(snap, pipeline);
                        LogMessage($"[SNAP-DESC-{requestId}] Fallback completed, result length: {description?.Length ?? 0} characters");
                    }
                }
                else
                {
                    LogMessage($"[SNAP-DESC-{requestId}] Using local analyzer (Azure AI disabled)");
                    description = GetGenericSnapDescription(snap, pipeline);
                    LogMessage($"[SNAP-DESC-{requestId}] Local analyzer completed, result length: {description?.Length ?? 0} characters");
                }

                if (!string.IsNullOrEmpty(description))
                {
                    // Get any existing pseudocode to preserve it
                    _cache.TryGetPseudocode(snap, out string existingPseudocode);
                    LogMessage($"[SNAP-DESC-{requestId}] Storing description in cache with existing pseudocode length: {existingPseudocode?.Length ?? 0}");
                    _cache.StoreDescription(snap, description, existingPseudocode);
                }
                else
                {
                    LogMessage($"[SNAP-DESC-{requestId}] WARNING: Generated description was null or empty");
                }

                var totalDuration = DateTime.Now - startTime;
                LogMessage($"[SNAP-DESC-{requestId}] SUCCESS: Snap description completed in {totalDuration.TotalMilliseconds:F0}ms, final length: {description?.Length ?? 0} characters");

                return description;
            }
            catch (Exception ex)
            {
                var duration = DateTime.Now - startTime;
                LogMessage($"[SNAP-DESC-{requestId}] FATAL ERROR: Snap description generation failed after {duration.TotalMilliseconds:F0}ms - {ex.Message}");
                LogMessage($"[SNAP-DESC-{requestId}] Stack trace: {ex.StackTrace}");
                throw;
            }
        }
        public async Task<string> GenerateDataFlowAnalysis(PipelineData pipeline, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.Now;
            var requestId = Guid.NewGuid().ToString("N")[0..8];

            LogMessage($"[DATAFLOW-{requestId}] Starting data flow analysis for pipeline '{pipeline?.Name ?? "Unknown"}'");
            LogMessage($"[DATAFLOW-{requestId}] Pipeline contains {pipeline?.Snaps?.Count ?? 0} snaps");
            LogMessage($"[DATAFLOW-{requestId}] Using Azure AI: {_useAzureAI}");

            try
            {
                // Create a representation of the data flow
                LogMessage($"[DATAFLOW-{requestId}] Creating data flow summary");
                var dataFlowSummary = CreateDataFlowSummary(pipeline);
                LogMessage($"[DATAFLOW-{requestId}] Data flow summary created, length: {dataFlowSummary?.Length ?? 0} characters");

                // The prompt to send to the AI
                string prompt = $"Concisely analyze data flow in this SnapLogic pipeline:\n\n{dataFlowSummary}\n\n" +
                               "In 150 words or less, cover only:\n" +
                               "1. How data moves through this pipeline\n" +
                               "2. Key transformation points\n" +
                               "3. Important decision points\n" +
                               "4. Architecture pattern used\n\n" +
                               "Focus on the most critical aspects. Use brief, direct statements.";

                if (_useAzureAI)
                {
                    try
                    {
                        LogMessage($"[DATAFLOW-{requestId}] Sending to Azure OpenAI, prompt length: {prompt?.Length ?? 0} characters");
                        var result = await SendPromptToAzureAI(prompt, cancellationToken);

                        var duration = DateTime.Now - startTime;
                        LogMessage($"[DATAFLOW-{requestId}] SUCCESS: Azure OpenAI completed in {duration.TotalMilliseconds:F0}ms, result length: {result?.Length ?? 0} characters");
                        return result;
                    }
                    catch (OperationCanceledException)
                    {
                        var duration = DateTime.Now - startTime;
                        LogMessage($"[DATAFLOW-{requestId}] CANCELLED: Azure OpenAI request cancelled after {duration.TotalMilliseconds:F0}ms");
                        throw;
                    }
                    catch (Exception ex)
                    {
                        var duration = DateTime.Now - startTime;
                        LogMessage($"[DATAFLOW-{requestId}] ERROR: Azure OpenAI failed after {duration.TotalMilliseconds:F0}ms - {ex.Message}");
                        LogMessage($"[DATAFLOW-{requestId}] Falling back to local implementation");
                        var fallbackResult = GetGenericDataFlowAnalysis(pipeline);
                        LogMessage($"[DATAFLOW-{requestId}] Fallback completed, result length: {fallbackResult?.Length ?? 0} characters");
                        return fallbackResult;
                    }
                }
                else
                {
                    LogMessage($"[DATAFLOW-{requestId}] Using local analyzer (Azure AI disabled)");
                    var result = GetGenericDataFlowAnalysis(pipeline);
                    var duration = DateTime.Now - startTime;
                    LogMessage($"[DATAFLOW-{requestId}] Local analyzer completed in {duration.TotalMilliseconds:F0}ms, result length: {result?.Length ?? 0} characters");
                    return result;
                }
            }
            catch (Exception ex)
            {
                var duration = DateTime.Now - startTime;
                LogMessage($"[DATAFLOW-{requestId}] FATAL ERROR: Data flow analysis failed after {duration.TotalMilliseconds:F0}ms - {ex.Message}");
                LogMessage($"[DATAFLOW-{requestId}] Stack trace: {ex.StackTrace}");
                throw;
            }
        }        // Helper method to check if a snap should have explicit mapping/condition pseudocode
        private bool IsMapperOrConditionSnap(SnapNode snap)
        {
            string snapType = snap.Type.ToLowerInvariant();

            LogMessage($"[MAPPER-CHECK] Checking snap: {snap.Label}");
            LogMessage($"[MAPPER-CHECK] Snap Type: '{snap.Type}' -> Lower: '{snapType}'");
            LogMessage($"[MAPPER-CHECK] Snap Category: {snap.Category}");

            bool isTransformation = snap.Category == SnapCategory.Transformation &&
                   (snapType.Contains("map") || snapType.Contains("transform") || snapType.Contains("datatransform"));
            bool isFlowControl = snap.Category == SnapCategory.FlowControl &&
                   (snapType.Contains("router") || snapType.Contains("condition") || snapType.Contains("filter"));

            LogMessage($"[MAPPER-CHECK] IsTransformation: {isTransformation}");
            LogMessage($"[MAPPER-CHECK] IsFlowControl: {isFlowControl} (Category={snap.Category == SnapCategory.FlowControl}, Contains router={snapType.Contains("router")})");

            bool result = isTransformation || isFlowControl;
            LogMessage($"[MAPPER-CHECK] Final result: {result}");

            return result;
        }

        // Generate explicit pseudocode without AI for mapper and condition snaps
        private string GenerateExplicitPseudocode(SnapNode snap, PipelineData pipeline)
        {
            if (snap.Category == SnapCategory.Transformation)
            {
                return GenerateMapperPseudocode(snap);
            }
            else if (snap.Category == SnapCategory.FlowControl)
            {
                if (snap.Type.ToLowerInvariant().Contains("router"))
                {
                    return GenerateRouterPseudocode(snap);
                }
                else
                {
                    return GenerateConditionPseudocode(snap);
                }
            }
            return null;
        }        // Helper method to extract mappings from various formats
        private Dictionary<string, string> ExtractMappings(SnapNode snap)
        {
            var mappings = new Dictionary<string, string>();

            // Log the fact that we're attempting to extract mappings for this snap
            Console.WriteLine($"Extracting mappings for snap: {snap.Label} ({snap.Type})");

            // First check for mappingTable in different potential locations
            List<string> mappingTableKeys = new List<string> {
                "settings.transformations.value.mappingTable",
                "settings.transformations.mappingTable.value",
                "settings.mappingTable.value",
                "settings.mappingTable",
                "mappingTable.value",
                "mappingTable",
                "transformations.value.mappingTable.value"  // Added this format
            };

            // Look for mappings in all potential locations
            string mappingTableJson = null;
            foreach (var key in mappingTableKeys)
            {
                if (snap.Properties.TryGetValue(key, out mappingTableJson) && !string.IsNullOrEmpty(mappingTableJson))
                {
                    Console.WriteLine($"Found mapping table at key: {key}");
                    break;
                }
            }

            // Process mapping table if found
            if (!string.IsNullOrEmpty(mappingTableJson))
            {
                try
                {
                    // Handle both array and object formats
                    if (mappingTableJson.TrimStart().StartsWith("["))
                    {
                        // Array format
                        using var doc = JsonDocument.Parse(mappingTableJson);
                        foreach (var mapping in doc.RootElement.EnumerateArray())
                        {
                            ExtractMappingFromJsonElement(mapping, mappings);
                        }
                    }
                    else if (mappingTableJson.TrimStart().StartsWith("{"))
                    {
                        // Object format
                        using var doc = JsonDocument.Parse(mappingTableJson);
                        ExtractMappingFromJsonElement(doc.RootElement, mappings);
                    }
                    else
                    {
                        // Simple string value - could be a reference or simple mapping
                        Console.WriteLine($"Simple mapping value: {mappingTableJson}");
                        mappings["output"] = mappingTableJson;
                    }
                }
                catch (JsonException ex)
                {
                    Console.WriteLine($"Error parsing mapping table JSON: {ex.Message}");
                    // Store the raw string if we can't parse it as JSON
                    if (mappingTableJson.Length < 200)
                    {
                        mappings["rawMapping"] = mappingTableJson;
                    }
                }
            }

            // Look for individual mapping entries
            // This section is critical for SnapLogic mapper snaps that store each mapping individually
            var mappingEntries = snap.Properties.Where(p =>
                p.Key.Contains("mappingTable") &&
                p.Key.Contains("targetPath") &&
                p.Key.Contains("value")).ToList();

            Console.WriteLine($"Found {mappingEntries.Count} individual mapping entries");

            foreach (var targetEntry in mappingEntries)
            {
                // Extract the mapping number from keys like "settings.transformations.value.mappingTable.0.targetPath.value"
                var match = Regex.Match(targetEntry.Key, @"mappingTable\.(\d+)\.targetPath");
                if (match.Success)
                {
                    string index = match.Groups[1].Value;
                    string targetPath = targetEntry.Value;

                    // Construct the corresponding expression key by replacing "targetPath" with "expression"
                    string baseKey = targetEntry.Key.Substring(0, targetEntry.Key.LastIndexOf(".targetPath"));
                    string expressionKey = $"{baseKey}.expression.value";

                    if (snap.Properties.TryGetValue(expressionKey, out string expression))
                    {
                        Console.WriteLine($"Found indexed mapping at {index}: {targetPath} = {expression}");
                        mappings[targetPath] = expression;
                    }
                }
            }

            // Check for direct mappings in properties
            var targetPathProps = snap.Properties.Where(p =>
                (p.Key.Contains("targetPath") || p.Key.Contains("target_path")) &&
                p.Key.EndsWith(".value")).ToList();

            foreach (var targetProp in targetPathProps)
            {
                string pathKey = targetProp.Key;
                string targetPath = targetProp.Value;

                // Extract the common prefix to look for the corresponding expression
                string prefix = pathKey.Substring(0, pathKey.LastIndexOf(".targetPath"));
                string expressionKey = $"{prefix}.expression.value";
                string alternateExprKey = $"{prefix}.source.value";

                // Try to find the matching expression
                if (snap.Properties.TryGetValue(expressionKey, out string expression))
                {
                    Console.WriteLine($"Found target-expression pair: {targetPath} = {expression}");
                    mappings[targetPath] = expression;
                }
                else if (snap.Properties.TryGetValue(alternateExprKey, out string altExpr))
                {
                    Console.WriteLine($"Found target-source pair: {targetPath} = {altExpr}");
                    mappings[targetPath] = altExpr;
                }
            }

            // Look specifically for mappingTable indexed entries using regex patterns
            var indexedMappings = snap.Properties.Where(p =>
                Regex.IsMatch(p.Key, @"settings\.transformations\.value\.mappingTable\.\d+\.targetPath\.value") ||
                Regex.IsMatch(p.Key, @"transformations\.value\.mappingTable\.\d+\.targetPath\.value") ||
                Regex.IsMatch(p.Key, @"mappingTable\.\d+\.targetPath\.value")).ToList();

            foreach (var indexedTarget in indexedMappings)
            {
                string targetPath = indexedTarget.Value;

                // Create the corresponding expression key by replacing "targetPath" with "expression"
                string exprKey = indexedTarget.Key.Replace("targetPath", "expression");
                string sourceKey = indexedTarget.Key.Replace("targetPath", "source");

                if (snap.Properties.TryGetValue(exprKey, out string expression))
                {
                    Console.WriteLine($"Found indexed mapping: {targetPath} = {expression}");
                    mappings[targetPath] = expression;
                }
                else if (snap.Properties.TryGetValue(sourceKey, out string source))
                {
                    Console.WriteLine($"Found indexed source mapping: {targetPath} = {source}");
                    mappings[targetPath] = source;
                }
            }

            // Check for transformersList format
            List<string> transformersListKeys = new List<string> {
                "settings.transformations.value.transformersList",
                "settings.transformersList.value",
                "settings.transformersList",
                "transformersList.value",
                "transformersList",
                "transformations.value.transformersList"  // Added this format
            };

            string transformersJson = null;
            foreach (var key in transformersListKeys)
            {
                if (snap.Properties.TryGetValue(key, out transformersJson) && !string.IsNullOrEmpty(transformersJson))
                {
                    Console.WriteLine($"Found transformers list at key: {key}");
                    break;
                }
            }

            if (!string.IsNullOrEmpty(transformersJson))
            {
                try
                {
                    if (transformersJson.TrimStart().StartsWith("["))
                    {
                        // Array format
                        using var doc = JsonDocument.Parse(transformersJson);
                        foreach (var transformer in doc.RootElement.EnumerateArray())
                        {
                            if (transformer.TryGetProperty("targetPath", out var targetProp) &&
                                transformer.TryGetProperty("expression", out var exprProp))
                            {
                                mappings[targetProp.GetString()] = exprProp.GetString();
                            }
                        }
                    }
                    else if (transformersJson.TrimStart().StartsWith("{"))
                    {
                        // Object format with named transformers
                        using var doc = JsonDocument.Parse(transformersJson);
                        foreach (var transformer in doc.RootElement.EnumerateObject())
                        {
                            if (transformer.Value.TryGetProperty("expressions", out var expressions))
                            {
                                foreach (var expr in expressions.EnumerateObject())
                                {
                                    string targetPath = $"{transformer.Name}.{expr.Name}";
                                    mappings[targetPath] = expr.Value.ToString();
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error processing transformers list: {ex.Message}");
                }
            }

            // Check for direct property mappings (field.value or field.expression) 
            var directMappings = snap.Properties
                .Where(p => (p.Key.EndsWith(".value") || p.Key.EndsWith(".expression")) &&
                           !p.Key.StartsWith("_") &&
                           !p.Key.Contains("settings.") &&
                           !p.Key.Contains("mappingTable") &&
                           !p.Key.Contains("transformersList"))
                .ToList();

            foreach (var mapping in directMappings)
            {
                string targetField = mapping.Key.Replace(".value", "").Replace(".expression", "");
                Console.WriteLine($"Found direct field mapping: {targetField} = {mapping.Value}");
                mappings[targetField] = mapping.Value;
            }

            // Look for any field pairs with the pattern: X.targetPath.value and X.expression.value
            foreach (var prop in snap.Properties)
            {
                if (prop.Key.Contains(".targetPath.value"))
                {
                    string prefix = prop.Key.Substring(0, prop.Key.IndexOf(".targetPath.value"));
                    string targetPath = prop.Value;
                    string expressionKey = $"{prefix}.expression.value";

                    if (snap.Properties.TryGetValue(expressionKey, out string expression))
                    {
                        Console.WriteLine($"Found targetPath-expression pair: {targetPath} = {expression}");
                        mappings[targetPath] = expression;
                    }
                }
            }

            // NEW: Extract mappings from SnapLogic mapper specific format - this handles the case in the sample files
            try
            {
                // Look for patterns like these in SnapLogic's datatransform snaps:
                // "transformations" : { "value" : { "mappingRoot" : { "value" : "$" }, "mappingTable" : { "value" : [ { ... } ] } } }
                var potentialMappingRoots = snap.Properties.Where(p =>
                    p.Key.Contains("mappingRoot") && p.Key.EndsWith(".value")).ToList();

                foreach (var rootProp in potentialMappingRoots)
                {
                    string rootPath = rootProp.Value; // Usually "$"
                    string rootPrefix = rootProp.Key.Substring(0, rootProp.Key.LastIndexOf(".mappingRoot"));
                    string mappingKeyBase = $"{rootPrefix}.mappingTable.value";

                    Console.WriteLine($"Found potential mapping root at {rootPrefix} with value {rootPath}");

                    // Now that we found the root, try to extract a mappingTable array
                    if (snap.Properties.TryGetValue(mappingKeyBase, out string mappingData) &&
                        !string.IsNullOrEmpty(mappingData))
                    {
                        try
                        {
                            Console.WriteLine($"Found mappingTable data at {mappingKeyBase}, parsing...");
                            using var doc = JsonDocument.Parse(mappingData);
                            if (doc.RootElement.ValueKind == JsonValueKind.Array)
                            {
                                foreach (var mapping in doc.RootElement.EnumerateArray())
                                {
                                    if (mapping.TryGetProperty("targetPath", out var targetProp) &&
                                        mapping.TryGetProperty("expression", out var exprProp))
                                    {
                                        string targetPath = targetProp.GetProperty("value").GetString();
                                        string expression = exprProp.GetProperty("value").GetString();
                                        Console.WriteLine($"Found SnapLogic mapping: {targetPath} = {expression}");
                                        mappings[targetPath] = expression;
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error parsing SnapLogic mapping format: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in SnapLogic specific mapping extraction: {ex.Message}");
            }

            // If we still have no mappings, check for any keys that might contain mapping information
            if (mappings.Count == 0)
            {
                var potentialMappingKeys = snap.Properties
                    .Where(p => p.Key.Contains("map") || p.Key.Contains("transform") ||
                              p.Key.Contains("expression") || p.Key.Contains("field"))
                    .ToList();

                foreach (var prop in potentialMappingKeys)
                {
                    try
                    {
                        Console.WriteLine($"Examining potential mapping key: {prop.Key} = {prop.Value.Substring(0, Math.Min(50, prop.Value.Length))}");

                        // Try to parse complex value as JSON
                        if (prop.Value.TrimStart().StartsWith("{") || prop.Value.TrimStart().StartsWith("["))
                        {
                            try
                            {
                                using var doc = JsonDocument.Parse(prop.Value);
                                if (doc.RootElement.ValueKind == JsonValueKind.Array)
                                {
                                    foreach (var item in doc.RootElement.EnumerateArray())
                                    {
                                        ExtractMappingFromJsonElement(item, mappings);
                                    }
                                }
                                else if (doc.RootElement.ValueKind == JsonValueKind.Object)
                                {
                                    ExtractMappingFromJsonElement(doc.RootElement, mappings);
                                }
                            }
                            catch
                            {
                                // If we can't parse it, just use the raw value
                                if (prop.Value.Length < 200)
                                {
                                    mappings[prop.Key] = prop.Value;
                                }
                            }
                        }
                        else if (prop.Value.Length < 200)
                        {
                            // Use raw value for simple strings
                            mappings[prop.Key] = prop.Value;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error processing potential mapping key {prop.Key}: {ex.Message}");
                    }
                }
            }

            // Log the number of mappings found
            Console.WriteLine($"Total mappings found: {mappings.Count}");

            return mappings;
        }

        // Helper method to extract mapping from a JSON element
        private void ExtractMappingFromJsonElement(JsonElement element, Dictionary<string, string> mappings)
        {
            try
            {
                // Try various property name combinations used in SnapLogic
                string targetPath = null;
                string expression = null;

                // Look for targetPath or target
                if (element.TryGetProperty("targetPath", out var targetElem))
                {
                    if (targetElem.ValueKind == JsonValueKind.Object && targetElem.TryGetProperty("value", out var targetValueElem))
                    {
                        targetPath = targetValueElem.GetString();
                    }
                    else
                    {
                        targetPath = targetElem.GetString();
                    }
                }
                else if (element.TryGetProperty("target", out var targetAlt))
                {
                    if (targetAlt.ValueKind == JsonValueKind.Object && targetAlt.TryGetProperty("value", out var targetValueElem))
                    {
                        targetPath = targetValueElem.GetString();
                    }
                    else
                    {
                        targetPath = targetAlt.GetString();
                    }
                }
                else if (element.TryGetProperty("$", out var dollarProp))
                {
                    targetPath = "$";
                }

                // Look for expression or source
                if (element.TryGetProperty("expression", out var exprElem))
                {
                    if (exprElem.ValueKind == JsonValueKind.Object && exprElem.TryGetProperty("value", out var exprValueElem))
                    {
                        expression = exprValueElem.GetString();
                    }
                    else
                    {
                        expression = exprElem.GetString();
                    }
                }
                else if (element.TryGetProperty("source", out var sourceElem))
                {
                    if (sourceElem.ValueKind == JsonValueKind.Object && sourceElem.TryGetProperty("value", out var sourceValueElem))
                    {
                        expression = sourceValueElem.GetString();
                    }
                    else
                    {
                        expression = sourceElem.GetString();
                    }
                }

                // If we have both a target path and expression, add to mappings
                if (!string.IsNullOrEmpty(targetPath) && !string.IsNullOrEmpty(expression))
                {
                    mappings[targetPath] = expression;
                }
                // If the element has named properties but no explicit target/expression,
                // try to extract mappings from each property
                else if (element.ValueKind == JsonValueKind.Object)
                {
                    foreach (var prop in element.EnumerateObject())
                    {
                        if (prop.Value.ValueKind == JsonValueKind.String ||
                            prop.Value.ValueKind == JsonValueKind.Number ||
                            prop.Value.ValueKind == JsonValueKind.True ||
                            prop.Value.ValueKind == JsonValueKind.False)
                        {
                            // Consider each property a mapping
                            mappings[prop.Name] = prop.Value.ToString();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error extracting mapping from JSON element: {ex.Message}");
            }
        }

        // Helper method to extract conditions from various formats
        private List<(string condition, string description)> ExtractConditions(SnapNode snap)
        {
            var conditions = new List<(string condition, string description)>();

            // Check for direct condition property
            if (snap.Properties.TryGetValue("condition", out string condition))
            {
                conditions.Add((condition, "Main Condition"));
            }

            // Check for expression property
            if (snap.Properties.TryGetValue("expression", out string expression))
            {
                conditions.Add((expression, "Filter Expression"));
            }

            // Check for filter configurations
            if (snap.Properties.TryGetValue("filterConfig", out string filterConfig))
            {
                try
                {
                    using var doc = JsonDocument.Parse(filterConfig);
                    foreach (var filter in doc.RootElement.EnumerateArray())
                    {
                        var filterExpr = filter.GetProperty("expression").GetString();
                        var description = filter.TryGetProperty("description", out var desc)
                            ? desc.GetString()
                            : "Filter Condition";
                        conditions.Add((filterExpr, description));
                    }
                }
                catch { /* Continue if JSON parsing fails */ }
            }

            // Check for conditions in complex JSON
            foreach (var prop in snap.Properties.Where(p => p.Key.Contains("condition", StringComparison.OrdinalIgnoreCase)))
            {
                try
                {
                    using var doc = JsonDocument.Parse(prop.Value);
                    if (doc.RootElement.TryGetProperty("expression", out var expr))
                    {
                        conditions.Add((expr.GetString(), prop.Key));
                    }
                }
                catch
                {
                    // If it's not JSON, treat it as a direct condition
                    if (!string.IsNullOrWhiteSpace(prop.Value))
                    {
                        conditions.Add((prop.Value, prop.Key));
                    }
                }
            }

            return conditions;
        }

        // Helper method to extract router paths and conditions
        private List<(string path, string condition)> ExtractRoutes(SnapNode snap)
        {
            var routes = new List<(string path, string condition)>();

            // Check for SnapLogic router format
            // Look specifically for the settings.routes array in SnapLogic structure
            if (snap.Properties.TryGetValue("settings", out string settingsJson))
            {
                try
                {
                    using var doc = JsonDocument.Parse(settingsJson);
                    if (doc.RootElement.TryGetProperty("routes", out var routesElement) &&
                        routesElement.TryGetProperty("value", out var routesArray) &&
                        routesArray.ValueKind == JsonValueKind.Array)
                    {

                        foreach (var route in routesArray.EnumerateArray())
                        {
                            if (route.TryGetProperty("expression", out var expressionObj) &&
                                route.TryGetProperty("outputViewName", out var outputViewObj))
                            {

                                // First check if expression has expression:true property
                                bool isExpression = false;
                                if (expressionObj.TryGetProperty("expression", out var exprFlag))
                                {
                                    isExpression = exprFlag.GetBoolean();
                                }

                                string condition = "";
                                if (expressionObj.TryGetProperty("value", out var valueElement))
                                {
                                    condition = valueElement.GetString();

                                    // Add $ prefix to make it clear it's a document field if it's an expression
                                    if (isExpression && !condition.StartsWith("$"))
                                    {
                                        condition = "$" + condition;
                                    }
                                }

                                string outputView = "";
                                if (outputViewObj.TryGetProperty("value", out var outputViewValue))
                                {
                                    outputView = outputViewValue.GetString();
                                }

                                // Only add if we have both a condition and output path
                                if (!string.IsNullOrEmpty(condition) && !string.IsNullOrEmpty(outputView))
                                {
                                    routes.Add((outputView, condition));
                                }
                            }
                        }
                    }
                }
                catch { /* Continue with other formats if JSON parsing fails */ }
            }            // If we still don't have routes, try other common properties structures
            if (routes.Count == 0)
            {
                // Try to extract from flattened property structure
                var routeEntries = snap.Properties
                    .Where(p => p.Key.StartsWith("settings.routes.value"))
                    .ToList();

                if (routeEntries.Count > 0)
                {
                    // Group by index number in the array
                    var routeGroups = routeEntries
                        .Select(entry =>
                        {
                            // Extract the index and property name
                            var match = System.Text.RegularExpressions.Regex.Match(entry.Key, @"settings\.routes\.value\[(\d+)\]\.(.+)");
                            if (match.Success)
                            {
                                int index = int.Parse(match.Groups[1].Value);
                                string propName = match.Groups[2].Value;
                                return (Index: index, PropName: propName, Value: entry.Value);
                            }
                            return (Index: -1, PropName: "", Value: "");
                        })
                        .Where(x => x.Index >= 0)
                        .GroupBy(x => x.Index);

                    foreach (var group in routeGroups)
                    {
                        var conditionEntry = group.FirstOrDefault(x => x.PropName == "expression.value");
                        var pathEntry = group.FirstOrDefault(x => x.PropName == "outputViewName.value");

                        if (!string.IsNullOrEmpty(conditionEntry.Value) && !string.IsNullOrEmpty(pathEntry.Value))
                        {
                            routes.Add((pathEntry.Value, conditionEntry.Value));
                        }
                    }
                }
            }

            // The old implementation for other formats
            if (routes.Count == 0 && snap.Properties.TryGetValue("routes", out string genericRoutesJson))
            {
                try
                {
                    using var doc = JsonDocument.Parse(genericRoutesJson);
                    foreach (var route in doc.RootElement.EnumerateArray())
                    {
                        var path = route.GetProperty("path").GetString();
                        var condition = route.GetProperty("condition").GetString();
                        routes.Add((path, condition));
                    }
                }
                catch { /* Continue with other formats if JSON parsing fails */ }
            }

            // Check for property-based routes
            if (routes.Count == 0)
            {
                var routeProps = snap.Properties
                    .Where(p => p.Key.StartsWith("route.", StringComparison.OrdinalIgnoreCase) ||
                               p.Key.EndsWith(".condition", StringComparison.OrdinalIgnoreCase))
                    .ToList();

                foreach (var prop in routeProps)
                {
                    string path = prop.Key.Replace(".condition", "");
                    if (path.StartsWith("route.", StringComparison.OrdinalIgnoreCase))
                    {
                        path = path.Substring(6);
                    }
                    routes.Add((path, prop.Value));
                }
            }

            // Add default route if present
            if (snap.Properties.TryGetValue("defaultRoute", out string defaultRoute))
            {
                routes.Add(("default", defaultRoute));
            }

            return routes;
        }        // Generate explicit pseudocode for mapper snaps
        private string GenerateMapperPseudocode(SnapNode snap)
        {
            var sb = new StringBuilder();
            var mappings = ExtractMappings(snap);

            sb.AppendLine($"BEGIN {snap.Label.Replace(" ", "")}Mapper");
            sb.AppendLine("    WHILE input has more documents DO");
            sb.AppendLine("        READ document from input");
            sb.AppendLine("        CREATE new transformed document");
            sb.AppendLine();

            if (mappings.Any())
            {
                sb.AppendLine("        // Apply field mappings");
                foreach (var mapping in mappings)
                {
                    // Clean up the mapping for display
                    string sanitizedKey = mapping.Key.Replace("settings.transformations.", "")
                                                .Replace(".value", "");
                    string sanitizedValue = mapping.Value;

                    // Format the mapping expression based on its content
                    if (sanitizedValue.Contains("=>"))
                    {
                        // Lambda expression format
                        sb.AppendLine($"        MAP {sanitizedKey} using expression: {sanitizedValue}");
                    }
                    else if (sanitizedValue.StartsWith("$"))
                    {
                        // SnapLogic path format
                        sb.AppendLine($"        SET {sanitizedKey} = value from document path {sanitizedValue}");
                    }
                    else if (sanitizedValue.Contains("{") && sanitizedValue.Contains("}"))
                    {
                        // JSON/Object mapping
                        sb.AppendLine($"        SET {sanitizedKey} = complex object mapping");
                    }
                    else if (sanitizedValue.ToLower() == "true" || sanitizedValue.ToLower() == "false" ||
                               double.TryParse(sanitizedValue, out _))
                    {
                        // Boolean or numeric value
                        sb.AppendLine($"        SET {sanitizedKey} = literal value {sanitizedValue}");
                    }
                    else
                    {
                        // Simple value mapping
                        if (sanitizedValue.Contains(".") || Regex.IsMatch(sanitizedValue, @"\w+\(.*\)"))
                        {
                            sb.AppendLine($"        SET {sanitizedKey} = result of expression {sanitizedValue}");
                        }
                        else
                        {
                            sb.AppendLine($"        SET {sanitizedKey} = \"{sanitizedValue}\"");
                        }
                    }
                }
            }
            else
            {
                sb.AppendLine("        // No explicit mappings found");
                
                // Check for specific configurations based on snap properties
                bool hasSchema = snap.Properties.Any(p => p.Key.Contains("schema", StringComparison.OrdinalIgnoreCase));
                bool hasTransform = snap.Properties.Any(p =>
                    p.Key.Contains("transform", StringComparison.OrdinalIgnoreCase) ||
                    p.Key.Contains("mapping", StringComparison.OrdinalIgnoreCase));

                if (hasSchema)
                {
                    sb.AppendLine("        APPLY schema-based transformation to document");
                }
                else if (hasTransform)
                {
                    sb.AppendLine("        APPLY complex transformation configuration");
                }
                else
                {
                    sb.AppendLine("        COPY all fields from input to output (pass-through)");
                }
            }

            sb.AppendLine();
            sb.AppendLine("        IF transformation successful THEN");
            sb.AppendLine("            WRITE transformed document to output");
            sb.AppendLine("        ELSE");
            sb.AppendLine("            WRITE error message to error output");
            sb.AppendLine("        END IF");
            sb.AppendLine("    END WHILE");
            sb.AppendLine("END");

            return sb.ToString();
        }

        // Helper to format complex mapping expressions
        private string FormatComplexMapping(string value)
        {
            // Try to format JSON-like strings for better readability
            try
            {
                if (value.TrimStart().StartsWith("{") || value.TrimStart().StartsWith("["))
                {
                    using var doc = JsonDocument.Parse(value);
                    return doc.RootElement.ToString();  // This will format it nicely
                }
            }
            catch
            {
                // Just return the value if we can't parse it
            }
            return value;
        }

        // Check if a string is a known variable in the SnapLogic context
        private bool IsKnownVariable(string value)
        {
            // List of common variables or functions that shouldn't be treated as strings
            string[] knownVariables = new string[] {
                "null", "undefined", "true", "false", "document", "input", "output",
                "error", "Date", "Math", "JSON", "Object", "Array", "String", "Number",
                "Boolean", "Map", "Set"
            };

            return knownVariables.Contains(value) ||
                   Regex.IsMatch(value, @"^\d+$") ||  // Numeric strings
                   Regex.IsMatch(value, @"^[a-zA-Z_][a-zA-Z0-9_]*$"); // Valid identifier names
        }        // Generate explicit pseudocode for router snaps
        private string GenerateRouterPseudocode(SnapNode snap)
        {
            var sb = new StringBuilder();
            var routes = ExtractRoutes(snap);

            sb.AppendLine($"BEGIN {snap.Label.Replace(" ", "")}Router");
            sb.AppendLine("    WHILE input has more documents DO");
            sb.AppendLine("        READ document from input");
            sb.AppendLine("        SET routed = FALSE");
            sb.AppendLine();

            if (routes.Any())
            {
                sb.AppendLine("        // Evaluate routing conditions");
                foreach (var (path, condition) in routes.Where(r => r.path != "default"))
                {
                    sb.AppendLine($"        // Route to: {path}");
                    
                    // Format the condition for better readability
                    string formattedCondition = FormatRouterCondition(condition);
                    
                    sb.AppendLine($"        IF {formattedCondition} THEN");
                    sb.AppendLine($"            WRITE document to {path} output");
                    sb.AppendLine("            SET routed = TRUE");

                    // If firstMatch is enabled, we exit after the first match
                    if (snap.Properties.TryGetValue("settings.firstMatch.value", out string firstMatchStr) &&
                        bool.TryParse(firstMatchStr, out bool firstMatch) && firstMatch)
                    {
                        sb.AppendLine("            EXIT routing (first match only)");
                    }

                    sb.AppendLine("        END IF");
                }

                // Handle default route if present
                var defaultRoute = routes.FirstOrDefault(r => r.path == "default");
                if (defaultRoute != default)
                {
                    sb.AppendLine();
                    sb.AppendLine("        // Default route handling");
                    sb.AppendLine("        IF routed = FALSE THEN");
                    sb.AppendLine("            WRITE document to default output");
                    sb.AppendLine("        END IF");
                }
                // Check if there's an implied default - when no routes match in SnapLogic
                else if (!routes.Any(r => r.condition.Contains("$") == false ||
                                         r.condition.Contains("!=") ||
                                         r.condition.Contains("<") ||
                                         r.condition.Contains(">")))
                {
                    sb.AppendLine();
                    sb.AppendLine("        // No default route specified");
                    sb.AppendLine("        IF routed = FALSE THEN");
                    sb.AppendLine("            // Document not routed when no conditions match");
                    sb.AppendLine("        END IF");
                }
            }
            else
            {
                sb.AppendLine("        // No routing conditions defined");
                sb.AppendLine("        WRITE document to default output");
            }

            sb.AppendLine("    END WHILE");
            sb.AppendLine("END");

            return sb.ToString();
        }

        // Helper method to format router conditions
        private string FormatRouterCondition(string condition)
        {
            if (string.IsNullOrEmpty(condition))
            {
                return "true"; // Default condition
            }

            // If this is a plain string, wrap it in quotes
            if (!condition.Contains("$") &&
                !condition.Contains("==") &&
                !condition.Contains("!=") &&
                !condition.Contains("<") &&
                !condition.Contains(">"))
            {
                return $"\"{condition}\"";
            }

            // Return the condition as is
            return condition;
        }        // Generate explicit pseudocode for condition snaps
        private string GenerateConditionPseudocode(SnapNode snap)
        {
            var sb = new StringBuilder();
            var conditions = ExtractConditions(snap);

            sb.AppendLine($"BEGIN {snap.Label.Replace(" ", "")}Filter");
            sb.AppendLine("    WHILE input has more documents DO");
            sb.AppendLine("        READ document from input");
            sb.AppendLine();

            // If we have multiple conditions, show them all
            if (conditions.Count > 1)
            {
                sb.AppendLine("        // Evaluate all conditions");
                sb.AppendLine("        SET allConditionsMet = TRUE");
                sb.AppendLine();
                foreach (var (condition, description) in conditions)
                {
                    sb.AppendLine($"        // Check: {description}");
                    sb.AppendLine($"        IF NOT ({condition}) THEN");
                    sb.AppendLine("            SET allConditionsMet = FALSE");
                    sb.AppendLine("        END IF");
                }
                sb.AppendLine();
                sb.AppendLine("        IF allConditionsMet = TRUE THEN");
                sb.AppendLine("            WRITE document to output");
                sb.AppendLine("        ELSE");
                sb.AppendLine("            WRITE document to error output");
                sb.AppendLine("        END IF");
            }
            // Single condition case
            else if (conditions.Count == 1)
            {
                var (condition, description) = conditions[0];
                sb.AppendLine($"        // Check: {description}");
                sb.AppendLine($"        IF {condition} THEN");
                sb.AppendLine("            WRITE document to output");
                sb.AppendLine("        ELSE");
                sb.AppendLine("            WRITE document to error output");
                sb.AppendLine("        END IF");
            }
            // No conditions found - pass through
            else
            {
                sb.AppendLine("        // No conditions found - pass all documents");
                sb.AppendLine("        WRITE document to output");
            }

            sb.AppendLine("    END WHILE");
            sb.AppendLine("END");

            return sb.ToString();
        }// Test connection to Azure OpenAI
        private async Task<bool> TestAzureOpenAIConnection(CancellationToken cancellationToken = default)
        {
            try
            {
                Console.WriteLine("🔍 Testing connection to Azure OpenAI...");
                Console.WriteLine($"🔍 API Key length: {_apiKey?.Length ?? 0}");
                Console.WriteLine($"🔍 Endpoint: {_endpoint}");
                Console.WriteLine($"🔍 Deployment: {_deploymentName}");

                // Validate configuration
                if (string.IsNullOrEmpty(_apiKey))
                {
                    Console.WriteLine("❌ API key is not configured");
                    return false;
                }

                if (string.IsNullOrEmpty(_endpoint))
                {
                    Console.WriteLine("❌ Endpoint is not configured");
                    return false;
                }

                if (string.IsNullOrEmpty(_deploymentName))
                {
                    Console.WriteLine("❌ Deployment name is not configured");
                    return false;
                }

                // HttpClient should already be initialized in the constructor
                if (_httpClient == null)
                {
                    Console.WriteLine("❌ HTTP client is null, which should not happen");
                    return false;
                }

                Console.WriteLine("✅ All configuration values are present");
                Console.WriteLine("🚀 Sending test request to Azure OpenAI...");

                var prompt = "Test connection. Please respond with 'ok'.";
                var response = await SendPromptToAzureAI(prompt, cancellationToken);

                Console.WriteLine($"📨 Test connection response: '{response}'");
                bool success = !string.IsNullOrEmpty(response);
                Console.WriteLine($"🎯 Connection test result: {(success ? "SUCCESS" : "FAILED")}");
                return success;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error testing Azure OpenAI connection: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                return false;
            }
        }        // Send prompt to Azure OpenAI
        private async Task<string> SendPromptToAzureAI(string prompt, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.Now;
            var requestId = Guid.NewGuid().ToString("N")[0..8];

            LogMessage($"[AI-REQ-{requestId}] Starting Azure OpenAI request");
            LogMessage($"[AI-REQ-{requestId}] Prompt length: {prompt?.Length ?? 0} characters");
            LogMessage($"[AI-REQ-{requestId}] CancellationToken: IsCancelled={cancellationToken.IsCancellationRequested}, CanBeCancelled={cancellationToken.CanBeCanceled}");

            // Validate required parameters
            if (string.IsNullOrEmpty(_apiKey) || string.IsNullOrEmpty(_endpoint))
            {
                LogMessage($"[AI-REQ-{requestId}] ERROR: Azure OpenAI API key or endpoint not configured");
                throw new InvalidOperationException("Azure OpenAI API key or endpoint not configured");
            }

            if (string.IsNullOrEmpty(_deploymentName))
            {
                LogMessage($"[AI-REQ-{requestId}] ERROR: Azure OpenAI deployment name not configured");
                throw new InvalidOperationException("Azure OpenAI deployment name not configured");
            }

            if (_httpClient == null)
            {
                LogMessage($"[AI-REQ-{requestId}] ERROR: HTTP client is not initialized");
                throw new InvalidOperationException("HTTP client is not initialized");
            }

            // Log the request details (excluding sensitive information)
            LogMessage($"[AI-REQ-{requestId}] Endpoint: {_endpoint}");
            LogMessage($"[AI-REQ-{requestId}] Deployment: {_deploymentName}");
            LogMessage($"[AI-REQ-{requestId}] HttpClient timeout: {_httpClient.Timeout}");
            var requestUri = $"{_endpoint}/openai/deployments/{_deploymentName}/chat/completions?api-version=2024-02-01";
            LogMessage($"[AI-REQ-{requestId}] Request URI: {requestUri}");

            var requestBody = new
            {
                messages = new[] {
                    new {
                        role = "user",
                        content = prompt
                    }
                },
                max_tokens = 2000,
                temperature = 0.7,
                frequency_penalty = 0,
                presence_penalty = 0
            };

            try
            {
                LogMessage($"[AI-REQ-{requestId}] Creating HTTP request message");

                // Create the request with proper content
                var request = new HttpRequestMessage(HttpMethod.Post, requestUri)
                {
                    Content = new StringContent(
                        JsonSerializer.Serialize(requestBody),
                        System.Text.Encoding.UTF8,
                        "application/json"
                    )
                };

                // Add the API key header
                request.Headers.Add("api-key", _apiKey);

                LogMessage($"[AI-REQ-{requestId}] About to send HTTP request to Azure OpenAI");
                var sendStartTime = DateTime.Now;

                // Send the request
                using var response = await _httpClient.SendAsync(request, cancellationToken);

                var sendDuration = DateTime.Now - sendStartTime;
                LogMessage($"[AI-REQ-{requestId}] HTTP request completed in {sendDuration.TotalMilliseconds:F0}ms, Status: {response.StatusCode}");

                response.EnsureSuccessStatusCode();

                LogMessage($"[AI-REQ-{requestId}] About to read response content");
                var readStartTime = DateTime.Now;

                var jsonResponse = await response.Content.ReadAsStringAsync(cancellationToken);

                var readDuration = DateTime.Now - readStartTime;
                LogMessage($"[AI-REQ-{requestId}] Response content read in {readDuration.TotalMilliseconds:F0}ms, Length: {jsonResponse?.Length ?? 0} characters");

                // Process the response
                if (string.IsNullOrEmpty(jsonResponse))
                {
                    LogMessage($"[AI-REQ-{requestId}] ERROR: Received empty response from Azure OpenAI");
                    return null;
                }

                LogMessage($"[AI-REQ-{requestId}] Parsing JSON response");
                using var doc = JsonDocument.Parse(jsonResponse);

                // Check if the response contains the choices property
                if (!doc.RootElement.TryGetProperty("choices", out var choices))
                {
                    LogMessage($"[AI-REQ-{requestId}] ERROR: Response doesn't contain 'choices' property");
                    LogMessage($"[AI-REQ-{requestId}] Response content: {jsonResponse}");
                    return null;
                }

                // Check if there are any choices
                if (choices.GetArrayLength() <= 0)
                {
                    LogMessage($"[AI-REQ-{requestId}] ERROR: No choices returned in the response");
                    return null;
                }
                // Get the first choice
                var firstChoice = choices[0];

                // For chat/completions, look for the message property
                if (!firstChoice.TryGetProperty("message", out var messageElement))
                {
                    LogMessage($"[AI-REQ-{requestId}] ERROR: First choice doesn't contain 'message' property");
                    return null;
                }

                // Get the content from the message
                if (!messageElement.TryGetProperty("content", out var contentElement))
                {
                    LogMessage($"[AI-REQ-{requestId}] ERROR: Message doesn't contain 'content' property");
                    return null;
                }

                // Return the content value
                var result = contentElement.GetString()?.Trim();
                var totalDuration = DateTime.Now - startTime;
                LogMessage($"[AI-REQ-{requestId}] SUCCESS: Request completed in {totalDuration.TotalMilliseconds:F0}ms, Result length: {result?.Length ?? 0} characters");

                return result;
            }
            catch (TaskCanceledException ex)
            {
                var duration = DateTime.Now - startTime;
                LogMessage($"[AI-REQ-{requestId}] TIMEOUT: Request timed out after {duration.TotalMilliseconds:F0}ms - {ex.Message}");
                throw;
            }
            catch (OperationCanceledException ex)
            {
                var duration = DateTime.Now - startTime;
                LogMessage($"[AI-REQ-{requestId}] CANCELLED: Request cancelled after {duration.TotalMilliseconds:F0}ms - {ex.Message}");
                throw;
            }
            catch (HttpRequestException ex)
            {
                var duration = DateTime.Now - startTime;
                LogMessage($"[AI-REQ-{requestId}] HTTP ERROR: Request failed after {duration.TotalMilliseconds:F0}ms - {ex.Message}");
                if (ex.InnerException != null)
                {
                    LogMessage($"[AI-REQ-{requestId}] HTTP ERROR Inner: {ex.InnerException.Message}");
                }
                throw;
            }
            catch (Exception ex)
            {
                var duration = DateTime.Now - startTime;
                LogMessage($"[AI-REQ-{requestId}] ERROR: Request failed after {duration.TotalMilliseconds:F0}ms - {ex.Message}");
                LogMessage($"[AI-REQ-{requestId}] Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    LogMessage($"[AI-REQ-{requestId}] Inner exception: {ex.InnerException.Message}");
                }
                throw;
            }
        }

        // Create summary for pipeline analysis
        private string CreatePipelineSummary(PipelineData pipeline)
        {
            var sb = new StringBuilder();

            sb.AppendLine($"Pipeline Name: {pipeline.Name}");
            sb.AppendLine($"Total Snaps: {pipeline.Snaps.Count}");
            sb.AppendLine();

            // List snaps in order
            sb.AppendLine("Snap Components:");
            foreach (var snap in pipeline.Snaps.OrderBy(s => s.Position.Y).ThenBy(s => s.Position.X))
            {
                sb.AppendLine($"- {snap.Label} ({snap.Type})");
            }

            return sb.ToString();
        }

        // Create data flow summary
        private string CreateDataFlowSummary(PipelineData pipeline)
        {
            var sb = new StringBuilder();

            // Start with input snaps
            var inputSnaps = pipeline.Snaps.Where(s => !s.InputConnections.Any());
            sb.AppendLine("Data Flow Analysis:");
            foreach (var snap in inputSnaps)
            {
                AnalyzeSnapFlow(snap, pipeline, sb, 1);
            }

            return sb.ToString();
        }

        private void AnalyzeSnapFlow(SnapNode snap, PipelineData pipeline, StringBuilder sb, int depth)
        {
            var indent = new string(' ', depth * 2);
            sb.AppendLine($"{indent}{snap.Label} ({snap.Type})");

            // Follow output connections
            foreach (var conn in snap.OutputConnections)
            {
                var targetSnap = pipeline.Snaps.FirstOrDefault(s => s.Id == conn.TargetId);
                if (targetSnap != null)
                {
                    AnalyzeSnapFlow(targetSnap, pipeline, sb, depth + 1);
                }
            }
        }

        // Create context for snap analysis
        private string CreateSnapContext(SnapNode snap, PipelineData pipeline)
        {
            var sb = new StringBuilder();

            sb.AppendLine($"Snap Name: {snap.Label}");
            sb.AppendLine($"Snap Type: {snap.Type}");
            sb.AppendLine();

            // Add relevant properties
            sb.AppendLine("Properties:");
            foreach (var prop in snap.Properties)
            {
                if (!prop.Key.StartsWith("_") && prop.Value?.Length < 500)
                {
                    sb.AppendLine($"- {prop.Key}: {prop.Value}");
                }
            }

            return sb.ToString();
        }

        // Generic description methods for when AI is not available
        private string GetGenericDescription(PipelineData pipeline)
        {
            return $"This pipeline contains {pipeline.Snaps.Count} snaps that process and transform data. " +
                   "It includes input sources, data transformation steps, and output destinations.";
        }

        private string GetGenericSnapDescription(SnapNode snap, PipelineData pipeline)
        {
            return $"This {snap.Type} snap processes data according to its configuration and sends it to the next step in the pipeline.";
        }

        private string GetGenericDataFlowAnalysis(PipelineData pipeline)
        {
            var inputCount = pipeline.Snaps.Count(s => !s.InputConnections.Any());
            var outputCount = pipeline.Snaps.Count(s => !s.OutputConnections.Any());

            return $"This pipeline has {inputCount} input source(s) and {outputCount} output destination(s). " +
                   $"Data flows through {pipeline.Snaps.Count} total processing steps.";
        }
        private bool ShouldSkipAIDescription(SnapNode snap)
        {
            // Skip AI description for basic snap types
            string snapType = snap.Type.ToLowerInvariant();
            return snapType.Contains("copy") ||
                   snapType.Contains("union") ||
                   snapType.Contains("exit") ||
                   snapType.Contains("unique");
        }
        private string GetSimplifiedDescription(SnapNode snap)
        {
            string snapType = snap.Type.ToLowerInvariant();

            if (snapType.Contains("copy"))
                return "Creates identical copies of each input document and sends them to multiple outputs without modifying the data.";
            else if (snapType.Contains("union"))
                return "Merges document streams from multiple inputs into a single output stream without changing the document content.";
            else if (snapType.Contains("exit"))
                return "Terminates pipeline execution when specific conditions are met, allowing for conditional pipeline stopping.";
            else if (snapType.Contains("unique"))
                return "Filters out duplicate documents from the input stream, ensuring only unique documents pass through based on specified criteria.";
            else
                return $"Standard {snap.Type} snap that performs core SnapLogic functionality.";
        }// Update Azure OpenAI configuration
        public void UpdateConfiguration(string endpoint, string deploymentName, int timeoutSeconds)
        {
            _endpoint = endpoint;
            _deploymentName = deploymentName;
            _timeoutSeconds = timeoutSeconds;
            _useAzureAI = !string.IsNullOrEmpty(_apiKey) && !string.IsNullOrEmpty(_endpoint);            // Update the HttpClient timeout to 5 minutes as safety net
            _httpClient.Timeout = TimeSpan.FromMinutes(5);

            // Test the new configuration
            Task.Run(async () =>
            {
                try
                {
                    bool isConnected = await TestAzureOpenAIConnection();
                    if (isConnected)
                    {
                        LogMessage("✅ Successfully connected to Azure OpenAI API with new configuration");
                    }
                    else
                    {
                        LogMessage("❌ Failed to connect to Azure OpenAI API with new configuration");
                        _useAzureAI = false;
                    }
                }
                catch (Exception ex)
                {
                    LogMessage($"❌ Error testing Azure OpenAI connection: {ex.Message}");
                    _useAzureAI = false;
                }
            });
        }        // Get current Azure OpenAI deployment information
        public DeploymentInfo GetDeploymentInfo()
        {
            return new DeploymentInfo
            {
                ApiKey = _apiKey,
                Endpoint = _endpoint,
                DeploymentName = _deploymentName,
                TimeoutSeconds = _timeoutSeconds
            };
        }

        public class DeploymentInfo
        {
            public string ApiKey { get; set; }
            public string Endpoint { get; set; }
            public string DeploymentName { get; set; }
            public int TimeoutSeconds { get; set; }
        }
        public async Task<string> GenerateSnapPseudocode(SnapNode snap, PipelineData pipeline, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.Now;
            var requestId = Guid.NewGuid().ToString("N")[0..8];

            LogMessage($"[PSEUDOCODE-{requestId}] Starting pseudocode generation for '{snap?.Label ?? "Unknown"}' ({snap?.Type ?? "Unknown"})");
            LogMessage($"[PSEUDOCODE-{requestId}] Using Azure AI: {_useAzureAI}");

            try
            {
                // For mapper and condition snaps, we can generate explicit pseudocode
                LogMessage($"[PSEUDOCODE-{requestId}] Checking if snap is mapper or condition type");
                if (IsMapperOrConditionSnap(snap))
                {
                    LogMessage($"[PSEUDOCODE-{requestId}] Snap is mapper/condition type, generating explicit pseudocode");
                    string pseudocode = GenerateExplicitPseudocode(snap, pipeline);
                    if (!string.IsNullOrEmpty(pseudocode))
                    {
                        var duration = DateTime.Now - startTime;
                        LogMessage($"[PSEUDOCODE-{requestId}] SUCCESS: Explicit pseudocode generated in {duration.TotalMilliseconds:F0}ms, length: {pseudocode?.Length ?? 0} characters");
                        LogMessage($"[PSEUDOCODE-{requestId}] Storing explicit pseudocode in cache");

                        // Store in cache if we have valid pseudocode
                        _cache.StoreDescription(snap, null, pseudocode);
                        return pseudocode;
                    }
                    else
                    {
                        LogMessage($"[PSEUDOCODE-{requestId}] WARNING: Explicit pseudocode generation returned null or empty");
                    }
                }
                else
                {
                    LogMessage($"[PSEUDOCODE-{requestId}] Snap is not mapper/condition type, will try AI-based generation");
                }

                // Otherwise, generate AI-based pseudocode if Azure OpenAI is available
                if (_useAzureAI)
                {
                    try
                    {
                        LogMessage($"[PSEUDOCODE-{requestId}] Creating context for AI-based pseudocode generation");
                        var context = CreateSnapContext(snap, pipeline);
                        LogMessage($"[PSEUDOCODE-{requestId}] Context created, length: {context?.Length ?? 0} characters");                        string prompt = $"Write abstract pseudocode (max 20 lines) following GeeksforGeeks standards for this SnapLogic snap:\n\n{context}\n\n" +
                                    "Use proper pseudocode format:\n" +
                                    "- Capital letters for keywords (IF, ELSE, FOR, WHILE, BEGIN, END)\n" +
                                    "- Simple English descriptions instead of programming syntax\n" +
                                    "- Language-agnostic and abstract\n" +
                                    "- No curly braces, semicolons, or specific language constructs\n\n" +
                                    "Include only:\n" +
                                    "1. Input processing logic\n" +
                                    "2. Main transformation steps\n" +
                                    "3. Basic error handling\n\n" +
                                    "Example format:\n" +
                                    "BEGIN ProcessDocument\n" +
                                    "    READ input data\n" +
                                    "    IF condition THEN\n" +
                                    "        PERFORM transformation\n" +
                                    "    ELSE\n" +
                                    "        HANDLE error\n" +
                                    "    END IF\n" +
                                    "    WRITE output data\n" +
                                    "END ProcessDocument";

                        LogMessage($"[PSEUDOCODE-{requestId}] Sending to Azure OpenAI, prompt length: {prompt?.Length ?? 0} characters");

                        string pseudocode = await SendPromptToAzureAI(prompt, cancellationToken);
                        if (!string.IsNullOrEmpty(pseudocode))
                        {
                            var duration = DateTime.Now - startTime;
                            LogMessage($"[PSEUDOCODE-{requestId}] SUCCESS: AI-based pseudocode generated in {duration.TotalMilliseconds:F0}ms, length: {pseudocode?.Length ?? 0} characters");
                            LogMessage($"[PSEUDOCODE-{requestId}] Storing AI-based pseudocode in cache");
                            _cache.StoreDescription(snap, null, pseudocode);
                            return pseudocode;
                        }
                        else
                        {
                            LogMessage($"[PSEUDOCODE-{requestId}] WARNING: Azure OpenAI returned null or empty pseudocode");
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        var duration = DateTime.Now - startTime;
                        LogMessage($"[PSEUDOCODE-{requestId}] CANCELLED: Azure OpenAI request cancelled after {duration.TotalMilliseconds:F0}ms");
                        throw;
                    }
                    catch (Exception ex)
                    {
                        var duration = DateTime.Now - startTime;
                        LogMessage($"[PSEUDOCODE-{requestId}] ERROR: AI-based pseudocode generation failed after {duration.TotalMilliseconds:F0}ms - {ex.Message}");
                    }
                }
                else
                {
                    LogMessage($"[PSEUDOCODE-{requestId}] Azure AI disabled, cannot generate AI-based pseudocode");
                }

                var finalDuration = DateTime.Now - startTime;
                LogMessage($"[PSEUDOCODE-{requestId}] COMPLETED: No pseudocode generated in {finalDuration.TotalMilliseconds:F0}ms");
                return null;
            }
            catch (Exception ex)
            {
                {
                    var duration = DateTime.Now - startTime;
                    LogMessage($"[PSEUDOCODE-{requestId}] FATAL ERROR: Pseudocode generation failed after {duration.TotalMilliseconds:F0}ms - {ex.Message}");
                    LogMessage($"[PSEUDOCODE-{requestId}] Stack trace: {ex.StackTrace}");
                    throw;
                }
            } 
        }


        public async Task<string> GenerateEnhancedSnapDescription(SnapNode snap, PipelineData pipeline, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.Now;
            var requestId = Guid.NewGuid().ToString("N")[0..8];

            LogMessage($"[ENHANCED-DESC-{requestId}] Starting enhanced snap description for '{snap?.Label ?? "Unknown"}' ({snap?.Type ?? "Unknown"})");
            LogMessage($"[ENHANCED-DESC-{requestId}] Using cached descriptions: {_useCachedDescriptions}");
            LogMessage($"[ENHANCED-DESC-{requestId}] Using Azure AI: {_useAzureAI}");

            try
            {
                if (_useCachedDescriptions && _cache.TryGetDescription(snap, out string cachedDescription))
                {
                    var duration = DateTime.Now - startTime;
                    LogMessage($"[ENHANCED-DESC-{requestId}] CACHE HIT: Found cached enhanced description in {duration.TotalMilliseconds:F0}ms, length: {cachedDescription?.Length ?? 0} characters");
                    return cachedDescription;
                }

                LogMessage($"[ENHANCED-DESC-{requestId}] CACHE MISS: No cached enhanced description found");

                if (_useAzureAI)
                {
                    try
                    {
                        LogMessage($"[ENHANCED-DESC-{requestId}] Creating snap context for Azure OpenAI");
                        var context = CreateSnapContext(snap, pipeline);
                        LogMessage($"[ENHANCED-DESC-{requestId}] Context created, length: {context?.Length ?? 0} characters");
                        string prompt = $"Write a concise technical description of this SnapLogic snap (max 150 words):\n\n{context}\n\n" +
                                    "Briefly cover only:\n" +
                                    "1. Primary function in data processing\n" +
                                    "2. Key configuration settings\n" +
                                    "3. Input/output behavior\n" +
                                    "4. Essential best practices\n\n" +
                                    "Use direct statements and avoid lengthy explanations.";

                        LogMessage($"[ENHANCED-DESC-{requestId}] Sending to Azure OpenAI, prompt length: {prompt?.Length ?? 0} characters");

                        string description = await SendPromptToAzureAI(prompt, cancellationToken);

                        if (!string.IsNullOrEmpty(description))
                        {
                            LogMessage($"[ENHANCED-DESC-{requestId}] Azure OpenAI response received, length: {description?.Length ?? 0} characters");
                            LogMessage($"[ENHANCED-DESC-{requestId}] Storing enhanced description in cache");
                            _cache.StoreDescription(snap, description, null);

                            var totalDuration = DateTime.Now - startTime;
                            LogMessage($"[ENHANCED-DESC-{requestId}] SUCCESS: Enhanced description completed in {totalDuration.TotalMilliseconds:F0}ms");
                            return description;
                        }
                        else
                        {
                            LogMessage($"[ENHANCED-DESC-{requestId}] WARNING: Azure OpenAI returned null or empty description");
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        var duration = DateTime.Now - startTime;
                        LogMessage($"[ENHANCED-DESC-{requestId}] CANCELLED: Azure OpenAI request cancelled after {duration.TotalMilliseconds:F0}ms");
                        throw;
                    }
                    catch (Exception ex)
                    {
                        var duration = DateTime.Now - startTime;
                        LogMessage($"[ENHANCED-DESC-{requestId}] ERROR: Azure OpenAI failed after {duration.TotalMilliseconds:F0}ms - {ex.Message}");
                        LogMessage($"[ENHANCED-DESC-{requestId}] No fallback available for enhanced descriptions");
                    }
                }
                else
                {
                    LogMessage($"[ENHANCED-DESC-{requestId}] Azure AI disabled, enhanced descriptions not available");
                }

                var finalDuration = DateTime.Now - startTime;
                LogMessage($"[ENHANCED-DESC-{requestId}] COMPLETED: No enhanced description generated in {finalDuration.TotalMilliseconds:F0}ms");
                return null;
            }
            catch (Exception ex)
            {
                var duration = DateTime.Now - startTime;
                LogMessage($"[ENHANCED-DESC-{requestId}] FATAL ERROR: Enhanced description generation failed after {duration.TotalMilliseconds:F0}ms - {ex.Message}");
                LogMessage($"[ENHANCED-DESC-{requestId}] Stack trace: {ex.StackTrace}");
                throw;
            }
        }
    }
}
