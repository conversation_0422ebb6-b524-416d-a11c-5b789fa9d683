@using SnapLogic.Documentation.Shared
@using SnapDocumenterWeb.Services
@inject ProjectService ProjectService
@inject IJSRuntime JSRuntime

<div class="modal fade" id="projectModal" tabindex="-1" aria-labelledby="projectModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="projectModalLabel">
                    @if (IsEditMode)
                    {
                        <i class="fas fa-edit me-2"></i><text>Edit Project</text>
                    }
                    else
                    {
                        <i class="fas fa-plus me-2"></i><text>New Project</text>
                    }
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <EditForm Model="@currentProject" OnValidSubmit="@HandleValidSubmit">
                    <DataAnnotationsValidator />
                    <ValidationSummary class="text-danger" />

                    <div class="mb-3">
                        <label for="projectName" class="form-label">Project Name *</label>
                        <InputText @bind-Value="currentProject.Name" class="form-control" id="projectName" placeholder="Enter project name" />
                    </div>

                    <div class="mb-3">
                        <label for="projectDescription" class="form-label">Description *</label>
                        <InputTextArea @bind-Value="currentProject.Description" class="form-control" id="projectDescription" rows="3" placeholder="Describe the purpose of this project" />
                    </div>

                    <div class="mb-3">
                        <label for="projectPurpose" class="form-label">Purpose</label>
                        <InputTextArea @bind-Value="currentProject.Purpose" class="form-control" id="projectPurpose" rows="2" placeholder="Additional details about the project's purpose" />
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6>Output Options</h6>
                            <div class="form-check">
                                <InputCheckbox @bind-Value="currentProject.GenerateHtml" class="form-check-input" id="projectHtml" />
                                <label class="form-check-label" for="projectHtml">
                                    Generate HTML Documentation
                                </label>
                            </div>
                            <div class="form-check">
                                <InputCheckbox @bind-Value="currentProject.GeneratePdf" class="form-check-input" id="projectPdf" />
                                <label class="form-check-label" for="projectPdf">
                                    Generate PDF Documentation
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>AI Options</h6>
                            <div class="form-check">
                                <InputCheckbox @bind-Value="currentProject.UseAI" class="form-check-input" id="projectAI" />
                                <label class="form-check-label" for="projectAI">
                                    Use AI for Pipeline Overview
                                </label>
                            </div>
                            <div class="form-check">
                                <InputCheckbox @bind-Value="currentProject.UseCachedDescriptions" class="form-check-input" id="projectCache" />
                                <label class="form-check-label" for="projectCache">
                                    Use Cached AI Descriptions
                                </label>
                            </div>
                        </div>
                    </div>

                    @if (validationErrors.Any())
                    {
                        <div class="alert alert-danger mt-3">
                            <h6>Please fix the following errors:</h6>
                            <ul class="mb-0">
                                @foreach (var error in validationErrors)
                                {
                                    <li>@error</li>
                                }
                            </ul>
                        </div>
                    }
                </EditForm>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" @onclick="SaveProject">
                    <i class="fas fa-save me-2"></i>
                    @(IsEditMode ? "Update" : "Create") Project
                </button>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public ProjectData? Project { get; set; }
    [Parameter] public EventCallback<ProjectData> OnProjectSaved { get; set; }
    [Parameter] public bool IsEditMode { get; set; }

    private ProjectData currentProject = new();
    private List<string> validationErrors = new();

    protected override void OnParametersSet()
    {
        if (Project != null)
        {
            // Create a copy to avoid modifying the original
            currentProject = new ProjectData
            {
                Name = Project.Name,
                Description = Project.Description,
                Purpose = Project.Purpose,
                GenerateHtml = Project.GenerateHtml,
                GeneratePdf = Project.GeneratePdf,
                UseAI = Project.UseAI,
                UseCachedDescriptions = Project.UseCachedDescriptions,
                SlpFiles = new List<string>(Project.SlpFiles),
                CreatedDate = Project.CreatedDate,
                LastModified = Project.LastModified
            };
        }
        else
        {
            currentProject = ProjectService.CreateNewProject("", "", "");
        }
    }

    private async Task HandleValidSubmit()
    {
        await SaveProject();
    }

    private async Task SaveProject()
    {
        validationErrors.Clear();
        
        // Validate the project
        validationErrors = ProjectService.ValidateProject(currentProject);
        
        if (validationErrors.Any())
        {
            StateHasChanged();
            return;
        }

        try
        {
            // If editing, update the original project
            if (IsEditMode && Project != null)
            {
                Project.Name = currentProject.Name;
                Project.Description = currentProject.Description;
                Project.Purpose = currentProject.Purpose;
                Project.GenerateHtml = currentProject.GenerateHtml;
                Project.GeneratePdf = currentProject.GeneratePdf;
                Project.UseAI = currentProject.UseAI;
                Project.UseCachedDescriptions = currentProject.UseCachedDescriptions;
                Project.LastModified = DateTime.Now;
            }

            // Notify parent component
            await OnProjectSaved.InvokeAsync(IsEditMode ? Project : currentProject);

            // Close modal
            await JSRuntime.InvokeVoidAsync("eval", "bootstrap.Modal.getInstance(document.getElementById('projectModal')).hide()");
        }
        catch (Exception ex)
        {
            validationErrors.Add($"Error saving project: {ex.Message}");
            StateHasChanged();
        }
    }

    public async Task ShowModal()
    {
        await JSRuntime.InvokeVoidAsync("eval", "new bootstrap.Modal(document.getElementById('projectModal')).show()");
    }

    public async Task HideModal()
    {
        await JSRuntime.InvokeVoidAsync("eval", "bootstrap.Modal.getInstance(document.getElementById('projectModal')).hide()");
    }
}
