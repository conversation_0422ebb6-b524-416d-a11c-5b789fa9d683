<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows7.0</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="DebugProgram.cs" />
    <Compile Include="MainForm.cs" />
    <Compile Include="ConfigManager.cs" />
    <Compile Include="SlpAnalyzer.cs" />
    <Compile Include="DiagramGenerator.cs" />
    <Compile Include="DocumentationGenerator.cs" />
    <Compile Include="AIDescriptionGenerator.cs" />
    <Compile Include="AzureOpenAITester.cs" />
    <Compile Include="DescriptionCache.cs" />
    <Compile Include="ProjectData.cs" />
    <Compile Include="SnapBestPractices.cs" />
    <Compile Include="FlowControlDiagramGenerator.cs" />
    <Compile Include="FlowControlConfigurationGenerator.cs" />
    <Compile Include="PipelinePatternAnalyzer.cs" />
    <Compile Include="PathAnalysis.cs" />
    <Compile Include="SnapLogicClient.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Select.HtmlToPdf.NetCore" Version="22.2.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="MainForm.resx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
</Project>
