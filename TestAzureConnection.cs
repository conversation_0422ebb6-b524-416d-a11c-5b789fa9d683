using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

class Program
{
    static async Task Main(string[] args)
    {
        // Configuration from the corrected config file
        string apiKey = "6dr7izA3Iuc1qhTiaKMZWnx1iHX0yI0MDx4Hhj1VWkSLHQ7CCPD7JQQJ99BEACmepeSXJ3w3AAABACOG0Qbs";
        string endpoint = "https://openai-snaplogic-documenter.openai.azure.com/";
        string deploymentName = "gpt-4.1-2";
        
        Console.WriteLine("Testing Azure OpenAI connection...");
        Console.WriteLine($"Endpoint: {endpoint}");
        Console.WriteLine($"Deployment: {deploymentName}");
        Console.WriteLine($"API Key: {apiKey.Substring(0, 8)}...");
        Console.WriteLine();
        
        await TestConnection(apiKey, endpoint, deploymentName);
    }
    
    static async Task TestConnection(string apiKey, string endpoint, string deploymentName)
    {
        try
        {
            var httpClientHandler = new HttpClientHandler
            {
                UseDefaultCredentials = true,
                UseProxy = true
            };
            
            using (var client = new HttpClient(httpClientHandler))
            {
                client.DefaultRequestHeaders.Add("api-key", apiKey);
                client.Timeout = TimeSpan.FromSeconds(30);
                
                var apiUrl = $"{endpoint.TrimEnd('/')}/openai/deployments/{deploymentName}/chat/completions?api-version=2023-05-15";
                Console.WriteLine($"Testing URL: {apiUrl}");
                
                var requestObj = new
                {
                    messages = new[]
                    {
                        new { role = "user", content = "Say hello!" }
                    },
                    max_tokens = 10
                };
                
                var jsonContent = JsonSerializer.Serialize(requestObj);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                
                Console.WriteLine("Sending test request...");
                
                var response = await client.PostAsync(apiUrl, content);
                var responseBody = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("✅ SUCCESS: Azure OpenAI connection working!");
                    Console.WriteLine($"Response: {responseBody}");
                }
                else
                {
                    Console.WriteLine($"❌ FAILED: {response.StatusCode}");
                    Console.WriteLine($"Response: {responseBody}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ ERROR: {ex.Message}");
        }
    }
}
