using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json.Linq;

namespace SnapAnalyser
{
    public enum SnapCategory
    {
        Database,
        Transformation,
        FlowControl,
        FileOperation,
        ExternalSystem,
        ErrorHandling,
        Other
    }

    public class SnapNode
    {
        public string Label { get; set; }
        public SnapCategory Category { get; set; }
        public Dictionary<string, string> Properties { get; set; } = new Dictionary<string, string>();
    }

    class TestDatabaseConnection
    {
        static void Main()
        {
            var snap = new SnapNode
            {
                Label = "Update NoOfBedrooms to Zero",
                Category = SnapCategory.Database,
                Properties = new Dictionary<string, string>
                {
                    {"account", "false"},
                    {"database", "PropertyDB"},
                    {"sqlserver", "true"},
                    {"execute", "UPDATE Properties SET NoOfBedrooms = 0"},
                    {"sql_statement", "UPDATE Properties SET NoOfBedrooms = 0"}
                }
            };

            string result = GetDatabaseConnectionInfo(snap);
            Console.WriteLine($"Result: '{result}'");
        }

        private static string GetDatabaseConnectionInfo(SnapNode snap)
        {
            if (snap.Category != SnapCategory.Database)
                return null;

            Console.WriteLine($"[DB-CONNECTION] Checking connection info for snap: {snap.Label}, Properties count: {snap.Properties.Count}");
            
            // Log all properties for debugging
            foreach (var prop in snap.Properties)
            {
                Console.WriteLine($"[DB-CONNECTION] Property: {prop.Key} = {prop.Value}");
            }

            // Look for account/connection properties, but filter out boolean values
            var connectionProps = snap.Properties.Where(p =>
                (p.Key.ToLower().Contains("account") ||
                p.Key.ToLower().Contains("connect") ||
                p.Key.ToLower().Contains("datasource") ||
                p.Key.ToLower().Contains("database") ||
                p.Key.ToLower().Contains("server") ||
                p.Key.ToLower().Contains("sqlserver") ||
                p.Key.ToLower().Contains("sql_server") ||
                p.Key.ToLower().Contains("dbaccount") ||
                p.Key.ToLower().Contains("db_account") ||
                p.Key.ToLower().Contains("connectionstring") ||
                p.Key.ToLower().Contains("connection_string") ||
                p.Key.ToLower().Contains("host") ||
                p.Key.ToLower().Contains("instance") ||
                p.Key.ToLower().Contains("servername") ||
                p.Key.ToLower().Contains("server_name")) &&
                !string.IsNullOrEmpty(p.Value) &&
                !p.Value.Equals("true", StringComparison.OrdinalIgnoreCase) &&
                !p.Value.Equals("false", StringComparison.OrdinalIgnoreCase) &&
                !bool.TryParse(p.Value, out _))
                .ToList();

            Console.WriteLine($"[DB-CONNECTION] Found {connectionProps.Count} connection properties after filtering");

            foreach (var prop in connectionProps)
            {
                Console.WriteLine($"[DB-CONNECTION] Found valid connection property: {prop.Key} = {prop.Value}");
                
                // Clean up the account name for display
                string accountName = prop.Value;
                
                // Remove common prefixes/suffixes that might not be user-friendly
                if (accountName.StartsWith("$") && accountName.Contains("."))
                {
                    // Handle parameter references like "$account.database_account"
                    accountName = accountName.Substring(accountName.LastIndexOf('.') + 1);
                }
                
                return accountName;
            }

            Console.WriteLine($"[DB-CONNECTION] No connection info found for snap: {snap.Label}");
            return null;
        }
    }
}
