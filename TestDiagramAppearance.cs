// Test script to verify diagram appearance fixes
// This script will run Snap-Documenter to generate documentation for a few pipelines
// and verify the appearance of flow diagrams

using System;
using System.IO;
using System.Threading;
using System.Text;
using SnapAnalyser;

class TestDiagramAppearance
{
    static void Main(string[] args)
    {
        Console.WriteLine("Diagram Appearance Test");
        Console.WriteLine("======================");
        
        // Test files with different diagram types
        string[] testFiles = Directory.GetFiles(
            Path.Combine(Environment.CurrentDirectory, "test_pipelines"), 
            "*.slp",
            SearchOption.AllDirectories);
        
        if (testFiles.Length == 0)
        {
            Console.WriteLine("No test files found. Please ensure test_pipelines directory exists with .slp files.");
            return;
        }
        
        var diagramGenerator = new DiagramGenerator();
        var flowControlDiagramGenerator = new FlowControlDiagramGenerator();
        var docGenerator = new DocumentationGenerator();
        
        string outputDir = Path.Combine(Environment.CurrentDirectory, "test_output");
        Directory.CreateDirectory(outputDir);
        
        // Create a test pipeline with router and join blocks
        var testPipeline = CreateTestPipeline();
        GenerateTestDocumentation(diagramGenerator, flowControlDiagramGenerator, docGenerator, testPipeline, outputDir);
        
        foreach (var file in testFiles)
        {
            try
            {
                Console.WriteLine($"Testing file: {Path.GetFileName(file)}");
                
                // Parse the pipeline
                var snapLogicClient = new SnapLogicClient();
                var pipeline = snapLogicClient.ParsePipelineFile(file);
                
                // Generate the main diagram
                string diagramSvg = diagramGenerator.GenerateDiagram(pipeline);
                
                // Generate documentation with diagrams
                string htmlDoc = docGenerator.GenerateHtmlDocumentation(pipeline, diagramSvg);
                
                // Save the output
                string outputFile = Path.Combine(outputDir, Path.GetFileNameWithoutExtension(file) + ".html");
                File.WriteAllText(outputFile, htmlDoc);
                
                Console.WriteLine($"  Generated documentation saved to: {outputFile}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  Error processing {Path.GetFileName(file)}: {ex.Message}");
            }
        }
        
        Console.WriteLine("\nTest completed. Please check the HTML files in the test_output directory");
        Console.WriteLine("to verify that diagrams are properly centered and have the correct background colors.");
    }
    
    static PipelineData CreateTestPipeline()
    {
        // Create a test pipeline with router and join blocks to specifically test color rendering
        var pipeline = new PipelineData
        {
            Name = "Test Pipeline with Flow Control Blocks",
            Author = "Test Author",
            Snaps = new List<SnapNode>(),
            Links = new List<SnapLink>()
        };
        
        // Create a few nodes of different types
        var sourceNode = new SnapNode
        {
            Id = "source1",
            Label = "Source Data",
            Type = "File Reader",
            Category = SnapCategory.FileOperation,
            IsStartPoint = true
        };
        
        var joinNode = new SnapNode
        {
            Id = "join1",
            Label = "Join Data",
            Type = "Join",
            Category = SnapCategory.FlowControl
        };
        
        var routerNode = new SnapNode
        {
            Id = "router1",
            Label = "Route Data",
            Type = "Router",
            Category = SnapCategory.FlowControl
        };
        
        var transformNode = new SnapNode
        {
            Id = "transform1",
            Label = "Transform Data",
            Type = "Mapper",
            Category = SnapCategory.Transformation
        };
        
        var databaseNode = new SnapNode
        {
            Id = "db1",
            Label = "Store Data",
            Type = "Database Writer",
            Category = SnapCategory.Database
        };
        
        // Add nodes to the pipeline
        pipeline.Snaps.Add(sourceNode);
        pipeline.Snaps.Add(joinNode);
        pipeline.Snaps.Add(routerNode);
        pipeline.Snaps.Add(transformNode);
        pipeline.Snaps.Add(databaseNode);
        
        // Add links between nodes
        pipeline.Links.Add(new SnapLink { Id = "link1", SourceId = sourceNode.Id, TargetId = joinNode.Id });
        pipeline.Links.Add(new SnapLink { Id = "link2", SourceId = joinNode.Id, TargetId = routerNode.Id });
        pipeline.Links.Add(new SnapLink { Id = "link3", SourceId = routerNode.Id, TargetId = transformNode.Id });
        pipeline.Links.Add(new SnapLink { Id = "link4", SourceId = transformNode.Id, TargetId = databaseNode.Id });
        
        return pipeline;
    }
    
    static void GenerateTestDocumentation(DiagramGenerator diagramGenerator, FlowControlDiagramGenerator flowControlDiagramGenerator, DocumentationGenerator docGenerator, PipelineData pipeline, string outputDir)
    {
        Console.WriteLine("Generating test documentation with flow control blocks...");
        
        // Generate diagrams
        string mainDiagram = diagramGenerator.GenerateDiagram(pipeline);
        
        // Create HTML documentation
        string htmlDoc = docGenerator.GenerateHtmlDocumentation(pipeline, mainDiagram);
        
        // Save the test file
        string outputFile = Path.Combine(outputDir, "test_flow_control_colors.html");
        File.WriteAllText(outputFile, htmlDoc);
        
        Console.WriteLine($"  Test documentation saved to: {outputFile}");
        
        // Create a standalone SVG test file for direct inspection
        StringBuilder svgTest = new StringBuilder();
        svgTest.AppendLine("<!DOCTYPE html>");
        svgTest.AppendLine("<html>");
        svgTest.AppendLine("<head>");
        svgTest.AppendLine("  <title>Flow Control Block Color Test</title>");
        svgTest.AppendLine("</head>");
        svgTest.AppendLine("<body>");
        svgTest.AppendLine("  <h1>Flow Control Block Color Test</h1>");
        svgTest.AppendLine("  <div style=\"width: 100%; text-align: center;\">");
        svgTest.AppendLine(mainDiagram);
        svgTest.AppendLine("  </div>");
        svgTest.AppendLine("</body>");
        svgTest.AppendLine("</html>");
        
        string svgTestFile = Path.Combine(outputDir, "svg_color_test.html");
        File.WriteAllText(svgTestFile, svgTest.ToString());
        
        Console.WriteLine($"  SVG color test saved to: {svgTestFile}");
    }
}
