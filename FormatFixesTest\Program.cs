using System;
using System.Text.RegularExpressions;

namespace FormatFixesTest
{
    public class Program
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("Running Format Fixes Tests");
            Console.WriteLine("=========================\n");
            
            TestInlineCodeProcessing();
            TestHeadingConversion();
        }

        private static void TestInlineCodeProcessing()
        {
            Console.WriteLine("TEST 1: HTML-formatted Inline Code Processing");
            Console.WriteLine("------------------------------------------");
            
            // Example AI response with inline code placeholders
            string aiResponse = "Example with simple placeholder: INLINE_CODE_0\n" +
                "Example with formatted placeholder: INLINE<em>CODE</em>1\n" +
                "Example with spaces: INLINE CODE 2";
                
            Console.WriteLine("Original response:");
            Console.WriteLine(aiResponse);
            Console.WriteLine();
                
            // Process using the same regex pattern as in AIDescriptionGenerator.ProcessAIResponse
            string processed = ProcessAIResponse(aiResponse);
            
            Console.WriteLine("Processed response:");
            Console.WriteLine(processed);
            Console.WriteLine();
            
            // Check the results
            bool success1 = processed.Contains("<code>code_placeholder_0</code>");
            bool success2 = processed.Contains("<code>code_placeholder_1</code>");
            bool success3 = processed.Contains("<code>code_placeholder_2</code>");
            
            Console.WriteLine("Fix simple placeholder: " + (success1 ? "SUCCESS ✓" : "FAILED ✗"));
            Console.WriteLine("Fix formatted placeholder: " + (success2 ? "SUCCESS ✓" : "FAILED ✗"));
            Console.WriteLine("Fix spaced placeholder: " + (success3 ? "SUCCESS ✓" : "FAILED ✗"));
            Console.WriteLine();
        }
        
        private static void TestHeadingConversion()
        {
            Console.WriteLine("TEST 2: HTML-formatted Headings Conversion");
            Console.WriteLine("----------------------------------------");
            
            // Example markdown with various heading levels
            string markdown = "# Heading 1\n\n" +
                "## Heading 2\n\n" +
                "### Heading 3\n\n" +
                "#### Heading 4\n\n" +
                "##### Heading 5\n\n" +
                "###### Heading 6";
            
            Console.WriteLine("Original markdown:");
            Console.WriteLine(markdown);
            Console.WriteLine();
            
            // Convert headings using the same regex pattern as in DocumentationGenerator.ConvertMarkdownToHtml
            string html = ConvertHeadings(markdown);
            
            Console.WriteLine("Converted HTML:");
            Console.WriteLine(html);
            Console.WriteLine();
            
            // Check the results
            bool success1 = html.Contains("<h3>Heading 1</h3>");
            bool success2 = html.Contains("<h4>Heading 2</h4>");
            bool success3 = html.Contains("<h5>Heading 3</h5>");
            bool success4 = html.Contains("<h6>Heading 4</h6>");
            bool success5 = html.Contains("<h6>Heading 5</h6>");
            bool success6 = html.Contains("<h6>Heading 6</h6>");
            
            Console.WriteLine("Convert h1: " + (success1 ? "SUCCESS ✓" : "FAILED ✗"));
            Console.WriteLine("Convert h2: " + (success2 ? "SUCCESS ✓" : "FAILED ✗"));
            Console.WriteLine("Convert h3: " + (success3 ? "SUCCESS ✓" : "FAILED ✗"));
            Console.WriteLine("Convert h4: " + (success4 ? "SUCCESS ✓" : "FAILED ✗"));
            Console.WriteLine("Convert h5: " + (success5 ? "SUCCESS ✓" : "FAILED ✗"));
            Console.WriteLine("Convert h6: " + (success6 ? "SUCCESS ✓" : "FAILED ✗"));
            Console.WriteLine();
        }
        
        /// <summary>
        /// Process AI response - identical to AIDescriptionGenerator.ProcessAIResponse method
        /// </summary>
        private static string ProcessAIResponse(string response)
        {
            if (string.IsNullOrEmpty(response))
                return response;
                
            // Fix inline code placeholders from the AI response (INLINE_CODE_0 -> <code>code</code>)
            // Pattern: INLINE_CODE_X where X is a number
            response = Regex.Replace(response, @"INLINE_CODE_(\d+)", match =>
            {
                // Get the number from the placeholder
                if (int.TryParse(match.Groups[1].Value, out int codeIndex))
                {
                    // Replace with HTML code tag
                    return $"<code>code_placeholder_{codeIndex}</code>";
                }
                return match.Value; // Keep original if parsing fails
            });
            
            // Also detect and fix cases where HTML formatting is mixed in, like INLINE<em>CODE</em>0
            response = Regex.Replace(response, @"INLINE\s*(?:<[^>]+>)*\s*CODE\s*(?:<[^>]+>)*\s*(\d+)", match =>
            {
                return $"<code>code_placeholder_{match.Groups[1].Value}</code>";
            });
            
            return response;
        }
        
        /// <summary>
        /// Convert Markdown headings to HTML - identical to DocumentationGenerator.ConvertMarkdownToHtml method
        /// </summary>
        private static string ConvertHeadings(string markdown)
        {
            // Split into paragraphs
            var paragraphs = Regex.Split(markdown, @"\r\n\r\n|\n\n");
            
            // Process each paragraph
            for (int i = 0; i < paragraphs.Length; i++)
            {
                var paragraph = paragraphs[i];
                
                // Check if paragraph starts with heading markers
                if (Regex.IsMatch(paragraph, @"^#{1,6} "))
                {
                    // Convert h1 to h3
                    paragraph = Regex.Replace(paragraph, @"^# (.+?)$", "<h3>$1</h3>", RegexOptions.Multiline);
                    
                    // Convert h2 to h4
                    paragraph = Regex.Replace(paragraph, @"^## (.+?)$", "<h4>$1</h4>", RegexOptions.Multiline);
                    
                    // Convert h3 to h5
                    paragraph = Regex.Replace(paragraph, @"^### (.+?)$", "<h5>$1</h5>", RegexOptions.Multiline);
                    
                    // Convert h4-h6 to h6
                    paragraph = Regex.Replace(paragraph, @"^#{4,6} (.+?)$", "<h6>$1</h6>", RegexOptions.Multiline);
                }
                
                paragraphs[i] = paragraph;
            }
            
            // Join paragraphs back together
            return string.Join("\n\n", paragraphs);
        }
    }
}