using System;
using System.IO;
using System.Threading.Tasks;

class AzureConnectionTest
{
    static async Task Main(string[] args)
    {
        try
        {
            Console.WriteLine("Azure OpenAI Connection Test");
            Console.WriteLine("============================");
            
            // Read config
            var configPath = "config.json";
            if (!File.Exists(configPath))
            {
                Console.WriteLine($"Config file not found: {configPath}");
                return;
            }

            var config = ConfigManager.LoadConfig();
            Console.WriteLine($"Endpoint: {config.AzureOpenAIEndpoint}");
            Console.WriteLine($"Deployment: {config.AzureOpenAIDeploymentName}");
            Console.WriteLine($"API Key: {(string.IsNullOrEmpty(config.OpenAIApiKey) ? "Not set" : "Set (hidden)")}");
            Console.WriteLine();

            // Test connection using existing AzureOpenAITester
            var tester = new AzureOpenAITester();
            
            Console.WriteLine("Testing connection...");
            var isConnected = await tester.TestConnectionAsync(
                config.AzureOpenAIEndpoint,
                config.OpenAIApiKey,
                config.AzureOpenAIDeploymentName,
                config.AzureOpenAITimeoutSeconds
            );

            if (isConnected)
            {
                Console.WriteLine("✓ Connection successful!");
                
                // Try to get deployment info
                Console.WriteLine("\nGetting deployment information...");
                var deploymentInfo = await tester.GetDeploymentInfo(
                    config.AzureOpenAIEndpoint,
                    config.OpenAIApiKey
                );
                
                if (!string.IsNullOrEmpty(deploymentInfo))
                {
                    Console.WriteLine("Available deployments:");
                    Console.WriteLine(deploymentInfo);
                }
                else
                {
                    Console.WriteLine("Could not retrieve deployment information.");
                }
            }
            else
            {
                Console.WriteLine("✗ Connection failed!");
                Console.WriteLine("The deployment name 'gpt-4' may not exist in your Azure OpenAI resource.");
                Console.WriteLine("\nTrying other common deployment names...");
                
                string[] commonDeployments = { "gpt-4-turbo", "gpt-35-turbo", "gpt-4o", "text-davinci-003" };
                
                foreach (var deployment in commonDeployments)
                {
                    Console.WriteLine($"\nTesting deployment: {deployment}");
                    var testResult = await tester.TestConnectionAsync(
                        config.AzureOpenAIEndpoint,
                        config.OpenAIApiKey,
                        deployment,
                        config.AzureOpenAITimeoutSeconds
                    );
                    
                    if (testResult)
                    {
                        Console.WriteLine($"✓ Success with deployment: {deployment}");
                        Console.WriteLine($"Consider updating config.json to use: {deployment}");
                        break;
                    }
                    else
                    {
                        Console.WriteLine($"✗ Failed with deployment: {deployment}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        
        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }
}
