using System;
using System.IO;
using System.Threading.Tasks;

namespace SnapAnalyser
{
    public class TestJoinFinal
    {
        public static async Task Main(string[] args)
        {
            try
            {
                Console.WriteLine("=== Testing Join Configuration Display ===");
                
                string pipelineFile = "CH03 - Update Kypera Property Characteristics_2025_05_16.slp";
                
                if (!File.Exists(pipelineFile))
                {
                    Console.WriteLine($"Pipeline file not found: {pipelineFile}");
                    return;
                }
                
                Console.WriteLine($"Loading pipeline: {pipelineFile}");
                
                // Initialize components
                var analyzer = new PipelineAnalyzer();
                var diagramGenerator = new DiagramGenerator();
                var docGenerator = new DocumentationGenerator();
                
                // Load and analyze pipeline
                string fileContent = File.ReadAllText(pipelineFile);
                var pipelineData = analyzer.AnalyzePipeline(fileContent);
                Console.WriteLine($"Pipeline loaded: {pipelineData.Name}");
                Console.WriteLine($"Snaps found: {pipelineData.Snaps.Count}");
                
                // Generate diagram
                string diagramSvg = diagramGenerator.GenerateDiagram(pipelineData);
                Console.WriteLine("Diagram generated");
                
                // Generate HTML documentation
                Console.WriteLine("Generating HTML documentation...");
                string htmlDoc = await docGenerator.GenerateHtmlDocumentationAsync(pipelineData, diagramSvg);
                
                // Save to file
                string outputFile = "join_final_test.html";
                File.WriteAllText(outputFile, htmlDoc);
                
                Console.WriteLine($"✅ SUCCESS: HTML documentation saved to {outputFile}");
                Console.WriteLine($"File size: {new FileInfo(outputFile).Length} bytes");
                
                // Check if Join configuration is in the HTML
                if (htmlDoc.Contains("Join Configuration"))
                {
                    Console.WriteLine("✅ Join Configuration sections found in HTML!");
                }
                else
                {
                    Console.WriteLine("❌ Join Configuration sections NOT found in HTML");
                }
                
                if (htmlDoc.Contains("Join Type:"))
                {
                    Console.WriteLine("✅ Join Type information found!");
                }
                
                if (htmlDoc.Contains("Join Conditions"))
                {
                    Console.WriteLine("✅ Join Conditions found!");
                }
                
                if (htmlDoc.Contains("Data Sources Being Joined"))
                {
                    Console.WriteLine("✅ Data Sources table found!");
                }
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
