# SnapAnalyzer

A Windows application for analyzing SnapLogic SLP files and generating comprehensive documentation with data flow diagrams.

## Features

- Parse and analyze SnapLogic Pipeline (SLP) files
- Generate visual flow diagrams showing pipeline structure
- Create detailed HTML documentation
- Export documentation to PDF format
- Identify and categorize different types of snaps
- Analyze data flow paths through the pipeline
- Generate AI-enhanced descriptions for pipeline components
- Cache AI-generated descriptions for better performance
- Smart handling of basic snap types with optimized descriptions
- Convert markdown-formatted AI descriptions to properly formatted HTML

## Requirements

- Windows operating system
- .NET 6.0 Runtime
- Sufficient permissions to read/write to the file system

## Installation

1. Download the latest release from the Releases section
2. Extract the ZIP file to a location of your choice
3. Run `SnapAnalyzer.exe` to start the application

## Usage

1. Launch the application
2. Click "Browse..." to select a SnapLogic SLP file
3. Choose an output folder for the documentation
4. Select the desired output format(s) (HTML and/or PDF)
5. Configure additional options:
   - Enable AI for pipeline overview (uses Azure OpenAI if configured)
   - Toggle "Use cached AI descriptions" to control whether the tool uses previously generated descriptions (faster) or generates fresh ones each time

### Caching System

The application now includes an intelligent caching system for AI-generated descriptions:
- When enabled (default), the system stores and reuses AI-generated descriptions for similar snap types
- Caching significantly improves performance and reduces API usage when generating multiple documents
- The cache is persisted between application sessions in the local application data folder
- Basic snap types (copy, union, exit) always use optimized built-in descriptions
- Pseudocode for snaps is always freshly generated by AI and never cached to ensure the most accurate representation
5. Click "Analyze" to process the file
6. Once processing is complete, open the output folder to view the generated documentation

## Documentation Format

The generated documentation includes:

- Pipeline overview and metadata
- Interactive data flow diagram
- Pipeline parameters
- Categorized snap details
- Execution flow paths

## Building from Source

To build the application from source:

1. Clone this repository
2. Open the solution in Visual Studio 2022 or later
3. Restore NuGet packages
4. Build the solution

## Dependencies

- Newtonsoft.Json - For parsing SLP files
- Select.HtmlToPdf.NetCore - For generating PDF documentation

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- SnapLogic for their integration platform
- Select.HtmlToPdf for the PDF generation library