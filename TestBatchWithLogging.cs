using System;
using System.IO;
using System.Threading.Tasks;
using System.Threading;

namespace SnapAnalyser
{
    public class TestBatchWithLogging
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("Testing batch processing with enhanced logging...");
            
            try
            {
                // Initialize components
                var analyzer = new SlpAnalyzer();
                var diagramGenerator = new DiagramGenerator();
                
                // Set up AI generator with logging
                var aiGenerator = new AIDescriptionGenerator(null);
                aiGenerator.UseCachedDescriptions = true;
                aiGenerator.EnableLogging(@"C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Generated Documentation");
                
                // Create DocumentationGenerator with logging
                var docGenerator = new DocumentationGenerator(aiGenerator);
                docGenerator.EnableLogging(@"C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Generated Documentation\test_doc_generation_log.log");
                
                // Test file that was known to hang
                string testFilePath = @"C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Snap-Pipelines\CH03.slp";
                
                Console.WriteLine($"Testing file: {testFilePath}");
                
                if (!File.Exists(testFilePath))
                {
                    Console.WriteLine("Test file not found. Please check the path.");
                    return;
                }
                
                // Read and analyze the file
                Console.WriteLine("Reading file...");
                string fileContent = await File.ReadAllTextAsync(testFilePath);
                
                Console.WriteLine("Analyzing pipeline...");
                var pipelineData = analyzer.AnalyzePipeline(fileContent);
                
                Console.WriteLine("Generating diagram...");
                string diagramSvg = diagramGenerator.GenerateDiagram(pipelineData);
                
                Console.WriteLine("Starting HTML generation with AI...");
                using (var cts = new CancellationTokenSource())
                {
                    cts.CancelAfter(TimeSpan.FromMinutes(3)); // 3 minute timeout
                    
                    try
                    {
                        string htmlDoc = await docGenerator.GenerateHtmlDocumentationAsync(pipelineData, diagramSvg, cts.Token);
                        Console.WriteLine("HTML generation completed successfully!");
                        
                        // Save the result
                        string outputPath = @"C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Generated Documentation\test_output.html";
                        await File.WriteAllTextAsync(outputPath, htmlDoc);
                        Console.WriteLine($"Output saved to: {outputPath}");
                    }
                    catch (OperationCanceledException)
                    {
                        Console.WriteLine("HTML generation was cancelled due to timeout.");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error during HTML generation: {ex.Message}");
                        Console.WriteLine($"Full exception: {ex}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in test: {ex.Message}");
                Console.WriteLine($"Full exception: {ex}");
            }
            
            Console.WriteLine("Test completed. Press any key to exit...");
            Console.ReadKey();
        }
    }
}
