# Azure OpenAI Deployment Checker
# This script helps you find the correct deployment names for your Azure OpenAI resource

Write-Host "=== Azure OpenAI Deployment Checker ===" -ForegroundColor Green
Write-Host ""

# Configuration from your config.json
$endpoint = "https://openai-snaplogic-documenter.openai.azure.com/"
$apiKey = "6dr7izA3Iuc1qhTiaKMZWnx1iHX0yI0MDx4Hhj1VWkSLHQ7CCPD7JQQJ99BEACmepeSXJ3w3AAABACOG0Qbs"

Write-Host "Current Configuration:" -ForegroundColor Yellow
Write-Host "  Endpoint: $endpoint"
Write-Host "  API Key: Present (hidden)"
Write-Host ""

Write-Host "Attempting to list deployments..." -ForegroundColor Cyan

try {
    # Azure OpenAI API endpoint for listing deployments
    $deploymentsUrl = "$($endpoint.TrimEnd('/'))/openai/deployments?api-version=2023-05-15"
    
    Write-Host "Requesting: $deploymentsUrl" -ForegroundColor Gray
    
    $headers = @{
        "api-key" = $apiKey
        "Content-Type" = "application/json"
    }
    
    $response = Invoke-RestMethod -Uri $deploymentsUrl -Headers $headers -Method GET
    
    Write-Host "✓ Successfully retrieved deployment information!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Available Deployments:" -ForegroundColor Yellow
    Write-Host "======================" -ForegroundColor Yellow
    
    if ($response.data -and $response.data.Count -gt 0) {
        foreach ($deployment in $response.data) {
            Write-Host "• Deployment Name: $($deployment.id)" -ForegroundColor White
            Write-Host "  Model: $($deployment.model)" -ForegroundColor Gray
            if ($deployment.status) {
                Write-Host "  Status: $($deployment.status)" -ForegroundColor Gray
            }
            Write-Host ""
        }
        
        Write-Host "NEXT STEPS:" -ForegroundColor Green
        Write-Host "1. Choose one of the deployment names above" -ForegroundColor White
        Write-Host "2. Update the 'AzureOpenAIDeploymentName' in config.json" -ForegroundColor White
        Write-Host "3. Test the connection again" -ForegroundColor White
        
    } else {
        Write-Host "No deployments found in the response." -ForegroundColor Red
        Write-Host "Raw response: $($response | ConvertTo-Json -Depth 3)" -ForegroundColor Gray
    }
    
} catch {
    Write-Host "❌ Error retrieving deployments:" -ForegroundColor Red
    Write-Host "  $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "  Status Code: $statusCode" -ForegroundColor Red
        
        if ($statusCode -eq 401) {
            Write-Host "  → This suggests an authentication issue. Please verify your API key." -ForegroundColor Yellow
        } elseif ($statusCode -eq 403) {
            Write-Host "  → This suggests a permissions issue. Ensure your API key has the necessary permissions." -ForegroundColor Yellow
        } elseif ($statusCode -eq 404) {
            Write-Host "  → This suggests the endpoint URL may be incorrect." -ForegroundColor Yellow
        }
    }
    
    Write-Host ""
    Write-Host "ALTERNATIVE METHODS:" -ForegroundColor Cyan
    Write-Host "1. Use the Azure Portal:" -ForegroundColor White
    Write-Host "   - Go to your Azure OpenAI resource" -ForegroundColor Gray
    Write-Host "   - Navigate to 'Deployments' section" -ForegroundColor Gray
    Write-Host "   - Note the deployment names listed there" -ForegroundColor Gray
    Write-Host ""
    Write-Host "2. Use Azure CLI:" -ForegroundColor White
    Write-Host "   az cognitiveservices account deployment list --name 'openai-snaplogic-documenter' --resource-group 'your-resource-group'" -ForegroundColor Gray
}

Write-Host ""
Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
