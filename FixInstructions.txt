INSTRUCTIONS TO FIX THE ISSUES

1. FIX FOR HEADING FORMATTING ISSUE:
------------------------------------
In DocumentationGenerator.cs, the regex pattern for headings only handles headings up to 3 hashtags, but not 4 or more hashtags.

CHANGE THIS:
```csharp
// Handle headings - must be done at the start of a paragraph
if (Regex.IsMatch(paragraph, @"^#{1,3} "))
{
    // h1-h3 get mapped to h3-h5 to maintain hierarchy in the document
    paragraph = Regex.Replace(paragraph, @"^# (.+?)$", "<h3>$1</h3>", RegexOptions.Multiline);
    paragraph = Regex.Replace(paragraph, @"^## (.+?)$", "<h4>$1</h4>", RegexOptions.Multiline);
    paragraph = Regex.Replace(paragraph, @"^### (.+?)$", "<h5>$1</h5>", RegexOptions.Multiline);
}
```

TO THIS:
```csharp
// Handle headings - must be done at the start of a paragraph
if (Regex.IsMatch(paragraph, @"^#{1,6} "))
{
    // h1-h3 get mapped to h3-h5 to maintain hierarchy in the document
    paragraph = Regex.Replace(paragraph, @"^# (.+?)$", "<h3>$1</h3>", RegexOptions.Multiline);
    paragraph = Regex.Replace(paragraph, @"^## (.+?)$", "<h4>$1</h4>", RegexOptions.Multiline);
    paragraph = Regex.Replace(paragraph, @"^### (.+?)$", "<h5>$1</h5>", RegexOptions.Multiline);
    paragraph = Regex.Replace(paragraph, @"^#{4,6} (.+?)$", "<h6>$1</h6>", RegexOptions.Multiline);
}
```

The changes are:
1. Change the pattern check from `^#{1,3} ` to `^#{1,6} ` to match headings with up to 6 hashtags
2. Add a new regex replace for `^#{4,6} (.+?)$` that maps all headings with 4-6 hashtags to <h6> tags

2. FIX FOR INLINE CODE PLACEHOLDERS:
------------------------------------
In AIDescriptionGenerator.cs:

1. Add the missing using directive:
```csharp
using System.Text.RegularExpressions;
```

2. Add a new method to process and fix the inline code placeholders:
```csharp
// Process the AI response to fix common formatting issues
private string ProcessAIResponse(string response)
{
    if (string.IsNullOrEmpty(response))
        return response;
        
    // Fix inline code placeholders from the AI response (INLINE_CODE_0 -> `code`)
    // Pattern: INLINE_CODE_X where X is a number
    response = Regex.Replace(response, @"INLINE_CODE_(\d+)", match =>
    {
        // Get the number from the placeholder
        if (int.TryParse(match.Groups[1].Value, out int codeIndex))
        {
            // For now, we'll just replace it with backtick notation since we don't have the actual code
            return "`code_placeholder_" + codeIndex + "`";
        }
        return match.Value; // Keep original if parsing fails
    });
    
    // Also detect and fix cases where HTML formatting is mixed in, like INLINE<em>CODE</em>0
    response = Regex.Replace(response, @"INLINE\s*(?:<[^>]+>)*\s*CODE\s*(?:<[^>]+>)*\s*(\d+)", match =>
    {
        return "`code_placeholder_" + match.Groups[1].Value + "`";
    });
    
    return response;
}
```

3. Modify the code where contentElement.GetString() is returned in the SendPromptToAzureAI method:

CHANGE THIS:
```csharp
LogMessage("Successfully parsed API response with choices");
return contentElement.GetString();
```

TO THIS:
```csharp
LogMessage("Successfully parsed API response with choices");
string aiContent = contentElement.GetString();
// Process the response to fix any formatting issues
return ProcessAIResponse(aiContent);
```
