Checking for config file at: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
Read config file, content length: 857 characters
Config successfully loaded with values:
  OpenAIApiKey: Set (hidden)
  LastSlpFolderLocation: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Snap-Pipelines\PTR\Legionella\P03 - Legionella Unknown Attribute CRM_2025_07_03.slp
  LastOutputFolderLocation: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Generated Documentation
  SnapLogicUrl: https://cdn.elastic.snaplogic.com
  SnapLogicUsername: <EMAIL>
  SnapLogicPassword: Not set
  SnapLogicOrgId: 5fd9a86c47060ece477be9b6
Successfully loaded config from: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
Attempting to save config to the following locations:
  C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
  C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
  C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\config.json
  C:\Users\<USER>\AppData\Roaming\SnapAnalyser\config.json
Successfully saved config to: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
Checking for config file at: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
Read config file, content length: 857 characters
Config successfully loaded with values:
  OpenAIApiKey: Set (hidden)
  LastSlpFolderLocation: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Snap-Pipelines\PTR\Legionella\P03 - Legionella Unknown Attribute CRM_2025_07_03.slp
  LastOutputFolderLocation: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Generated Documentation
  SnapLogicUrl: https://cdn.elastic.snaplogic.com
  SnapLogicUsername: <EMAIL>
  SnapLogicPassword: Not set
  SnapLogicOrgId: 5fd9a86c47060ece477be9b6
Successfully loaded config from: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
Initializing logging...
Base directory: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\
Solution directory: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin
Using documentation directory: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Generated Documentation
Documentation directory already exists
Attempting to write to: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Generated Documentation\SnapDocumenter_MapperDebug.log
Successfully initialized logging to: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Generated Documentation\SnapDocumenter_MapperDebug.log
Log location written to: C:\Users\<USER>\OneDrive - Newport City Homes\Desktop\SnapDocumenter_LogLocation.txt
Checking for config file at: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
Read config file, content length: 857 characters
Config successfully loaded with values:
  OpenAIApiKey: Set (hidden)
  LastSlpFolderLocation: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Snap-Pipelines\PTR\Legionella\P03 - Legionella Unknown Attribute CRM_2025_07_03.slp
  LastOutputFolderLocation: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Generated Documentation
  SnapLogicUrl: https://cdn.elastic.snaplogic.com
  SnapLogicUsername: <EMAIL>
  SnapLogicPassword: Not set
  SnapLogicOrgId: 5fd9a86c47060ece477be9b6
Successfully loaded config from: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
Attempting to save config to the following locations:
  C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
  C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
  C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\config.json
  C:\Users\<USER>\AppData\Roaming\SnapAnalyser\config.json
Successfully saved config to: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
Extracting transformation properties from prefix: settings.transformations
Processing property: settings.transformations.mappingRoot
  Simple value: settings.transformations.mappingRoot.value = $
Processing property: settings.transformations.mappingTable
  Complex value at settings.transformations.mappingTable.value (length: 556)
  Found mappingTable, doing deeper extraction
Processing mapper expressions from settings.transformations.mappingTable, token type: Object
Processing mapping table entry 0
Found mapping: $nch_propertytechnicalrecordsid = $nch_propertytechnicalrecordsid
Processing mapping table entry 1
Found mapping: $nch_legionellainspectionrequired = 803030000
Processing mapping table entry 2
Found mapping: $nch_legionellainspectiondate = '01/01/1753 00:00:00'
  Found mapping pair: {
  "value": "$nch_propertytechnicalrecordsid"
} = {
  "value": "$nch_propertytechnicalrecordsid",
  "expression": true
}
  Found mapping pair: {
  "value": "$nch_legionellainspectionrequired"
} = {
  "value": "803030000",
  "expression": true
}
  Found mapping pair: {
  "value": "$nch_legionellainspectiondate"
} = {
  "value": "'01/01/1753 00:00:00'",
  "expression": true
}
Extracting transformation properties from prefix: settings.transformations
Processing property: settings.transformations.mappingRoot
  Simple value: settings.transformations.mappingRoot.value = $
Processing property: settings.transformations.mappingTable
  Complex value at settings.transformations.mappingTable.value (length: 404)
  Found mappingTable, doing deeper extraction
Processing mapper expressions from settings.transformations.mappingTable, token type: Object
Processing mapping table entry 0
Found mapping: $nch_uprn = $nch_unitcode>=11000000 ? $nch_unitcode : $nch_unitcode-********
Processing mapping table entry 1
Found mapping: $nch_propertytechnicalrecordsid = $nch_propertytechnicalrecordsid
  Found mapping pair: {
  "value": "$nch_uprn"
} = {
  "expression": true,
  "value": "$nch_unitcode>=11000000 ? $nch_unitcode : $nch_unitcode-********"
}
  Found mapping pair: {
  "value": "$nch_propertytechnicalrecordsid"
} = {
  "value": "$nch_propertytechnicalrecordsid",
  "expression": true
}
[DEBUG] === SNAP CATEGORIZATION ===
[DEBUG] Processing 12 snaps for categorization
[DEBUG] Processing snap: 'Join on UPRN' (Type: 'com-snaplogic-snaps-transform-multijoin')
[DEBUG] CATEGORIZED as FlowControl: 'Join on UPRN' (Type: com-snaplogic-snaps-transform-multijoin)
[DEBUG] Processing snap: 'Get LEG Unknown Properties' (Type: 'com-snaplogic-snaps-sqlserver-execute')
[DEBUG] CATEGORIZED as Database: 'Get LEG Unknown Properties' (Type: com-snaplogic-snaps-sqlserver-execute)
[DEBUG] Processing snap: 'Map legionella inspection required' (Type: 'com-snaplogic-snaps-transform-datatransform')
[DEBUG] CATEGORIZED as Transformation: 'Map legionella inspection required' (Type: com-snaplogic-snaps-transform-datatransform)
[DEBUG] Processing snap: 'Update CRM PTR' (Type: 'com-snaplogic-snaps-dynamics365forsales-update')
[DEBUG] CATEGORIZED as Database: 'Update CRM PTR' (Type: com-snaplogic-snaps-dynamics365forsales-update)
[DEBUG] Processing snap: 'Get CRM PTRs' (Type: 'com-snaplogic-snaps-dynamics365forsales-search')
[DEBUG] CATEGORIZED as ExternalSystem: 'Get CRM PTRs' (Type: com-snaplogic-snaps-dynamics365forsales-search)
[DEBUG] Processing snap: 'Get CRM Properties' (Type: 'com-snaplogic-snaps-dynamics365forsales-read')
[DEBUG] CATEGORIZED as ExternalSystem: 'Get CRM Properties' (Type: com-snaplogic-snaps-dynamics365forsales-read)
[DEBUG] Processing snap: 'Join propertyid' (Type: 'com-snaplogic-snaps-transform-multijoin')
[DEBUG] CATEGORIZED as FlowControl: 'Join propertyid' (Type: com-snaplogic-snaps-transform-multijoin)
[DEBUG] Processing snap: 'Map propertytechnicalrecordsid' (Type: 'com-snaplogic-snaps-transform-datatransform')
[DEBUG] CATEGORIZED as Transformation: 'Map propertytechnicalrecordsid' (Type: com-snaplogic-snaps-transform-datatransform)
[DEBUG] Processing snap: 'Exit' (Type: 'com-snaplogic-snaps-flow-exit')
[DEBUG] CATEGORIZED as FlowControl: 'Exit' (Type: com-snaplogic-snaps-flow-exit)
[DEBUG] Processing snap: 'Save to NCHVINT01' (Type: 'com-snaplogic-snaps-binary-write')
[DEBUG] CATEGORIZED as FileOperation: 'Save to NCHVINT01' (Type: com-snaplogic-snaps-binary-write)
[DEBUG] Processing snap: 'Document to Binary' (Type: 'com-snaplogic-snaps-transform-documenttobinary')
[DEBUG] CATEGORIZED as Transformation: 'Document to Binary' (Type: com-snaplogic-snaps-transform-documenttobinary)
[DEBUG] Processing snap: 'Copy' (Type: 'com-snaplogic-snaps-flow-copy')
[DEBUG] CATEGORIZED as FlowControl: 'Copy' (Type: com-snaplogic-snaps-flow-copy)
[DEBUG] === END SNAP CATEGORIZATION ===
----- Azure OpenAI Configuration -----
API Key provided: True
API Key length: 84
Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
Deployment Name: gpt-4.1
Timeout: 30 seconds
Azure OpenAI will be used: True
Using cached descriptions: True
AIDescriptionGenerator initialized with Azure OpenAI
Using API key starting with: CSunqsaW...
?? STARTING Azure OpenAI connection test...
?? FINAL Azure AI status after connection test: True
?? Testing Azure OpenAI connection...
?? Testing connection to Azure OpenAI...
?? API Key length: 84
?? Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
?? Deployment: gpt-4.1
? All configuration values are present
?? Sending test request to Azure OpenAI...
Logging initialized
Initializing logging...
Base directory: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\
Solution directory: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin
Using documentation directory: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Generated Documentation
Documentation directory already exists
Attempting to write to: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Generated Documentation\SnapDocumenter_MapperDebug.log
[AI-REQ-311634d7] Starting Azure OpenAI request
[AI-REQ-311634d7] Prompt length: 42 characters
[AI-REQ-311634d7] CancellationToken: IsCancelled=False, CanBeCancelled=False
Successfully initialized logging to: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Generated Documentation\SnapDocumenter_MapperDebug.log
[AI-REQ-311634d7] Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
Log location written to: C:\Users\<USER>\OneDrive - Newport City Homes\Desktop\SnapDocumenter_LogLocation.txt
[AI-REQ-311634d7] Deployment: gpt-4.1
[AI-REQ-311634d7] HttpClient timeout: 00:05:00
[AI-REQ-311634d7] Request URI: https://openai-general-assistan-resource.cognitiveservices.azure.com//openai/deployments/gpt-4.1/chat/completions?api-version=2024-02-01
[AI-REQ-311634d7] Creating HTTP request message
[AI-REQ-311634d7] About to send HTTP request to Azure OpenAI
Resetting processed snap types for new pipeline documentation
[PIPELINE-DESC-3f986f8f] Starting pipeline description generation for 'P03 - Legionella Unknown Attribute CRM'
[PIPELINE-DESC-3f986f8f] Pipeline contains 12 snaps
[PIPELINE-DESC-3f986f8f] Using Azure AI: True
[PIPELINE-DESC-3f986f8f] Creating pipeline summary
[PIPELINE-DESC-3f986f8f] Pipeline summary created, length: 853 characters
[PIPELINE-DESC-3f986f8f] Sending to Azure OpenAI, prompt length: 1148 characters
[AI-REQ-5c7a2702] Starting Azure OpenAI request
[AI-REQ-5c7a2702] Prompt length: 1148 characters
[AI-REQ-5c7a2702] CancellationToken: IsCancelled=False, CanBeCancelled=False
[AI-REQ-5c7a2702] Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
[AI-REQ-5c7a2702] Deployment: gpt-4.1
[AI-REQ-5c7a2702] HttpClient timeout: 00:05:00
[AI-REQ-5c7a2702] Request URI: https://openai-general-assistan-resource.cognitiveservices.azure.com//openai/deployments/gpt-4.1/chat/completions?api-version=2024-02-01
[AI-REQ-5c7a2702] Creating HTTP request message
[AI-REQ-5c7a2702] About to send HTTP request to Azure OpenAI
[AI-REQ-311634d7] HTTP request completed in 444ms, Status: OK
[AI-REQ-311634d7] About to read response content
[AI-REQ-311634d7] Response content read in 1ms, Length: 1208 characters
[AI-REQ-311634d7] Parsing JSON response
[AI-REQ-311634d7] SUCCESS: Request completed in 472ms, Result length: 2 characters
?? Test connection response: 'ok'
?? Connection test result: SUCCESS
? Successfully connected to Azure OpenAI API
[AI-REQ-5c7a2702] HTTP request completed in 1708ms, Status: OK
[AI-REQ-5c7a2702] About to read response content
[AI-REQ-5c7a2702] Response content read in 0ms, Length: 1947 characters
[AI-REQ-5c7a2702] Parsing JSON response
[AI-REQ-5c7a2702] SUCCESS: Request completed in 1715ms, Result length: 735 characters
[PIPELINE-DESC-3f986f8f] SUCCESS: Azure OpenAI completed in 1722ms, result length: 735 characters
[DEBUG-FILTER] Total snaps in pipeline: 12
[DEBUG-FILTER] Snap 'Join on UPRN' has Category: FlowControl (Type: com-snaplogic-snaps-transform-multijoin)
[DEBUG-FILTER] Snap 'Get LEG Unknown Properties' has Category: Database (Type: com-snaplogic-snaps-sqlserver-execute)
[DEBUG-FILTER] Snap 'Map legionella inspection required' has Category: Transformation (Type: com-snaplogic-snaps-transform-datatransform)
[DEBUG-FILTER] Snap 'Update CRM PTR' has Category: Database (Type: com-snaplogic-snaps-dynamics365forsales-update)
[DEBUG-FILTER] Snap 'Get CRM PTRs' has Category: ExternalSystem (Type: com-snaplogic-snaps-dynamics365forsales-search)
[DEBUG-FILTER] Snap 'Get CRM Properties' has Category: ExternalSystem (Type: com-snaplogic-snaps-dynamics365forsales-read)
[DEBUG-FILTER] Snap 'Join propertyid' has Category: FlowControl (Type: com-snaplogic-snaps-transform-multijoin)
[DEBUG-FILTER] Snap 'Map propertytechnicalrecordsid' has Category: Transformation (Type: com-snaplogic-snaps-transform-datatransform)
[DEBUG-FILTER] Snap 'Exit' has Category: FlowControl (Type: com-snaplogic-snaps-flow-exit)
[DEBUG-FILTER] Snap 'Save to NCHVINT01' has Category: FileOperation (Type: com-snaplogic-snaps-binary-write)
[DEBUG-FILTER] Snap 'Document to Binary' has Category: Transformation (Type: com-snaplogic-snaps-transform-documenttobinary)
[DEBUG-FILTER] Snap 'Copy' has Category: FlowControl (Type: com-snaplogic-snaps-flow-copy)
[DEBUG-FILTER-RESULT] Database snaps filtered: 2
[DEBUG-FILTER-RESULT] Database snap: 'Get LEG Unknown Properties' (Category: Database)
[DEBUG-FILTER-RESULT] Database snap: 'Update CRM PTR' (Category: Database)
[DEBUG] ===== ALL SNAPS IN CATEGORY 'Flow Control' =====
[DEBUG] Snap 1: 'Join on UPRN' (Type: 'com-snaplogic-snaps-transform-multijoin', ID: 22ada40c-10ce-4c23-b57a-09ba37c81262)
[DEBUG] Snap 2: 'Join propertyid' (Type: 'com-snaplogic-snaps-transform-multijoin', ID: ba9ceedf-4b5e-47f1-bfdf-33e331a37191)
[DEBUG] Snap 3: 'Exit' (Type: 'com-snaplogic-snaps-flow-exit', ID: 714d4edd-65bd-439f-9502-2174458261cb)
[DEBUG] *** POTENTIAL CONDITION SNAP FOUND: 'Exit' (Type: 'com-snaplogic-snaps-flow-exit') ***
[DEBUG] Snap 4: 'Copy' (Type: 'com-snaplogic-snaps-flow-copy', ID: 1e6c6db0-709e-45c9-aa8c-9fb6b38724a7)
[DEBUG] *** POTENTIAL CONDITION SNAP FOUND: 'Copy' (Type: 'com-snaplogic-snaps-flow-copy') ***
[DEBUG] ===== END CATEGORY 'Flow Control' =====
[DEBUG-SECTION-START] AddSnapCategorySection called for 'Flow Control' with 4 snaps
[DEBUG-SECTION-START] Snap in Flow Control: 'Join on UPRN' (Category: FlowControl)
[DEBUG-SECTION-START] Snap in Flow Control: 'Join propertyid' (Category: FlowControl)
[DEBUG-SECTION-START] Snap in Flow Control: 'Exit' (Category: FlowControl)
[DEBUG-SECTION-START] Snap in Flow Control: 'Copy' (Category: FlowControl)
[DEBUG] ===== ALL SNAPS IN CATEGORY 'Flow Control' =====
[DEBUG] Snap 1: 'Join on UPRN' (Type: 'com-snaplogic-snaps-transform-multijoin', ID: 22ada40c-10ce-4c23-b57a-09ba37c81262)
[DEBUG] Snap 2: 'Join propertyid' (Type: 'com-snaplogic-snaps-transform-multijoin', ID: ba9ceedf-4b5e-47f1-bfdf-33e331a37191)
[DEBUG] Snap 3: 'Exit' (Type: 'com-snaplogic-snaps-flow-exit', ID: 714d4edd-65bd-439f-9502-2174458261cb)
[DEBUG] *** POTENTIAL CONDITION SNAP FOUND: 'Exit' (Type: 'com-snaplogic-snaps-flow-exit') ***
[DEBUG] Snap 4: 'Copy' (Type: 'com-snaplogic-snaps-flow-copy', ID: 1e6c6db0-709e-45c9-aa8c-9fb6b38724a7)
[DEBUG] *** POTENTIAL CONDITION SNAP FOUND: 'Copy' (Type: 'com-snaplogic-snaps-flow-copy') ***
[DEBUG] ===== END CATEGORY 'Flow Control' =====
[DEBUG-BEFORE-LOOP] About to process 4 snaps in Flow Control
[DEBUG-IN-LOOP] Processing snap: 'Join on UPRN' in Flow Control
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Join on UPRN'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Join on UPRN'. Category: FlowControl, Type: com-snaplogic-snaps-transform-multijoin
[DEBUG-SKIP-CONNECTION] Skipping connection info for mapper/transform snap: 'Join on UPRN'
[ENHANCED-DESC-0afc0ad1] Starting enhanced snap description for 'Join on UPRN' (com-snaplogic-snaps-transform-multijoin)
[ENHANCED-DESC-0afc0ad1] Using cached descriptions: True
[ENHANCED-DESC-0afc0ad1] Using Azure AI: True
[ENHANCED-DESC-0afc0ad1] CACHE HIT: Found cached enhanced description in 22ms, length: 621 characters
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Join on UPRN' is flow control snap
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Join on UPRN' isFlowControlSnap: True
[DEBUG-JOIN-DETECTION] Found Join snap: 'Join on UPRN' (Type: com-snaplogic-snaps-transform-multijoin), isFlowControlSnap: True
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Join on UPRN'
[DEBUG-FLOW-CONTROL-ENTERED] *** ENTERED FLOW CONTROL CONFIGURATION SECTION FOR 'Join on UPRN' ***
[DEBUG-FLOW-CONTROL-TRY] *** ENTERED TRY BLOCK FOR FLOW CONTROL CONFIG: 'Join on UPRN' ***
[DEBUG-FLOW-CONTROL-GENERATOR] FlowControlConfigurationGenerator created successfully for: 'Join on UPRN'
[DEBUG-SETUP-CONNECTIONS] About to call SetupSnapConnections for: 'Join on UPRN'
[DEBUG-SETUP-CONNECTIONS] SetupSnapConnections completed for: 'Join on UPRN'
[DEBUG-FLOW-CONTROL] About to start snap type detection for: 'Join on UPRN'
[DEBUG-FLOW-CONTROL] flowControlConfig variable initialized for: 'Join on UPRN'
[DEBUG-FLOW-CONTROL] snapTypeLower = 'com-snaplogic-snaps-transform-multijoin' for: 'Join on UPRN'
[DEBUG-FLOW-CONTROL] About to call LogMessage for snap type detection: 'Join on UPRN'
[DEBUG-FLOW-CONTROL] Computed boolean values - router: False, join: True, union: False
[DEBUG-FLOW-CONTROL] About to check configuration type for: 'Join on UPRN'
[DEBUG-FLOW-CONTROL] Detected JOIN snap: 'Join on UPRN'
[FLOW-CONTROL-CONFIG] *** GENERATING JOIN CONFIGURATION *** for: Join on UPRN
[DEBUG-JOIN-CALL] About to call GenerateJoinConfiguration for: 'Join on UPRN'
[DEBUG-JOIN-CONDITIONS] Extracting join conditions for: Join on UPRN
[DEBUG-JOIN-CONDITIONS] Total properties: 15
[DEBUG-JOIN-CONDITIONS] Found 0 join expressions
[DEBUG-JOIN-CONDITIONS] Found 1 left paths, 1 right paths
[DEBUG-JOIN-CONDITIONS] Left path: settings.joinPaths.value.0.leftPath.value = $nch_uprn
[DEBUG-JOIN-CONDITIONS] Right path: settings.joinPaths.value.0.rightPath.value = $nch_uprn.toString()
[DEBUG-JOIN-CONDITIONS] Added left path for index 0: $nch_uprn
[DEBUG-JOIN-CONDITIONS] Added right path for index 0: $nch_uprn.toString()
[DEBUG-JOIN-CONDITIONS] Created condition: $nch_uprn == $nch_uprn.toString()
[DEBUG-JOIN-CONDITIONS] Found 0 multijoin properties
[DEBUG-JOIN-CONDITIONS] Total conditions extracted: 1
[DEBUG-JOIN-RESULT] GenerateJoinConfiguration returned 2202 characters for: 'Join on UPRN'
[DEBUG-IN-LOOP] Processing snap: 'Join propertyid' in Flow Control
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Join propertyid'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Join propertyid'. Category: FlowControl, Type: com-snaplogic-snaps-transform-multijoin
[DEBUG-SKIP-CONNECTION] Skipping connection info for mapper/transform snap: 'Join propertyid'
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Join propertyid' is flow control snap
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Join propertyid' isFlowControlSnap: True
[DEBUG-JOIN-DETECTION] Found Join snap: 'Join propertyid' (Type: com-snaplogic-snaps-transform-multijoin), isFlowControlSnap: True
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Join propertyid'
[DEBUG-FLOW-CONTROL-ENTERED] *** ENTERED FLOW CONTROL CONFIGURATION SECTION FOR 'Join propertyid' ***
[DEBUG-FLOW-CONTROL-TRY] *** ENTERED TRY BLOCK FOR FLOW CONTROL CONFIG: 'Join propertyid' ***
[DEBUG-FLOW-CONTROL-GENERATOR] FlowControlConfigurationGenerator created successfully for: 'Join propertyid'
[DEBUG-SETUP-CONNECTIONS] About to call SetupSnapConnections for: 'Join propertyid'
[DEBUG-SETUP-CONNECTIONS] SetupSnapConnections completed for: 'Join propertyid'
[DEBUG-FLOW-CONTROL] About to start snap type detection for: 'Join propertyid'
[DEBUG-FLOW-CONTROL] flowControlConfig variable initialized for: 'Join propertyid'
[DEBUG-FLOW-CONTROL] snapTypeLower = 'com-snaplogic-snaps-transform-multijoin' for: 'Join propertyid'
[DEBUG-FLOW-CONTROL] About to call LogMessage for snap type detection: 'Join propertyid'
[DEBUG-FLOW-CONTROL] Computed boolean values - router: False, join: True, union: False
[DEBUG-FLOW-CONTROL] About to check configuration type for: 'Join propertyid'
[DEBUG-FLOW-CONTROL] Detected JOIN snap: 'Join propertyid'
[FLOW-CONTROL-CONFIG] *** GENERATING JOIN CONFIGURATION *** for: Join propertyid
[DEBUG-JOIN-CALL] About to call GenerateJoinConfiguration for: 'Join propertyid'
[DEBUG-JOIN-CONDITIONS] Extracting join conditions for: Join propertyid
[DEBUG-JOIN-CONDITIONS] Total properties: 15
[DEBUG-JOIN-CONDITIONS] Found 0 join expressions
[DEBUG-JOIN-CONDITIONS] Found 1 left paths, 1 right paths
[DEBUG-JOIN-CONDITIONS] Left path: settings.joinPaths.value.0.leftPath.value = $nch_propertyid
[DEBUG-JOIN-CONDITIONS] Right path: settings.joinPaths.value.0.rightPath.value = $_nch_relatedproperty_value
[DEBUG-JOIN-CONDITIONS] Added left path for index 0: $nch_propertyid
[DEBUG-JOIN-CONDITIONS] Added right path for index 0: $_nch_relatedproperty_value
[DEBUG-JOIN-CONDITIONS] Created condition: $nch_propertyid == $_nch_relatedproperty_value
[DEBUG-JOIN-CONDITIONS] Found 0 multijoin properties
[DEBUG-JOIN-CONDITIONS] Total conditions extracted: 1
[DEBUG-JOIN-RESULT] GenerateJoinConfiguration returned 2192 characters for: 'Join propertyid'
[DEBUG-IN-LOOP] Processing snap: 'Exit' in Flow Control
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Exit'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Exit'. Category: FlowControl, Type: com-snaplogic-snaps-flow-exit
[DEBUG-BEFORE-GET-INFO] About to call GetConnectionInfo for: 'Exit'
[DEBUG-RAW-JSON] RawPipelineJson is null: False
[DEBUG-RAW-JSON] RawPipelineJson has snaps: False
[DEBUG-CONNECTION-START] GetConnectionInfo called for: Exit
[DEBUG-CONNECTION-PROPS] Snap has 3 properties
[DEBUG-CONNECTION-PROP] settings.execution_mode.value = Validate & Execute...
[DEBUG-CONNECTION-PROP] settings.KeyThresholdLimit.value = ********...
[DEBUG-CONNECTION-PROP] settings.KeyErrorMsg.value = Exceeded threshold limit...
[DEBUG-JSON-KEYS] Raw JSON top-level keys: class_fqid, snode_id, instance_id, instance_version, link_map, link_serial, property_map, render_map, snap_map, path_id, path_snode
[DEBUG-SNAPS] snap_map collection exists: True
[DEBUG-SNAP-JSON] Found snap JSON for ID 714d4edd-65bd-439f-9502-2174458261cb: True
[DEBUG-PROPERTY-MAP] property_map exists: True
[DEBUG-ACCOUNT] account property exists: False
[DEBUG-CONNECTION-NO-ACCOUNT] No account info found - returning NULL
[DEBUG-AFTER-GET-INFO] GetConnectionInfo returned: 'NULL'
[ENHANCED-DESC-cd58e0ff] Starting enhanced snap description for 'Exit' (com-snaplogic-snaps-flow-exit)
[ENHANCED-DESC-cd58e0ff] Using cached descriptions: True
[ENHANCED-DESC-cd58e0ff] Using Azure AI: True
[ENHANCED-DESC-cd58e0ff] CACHE HIT: Found cached enhanced description in 1ms, length: 677 characters
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Exit' is flow control snap
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Exit' isFlowControlSnap: True
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Exit'
[DEBUG-FLOW-CONTROL-ENTERED] *** ENTERED FLOW CONTROL CONFIGURATION SECTION FOR 'Exit' ***
[DEBUG-FLOW-CONTROL-TRY] *** ENTERED TRY BLOCK FOR FLOW CONTROL CONFIG: 'Exit' ***
[DEBUG-FLOW-CONTROL-GENERATOR] FlowControlConfigurationGenerator created successfully for: 'Exit'
[DEBUG-SETUP-CONNECTIONS] About to call SetupSnapConnections for: 'Exit'
[DEBUG-SETUP-CONNECTIONS] SetupSnapConnections completed for: 'Exit'
[DEBUG-FLOW-CONTROL] About to start snap type detection for: 'Exit'
[DEBUG-FLOW-CONTROL] flowControlConfig variable initialized for: 'Exit'
[DEBUG-FLOW-CONTROL] snapTypeLower = 'com-snaplogic-snaps-flow-exit' for: 'Exit'
[DEBUG-FLOW-CONTROL] About to call LogMessage for snap type detection: 'Exit'
[DEBUG-FLOW-CONTROL] Computed boolean values - router: False, join: False, union: False
[DEBUG-FLOW-CONTROL] About to check configuration type for: 'Exit'
[DEBUG-IN-LOOP] Processing snap: 'Copy' in Flow Control
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Copy'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Copy'. Category: FlowControl, Type: com-snaplogic-snaps-flow-copy
[DEBUG-BEFORE-GET-INFO] About to call GetConnectionInfo for: 'Copy'
[DEBUG-RAW-JSON] RawPipelineJson is null: False
[DEBUG-RAW-JSON] RawPipelineJson has snaps: False
[DEBUG-CONNECTION-START] GetConnectionInfo called for: Copy
[DEBUG-CONNECTION-PROPS] Snap has 1 properties
[DEBUG-CONNECTION-PROP] settings.execution_mode.value = Validate & Execute...
[DEBUG-JSON-KEYS] Raw JSON top-level keys: class_fqid, snode_id, instance_id, instance_version, link_map, link_serial, property_map, render_map, snap_map, path_id, path_snode
[DEBUG-SNAPS] snap_map collection exists: True
[DEBUG-SNAP-JSON] Found snap JSON for ID 1e6c6db0-709e-45c9-aa8c-9fb6b38724a7: True
[DEBUG-PROPERTY-MAP] property_map exists: True
[DEBUG-ACCOUNT] account property exists: False
[DEBUG-CONNECTION-NO-ACCOUNT] No account info found - returning NULL
[DEBUG-AFTER-GET-INFO] GetConnectionInfo returned: 'NULL'
[ENHANCED-DESC-fa25c3e3] Starting enhanced snap description for 'Copy' (com-snaplogic-snaps-flow-copy)
[ENHANCED-DESC-fa25c3e3] Using cached descriptions: True
[ENHANCED-DESC-fa25c3e3] Using Azure AI: True
[ENHANCED-DESC-fa25c3e3] CACHE HIT: Found cached enhanced description in 1ms, length: 529 characters
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Copy' is flow control snap
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Copy' isFlowControlSnap: True
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Copy'
[DEBUG-FLOW-CONTROL-ENTERED] *** ENTERED FLOW CONTROL CONFIGURATION SECTION FOR 'Copy' ***
[DEBUG-FLOW-CONTROL-TRY] *** ENTERED TRY BLOCK FOR FLOW CONTROL CONFIG: 'Copy' ***
[DEBUG-FLOW-CONTROL-GENERATOR] FlowControlConfigurationGenerator created successfully for: 'Copy'
[DEBUG-SETUP-CONNECTIONS] About to call SetupSnapConnections for: 'Copy'
[DEBUG-SETUP-CONNECTIONS] SetupSnapConnections completed for: 'Copy'
[DEBUG-FLOW-CONTROL] About to start snap type detection for: 'Copy'
[DEBUG-FLOW-CONTROL] flowControlConfig variable initialized for: 'Copy'
[DEBUG-FLOW-CONTROL] snapTypeLower = 'com-snaplogic-snaps-flow-copy' for: 'Copy'
[DEBUG-FLOW-CONTROL] About to call LogMessage for snap type detection: 'Copy'
[DEBUG-FLOW-CONTROL] Computed boolean values - router: False, join: False, union: False
[DEBUG-FLOW-CONTROL] About to check configuration type for: 'Copy'
[DEBUG-SECTION-START] AddSnapCategorySection called for 'Transformations' with 3 snaps
[DEBUG-SECTION-START] Snap in Transformations: 'Map legionella inspection required' (Category: Transformation)
[DEBUG-SECTION-START] Snap in Transformations: 'Map propertytechnicalrecordsid' (Category: Transformation)
[DEBUG-SECTION-START] Snap in Transformations: 'Document to Binary' (Category: Transformation)
[DEBUG] ===== ALL SNAPS IN CATEGORY 'Transformations' =====
[DEBUG] Snap 1: 'Map legionella inspection required' (Type: 'com-snaplogic-snaps-transform-datatransform', ID: 08684fba-9c1b-41a8-9e92-aba2eea8433c)
[DEBUG] Snap 2: 'Map propertytechnicalrecordsid' (Type: 'com-snaplogic-snaps-transform-datatransform', ID: c17e720d-6c35-4741-bda9-28663d0895b7)
[DEBUG] Snap 3: 'Document to Binary' (Type: 'com-snaplogic-snaps-transform-documenttobinary', ID: cb4edbd0-6f49-406c-b23e-62c8d0bc62ef)
[DEBUG] ===== END CATEGORY 'Transformations' =====
[DEBUG-BEFORE-LOOP] About to process 3 snaps in Transformations
[DEBUG-IN-LOOP] Processing snap: 'Map legionella inspection required' in Transformations
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Map legionella inspection required'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Map legionella inspection required'. Category: Transformation, Type: com-snaplogic-snaps-transform-datatransform
[DEBUG-SKIP-CONNECTION] Skipping connection info for mapper/transform snap: 'Map legionella inspection required'
[ENHANCED-DESC-8c02a662] Starting enhanced snap description for 'Map legionella inspection required' (com-snaplogic-snaps-transform-datatransform)
[ENHANCED-DESC-8c02a662] Using cached descriptions: True
[ENHANCED-DESC-8c02a662] Using Azure AI: True
[ENHANCED-DESC-8c02a662] CACHE HIT: Found cached enhanced description in 1ms, length: 827 characters
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Map legionella inspection required' is flow control snap
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Map legionella inspection required' isFlowControlSnap: False
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Map legionella inspection required'
[DEBUG-IN-LOOP] Processing snap: 'Map propertytechnicalrecordsid' in Transformations
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Map propertytechnicalrecordsid'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Map propertytechnicalrecordsid'. Category: Transformation, Type: com-snaplogic-snaps-transform-datatransform
[DEBUG-SKIP-CONNECTION] Skipping connection info for mapper/transform snap: 'Map propertytechnicalrecordsid'
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Map propertytechnicalrecordsid' is flow control snap
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Map propertytechnicalrecordsid' isFlowControlSnap: False
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Map propertytechnicalrecordsid'
[DEBUG-IN-LOOP] Processing snap: 'Document to Binary' in Transformations
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Document to Binary'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Document to Binary'. Category: Transformation, Type: com-snaplogic-snaps-transform-documenttobinary
[DEBUG-SKIP-CONNECTION] Skipping connection info for mapper/transform snap: 'Document to Binary'
[ENHANCED-DESC-f3febb29] Starting enhanced snap description for 'Document to Binary' (com-snaplogic-snaps-transform-documenttobinary)
[ENHANCED-DESC-f3febb29] Using cached descriptions: True
[ENHANCED-DESC-f3febb29] Using Azure AI: True
[ENHANCED-DESC-f3febb29] CACHE HIT: Found cached enhanced description in 1ms, length: 654 characters
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Document to Binary' is flow control snap
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Document to Binary' isFlowControlSnap: False
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Document to Binary'
[DEBUG-SECTION-START] AddSnapCategorySection called for 'Database Operations' with 2 snaps
[DEBUG-SECTION-START] Snap in Database Operations: 'Get LEG Unknown Properties' (Category: Database)
[DEBUG-SECTION-START] Snap in Database Operations: 'Update CRM PTR' (Category: Database)
[DEBUG] ===== ALL SNAPS IN CATEGORY 'Database Operations' =====
[DEBUG] Snap 1: 'Get LEG Unknown Properties' (Type: 'com-snaplogic-snaps-sqlserver-execute', ID: eba9da03-aa03-4a15-b5a8-3f774fbb0abe)
[DEBUG] Snap 2: 'Update CRM PTR' (Type: 'com-snaplogic-snaps-dynamics365forsales-update', ID: 47c52b57-6a3f-4c0e-8169-8a17f1987b48)
[DEBUG] ===== END CATEGORY 'Database Operations' =====
[DEBUG-BEFORE-LOOP] About to process 2 snaps in Database Operations
[DEBUG-IN-LOOP] Processing snap: 'Get LEG Unknown Properties' in Database Operations
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Get LEG Unknown Properties'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Get LEG Unknown Properties'. Category: Database, Type: com-snaplogic-snaps-sqlserver-execute
[DEBUG-BEFORE-GET-INFO] About to call GetConnectionInfo for: 'Get LEG Unknown Properties'
[DEBUG-RAW-JSON] RawPipelineJson is null: False
[DEBUG-RAW-JSON] RawPipelineJson has snaps: False
[DEBUG-CONNECTION-START] GetConnectionInfo called for: Get LEG Unknown Properties
[DEBUG-CONNECTION-PROPS] Snap has 8 properties
[DEBUG-CONNECTION-PROP] settings.execution_mode.value = Validate & Execute...
[DEBUG-CONNECTION-PROP] settings.executable_during_suggest.value = False...
[DEBUG-CONNECTION-PROP] settings.passThrough.value = False...
[DEBUG-CONNECTION-PROP] settings.maxRetryProp.value = 0...
[DEBUG-CONNECTION-PROP] settings.retryIntervalProp.value = 1...
[DEBUG-JSON-KEYS] Raw JSON top-level keys: class_fqid, snode_id, instance_id, instance_version, link_map, link_serial, property_map, render_map, snap_map, path_id, path_snode
[DEBUG-SNAPS] snap_map collection exists: True
[DEBUG-SNAP-JSON] Found snap JSON for ID eba9da03-aa03-4a15-b5a8-3f774fbb0abe: True
[DEBUG-PROPERTY-MAP] property_map exists: True
[DEBUG-ACCOUNT] account property exists: True
[DEBUG-AFTER-GET-INFO] GetConnectionInfo returned: 'URM (Live)'
[ENHANCED-DESC-********] Starting enhanced snap description for 'Get LEG Unknown Properties' (com-snaplogic-snaps-sqlserver-execute)
[ENHANCED-DESC-********] Using cached descriptions: True
[ENHANCED-DESC-********] Using Azure AI: True
[ENHANCED-DESC-********] CACHE HIT: Found cached enhanced description in 1ms, length: 657 characters
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Get LEG Unknown Properties' is flow control snap
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Get LEG Unknown Properties' isFlowControlSnap: False
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Get LEG Unknown Properties'
[DEBUG-IN-LOOP] Processing snap: 'Update CRM PTR' in Database Operations
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Update CRM PTR'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Update CRM PTR'. Category: Database, Type: com-snaplogic-snaps-dynamics365forsales-update
[DEBUG-BEFORE-GET-INFO] About to call GetConnectionInfo for: 'Update CRM PTR'
[DEBUG-RAW-JSON] RawPipelineJson is null: False
[DEBUG-RAW-JSON] RawPipelineJson has snaps: False
[DEBUG-CONNECTION-START] GetConnectionInfo called for: Update CRM PTR
[DEBUG-CONNECTION-PROPS] Snap has 12 properties
[DEBUG-CONNECTION-PROP] settings.continueOnError.value = False...
[DEBUG-CONNECTION-PROP] settings.relatedObjectRelationship.value = NULL/EMPTY
[DEBUG-CONNECTION-PROP] settings.relatedObjectType.value = NULL/EMPTY
[DEBUG-CONNECTION-PROP] settings.retries.value = 0...
[DEBUG-CONNECTION-PROP] settings.execution_mode.value = Execute only...
[DEBUG-JSON-KEYS] Raw JSON top-level keys: class_fqid, snode_id, instance_id, instance_version, link_map, link_serial, property_map, render_map, snap_map, path_id, path_snode
[DEBUG-SNAPS] snap_map collection exists: True
[DEBUG-SNAP-JSON] Found snap JSON for ID 47c52b57-6a3f-4c0e-8169-8a17f1987b48: True
[DEBUG-PROPERTY-MAP] property_map exists: True
[DEBUG-ACCOUNT] account property exists: True
[DEBUG-AFTER-GET-INFO] GetConnectionInfo returned: 'NCH 365 PROD'
[DEBUG-DYNAMICS365] Adding Dynamics365ForSales details for: 'Update CRM PTR'
[ENHANCED-DESC-a4a1b11a] Starting enhanced snap description for 'Update CRM PTR' (com-snaplogic-snaps-dynamics365forsales-update)
[ENHANCED-DESC-a4a1b11a] Using cached descriptions: True
[ENHANCED-DESC-a4a1b11a] Using Azure AI: True
[ENHANCED-DESC-a4a1b11a] CACHE HIT: Found cached enhanced description in 1ms, length: 779 characters
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Update CRM PTR' is flow control snap
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Update CRM PTR' isFlowControlSnap: False
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Update CRM PTR'
[DEBUG-SECTION-START] AddSnapCategorySection called for 'External Systems' with 2 snaps
[DEBUG-SECTION-START] Snap in External Systems: 'Get CRM PTRs' (Category: ExternalSystem)
[DEBUG-SECTION-START] Snap in External Systems: 'Get CRM Properties' (Category: ExternalSystem)
[DEBUG] ===== ALL SNAPS IN CATEGORY 'External Systems' =====
[DEBUG] Snap 1: 'Get CRM PTRs' (Type: 'com-snaplogic-snaps-dynamics365forsales-search', ID: a75c73ce-c8fc-46f2-a10e-ee923b3d95cb)
[DEBUG] Snap 2: 'Get CRM Properties' (Type: 'com-snaplogic-snaps-dynamics365forsales-read', ID: 475ac7d8-fc9e-4b34-9bca-e6e7309b08c8)
[DEBUG] ===== END CATEGORY 'External Systems' =====
[DEBUG-BEFORE-LOOP] About to process 2 snaps in External Systems
[DEBUG-IN-LOOP] Processing snap: 'Get CRM PTRs' in External Systems
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Get CRM PTRs'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Get CRM PTRs'. Category: ExternalSystem, Type: com-snaplogic-snaps-dynamics365forsales-search
[DEBUG-BEFORE-GET-INFO] About to call GetConnectionInfo for: 'Get CRM PTRs'
[DEBUG-RAW-JSON] RawPipelineJson is null: False
[DEBUG-RAW-JSON] RawPipelineJson has snaps: False
[DEBUG-CONNECTION-START] GetConnectionInfo called for: Get CRM PTRs
[DEBUG-CONNECTION-PROPS] Snap has 8 properties
[DEBUG-CONNECTION-PROP] settings.continueOnError.value = False...
[DEBUG-CONNECTION-PROP] settings.retries.value = 0...
[DEBUG-CONNECTION-PROP] settings.execution_mode.value = Validate & Execute...
[DEBUG-CONNECTION-PROP] settings.pageSize.value = 1000...
[DEBUG-CONNECTION-PROP] settings.executeDuringPreview.value = True...
[DEBUG-JSON-KEYS] Raw JSON top-level keys: class_fqid, snode_id, instance_id, instance_version, link_map, link_serial, property_map, render_map, snap_map, path_id, path_snode
[DEBUG-SNAPS] snap_map collection exists: True
[DEBUG-SNAP-JSON] Found snap JSON for ID a75c73ce-c8fc-46f2-a10e-ee923b3d95cb: True
[DEBUG-PROPERTY-MAP] property_map exists: True
[DEBUG-ACCOUNT] account property exists: True
[DEBUG-AFTER-GET-INFO] GetConnectionInfo returned: 'NCH 365 PROD'
[DEBUG-DYNAMICS365] Adding Dynamics365ForSales details for: 'Get CRM PTRs'
[ENHANCED-DESC-97cfe634] Starting enhanced snap description for 'Get CRM PTRs' (com-snaplogic-snaps-dynamics365forsales-search)
[ENHANCED-DESC-97cfe634] Using cached descriptions: True
[ENHANCED-DESC-97cfe634] Using Azure AI: True
[ENHANCED-DESC-97cfe634] CACHE HIT: Found cached enhanced description in 2ms, length: 592 characters
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Get CRM PTRs' is flow control snap
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Get CRM PTRs' isFlowControlSnap: False
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Get CRM PTRs'
[DEBUG-IN-LOOP] Processing snap: 'Get CRM Properties' in External Systems
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Get CRM Properties'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Get CRM Properties'. Category: ExternalSystem, Type: com-snaplogic-snaps-dynamics365forsales-read
[DEBUG-BEFORE-GET-INFO] About to call GetConnectionInfo for: 'Get CRM Properties'
[DEBUG-RAW-JSON] RawPipelineJson is null: False
[DEBUG-RAW-JSON] RawPipelineJson has snaps: False
[DEBUG-CONNECTION-START] GetConnectionInfo called for: Get CRM Properties
[DEBUG-CONNECTION-PROPS] Snap has 10 properties
[DEBUG-CONNECTION-PROP] settings.continueOnError.value = False...
[DEBUG-CONNECTION-PROP] settings.retries.value = 0...
[DEBUG-CONNECTION-PROP] settings.execution_mode.value = Validate & Execute...
[DEBUG-CONNECTION-PROP] settings.pageSize.value = 1000...
[DEBUG-CONNECTION-PROP] settings.startPage.value = 1...
[DEBUG-JSON-KEYS] Raw JSON top-level keys: class_fqid, snode_id, instance_id, instance_version, link_map, link_serial, property_map, render_map, snap_map, path_id, path_snode
[DEBUG-SNAPS] snap_map collection exists: True
[DEBUG-SNAP-JSON] Found snap JSON for ID 475ac7d8-fc9e-4b34-9bca-e6e7309b08c8: True
[DEBUG-PROPERTY-MAP] property_map exists: True
[DEBUG-ACCOUNT] account property exists: True
[DEBUG-AFTER-GET-INFO] GetConnectionInfo returned: 'NCH 365 PROD'
[DEBUG-DYNAMICS365] Adding Dynamics365ForSales details for: 'Get CRM Properties'
[ENHANCED-DESC-49967fc9] Starting enhanced snap description for 'Get CRM Properties' (com-snaplogic-snaps-dynamics365forsales-read)
[ENHANCED-DESC-49967fc9] Using cached descriptions: True
[ENHANCED-DESC-49967fc9] Using Azure AI: True
[ENHANCED-DESC-49967fc9] CACHE HIT: Found cached enhanced description in 1ms, length: 765 characters
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Get CRM Properties' is flow control snap
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Get CRM Properties' isFlowControlSnap: False
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Get CRM Properties'
[DEBUG-SECTION-START] AddSnapCategorySection called for 'File Operations' with 1 snaps
[DEBUG-SECTION-START] Snap in File Operations: 'Save to NCHVINT01' (Category: FileOperation)
[DEBUG] ===== ALL SNAPS IN CATEGORY 'File Operations' =====
[DEBUG] Snap 1: 'Save to NCHVINT01' (Type: 'com-snaplogic-snaps-binary-write', ID: 550b2d53-16be-40c4-b16f-157ea043a388)
[DEBUG] ===== END CATEGORY 'File Operations' =====
[DEBUG-BEFORE-LOOP] About to process 1 snaps in File Operations
[DEBUG-IN-LOOP] Processing snap: 'Save to NCHVINT01' in File Operations
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Save to NCHVINT01'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Save to NCHVINT01'. Category: FileOperation, Type: com-snaplogic-snaps-binary-write
[DEBUG-BEFORE-GET-INFO] About to call GetConnectionInfo for: 'Save to NCHVINT01'
[DEBUG-RAW-JSON] RawPipelineJson is null: False
[DEBUG-RAW-JSON] RawPipelineJson has snaps: False
[DEBUG-CONNECTION-START] GetConnectionInfo called for: Save to NCHVINT01
[DEBUG-CONNECTION-PROPS] Snap has 13 properties
[DEBUG-CONNECTION-PROP] settings.flushIntervalKb.value = -1...
[DEBUG-CONNECTION-PROP] settings.retries.value = 0...
[DEBUG-CONNECTION-PROP] settings.cannedAcls.value = None...
[DEBUG-CONNECTION-PROP] settings.filename.value = "file:///SnapFileDownloads/PTR_Debug Files/legione...
[DEBUG-CONNECTION-PROP] settings.execution_mode.value = Execute only...
[DEBUG-JSON-KEYS] Raw JSON top-level keys: class_fqid, snode_id, instance_id, instance_version, link_map, link_serial, property_map, render_map, snap_map, path_id, path_snode
[DEBUG-SNAPS] snap_map collection exists: True
[DEBUG-SNAP-JSON] Found snap JSON for ID 550b2d53-16be-40c4-b16f-157ea043a388: True
[DEBUG-PROPERTY-MAP] property_map exists: True
[DEBUG-ACCOUNT] account property exists: True
[DEBUG-CONNECTION-NO-ACCOUNT] No account info found - returning NULL
[DEBUG-AFTER-GET-INFO] GetConnectionInfo returned: 'NULL'
[ENHANCED-DESC-02e885c9] Starting enhanced snap description for 'Save to NCHVINT01' (com-snaplogic-snaps-binary-write)
[ENHANCED-DESC-02e885c9] Using cached descriptions: True
[ENHANCED-DESC-02e885c9] Using Azure AI: True
[ENHANCED-DESC-02e885c9] CACHE HIT: Found cached enhanced description in 1ms, length: 0 characters
[PSEUDOCODE-c36de452] Starting pseudocode generation for 'Save to NCHVINT01' (com-snaplogic-snaps-binary-write)
[PSEUDOCODE-c36de452] Using Azure AI: True
[PSEUDOCODE-c36de452] Checking if snap is mapper or condition type
[MAPPER-CHECK] Checking snap: Save to NCHVINT01
[MAPPER-CHECK] Snap Type: 'com-snaplogic-snaps-binary-write' -> Lower: 'com-snaplogic-snaps-binary-write'
[MAPPER-CHECK] Snap Category: FileOperation
[MAPPER-CHECK] IsTransformation: False
[MAPPER-CHECK] IsFlowControl: False (Category=False, Contains router=False)
[MAPPER-CHECK] Final result: False
[PSEUDOCODE-c36de452] Snap is not mapper/condition type, will try AI-based generation
[PSEUDOCODE-c36de452] Creating context for AI-based pseudocode generation
[PSEUDOCODE-c36de452] Context created, length: 727 characters
[PSEUDOCODE-c36de452] Sending to Azure OpenAI, prompt length: 1370 characters
[AI-REQ-d920005d] Starting Azure OpenAI request
[AI-REQ-d920005d] Prompt length: 1370 characters
[AI-REQ-d920005d] CancellationToken: IsCancelled=False, CanBeCancelled=False
[AI-REQ-d920005d] Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
[AI-REQ-d920005d] Deployment: gpt-4.1
[AI-REQ-d920005d] HttpClient timeout: 00:05:00
[AI-REQ-d920005d] Request URI: https://openai-general-assistan-resource.cognitiveservices.azure.com//openai/deployments/gpt-4.1/chat/completions?api-version=2024-02-01
[AI-REQ-d920005d] Creating HTTP request message
[AI-REQ-d920005d] About to send HTTP request to Azure OpenAI
[AI-REQ-d920005d] HTTP request completed in 1506ms, Status: OK
[AI-REQ-d920005d] About to read response content
[AI-REQ-d920005d] Response content read in 0ms, Length: 1939 characters
[AI-REQ-d920005d] Parsing JSON response
[AI-REQ-d920005d] SUCCESS: Request completed in 1511ms, Result length: 697 characters
[PSEUDOCODE-c36de452] SUCCESS: AI-based pseudocode generated in 1518ms, length: 697 characters
[PSEUDOCODE-c36de452] Storing AI-based pseudocode in cache
Storing description for Save to NCHVINT01 (Type: com-snaplogic-snaps-binary-write)
Description length: 0
Pseudocode length: 697
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Save to NCHVINT01' is flow control snap
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Save to NCHVINT01' isFlowControlSnap: False
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Save to NCHVINT01'
[DEBUG-SECTION-START] AddSnapCategorySection called for 'Error Handling' with 0 snaps
[DEBUG] ===== ALL SNAPS IN CATEGORY 'Error Handling' =====
[DEBUG] ===== END CATEGORY 'Error Handling' =====
[DEBUG-SECTION-START] AddSnapCategorySection called for 'Other Snaps' with 0 snaps
[DEBUG] ===== ALL SNAPS IN CATEGORY 'Other Snaps' =====
[DEBUG] ===== END CATEGORY 'Other Snaps' =====
