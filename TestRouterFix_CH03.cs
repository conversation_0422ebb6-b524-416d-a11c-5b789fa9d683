using System;
using System.IO;
using System.Threading.Tasks;

namespace SnapAnalyzer
{
    class TestRouterFix_CH03
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("Testing Router Fix with CH03 Pipeline");
            Console.WriteLine("====================================");
            
            try
            {
                // Path to the CH03 pipeline file
                string ch03FilePath = @"C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Snap-Pipelines\CH03 - Update Kypera Property Characteristics_2025_05_16.slp";
                
                if (!File.Exists(ch03FilePath))
                {
                    Console.WriteLine($"ERROR: CH03 file not found at: {ch03FilePath}");
                    return;
                }
                
                Console.WriteLine($"Found CH03 file: {Path.GetFileName(ch03FilePath)}");
                
                // Initialize components
                var analyzer = new SlpAnalyzer();
                var aiGenerator = new AIDescriptionGenerator();
                var docGenerator = new DocumentationGenerator();
                
                // Configure AI generator (disable Azure AI to avoid hanging)
                aiGenerator.IsAzureEnabled = false;
                aiGenerator.EnableCachedDescriptions = true;
                
                Console.WriteLine("Analyzing CH03 pipeline...");
                
                // Read and analyze the pipeline
                string fileContent = File.ReadAllText(ch03FilePath);
                var pipeline = analyzer.AnalyzePipeline(fileContent, Path.GetFileNameWithoutExtension(ch03FilePath));
                
                Console.WriteLine($"Pipeline loaded: {pipeline.Name}");
                Console.WriteLine($"Total snaps: {pipeline.Snaps.Count}");
                
                // Find the Router snap
                var routerSnap = pipeline.Snaps.Find(s => s.Label == "Route Unit Count" || s.Type.Contains("router"));
                
                if (routerSnap != null)
                {
                    Console.WriteLine($"\nFound Router snap: '{routerSnap.Label}' (Type: {routerSnap.Type})");
                    Console.WriteLine($"Category: {routerSnap.Category}");
                    
                    // Test the IsMapperOrConditionSnap method
                    bool isMapperCondition = aiGenerator.IsMapperOrConditionSnap(routerSnap);
                    Console.WriteLine($"IsMapperOrConditionSnap result: {isMapperCondition}");
                    
                    // Test DocumentationGenerator logic
                    string snapType = routerSnap.Type.ToLower();
                    bool isMapperType = snapType.Contains("map") || snapType.Contains("datatransform") || snapType.Contains("transform");
                    bool isRouterType = routerSnap.Category == SnapCategory.FlowControl && snapType.Contains("router");
                    bool isExitOrUnionType = snapType.Contains("exit") || snapType.Contains("union");
                    
                    Console.WriteLine($"DocumentationGenerator checks:");
                    Console.WriteLine($"  isMapperType: {isMapperType}");
                    Console.WriteLine($"  isRouterType: {isRouterType}");
                    Console.WriteLine($"  isExitOrUnionType: {isExitOrUnionType}");
                    
                    bool shouldUseAI = !isMapperType && !isRouterType && !isExitOrUnionType;
                    Console.WriteLine($"  shouldUseAI: {shouldUseAI}");
                    
                    if (!shouldUseAI)
                    {
                        Console.WriteLine("✅ SUCCESS: Router snap will NOT use AI-based pseudocode generation");
                    }
                    else
                    {
                        Console.WriteLine("❌ FAILED: Router snap would still use AI-based pseudocode generation");
                    }
                    
                    // Test pseudocode generation
                    Console.WriteLine("\nTesting pseudocode generation...");
                    string pseudocode = await aiGenerator.GenerateSnapPseudocode(routerSnap);
                    
                    if (!string.IsNullOrEmpty(pseudocode))
                    {
                        Console.WriteLine($"✅ Pseudocode generated successfully ({pseudocode.Length} characters)");
                        Console.WriteLine($"First 100 chars: {pseudocode.Substring(0, Math.Min(100, pseudocode.Length))}...");
                    }
                    else
                    {
                        Console.WriteLine("❌ No pseudocode generated");
                    }
                }
                else
                {
                    Console.WriteLine("❌ Router snap not found in CH03 pipeline");
                    Console.WriteLine("Available snaps:");
                    foreach (var snap in pipeline.Snaps)
                    {
                        Console.WriteLine($"  - {snap.Label} ({snap.Type}) - Category: {snap.Category}");
                    }
                }
                
                Console.WriteLine("\nTest completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
