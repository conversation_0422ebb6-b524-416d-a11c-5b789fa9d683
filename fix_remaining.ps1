# Script to fix remaining Example Usage sections

# Create a backup
Copy-Item -Path "SnapBestPractices.cs" -Destination "SnapBestPractices.cs.powershell_backup"
Write-Host "Created backup at SnapBestPractices.cs.powershell_backup"

# Read the file content
$content = Get-Content -Path "SnapBestPractices.cs" -Raw

# Fix Copy snap section
$pattern = '(?s)details\.AppendLine\("<p><strong>Best Practices for Copy Snaps:</strong></p>"\);\r?\ndetails\.AppendLine\("<ul><li>Use Copy snaps to duplicate document streams for parallel processing paths\.</li><li>Be aware that this creates full duplicates of documents, which can increase memory usage\.</li></ul>"\);\r?\ndetails\.AppendLine\("<p><strong>Example Usage:</strong></p>"\);\r?\ndetails\.AppendLine\("<p>This Copy snap duplicates each incoming document to its multiple output views, allowing identical data to flow through different processing branches simultaneously\.</p>"\);'
$replacement = 'details.AppendLine("<p><strong>Best Practices for Copy Snaps:</strong></p>");
                        details.AppendLine("<ul><li>Use Copy snaps to duplicate document streams for parallel processing paths.</li><li>Be aware that this creates full duplicates of documents, which can increase memory usage.</li></ul>");
                        // No Example Usage section for Copy snap types as per requirements'
$content = $content -replace $pattern, $replacement
Write-Host "Fixed Copy snap section"

# Fix Pipeline Execute snap section
$pattern = '(?s)details\.AppendLine\("<p><strong>Best Practices for Pipeline Execute Snaps:</strong></p>"\);\r?\ndetails\.AppendLine\("<ul><li>Pass only necessary parameters to the child pipeline\.</li><li>Ensure the child pipeline is designed to handle the inputs and produce expected outputs\.</li><li>Manage error handling for child pipeline failures\.</li></ul>"\);\r?\ndetails\.AppendLine\("<p><strong>Example Usage:</strong></p>"\);'
$replacement = 'details.AppendLine("<p><strong>Best Practices for Pipeline Execute Snaps:</strong></p>");
                        details.AppendLine("<ul><li>Pass only necessary parameters to the child pipeline.</li><li>Ensure the child pipeline is designed to handle the inputs and produce expected outputs.</li><li>Manage error handling for child pipeline failures.</li></ul>");
                        // No Example Usage section for Pipeline Execute snap types as per requirements'
$content = $content -replace $pattern, $replacement
Write-Host "Fixed Pipeline Execute snap section"

# Fix Generic Transformation snap section
$pattern = '(?s)details\.AppendLine\("<p><strong>Best Practices for this Transformation Snap:</strong></p>"\);\r?\ndetails\.AppendLine\("<ul><li>Understand the specific transformation logic this snap applies\.</li><li>Validate output against expected schema and values\.</li></ul>"\);\r?\ndetails\.AppendLine\("<p><strong>Example Usage:</strong></p>"\);'
$replacement = 'details.AppendLine("<p><strong>Best Practices for this Transformation Snap:</strong></p>");
                                details.AppendLine("<ul><li>Understand the specific transformation logic this snap applies.</li><li>Validate output against expected schema and values.</li></ul>");
                                // No Example Usage section for Generic Transformation snap types as per requirements'
$content = $content -replace $pattern, $replacement
Write-Host "Fixed Generic Transformation snap section"

# Write the modified content back to the file
Set-Content -Path "SnapBestPractices.cs" -Value $content
Write-Host "All changes have been applied to SnapBestPractices.cs"
