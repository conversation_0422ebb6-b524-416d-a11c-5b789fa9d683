# PowerShell script to fix the GetDatabaseConnectionInfo method and add SQL extraction

$filePath = "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\DocumentationGenerator.cs"

Write-Host "Reading file..." -ForegroundColor Green
$content = Get-Content $filePath -Raw

Write-Host "Making replacements..." -ForegroundColor Green

# Replace LogMessage with Console.WriteLine for database connection debugging
$content = $content -replace 'LogMessage\(\$"\[DB-CONNECTION\]([^"]+)"\)', 'Console.WriteLine($"[DB-CONNECTION]$1")'

# Replace the GetDatabaseConnectionInfo method
$oldMethodPattern = '(?s)private string GetDatabaseConnectionInfo\(SnapNode snap\).*?(?=\s*private|\s*public|\s*internal|\s*protected|\s*\}[\s]*$)'

$newMethod = @'
/// <summary>
/// Extracts database connection information from snap properties
/// </summary>
private string GetDatabaseConnectionInfo(SnapNode snap)
{
    if (snap.Category != SnapCategory.Database)
        return null;

    Console.WriteLine($"[DB-CONNECTION] Checking connection info for snap: {snap.Label}, Properties count: {snap.Properties.Count}");
    
    // Log all properties for debugging
    foreach (var prop in snap.Properties)
    {
        var preview = prop.Value?.ToString().Substring(0, Math.Min(100, prop.Value?.ToString().Length ?? 0));
        Console.WriteLine($"[DB-CONNECTION] Property: {prop.Key} = {preview}...");
    }

    // First, look for the nested account structure in the 'account' property
    if (snap.Properties.ContainsKey("account"))
    {
        try
        {
            string accountJson = snap.Properties["account"];
            Console.WriteLine($"[DB-CONNECTION] Found account property, parsing JSON");
            
            var accountObj = JObject.Parse(accountJson);
            
            // Navigate through the nested structure: account -> account_ref -> value -> label -> value
            var accountRef = accountObj["account_ref"];
            if (accountRef != null)
            {
                var valueObj = accountRef["value"];
                if (valueObj != null)
                {
                    var labelObj = valueObj["label"];
                    if (labelObj != null)
                    {
                        var accountName = labelObj["value"]?.ToString();
                        if (!string.IsNullOrEmpty(accountName))
                        {
                            Console.WriteLine($"[DB-CONNECTION] Found account name from nested JSON: {accountName}");
                            return accountName;
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[DB-CONNECTION] Error parsing account JSON: {ex.Message}");
        }
    }

    Console.WriteLine($"[DB-CONNECTION] No connection info found for snap: {snap.Label}");
    return null;
}

/// <summary>
/// Extracts SQL statement from SQL execute snaps
/// </summary>
private string GetSqlStatement(SnapNode snap)
{
    if (snap.Category != SnapCategory.Database)
        return null;

    // Only get SQL for execute snaps
    if (!snap.SnapType.Contains("execute"))
        return null;

    Console.WriteLine($"[SQL-EXTRACT] Checking SQL for snap: {snap.Label}");

    // Look for SQL statement in settings
    if (snap.Properties.ContainsKey("settings"))
    {
        try
        {
            string settingsJson = snap.Properties["settings"];
            Console.WriteLine($"[SQL-EXTRACT] Found settings property, parsing JSON");
            
            var settingsObj = JObject.Parse(settingsJson);
            var sqlStatement = settingsObj["sqlStatement"];
            
            if (sqlStatement != null)
            {
                var sqlValue = sqlStatement["value"]?.ToString();
                if (!string.IsNullOrEmpty(sqlValue))
                {
                    Console.WriteLine($"[SQL-EXTRACT] Found SQL statement: {sqlValue}");
                    return sqlValue;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[SQL-EXTRACT] Error parsing settings JSON: {ex.Message}");
        }
    }

    Console.WriteLine($"[SQL-EXTRACT] No SQL statement found for snap: {snap.Label}");
    return null;
}

'@

$content = $content -replace $oldMethodPattern, $newMethod

# Add SQL statement display after connection info
$sqlDisplayCode = @'
                // Add SQL statement for execute snaps
                if (snap.SnapType.Contains("execute"))
                {
                    string sqlStatement = GetSqlStatement(snap);
                    if (!string.IsNullOrEmpty(sqlStatement))
                    {
                        html.AppendLine($"          <p><strong>SQL Statement:</strong></p>");
                        html.AppendLine($"          <pre style='background-color: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto;'><code>{System.Net.WebUtility.HtmlEncode(sqlStatement)}</code></pre>");
                    }
                }

'@

# Find the location after connection info display and add SQL display
$connectionPattern = '(\s+else\s*\{\s*LogMessage\(\$"\[DB-CONNECTION\] No connection info found for: \{snap\.Label\}"\);\s*\}\s*\})'
$replacement = $1 + $sqlDisplayCode

$content = $content -replace $connectionPattern, $replacement

Write-Host "Writing updated file..." -ForegroundColor Green
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Fix applied successfully!" -ForegroundColor Green
Write-Host "The GetDatabaseConnectionInfo method has been updated to work with the actual JSON structure." -ForegroundColor Yellow
Write-Host "SQL statement extraction has been added for execute snaps." -ForegroundColor Yellow
'@
<parameter name="EmptyFile">false
