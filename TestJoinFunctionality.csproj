<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0-windows7.0</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="SnapAnalyzer.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="**/*.backup.cs" />
    <Compile Remove="**/*_backup.cs" />
    <Compile Remove="**/*_before_*.cs" />
    <Compile Remove="**/Test*.cs" Exclude="**/TestJoinFunctionality.cs" />
    <Compile Remove="**/Quick*.cs" />
    <Compile Remove="**/Simple*.cs" />
    <Compile Remove="**/Debug*.cs" />
    <Compile Remove="**/Router*.cs" />
    <Compile Remove="**/GetDatabaseConnectionInfo_*.cs" />
    <Compile Remove="Program.cs" />
    <Compile Remove="MainForm.cs" />
    <Compile Remove="BatchDocumentationForm.cs" />
    <Compile Remove="ProjectForm.cs" />
    <Compile Remove="AzureConfigForm.cs" />
  </ItemGroup>
  
  <ItemGroup>
    <Compile Include="TestJoinFunctionality.cs" />
  </ItemGroup>

</Project>
