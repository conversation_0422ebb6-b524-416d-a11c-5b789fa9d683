using System;
using System.Collections.Generic;
using System.Linq;

namespace SnapAnalyser
{
    class RouterConnectionFixTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Testing Router Connection Fix ===");
            
            // Create a test router snap with the exact same issue you reported
            var routerSnap = new SnapNode
            {
                Id = "router-rent-officer",
                Label = "Route Rent Officer",
                Type = "Flow Router",
                Category = SnapCategory.FlowControl,
                Properties = new Dictionary<string, string>
                {
                    {"routingConditions", "[$RentOfficer != null, $RentOfficer == null]"}
                },
                OutputConnections = new List<SnapConnection>
                {
                    new SnapConnection { SourceId = "router-rent-officer", TargetId = "exit-snap-1", SourceViewId = "" }, // Empty SourceViewId (common case)
                    new SnapConnection { SourceId = "router-rent-officer", TargetId = "exit-snap-2", SourceViewId = "" }  // Empty SourceViewId (common case)
                },
                InputConnections = new List<SnapConnection>()
            };
            
            // Create connected snaps - these should appear in DIFFERENT rows, not the same
            var allSnaps = new List<SnapNode>
            {
                routerSnap,
                new SnapNode
                {
                    Id = "exit-snap-1",
                    Label = "Exit - High Priority",
                    Type = "Exit",
                    Category = SnapCategory.FlowControl,
                    InputConnections = new List<SnapConnection>(),
                    OutputConnections = new List<SnapConnection>()
                },
                new SnapNode
                {
                    Id = "exit-snap-2", 
                    Label = "Exit - Normal Priority",
                    Type = "Exit",
                    Category = SnapCategory.FlowControl,
                    InputConnections = new List<SnapConnection>(),
                    OutputConnections = new List<SnapConnection>()
                }
            };
            
            // Create the generator
            var generator = new FlowControlConfigurationGenerator();
            
            // Generate router configuration
            Console.WriteLine("Generating router configuration...");
            string routerConfig = generator.GenerateRouterConfiguration(routerSnap, allSnaps);
            
            Console.WriteLine("\nGenerated Router Configuration:");
            Console.WriteLine("=" + new string('=', 80));
            Console.WriteLine(routerConfig);
            Console.WriteLine("=" + new string('=', 80));
            
            // Create HTML file to view the results
            var htmlContent = $@"<!DOCTYPE html>
<html>
<head>
    <title>Router Connection Fix Test Results</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .test-result {{ border: 1px solid #ddd; padding: 20px; margin: 10px 0; }}
        .success {{ background-color: #d4edda; border-color: #c3e6cb; }}
        .error {{ background-color: #f8d7da; border-color: #f5c6cb; }}
        .before-after {{ display: flex; gap: 20px; }}
        .before, .after {{ flex: 1; padding: 15px; border-radius: 5px; }}
        .before {{ background-color: #f8d7da; }}
        .after {{ background-color: #d4edda; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f8f9fa; }}
        code {{ background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px; }}
    </style>
</head>
<body>
    <h1>🔧 Router Connection Test - Output Mapping Fix</h1>
    <p>Generated on: {DateTime.Now}</p>
    
    <div class='test-result'>
        <h2>📋 Problem Description</h2>
        <p>You reported that router outputs were showing the same connected snap for both outputs:</p>
        <div class='before-after'>
            <div class='before'>
                <h3>❌ Before (Broken)</h3>
                <table>
                    <tr><th>Condition</th><th>Output Path</th><th>Connected Snaps</th></tr>
                    <tr><td>$RentOfficer != null</td><td>output0</td><td><strong>Exit</strong></td></tr>
                    <tr><td>$RentOfficer == null</td><td>output1</td><td><strong>Exit</strong></td></tr>
                </table>
                <p><em>Same snap name for both outputs - incorrect!</em></p>
            </div>
            <div class='after'>
                <h3>✅ After (Fixed)</h3>
                <table>
                    <tr><th>Condition</th><th>Output Path</th><th>Connected Snaps</th></tr>
                    <tr><td>$RentOfficer != null</td><td>output0</td><td><strong>Exit - High Priority</strong></td></tr>
                    <tr><td>$RentOfficer == null</td><td>output1</td><td><strong>Exit - Normal Priority</strong></td></tr>
                </table>
                <p><em>Different snap names for each output - correct!</em></p>
            </div>
        </div>
    </div>
    
    <div class='test-result'>
        <h2>🧪 Test Results</h2>
        {routerConfig}
    </div>
    
    <div class='test-result'>
        <h2>✅ Validation</h2>
        <p>The fix should show:</p>
        <ul>
            <li><strong>Condition 1:</strong> $RentOfficer != null → output0 → Exit - High Priority</li>
            <li><strong>Condition 2:</strong> $RentOfficer == null → output1 → Exit - Normal Priority</li>
        </ul>
        <p><strong>Key Fix:</strong> The GetConnectedSnapsForOutput method now properly maps router conditions to their specific connected snaps using connection order when SourceViewId is empty.</p>
    </div>
    
</body>
</html>";
            
            string filePath = "router_connection_fix_results.html";
            System.IO.File.WriteAllText(filePath, htmlContent);
            
            Console.WriteLine($"\nTest results saved to: {filePath}");
            
            // Basic validation
            bool hasHighPriority = routerConfig.Contains("Exit - High Priority");
            bool hasNormalPriority = routerConfig.Contains("Exit - Normal Priority");
            
            Console.WriteLine("\n=== VALIDATION RESULTS ===");
            if (hasHighPriority && hasNormalPriority)
            {
                Console.WriteLine("✅ PASS: Both unique target snaps are correctly identified");
                Console.WriteLine("✅ PASS: Router outputs now show different connected snaps");
            }
            else
            {
                Console.WriteLine("❌ FAIL: Router connection mapping is still broken");
                Console.WriteLine($"   High Priority found: {hasHighPriority}");
                Console.WriteLine($"   Normal Priority found: {hasNormalPriority}");
            }
            
            Console.WriteLine("\nRouter connection fix test completed!");
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
