using System;
using System.Threading.Tasks;
using SnapAnalyser;

namespace TestAzureConfig
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("===== AZURE AI DEPLOYMENT CHECKER =====");
            Console.WriteLine($"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine();
            
            // Load configuration
            string apiKey = ConfigManager.OpenAIApiKey;
            string endpoint = ConfigManager.AzureOpenAIEndpoint;
            string currentDeployment = ConfigManager.AzureOpenAIDeploymentName;
            
            Console.WriteLine("Current Configuration:");
            Console.WriteLine($"  Endpoint: {endpoint}");
            Console.WriteLine($"  Current Deployment Name: {currentDeployment}");
            Console.WriteLine($"  API Key: {(string.IsNullOrEmpty(apiKey) ? "MISSING" : "Present")}");
            Console.WriteLine();
            
            if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(endpoint))
            {
                Console.WriteLine("❌ Cannot check deployments - missing API key or endpoint");
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
                return;
            }
            
            // Create tester and get deployment info
            var tester = new AzureOpenAITester();
            Console.WriteLine("🔍 Checking available deployments...");
            Console.WriteLine();
            
            string result = await tester.GetDeploymentInfo();
            Console.WriteLine(result);
            
            Console.WriteLine();
            Console.WriteLine("💡 SOLUTION:");
            Console.WriteLine("To fix the 404 error, update your config.json file with one of the deployment names above.");
            Console.WriteLine("The current deployment name 'gpt-4.1-2' doesn't exist in your Azure OpenAI resource.");
            
            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
