# Script to fix the pipeline data access in DocumentationGenerator

$filePath = "DocumentationGenerator.cs"

Write-Host "Fixing pipeline data access..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Fix the RawPipelineJson access
$oldAccess = 'if (_currentPipeline?.RawPipelineJson != null)'
$newAccess = 'if (_currentPipeline?.RawPipelineJson != null)'

# Also fix the JSON parsing - need to access snap_map correctly
$oldJsonAccess = @'
var pipelineJson = JObject.Parse(_currentPipeline.RawPipelineJson);
                            var snapsArray = pipelineJson["snap_map"];
'@

$newJsonAccess = @'
var pipelineJson = _currentPipeline.RawPipelineJson;
                            var snapsArray = pipelineJson["snap_map"];
'@

if ($content.Contains($oldJsonAccess)) {
    $content = $content.Replace($oldJsonAccess, $newJsonAccess)
    Write-Host "Fixed JSON parsing - no need to parse again" -ForegroundColor Yellow
} else {
    Write-Host "JSON parsing pattern not found" -ForegroundColor Red
}

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Fixed pipeline data access!" -ForegroundColor Green
