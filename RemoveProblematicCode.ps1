# PowerShell script to remove the problematic raw pipeline access code

$filePath = "DocumentationGenerator.cs"

Write-Host "Removing problematic raw pipeline access code..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Remove the entire enhanced account extraction block that's causing issues
$pattern = '(\s+\/\/ Enhanced account extraction - also check raw pipeline data.*?catch \(Exception ex\)\s*\{\s*Console\.WriteLine\(\$"\[DB-CONNECTION\] Error accessing raw pipeline data: \{ex\.Message\}"\);\s*\}\s*\})'
$content = $content -replace $pattern, '', 'Singleline'

# Clean up any double empty lines
$content = $content -replace '\r?\n\r?\n\r?\n', "`r`n`r`n"

# Write the updated file
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Problematic code removed successfully!" -ForegroundColor Green
Write-Host "The SQL statement extraction should still work." -ForegroundColor Yellow
