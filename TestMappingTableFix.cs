// Test program to verify the duplicate mapping table fix
using System;
using System.Collections.Generic;
using SnapAnalyser;

namespace SnapAnalyser
{
    public class TestMappingTableFix
    {
        public static void Main()
        {
            Console.WriteLine("Testing Duplicate Mapping Table Fix");
            Console.WriteLine("===================================\n");
            
            TestWithMappingsTrue();
            TestWithMappingsFalse();
            
            Console.WriteLine("\nTest completed. Press any key to exit...");
            Console.ReadKey();
        }
        
        private static void TestWithMappingsTrue()
        {
            Console.WriteLine("Test 1: hasMappings = true (should NOT generate mapping tables)");
            Console.WriteLine("------------------------------------------------------------------");
            
            try
            {
                // Create a mock snap with mapping properties
                var snap = CreateMockMapperSnap();
                var allSnaps = new List<SnapNode> { snap };
                
                // Call with hasMappings = true
                string result = SnapBestPractices.GetSnapBestPractices(snap, allSnaps, hasMappings: true);
                
                // Check if mapping tables are NOT present
                bool hasFieldMappingsTable = result.Contains("Field mappings defined in this snap:");
                bool hasExpressionCode = result.Contains("SnapLogic Expression Language for advanced transformations:");
                
                Console.WriteLine($"Contains field mappings table: {hasFieldMappingsTable}");
                Console.WriteLine($"Contains expression code: {hasExpressionCode}");
                
                if (!hasFieldMappingsTable && !hasExpressionCode)
                {
                    Console.WriteLine("✓ PASS: No mapping tables generated when hasMappings = true");
                }
                else
                {
                    Console.WriteLine("✗ FAIL: Mapping tables were generated when hasMappings = true");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ ERROR: {ex.Message}");
            }
            
            Console.WriteLine();
        }
        
        private static void TestWithMappingsFalse()
        {
            Console.WriteLine("Test 2: hasMappings = false (should generate mapping tables)");
            Console.WriteLine("-----------------------------------------------------------");
            
            try
            {
                // Create a mock snap with mapping properties
                var snap = CreateMockMapperSnap();
                var allSnaps = new List<SnapNode> { snap };
                
                // Call with hasMappings = false (default)
                string result = SnapBestPractices.GetSnapBestPractices(snap, allSnaps, hasMappings: false);
                
                // Check if mapping tables ARE present
                bool hasFieldMappingsTable = result.Contains("Field mappings defined in this snap:");
                bool hasExpressionCode = result.Contains("SnapLogic Expression Language for advanced transformations:");
                
                Console.WriteLine($"Contains field mappings table: {hasFieldMappingsTable}");
                Console.WriteLine($"Contains expression code: {hasExpressionCode}");
                
                if (hasFieldMappingsTable || hasExpressionCode)
                {
                    Console.WriteLine("✓ PASS: Mapping tables generated when hasMappings = false");
                }
                else
                {
                    Console.WriteLine("✗ FAIL: No mapping tables were generated when hasMappings = false");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ ERROR: {ex.Message}");
            }
            
            Console.WriteLine();
        }
        
        private static SnapNode CreateMockMapperSnap()
        {
            var snap = new SnapNode
            {
                Name = "Test Mapper",
                Type = "com.snaplogic.snaps.transform.datatransform.DataMapper",
                Properties = new List<KeyValuePair<string, string>>
                {
                    new KeyValuePair<string, string>("execution_mode", "Validate & Execute"),
                    new KeyValuePair<string, string>("customer.name.expression", "$customer_data.name"),
                    new KeyValuePair<string, string>("customer.email.expression", "$customer_data.email"),
                    new KeyValuePair<string, string>("order.id.targetPath", "order_id"),
                    new KeyValuePair<string, string>("order.total.expression", "$order_data.amount * 1.1"),
                    new KeyValuePair<string, string>("mapping.simple", "direct_mapping_value")
                }
            };
            
            return snap;
        }
    }
}
