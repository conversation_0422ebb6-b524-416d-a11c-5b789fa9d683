using System;
using System.IO;
using System.Text;

namespace SnapAnalyzer
{
    public class FinalCytoscapeTest
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("=== Final Cytoscape.js Implementation Test ===");
            
            try
            {
                // Test 1: Create a simple pipeline flow diagram
                Console.WriteLine("1. Testing Pipeline Flow Diagram generation...");
                var cytoscapeGenerator = new CytoscapeJsGenerator();
                
                // Create test data similar to what would come from a SnapLogic pipeline
                var testElements = new[]
                {
                    new { data = new { id = "csv_reader", label = "CSV Reader", type = "input" } },
                    new { data = new { id = "mapper", label = "Mapper", type = "transform" } },
                    new { data = new { id = "router", label = "Router", type = "flow_control" } },
                    new { data = new { id = "join", label = "Join", type = "flow_control" } },
                    new { data = new { id = "json_formatter", label = "JSON Formatter", type = "transform" } },
                    new { data = new { id = "file_writer", label = "File Writer", type = "output" } }
                };
                
                var testEdges = new[]
                {
                    new { data = new { id = "e1", source = "csv_reader", target = "mapper" } },
                    new { data = new { id = "e2", source = "mapper", target = "router" } },
                    new { data = new { id = "e3", source = "router", target = "join" } },
                    new { data = new { id = "e4", source = "join", target = "json_formatter" } },
                    new { data = new { id = "e5", source = "json_formatter", target = "file_writer" } }
                };
                
                string cytoscapeElements = cytoscapeGenerator.GenerateCytoscapeElements(testElements, testEdges);
                Console.WriteLine("✓ Cytoscape elements generated successfully");
                
                // Test 2: Generate complete HTML with proper sizing
                Console.WriteLine("2. Testing complete HTML generation...");
                string completeHtml = cytoscapeGenerator.GenerateCytoscapeHtml("test-pipeline", cytoscapeElements, 
                    "Pipeline Flow Diagram Test", "800px");
                
                // Verify the HTML contains the expected elements
                if (completeHtml.Contains("cytoscape-container") && 
                    completeHtml.Contains("height: 800px") &&
                    completeHtml.Contains("layout: { name: 'dagre', rankDir: 'LR'"))
                {
                    Console.WriteLine("✓ HTML generation with proper sizing confirmed");
                }
                else
                {
                    Console.WriteLine("⚠ HTML may not have expected sizing properties");
                }
                
                // Test 3: Save test HTML file
                Console.WriteLine("3. Saving test HTML file...");
                string testFileName = "final_cytoscape_verification.html";
                File.WriteAllText(testFileName, completeHtml);
                Console.WriteLine($"✓ Test file saved as: {testFileName}");
                
                // Test 4: Test DiagramGenerator integration
                Console.WriteLine("4. Testing DiagramGenerator integration...");
                var diagramGenerator = new DiagramGenerator();
                string diagramHtml = diagramGenerator.GeneratePipelineFlowDiagram(
                    "Test Pipeline", 
                    new[] { "CSV Reader", "Mapper", "Router", "Join", "JSON Formatter", "File Writer" }
                );
                
                if (diagramHtml.Contains("cytoscape") && diagramHtml.Contains("LR"))
                {
                    Console.WriteLine("✓ DiagramGenerator properly integrated with Cytoscape.js");
                }
                else
                {
                    Console.WriteLine("⚠ DiagramGenerator may not be using Cytoscape.js correctly");
                }
                
                // Test 5: Verify FlowControlDiagramGenerator
                Console.WriteLine("5. Testing FlowControlDiagramGenerator...");
                var flowControlGenerator = new FlowControlDiagramGenerator();
                
                string routerDiagram = flowControlGenerator.GenerateRouterDiagram("test-router", 
                    new[] { "input1", "input2" }, new[] { "output1", "output2" });
                
                if (routerDiagram.Contains("cytoscape") && routerDiagram.Contains("Router"))
                {
                    Console.WriteLine("✓ FlowControlDiagramGenerator working with Cytoscape.js");
                }
                else
                {
                    Console.WriteLine("⚠ FlowControlDiagramGenerator may have issues");
                }
                
                // Test 6: Save comprehensive test file
                Console.WriteLine("6. Creating comprehensive test file...");
                var comprehensiveTest = new StringBuilder();
                comprehensiveTest.AppendLine("<!DOCTYPE html>");
                comprehensiveTest.AppendLine("<html lang=\"en\">");
                comprehensiveTest.AppendLine("<head>");
                comprehensiveTest.AppendLine("    <meta charset=\"UTF-8\">");
                comprehensiveTest.AppendLine("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">");
                comprehensiveTest.AppendLine("    <title>Cytoscape.js Implementation Verification</title>");
                comprehensiveTest.AppendLine("    <script src=\"https://unpkg.com/cytoscape@3.26.0/dist/cytoscape.min.js\"></script>");
                comprehensiveTest.AppendLine("    <script src=\"https://unpkg.com/dagre@0.8.5/dist/dagre.min.js\"></script>");
                comprehensiveTest.AppendLine("    <script src=\"https://unpkg.com/cytoscape-dagre@2.5.0/cytoscape-dagre.js\"></script>");
                comprehensiveTest.AppendLine("    <style>");
                comprehensiveTest.AppendLine("        .container { max-width: 1600px; margin: 0 auto; padding: 20px; }");
                comprehensiveTest.AppendLine("        .cytoscape-container { width: 100%; height: 800px; border: 2px solid #ccc; margin: 20px 0; }");
                comprehensiveTest.AppendLine("        .test-section { margin: 30px 0; }");
                comprehensiveTest.AppendLine("        .test-title { font-size: 1.5em; font-weight: bold; margin-bottom: 10px; }");
                comprehensiveTest.AppendLine("    </style>");
                comprehensiveTest.AppendLine("</head>");
                comprehensiveTest.AppendLine("<body>");
                comprehensiveTest.AppendLine("    <div class=\"container\">");
                comprehensiveTest.AppendLine("        <h1>Cytoscape.js Implementation Verification</h1>");
                
                comprehensiveTest.AppendLine("        <div class=\"test-section\">");
                comprehensiveTest.AppendLine("            <div class=\"test-title\">1. Pipeline Flow Diagram</div>");
                comprehensiveTest.AppendLine(completeHtml.Substring(completeHtml.IndexOf("<div"), completeHtml.LastIndexOf("</div>") + 6));
                comprehensiveTest.AppendLine("        </div>");
                
                comprehensiveTest.AppendLine("        <div class=\"test-section\">");
                comprehensiveTest.AppendLine("            <div class=\"test-title\">2. Router Diagram</div>");
                comprehensiveTest.AppendLine(routerDiagram);
                comprehensiveTest.AppendLine("        </div>");
                
                comprehensiveTest.AppendLine("    </div>");
                comprehensiveTest.AppendLine("</body>");
                comprehensiveTest.AppendLine("</html>");
                
                string comprehensiveFileName = "cytoscape_comprehensive_verification.html";
                File.WriteAllText(comprehensiveFileName, comprehensiveTest.ToString());
                Console.WriteLine($"✓ Comprehensive test file saved as: {comprehensiveFileName}");
                
                Console.WriteLine("\n=== Test Summary ===");
                Console.WriteLine("✓ All Cytoscape.js components appear to be working correctly");
                Console.WriteLine("✓ Diagrams should now be wider and more visible");
                Console.WriteLine("✓ Horizontal (left-to-right) layout is configured");
                Console.WriteLine("✓ Enhanced CSS sizing is in place");
                Console.WriteLine($"✓ Test files created: {testFileName}, {comprehensiveFileName}");
                Console.WriteLine("\n=== Next Steps ===");
                Console.WriteLine("1. Open the test HTML files in a browser to verify diagram appearance");
                Console.WriteLine("2. Run the main application to generate documentation with new diagrams");
                Console.WriteLine("3. Verify that Pipeline Flow Diagrams are no longer narrow/obscured");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error during testing: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine("\nPress any key to continue...");
            Console.ReadKey();
        }
    }
}
