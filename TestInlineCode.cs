using System;
using System.Reflection;
using System.Text.RegularExpressions;

namespace SnapAnalyser.InlineCodeTester
{    public class InlineCodeTestProgram
    {
        public static void Main()
        {
            Console.WriteLine("Testing inline code fix...");
            RunInlineCodeTest();
        }
        
        public static void RunInlineCodeTest()
        {
            // Create a test markdown string with inline code
            var testMarkdown = "No Pass-through: With `autoCommit` set to `True`, only mapped fields are included.";
            Console.WriteLine($"Original markdown: {testMarkdown}");
            
            // Get the DocumentationGenerator instance
            var docGen = new DocumentationGenerator();
            
            // Get the ConvertMarkdownToHtml method using reflection
            var methodInfo = typeof(DocumentationGenerator).GetMethod(
                "ConvertMarkdownToHtml", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            if (methodInfo == null)
            {
                Console.WriteLine("ERROR: Could not find ConvertMarkdownToHtml method");
                return;
            }
            
            // Convert the markdown to HTML
            var html = (string)methodInfo.Invoke(docGen, new object[] { testMarkdown });
            Console.WriteLine($"Converted HTML: {html}");
            
            // Check for the presence of placeholders
            if (html.Contains("INLINE") || html.Contains("CODE"))
            {
                Console.WriteLine("FAILED: Output contains placeholder text");
                
                // Check specifically for placeholders
                if (html.Contains("!!INLINECODE"))
                {
                    Console.WriteLine("Found new style placeholder (!!INLINECODE)");
                }
                
                if (html.Contains("INLINE_CODE_"))
                {
                    Console.WriteLine("Found old style placeholder (INLINE_CODE_)");
                }
            }
            else
            {
                Console.WriteLine("SUCCESS: No placeholders found in output");
                
                // Check if code tags are present
                if (html.Contains("<code>autoCommit</code>") && html.Contains("<code>True</code>"))
                {
                    Console.WriteLine("Code tags were correctly inserted");
                }
                else
                {
                    Console.WriteLine("ERROR: Code tags are missing or incorrect");
                }
            }
            
            // Try another test case specifically for the issue observed in the generated HTML
            var complexMarkdown = "**Pass-through Properties:** With `passThrough` set to `True`, all fields from the input document are included in the output payload, even if they're not explicitly mapped.";
            Console.WriteLine($"\nTesting complex case: {complexMarkdown}");
            
            html = (string)methodInfo.Invoke(docGen, new object[] { complexMarkdown });
            Console.WriteLine($"Converted HTML: {html}");
            
            // Check if placeholders exist
            if (html.Contains("INLINE") || html.Contains("CODE"))
            {
                Console.WriteLine("FAILED: Complex test output contains placeholder text");
            }
            else
            {
                Console.WriteLine("SUCCESS: Complex test contains no placeholders");
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
