# Fix the accountJson scope issue

$filePath = "DocumentationGenerator.cs"

Write-Host "Fixing accountJson scope issue..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Fix the out-of-scope reference
$badLine = 'Console.WriteLine($"[DB-CONNECTION] JSON that failed: {accountJson}");'
$goodLine = 'Console.WriteLine($"[DB-CONNECTION] JSON parsing failed - see details above");'

$content = $content.Replace($badLine, $goodLine)

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Fixed accountJson scope issue!" -ForegroundColor Green
