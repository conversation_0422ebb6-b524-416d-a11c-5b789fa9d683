using System;
using System.Collections.Generic;
using System.Linq;
using SnapAnalyser;

namespace SnapAnalyser
{
    class TestJoinEnhanced
    {
        static void Main()
        {
            Console.WriteLine("Testing Join Snap Enhanced Configuration");
            Console.WriteLine("=====================================");

            // Create a mock Join snap with typical properties
            var joinSnap = new SnapNode
            {
                Id = "join-test-001",
                Label = "Test Join",
                Type = "com-snaplogic-snaps-transform-multijoin",
                Properties = new List<KeyValuePair<string, string>>
                {
                    new KeyValuePair<string, string>("joinType", "Inner"),
                    new KeyValuePair<string, string>("settings.joinPaths.value.0.leftPath", "$Property"),
                    new KeyValuePair<string, string>("settings.joinPaths.value.0.rightPath", "$nch_propertytechnicalrecordsid"),
                    new KeyValuePair<string, string>("settings.joinPaths.value.1.leftPath", "$Status"),
                    new KeyValuePair<string, string>("settings.joinPaths.value.1.rightPath", "$status"),
                    new KeyValuePair<string, string>("execution_mode", "Validate & Execute"),
                    new KeyValuePair<string, string>("passThrough", "false")
                },
                InputConnections = new List<SnapConnection>
                {
                    new SnapConnection { SourceId = "source1", TargetId = "join-test-001" },
                    new SnapConnection { SourceId = "source2", TargetId = "join-test-001" }
                }
            };

            var sourceSnap1 = new SnapNode
            {
                Id = "source1",
                Label = "Property Data",
                Type = "com-snaplogic-snaps-database-select"
            };

            var sourceSnap2 = new SnapNode
            {
                Id = "source2",
                Label = "CRM Data",
                Type = "com-snaplogic-snaps-database-select"
            };

            var allSnaps = new List<SnapNode> { joinSnap, sourceSnap1, sourceSnap2 };

            Console.WriteLine("1. Testing FlowControlConfigurationGenerator.GenerateJoinConfiguration()");
            Console.WriteLine("========================================================================");

            try
            {
                var flowConfigGenerator = new FlowControlConfigurationGenerator();
                string joinConfig = flowConfigGenerator.GenerateJoinConfiguration(joinSnap, allSnaps);
                
                if (!string.IsNullOrEmpty(joinConfig))
                {
                    Console.WriteLine("SUCCESS: Enhanced Join configuration generated:");
                    Console.WriteLine(joinConfig);
                }
                else
                {
                    Console.WriteLine("ISSUE: Enhanced Join configuration returned empty string");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR: Exception in GenerateJoinConfiguration: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine("\n2. Testing SnapBestPractices.GetSnapBestPractices() for Join snap");
            Console.WriteLine("================================================================");

            try
            {
                string bestPractices = SnapBestPractices.GetSnapBestPractices(joinSnap, allSnaps, false);
                
                if (!string.IsNullOrEmpty(bestPractices))
                {
                    Console.WriteLine("SUCCESS: Best practices generated:");
                    Console.WriteLine("Content length: " + bestPractices.Length);
                    
                    // Check if it contains enhanced configuration or raw configuration
                    if (bestPractices.Contains("Join Configuration:") && bestPractices.Contains("Join Type:"))
                    {
                        Console.WriteLine("✓ Contains enhanced Join configuration elements");
                    }
                    else if (bestPractices.Contains("Configuration:"))
                    {
                        Console.WriteLine("⚠ Contains basic/raw configuration section");
                    }
                    else
                    {
                        Console.WriteLine("? Unknown configuration format");
                    }
                    
                    // Show a snippet of the output
                    var snippet = bestPractices.Length > 500 ? bestPractices.Substring(0, 500) + "..." : bestPractices;
                    Console.WriteLine("\nFirst 500 characters:");
                    Console.WriteLine(snippet);
                }
                else
                {
                    Console.WriteLine("ERROR: Best practices returned empty string");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR: Exception in GetSnapBestPractices: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine("\n3. Testing with actual Join snap properties from pipeline");
            Console.WriteLine("========================================================");

            // Load actual Join snap data from the pipeline to test with real data
            try
            {
                var parser = new PipelineJsonParser();
                var pipelineFile = @"c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Snap-Pipelines\P01 - Smoke Alarm Attribute CRM_2025_05_21.json";
                
                if (System.IO.File.Exists(pipelineFile))
                {
                    var snaps = parser.ParsePipelineJson(pipelineFile);
                    var realJoinSnap = snaps.FirstOrDefault(s => s.Type.Contains("join"));
                    
                    if (realJoinSnap != null)
                    {
                        Console.WriteLine($"Found real Join snap: {realJoinSnap.Label} ({realJoinSnap.Type})");
                        Console.WriteLine($"Properties count: {realJoinSnap.Properties.Count}");
                        
                        // Test with real data
                        var flowConfigGenerator = new FlowControlConfigurationGenerator();
                        string realJoinConfig = flowConfigGenerator.GenerateJoinConfiguration(realJoinSnap, snaps);
                        
                        if (!string.IsNullOrEmpty(realJoinConfig))
                        {
                            Console.WriteLine("✓ Enhanced configuration generated for real Join snap");
                            Console.WriteLine($"Config length: {realJoinConfig.Length}");
                        }
                        else
                        {
                            Console.WriteLine("⚠ No enhanced configuration generated for real Join snap");
                            
                            // Debug: Show what properties the real Join snap has
                            Console.WriteLine("Real Join snap properties:");
                            foreach (var prop in realJoinSnap.Properties.Take(10))
                            {
                                Console.WriteLine($"  {prop.Key}: {(prop.Value.Length > 50 ? prop.Value.Substring(0, 50) + "..." : prop.Value)}");
                            }
                        }
                    }
                    else
                    {
                        Console.WriteLine("No Join snap found in the pipeline");
                    }
                }
                else
                {
                    Console.WriteLine("Pipeline file not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR: Exception testing with real data: {ex.Message}");
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
