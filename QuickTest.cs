using System;
using System.Collections.Generic;

class QuickTest 
{
    static void Main()
    {
        Console.WriteLine("=== Testing Mapping Table Fix ===");
        
        // Create a mock snap with mapping properties
        var snap = new Dictionary<string, object>
        {
            ["name"] = "TestSnap",
            ["type"] = "Mapper",
            ["property_map"] = new Dictionary<string, object>
            {
                ["source_field"] = "target_field",
                ["input_data"] = "output_data"
            }
        };

        var allSnaps = new List<Dictionary<string, object>> { snap };

        try 
        {
            Console.WriteLine("\n--- Test 1: hasMappings = true (should NOT show mapping tables) ---");
            var result1 = SnapBestPractices.GetSnapBestPractices(snap, allSnaps, hasMappings: true);
            bool hasMappingTable1 = result1.Contains("| Field") || result1.Contains("Mapping Table");
            Console.WriteLine($"Contains mapping table: {hasMappingTable1}");
            Console.WriteLine($"Result length: {result1.Length} characters");
            
            Console.WriteLine("\n--- Test 2: hasMappings = false (should show mapping tables) ---");
            var result2 = SnapBestPractices.GetSnapBestPractices(snap, allSnaps, hasMappings: false);
            bool hasMappingTable2 = result2.Contains("| Field") || result2.Contains("Mapping Table");
            Console.WriteLine($"Contains mapping table: {hasMappingTable2}");
            Console.WriteLine($"Result length: {result2.Length} characters");
            
            Console.WriteLine("\n=== TEST RESULTS ===");
            Console.WriteLine($"Test 1 (hasMappings=true): {(hasMappingTable1 ? "FAILED - Found mapping table when shouldn't" : "PASSED - No mapping table found")}");
            Console.WriteLine($"Test 2 (hasMappings=false): {(hasMappingTable2 ? "PASSED - Found mapping table as expected" : "FAILED - No mapping table found when should")}");
            
            if (!hasMappingTable1 && hasMappingTable2)
            {
                Console.WriteLine("\n✅ ALL TESTS PASSED - Fix is working correctly!");
            }
            else
            {
                Console.WriteLine("\n❌ TESTS FAILED - Fix needs more work");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error during test: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        
        Console.WriteLine("\nPress any key to continue...");
        Console.ReadKey();
    }
}
