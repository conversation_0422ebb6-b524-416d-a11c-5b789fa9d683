using System.Text;

namespace SnapAnalyser
{
    public class DiagramGenerator
    {
        private readonly CytoscapeJsGenerator _cytoscapeGenerator;
        
        public DiagramGenerator()
        {
            _cytoscapeGenerator = new CytoscapeJsGenerator();
        }
        
        public string GenerateDiagram(PipelineData pipeline)
        {
            // Generate cytoscape.js elements JSON
            string elementsJson = _cytoscapeGenerator.GenerateCytoscapeElements(pipeline);
            
            // Generate HTML with cytoscape.js rendering
            return _cytoscapeGenerator.GenerateCytoscapeHtml(elementsJson, "main-pipeline-diagram", "Pipeline Flow Diagram");
        }
    }
}
