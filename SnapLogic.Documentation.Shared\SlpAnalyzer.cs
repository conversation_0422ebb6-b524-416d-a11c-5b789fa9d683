using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace SnapLogic.Documentation.Shared
{
    public class SlpAnalyzer
    {
        public PipelineData AnalyzePipeline(string slpContent)
        {
            try
            {
                JObject pipelineJson = JObject.Parse(slpContent);
                
                PipelineData pipeline = new PipelineData
                {
                    Name = GetPipelineName(pipelineJson),
                    Author = GetPipelineAuthor(pipelineJson),
                    Parameters = GetPipelineParameters(pipelineJson),
                    Snaps = GetSnaps(pipelineJson),
                    Links = GetLinks(pipelineJson),
                    RawPipelineJson = pipelineJson
                };
                
                // Post-processing to enrich the model
                EnrichSnapData(pipeline);
                CalculateSnapCategories(pipeline);
                
                return pipeline;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error analyzing pipeline: {ex.Message}", ex);
            }
        }

        private string GetPipelineName(JObject pipelineJson)
        {
            return pipelineJson.SelectToken("property_map.info.label.value")?.ToString() ?? "Unnamed Pipeline";
        }

        private string GetPipelineAuthor(JObject pipelineJson)
        {
            return pipelineJson.SelectToken("property_map.info.author.value")?.ToString() ?? "Unknown";
        }

        private List<PipelineParameter> GetPipelineParameters(JObject pipelineJson)
        {
            var parameters = new List<PipelineParameter>();
            var paramsArray = pipelineJson.SelectToken("property_map.settings.param_table.value") as JArray;

            if (paramsArray != null)
            {
                foreach (var param in paramsArray)
                {
                    parameters.Add(new PipelineParameter
                    {
                        Key = param.SelectToken("key.value")?.ToString() ?? "",
                        Value = param.SelectToken("value.value")?.ToString() ?? "",
                        DataType = param.SelectToken("data_type.value")?.ToString() ?? "String",
                        Required = param.SelectToken("required.value")?.ToString() == "true",
                        Description = param.SelectToken("description.value")?.ToString() ?? ""
                    });
                }
            }

            return parameters;
        }

        private List<SnapNode> GetSnaps(JObject pipelineJson)
        {
            var snaps = new List<SnapNode>();
            var snapMap = pipelineJson.SelectToken("snap_map") as JObject;
            var renderMap = pipelineJson.SelectToken("render_map.detail_map") as JObject;

            if (snapMap != null)
            {
                foreach (var snapProperty in snapMap.Properties())
                {
                    var snapData = snapProperty.Value as JObject;
                    if (snapData != null)
                    {
                        var snap = new SnapNode
                        {
                            Id = snapProperty.Name,
                            Label = snapData.SelectToken("property_map.info.label.value")?.ToString() ?? "Unknown Snap",
                            Type = snapData.SelectToken("class_id")?.ToString() ?? "unknown"
                        };

                        // Extract position from render_map
                        if (renderMap != null)
                        {
                            var renderData = renderMap.SelectToken(snapProperty.Name);
                            if (renderData != null)
                            {
                                snap.Position = new Position
                                {
                                    X = renderData.SelectToken("x")?.ToObject<int>() ?? 0,
                                    Y = renderData.SelectToken("y")?.ToObject<int>() ?? 0
                                };
                            }
                        }

                        // Extract properties
                        var propertyMap = snapData.SelectToken("property_map") as JObject;
                        if (propertyMap != null)
                        {
                            ExtractSnapProperties(snap, propertyMap);
                        }

                        snaps.Add(snap);
                    }
                }
            }

            return snaps;
        }

        private void ExtractSnapProperties(SnapNode snap, JObject propertyMap)
        {
            foreach (var property in propertyMap.Properties())
            {
                if (property.Value is JObject propObj)
                {
                    var value = propObj.SelectToken("value");
                    if (value != null)
                    {
                        snap.Properties[property.Name] = value.ToString();
                    }
                }
            }
        }

        private List<SnapLink> GetLinks(JObject pipelineJson)
        {
            var links = new List<SnapLink>();
            var linkMap = pipelineJson.SelectToken("link_map") as JObject;

            if (linkMap != null)
            {
                foreach (var linkProperty in linkMap.Properties())
                {
                    var linkData = linkProperty.Value as JObject;
                    if (linkData != null)
                    {
                        links.Add(new SnapLink
                        {
                            Id = linkProperty.Name,
                            SourceId = linkData.SelectToken("src_id")?.ToString() ?? "",
                            TargetId = linkData.SelectToken("dst_id")?.ToString() ?? "",
                            SourceViewId = linkData.SelectToken("src_view_id")?.ToString() ?? "",
                            TargetViewId = linkData.SelectToken("dst_view_id")?.ToString() ?? ""
                        });
                    }
                }
            }

            return links;
        }

        private void EnrichSnapData(PipelineData pipeline)
        {
            // Add source and target connections to each snap
            foreach (var link in pipeline.Links)
            {
                var sourceSnap = pipeline.Snaps.FirstOrDefault(s => s.Id == link.SourceId);
                var targetSnap = pipeline.Snaps.FirstOrDefault(s => s.Id == link.TargetId);

                if (sourceSnap != null)
                {
                    sourceSnap.OutputConnections.Add(link);
                }

                if (targetSnap != null)
                {
                    targetSnap.InputConnections.Add(link);
                }
            }

            // Identify start and end points of the pipeline
            foreach (var snap in pipeline.Snaps)
            {
                if (snap.InputConnections.Count == 0)
                {
                    snap.IsStartPoint = true;
                }

                if (snap.OutputConnections.Count == 0)
                {
                    snap.IsEndPoint = true;
                }
            }
        }

        private void CalculateSnapCategories(PipelineData pipeline)
        {
            foreach (var snap in pipeline.Snaps)
            {
                // Check for router/flow control snaps FIRST (pure flow control)
                if (snap.Type.Contains("router") || 
                    snap.Type.Contains("flow") || 
                    snap.Type.Contains("branch") || 
                    snap.Type.Contains("switch") || 
                    snap.Type.Contains("gate") || 
                    snap.Type.Contains("join") ||
                    snap.Type.Contains("copy") ||
                    snap.Type.Contains("filter"))
                {
                    snap.Category = SnapCategory.FlowControl;
                }
                // Database operations
                else if (snap.Type.Contains("sql") || 
                         snap.Type.Contains("sqlserver") || 
                         snap.Type.Contains("database") || 
                         snap.Type.Contains("insert") || 
                         snap.Type.Contains("update") || 
                         snap.Type.Contains("select") ||
                         snap.Type.Contains("execute") ||
                         snap.Type.Contains("mysql") ||
                         snap.Type.Contains("oracle") ||
                         snap.Type.Contains("postgres"))
                {
                    snap.Category = SnapCategory.Database;
                }
                else if (snap.Type.Contains("dynamics"))
                {
                    snap.Category = SnapCategory.ExternalSystem;
                }
                // File operations
                else if (snap.Type.Contains("file") || 
                         snap.Type.Contains("csv") || 
                         snap.Type.Contains("excel") || 
                         snap.Type.Contains("json") || 
                         snap.Type.Contains("xml"))
                {
                    snap.Category = SnapCategory.FileOperation;
                }
                // Transformation operations
                else if (snap.Type.Contains("mapper") || 
                         snap.Type.Contains("transform") || 
                         snap.Type.Contains("aggregate") || 
                         snap.Type.Contains("sort") || 
                         snap.Type.Contains("group"))
                {
                    snap.Category = SnapCategory.Transformation;
                }
                // Error handling
                else if (snap.Type.Contains("error") || 
                         snap.Type.Contains("exception"))
                {
                    snap.Category = SnapCategory.ErrorHandling;
                }
                else
                {
                    snap.Category = SnapCategory.Other;
                }
            }
        }
    }
}
