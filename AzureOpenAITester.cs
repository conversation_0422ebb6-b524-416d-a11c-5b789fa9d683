using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace SnapAnalyser
{
    public class AzureOpenAITester
    {
        private readonly string _apiKey;
        private readonly string _endpoint;
        private readonly string _deploymentName;
          public AzureOpenAITester()
        {
            _apiKey = ConfigManager.OpenAIApiKey;
            _endpoint = ConfigManager.AzureOpenAIEndpoint;
            _deploymentName = ConfigManager.AzureOpenAIDeploymentName;
        }
          public async Task<bool> TestConnection()
        {
            if (string.IsNullOrEmpty(_apiKey))
            {
                Console.WriteLine("API key is empty. Please configure the Azure OpenAI API key.");
                return false;
            }
            
            try
            {
                var apiVersion = "2023-05-15";
                var apiUrl = $"{_endpoint.TrimEnd('/')}/openai/deployments/{_deploymentName}/chat/completions?api-version={apiVersion}";
                
                // Log connection details
                Console.WriteLine($"Testing connection to endpoint: {_endpoint}");
                Console.WriteLine($"Deployment name: {_deploymentName}");
                Console.WriteLine($"API version: {apiVersion}");
                
                // Configure HttpClient with proxy settings if needed in corporate environment
                var httpClientHandler = new HttpClientHandler
                {
                    UseDefaultCredentials = true, // Use Windows credentials for proxy authentication
                    UseProxy = true
                };
                
                using (var client = new HttpClient(httpClientHandler))
                {
                    // Set the API key in the header
                    client.DefaultRequestHeaders.Add("api-key", _apiKey);
                      // Set a short timeout to avoid long waits
                    client.Timeout = TimeSpan.FromSeconds(ConfigManager.AzureOpenAITimeoutSeconds);
                    
                    // Create a simple request object for testing
                    var requestObj = new 
                    {
                        messages = new[]
                        {
                            new { role = "system", content = "You are a helpful assistant." },
                            new { role = "user", content = "This is a test message to verify the connection. Please respond with 'Connection successful'." }
                        },
                        temperature = 0.7,
                        max_tokens = 20
                    };
                    
                    var jsonContent = JsonSerializer.Serialize(requestObj);
                    var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                    
                    Console.WriteLine($"Sending test request to Azure OpenAI: {apiUrl}");
                    
                    var response = await client.PostAsync(apiUrl, content);
                    var responseBody = await response.Content.ReadAsStringAsync();
                    
                    Console.WriteLine($"Azure OpenAI test response status: {response.StatusCode}");
                    
                    if (response.IsSuccessStatusCode)
                    {
                        Console.WriteLine("Test connection to Azure OpenAI successful!");
                        return true;
                    }
                    else
                    {
                        Console.WriteLine($"Test connection failed: {responseBody}");
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception during Azure OpenAI test connection: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                return false;
            }
        }
        
        public async Task<string> TestWithResponse()
        {
            if (string.IsNullOrEmpty(_apiKey))
            {
                return "API key is empty. Please configure the Azure OpenAI API key.";
            }
            
            try
            {
                var apiVersion = "2023-05-15";
                var apiUrl = $"{_endpoint.TrimEnd('/')}/openai/deployments/{_deploymentName}/chat/completions?api-version={apiVersion}";
                
                // Configure HttpClient with proxy settings if needed in corporate environment
                var httpClientHandler = new HttpClientHandler
                {
                    UseDefaultCredentials = true,
                    UseProxy = true
                };
                
                using (var client = new HttpClient(httpClientHandler))                {
                    client.DefaultRequestHeaders.Add("api-key", _apiKey);
                    client.Timeout = TimeSpan.FromSeconds(ConfigManager.AzureOpenAITimeoutSeconds);
                    
                    var requestObj = new 
                    {
                        messages = new[]
                        {
                            new { role = "system", content = "You are a helpful assistant." },
                            new { role = "user", content = "This is a test message to verify the connection. Please respond with 'Connection successful'." }
                        },
                        temperature = 0.7,
                        max_tokens = 50
                    };
                    
                    var jsonContent = JsonSerializer.Serialize(requestObj);
                    var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                    
                    var response = await client.PostAsync(apiUrl, content);
                    var responseBody = await response.Content.ReadAsStringAsync();
                    
                    if (response.IsSuccessStatusCode)
                    {
                        try
                        {
                            using (JsonDocument document = JsonDocument.Parse(responseBody))
                            {
                                JsonElement root = document.RootElement;
                                  // Navigate to the first choice's message content if available
                                if (root.TryGetProperty("choices", out JsonElement choices) && 
                                    choices.GetArrayLength() > 0 && 
                                    choices[0].TryGetProperty("message", out JsonElement message) &&
                                    message.TryGetProperty("content", out JsonElement contentElement))
                                {
                                    return $"Connection successful! Response: {contentElement.GetString()}";
                                }
                                
                                return $"Connection successful but couldn't parse response: {responseBody}";
                            }
                        }
                        catch (JsonException)
                        {
                            return $"Connection successful but invalid JSON response: {responseBody}";
                        }
                    }
                    else
                    {
                        return $"Connection failed ({response.StatusCode}): {responseBody}";
                    }
                }
            }
            catch (Exception ex)
            {
                return $"Error: {ex.Message}";
            }
        }
        
        public async Task<string> GetDeploymentInfo()
        {
            if (string.IsNullOrEmpty(_apiKey))
            {
                return "API key is empty. Please configure the Azure OpenAI API key.";
            }
            
            try
            {
                var apiVersion = "2023-05-15";
                var apiUrl = $"{_endpoint.TrimEnd('/')}/openai/deployments?api-version={apiVersion}";
                
                // Configure HttpClient with proxy settings if needed in corporate environment
                var httpClientHandler = new HttpClientHandler
                {
                    UseDefaultCredentials = true,
                    UseProxy = true
                };
                
                using (var client = new HttpClient(httpClientHandler))
                {
                    client.DefaultRequestHeaders.Add("api-key", _apiKey);
                    client.Timeout = TimeSpan.FromSeconds(ConfigManager.AzureOpenAITimeoutSeconds);
                    
                    Console.WriteLine($"Requesting deployment information from: {apiUrl}");
                    
                    var response = await client.GetAsync(apiUrl);
                    var responseBody = await response.Content.ReadAsStringAsync();
                    
                    if (response.IsSuccessStatusCode)
                    {
                        try
                        {
                            using (JsonDocument document = JsonDocument.Parse(responseBody))
                            {
                                var sb = new StringBuilder();
                                sb.AppendLine("Azure OpenAI Deployments:");
                                
                                if (document.RootElement.TryGetProperty("data", out JsonElement data))
                                {
                                    foreach (var deployment in data.EnumerateArray())
                                    {
                                        if (deployment.TryGetProperty("id", out JsonElement id) &&
                                            deployment.TryGetProperty("model", out JsonElement model))
                                        {
                                            sb.AppendLine($"- {id.GetString()}: {model.GetString()}");
                                            
                                            // Add status if available
                                            if (deployment.TryGetProperty("status", out JsonElement status))
                                            {
                                                sb.AppendLine($"  Status: {status.GetString()}");
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    sb.AppendLine("No deployment data found in the response.");
                                }
                                
                                return sb.ToString();
                            }
                        }
                        catch (JsonException ex)
                        {
                            return $"Connection successful but error parsing JSON response: {ex.Message}\n\nRaw response: {responseBody}";
                        }
                    }
                    else
                    {
                        return $"Failed to get deployment information ({response.StatusCode}): {responseBody}";
                    }
                }
            }
            catch (Exception ex)
            {
                return $"Error requesting deployment information: {ex.Message}";
            }
        }
    }
}
