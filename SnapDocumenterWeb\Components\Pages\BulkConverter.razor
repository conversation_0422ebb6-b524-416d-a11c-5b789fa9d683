@page "/bulk-converter"
@using System.Linq
@using SnapLogic.Documentation.Shared
@using SnapDocumenterWeb.Services
@inject FileProcessingService FileProcessor
@inject ProjectService ProjectService
@inject IJSRuntime JSRuntime
@inject ILogger<BulkConverter> Logger
@rendermode InteractiveServer

<PageTitle>Bulk SLP Converter - SnapLogic Pipeline Documenter</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-file-alt me-2"></i>
                Bulk SLP File Processor
            </h1>
        </div>
    </div>

    <div class="row">
        <!-- File Upload Section -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-upload me-2"></i>
                        SLP Files
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <InputFile OnChange="OnFileSelected" multiple accept=".slp" class="form-control" />
                        <div class="form-text">Select one or more .slp files to process</div>
                    </div>

                    @if (!string.IsNullOrEmpty(fileUploadError))
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            @fileUploadError
                            <button type="button" class="btn-close" @onclick="() => fileUploadError = string.Empty"></button>
                        </div>
                    }

                    @if (uploadedFiles.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>File Name</th>
                                        <th>Size</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var file in uploadedFiles)
                                    {
                                        <tr>
                                            <td>@file.FileName</td>
                                            <td>@FormatFileSize(file.Size)</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-danger" 
                                                        @onclick="() => RemoveFile(file)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                </div>
            </div>

            <!-- Processing Options -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        Processing Options
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <InputCheckbox @bind-Value="processingOptions.GenerateHtml" class="form-check-input" id="chkHtml" />
                                <label class="form-check-label" for="chkHtml">
                                    Generate HTML Documentation
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <InputCheckbox @bind-Value="processingOptions.GeneratePdf" class="form-check-input" id="chkPdf" />
                                <label class="form-check-label" for="chkPdf">
                                    Generate PDF Documentation
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <InputCheckbox @bind-Value="processingOptions.UseAI" class="form-check-input" id="chkAI" />
                                <label class="form-check-label" for="chkAI">
                                    Use AI for Pipeline Overview
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <InputCheckbox @bind-Value="processingOptions.UseCachedDescriptions" class="form-check-input" id="chkCache" />
                                <label class="form-check-label" for="chkCache">
                                    Use Cached AI Descriptions
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Project Management Section -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-project-diagram me-2"></i>
                        Project Management
                    </h5>
                </div>
                <div class="card-body">
                    @if (currentProject != null)
                    {
                        <div class="alert alert-info">
                            <strong>@currentProject.Name</strong><br />
                            <small>@currentProject.Description</small>
                        </div>
                    }
                    else
                    {
                        <p class="text-muted">No project loaded</p>
                    }

                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" @onclick="CreateNewProject">
                            <i class="fas fa-plus me-1"></i> New Project
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" @onclick="LoadProject">
                            <i class="fas fa-folder-open me-1"></i> Load Project
                        </button>
                        <button class="btn btn-outline-success btn-sm" @onclick="SaveProject" disabled="@(currentProject == null)">
                            <i class="fas fa-save me-1"></i> Save Project
                        </button>
                        <button class="btn btn-outline-info btn-sm" @onclick="EditProject" disabled="@(currentProject == null)">
                            <i class="fas fa-edit me-1"></i> Edit Project
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Processing Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-play me-2"></i>
                        Processing
                    </h5>
                    <button class="btn btn-primary" 
                            @onclick="ProcessFiles" 
                            disabled="@(!CanProcess || isProcessing)">
                        @if (isProcessing)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            <text>Processing...</text>
                        }
                        else
                        {
                            <i class="fas fa-play me-2"></i>
                            <text>Process Files</text>
                        }
                    </button>
                </div>
                <div class="card-body">
                    @if (isProcessing && currentProgress != null)
                    {
                        <div class="mb-3">
                            <div class="d-flex justify-content-between mb-1">
                                <span>@currentProgress.Status</span>
                                <span>@currentProgress.ProcessedFiles / @currentProgress.TotalFiles</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" 
                                     role="progressbar" 
                                     style="width: @(currentProgress.PercentComplete)%"
                                     aria-valuenow="@currentProgress.PercentComplete" 
                                     aria-valuemin="0" 
                                     aria-valuemax="100">
                                    @currentProgress.PercentComplete%
                                </div>
                            </div>
                        </div>
                        
                        @if (!string.IsNullOrEmpty(currentProgress?.CurrentFileName))
                        {
                            <p class="text-muted mb-0">
                                <i class="fas fa-file me-1"></i>
                                Currently processing: @currentProgress?.CurrentFileName
                            </p>
                        }
                    }

                    @if (lastResult != null)
                    {
                        <div class="mt-3">
                            @if (lastResult.Errors.Any())
                            {
                                <div class="alert alert-warning">
                                    <h6>Processing completed with errors:</h6>
                                    <ul class="mb-0">
                                        @foreach (var error in lastResult.Errors)
                                        {
                                            <li>@error</li>
                                        }
                                    </ul>
                                </div>
                            }
                            else
                            {
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    Successfully processed @lastResult.ProcessedFiles.Count file(s)!
                                </div>
                            }

                            @if (!string.IsNullOrEmpty(lastResult.ZipFilePath) || !string.IsNullOrEmpty(lastResult.DownloadUrl))
                            {
                                <button class="btn btn-success" @onclick="DownloadResults">
                                    <i class="fas fa-download me-2"></i>
                                    Download Documentation ZIP
                                </button>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Project Modal -->
<ProjectModal @ref="projectModal"
              Project="@currentProject"
              IsEditMode="@isEditingProject"
              OnProjectSaved="@OnProjectSaved" />

@code {
    private List<UploadedFile> uploadedFiles = new();
    private ProcessingOptions processingOptions = new() { GenerateHtml = true };
    private ProjectData? currentProject;
    private bool isProcessing = false;
    private ProcessingProgress? currentProgress;
    private ProcessingResult? lastResult;
    private CancellationTokenSource? processingCancellation;
    private ProjectModal? projectModal;
    private bool isEditingProject = false;
    private string fileUploadError = string.Empty;

    private bool CanProcess => uploadedFiles.Any() && 
                              (processingOptions.GenerateHtml || processingOptions.GeneratePdf) && 
                              !isProcessing;

    private async Task OnFileSelected(InputFileChangeEventArgs e)
    {
        try
        {
            foreach (var file in e.GetMultipleFiles(10)) // Limit to 10 files
            {
                if (file.Name.EndsWith(".slp", StringComparison.OrdinalIgnoreCase))
                {
                    using var stream = file.OpenReadStream(maxAllowedSize: 10 * 1024 * 1024); // 10MB limit
                    using var reader = new StreamReader(stream);
                    var content = await reader.ReadToEndAsync();

                    var uploadedFile = new UploadedFile
                    {
                        FileName = file.Name,
                        Content = content,
                        Size = file.Size
                    };

                    if (!uploadedFiles.Any(f => f.FileName == uploadedFile.FileName))
                    {
                        uploadedFiles.Add(uploadedFile);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error uploading files");
            fileUploadError = $"Error uploading files: {ex.Message}";
            StateHasChanged();
        }
    }

    private void RemoveFile(UploadedFile file)
    {
        uploadedFiles.Remove(file);
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    private async Task ProcessFiles()
    {
        if (!CanProcess) return;

        isProcessing = true;
        lastResult = null;
        processingCancellation = new CancellationTokenSource();

        try
        {
            var progress = new Progress<ProcessingProgress>(p =>
            {
                currentProgress = p;
                InvokeAsync(StateHasChanged);
            });

            lastResult = await FileProcessor.ProcessFilesAsync(
                uploadedFiles, 
                processingOptions, 
                currentProject, 
                progress, 
                processingCancellation.Token);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during file processing");
            lastResult = new ProcessingResult();
            lastResult.Errors.Add($"Processing error: {ex.Message}");
        }
        finally
        {
            isProcessing = false;
            currentProgress = null;
            processingCancellation?.Dispose();
            processingCancellation = null;
        }
    }

    private async Task DownloadResults()
    {
        var zipPath = lastResult?.ZipFilePath ?? lastResult?.DownloadUrl;
        if (string.IsNullOrEmpty(zipPath))
        {
            await JSRuntime.InvokeVoidAsync("showToast", "No download file available", "warning");
            return;
        }

        try
        {
            Logger.LogInformation("Attempting to download file from path: {ZipPath}", zipPath);
            var fileBytes = await FileProcessor.GetZipFileAsync(zipPath);
            var fileName = "pipeline_documentation.zip";

            Logger.LogInformation("File size: {FileSize} bytes", fileBytes.Length);
            await JSRuntime.InvokeVoidAsync("downloadFile", fileName, Convert.ToBase64String(fileBytes));

            await JSRuntime.InvokeVoidAsync("showToast", "Download started successfully!", "success");

            // Cleanup temp files after download
            FileProcessor.CleanupTempFiles(zipPath);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error downloading results");
            await JSRuntime.InvokeVoidAsync("showToast", $"Error downloading file: {ex.Message}", "danger");
        }
    }

    private async Task CreateNewProject()
    {
        isEditingProject = false;
        currentProject = null;
        if (projectModal != null)
        {
            await projectModal.ShowModal();
        }
    }

    private void LoadProject()
    {
        // For now, just create a sample project
        // In a real implementation, this would show a file picker or project list
        currentProject = ProjectService.CreateNewProject("Sample Project", "A sample project for demonstration", "Testing the project functionality");

        // Sync project settings with form
        processingOptions.GenerateHtml = currentProject.GenerateHtml;
        processingOptions.GeneratePdf = currentProject.GeneratePdf;
        processingOptions.UseAI = currentProject.UseAI;
        processingOptions.UseCachedDescriptions = currentProject.UseCachedDescriptions;

        StateHasChanged();
    }

    private async Task SaveProject()
    {
        if (currentProject == null) return;

        try
        {
            // Update project with current settings
            ProjectService.UpdateProjectSettings(currentProject, processingOptions, uploadedFiles);

            // Export as JSON for download
            var json = ProjectService.ExportProjectAsJson(currentProject);
            var fileName = $"{currentProject.Name.Replace(" ", "_")}_project.json";

            await JSRuntime.InvokeVoidAsync("downloadFile", fileName, Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(json)));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error saving project");
        }
    }

    private async Task EditProject()
    {
        if (currentProject == null) return;

        isEditingProject = true;
        if (projectModal != null)
        {
            await projectModal.ShowModal();
        }
    }

    private void OnProjectSaved(ProjectData project)
    {
        currentProject = project;

        // Sync project settings with form
        processingOptions.GenerateHtml = project.GenerateHtml;
        processingOptions.GeneratePdf = project.GeneratePdf;
        processingOptions.UseAI = project.UseAI;
        processingOptions.UseCachedDescriptions = project.UseCachedDescriptions;

        StateHasChanged();
    }
}
