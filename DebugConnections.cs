using System;
using System.Linq;
using System.IO;
using System.Text.Json;
using Newtonsoft.Json.Linq;

namespace SnapAnalyser
{
    public class DebugConnections
    {
        public static void TestConnectionParsing()
        {
            Console.WriteLine("=== Debugging Connection Parsing ===");

            var pipelineJsonPath = @"c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Snap-Pipelines\P01 - Smoke Alarm Attribute CRM_2025_05_21.json";
            
            if (!File.Exists(pipelineJsonPath))
            {
                Console.WriteLine($"Pipeline file not found: {pipelineJsonPath}");
                return;
            }

            try
            {
                var analyzer = new SlpAnalyzer();
                var pipeline = analyzer.AnalyzePipeline(pipelineJsonPath);

                Console.WriteLine($"Pipeline: {pipeline.Name}");
                Console.WriteLine($"Total Snaps: {pipeline.Snaps.Count}");
                Console.WriteLine($"Total Links: {pipeline.Links.Count}");
                Console.WriteLine();

                // Look specifically at the copy snap connections
                var copySnap = pipeline.Snaps.FirstOrDefault(s => s.Type.Contains("copy"));
                if (copySnap != null)
                {
                    Console.WriteLine($"Found Copy Snap: {copySnap.Label} (ID: {copySnap.Id})");
                    Console.WriteLine($"Type: {copySnap.Type}");
                    Console.WriteLine();

                    Console.WriteLine("Output Connections:");
                    foreach (var conn in copySnap.OutputConnections)
                    {
                        var targetSnap = pipeline.Snaps.FirstOrDefault(s => s.Id == conn.TargetId);
                        Console.WriteLine($"  - Target: {targetSnap?.Label ?? "Unknown"} (ID: {conn.TargetId})");
                        Console.WriteLine($"    SourceViewId: '{conn.SourceViewId}'");
                        Console.WriteLine($"    TargetViewId: '{conn.TargetViewId}'");
                        Console.WriteLine($"    Link ID: {conn.Id}");
                        Console.WriteLine();
                    }

                    // Test the connection info logic manually
                    Console.WriteLine("Manual Connection Info Test:");
                    foreach (var conn in copySnap.OutputConnections)
                    {
                        var targetSnap = pipeline.Snaps.FirstOrDefault(s => s.Id == conn.TargetId);
                        var connectionInfo = conn.SourceViewId ?? "";
                        Console.WriteLine($"  Target: {targetSnap?.Label}");
                        Console.WriteLine($"  ConnectionInfo would be: '{connectionInfo}'");
                        
                        // Test the matching logic from lines 40-41
                        for (int i = 0; i < 2; i++) // Test output0, output1
                        {
                            var outputPattern = $"output{i}";
                            var matches = connectionInfo.Contains(outputPattern, StringComparison.OrdinalIgnoreCase);
                            Console.WriteLine($"    Contains '{outputPattern}': {matches}");
                        }
                        Console.WriteLine();
                    }
                }
                else
                {
                    Console.WriteLine("No copy snap found in pipeline");
                }

                // Also examine all links to see the pattern
                Console.WriteLine("All Links with Copy Snap:");
                foreach (var link in pipeline.Links.Where(l => l.SourceId == copySnap?.Id || l.TargetId == copySnap?.Id))
                {
                    var sourceSnap = pipeline.Snaps.FirstOrDefault(s => s.Id == link.SourceId);
                    var targetSnap = pipeline.Snaps.FirstOrDefault(s => s.Id == link.TargetId);
                    Console.WriteLine($"  {sourceSnap?.Label ?? "Unknown"} -> {targetSnap?.Label ?? "Unknown"}");
                    Console.WriteLine($"    SourceViewId: '{link.SourceViewId}'");
                    Console.WriteLine($"    TargetViewId: '{link.TargetViewId}'");
                    Console.WriteLine();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
