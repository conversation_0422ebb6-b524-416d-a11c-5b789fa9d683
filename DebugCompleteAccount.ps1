# Debug script to print complete account structure

$filePath = "DocumentationGenerator.cs"

Write-Host "Adding complete account structure debugging..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Replace the account structure debugging with more detailed output
$oldAccountDebug = 'Console.WriteLine($"[DB-CONNECTION-DEBUG]         account structure: {accountObj.ToString()}");'
$newAccountDebug = @'
Console.WriteLine($"[DB-CONNECTION-DEBUG]         account structure (formatted):");
                                                        Console.WriteLine($"[DB-CONNECTION-DEBUG]         {accountObj.ToString(Formatting.Indented)}");
'@

if ($content.Contains($oldAccountDebug)) {
    $content = $content.Replace($oldAccountDebug, $newAccountDebug)
    Write-Host "Updated account structure debugging for better formatting" -ForegroundColor Yellow
} else {
    Write-Host "Account debug line not found" -ForegroundColor Red
}

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Enhanced account structure debugging!" -ForegroundColor Green
