# Simple script to enable Console.WriteLine debugging for database connection info

$filePath = "DocumentationGenerator.cs"

Write-Host "Enabling debug output for database connection info..." -ForegroundColor Green

# Replace LogMessage with Console.WriteLine for DB-CONNECTION debugging
(Get-Content $filePath) -replace 'LogMessage\(\$"\[DB-CONNECTION\]', 'Console.WriteLine($"[DB-CONNECTION]' | Set-Content $filePath

Write-Host "Debug output enabled. DB-CONNECTION messages will now appear in console." -ForegroundColor Yellow
