# PowerShell script to intelligently split DocumentationGenerator.cs into two logical partial class files
# This version analyzes method boundaries to ensure clean splits

$originalFile = "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\DocumentationGenerator.cs"
$part1File = "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\DocumentationGenerator.Core.cs"
$part2File = "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\DocumentationGenerator.Helpers.cs"

Write-Host "Reading DocumentationGenerator.cs and analyzing structure..."
$content = Get-Content -Path $originalFile

# Find class declaration
$classStartLine = -1
$namespaceStartLine = -1
for ($i = 0; $i -lt $content.Length; $i++) {
    if ($content[$i] -match '^\s*namespace\s+') {
        $namespaceStartLine = $i
    }
    if ($content[$i] -match '^\s*public\s+partial\s+class\s+DocumentationGenerator\s*$') {
        $classStartLine = $i
        break
    }
}

if ($classStartLine -eq -1) {
    Write-Error "Could not find class declaration"
    exit 1
}

Write-Host "Found class at line: $($classStartLine + 1)"

# Find all method starts to identify good split points
$methodStarts = @()
$braceDepth = 0
$inMethod = $false

for ($i = $classStartLine + 1; $i -lt $content.Length - 50; $i++) {
    $line = $content[$i]
    
    # Count braces to track depth
    $braceDepth += ($line.ToCharArray() | Where-Object { $_ -eq '{' }).Count
    $braceDepth -= ($line.ToCharArray() | Where-Object { $_ -eq '}' }).Count
    
    # Look for method signatures at class level (depth = 1)
    if ($braceDepth -eq 1 -and $line -match '^\s*(public|private|protected|internal).*\w+\s*\(' -and 
        $line -notmatch '^\s*//' -and $line -notmatch 'new\s+\w+\s*\(') {
        $methodStarts += $i
        Write-Host "Found method at line: $($i + 1) - $($line.Trim().Substring(0, [Math]::Min(60, $line.Trim().Length)))"
    }
}

Write-Host "Found $($methodStarts.Count) methods"

# Choose split point around the middle method
$midPoint = [Math]::Floor($methodStarts.Count / 2)
$splitLine = $methodStarts[$midPoint]

Write-Host "Splitting at method line: $($splitLine + 1)"
Write-Host "Method signature: $($content[$splitLine].Trim())"

# Extract header (namespace start to class opening brace)
$headerLines = @()
for ($i = $namespaceStartLine; $i -le $classStartLine + 1; $i++) {
    $headerLines += $content[$i]
}

# Create Part 1 - Core functionality (class start to split point)
Write-Host "Creating Part 1: DocumentationGenerator.Core.cs..."
$part1Content = $headerLines

# Add class members from start to split point
for ($i = $classStartLine + 2; $i -lt $splitLine; $i++) {
    $part1Content += $content[$i]
}

# Close class and namespace
$part1Content += "    }"
$part1Content += "}"

# Create Part 2 - Helper methods (split point to end)
Write-Host "Creating Part 2: DocumentationGenerator.Helpers.cs..."
$part2Content = $headerLines

# Add class members from split point to end
for ($i = $splitLine; $i -lt $content.Length; $i++) {
    if ($content[$i] -match '^\s*}\s*$' -and $i -gt $content.Length - 5) {
        # Skip the final closing braces as we'll add our own
        continue
    }
    $part2Content += $content[$i]
}

# Ensure proper closing
if ($part2Content[-1] -notmatch '^\s*}\s*$') {
    $part2Content += "    }"
}
if ($part2Content[-1] -notmatch '^\s*}\s*$' -or $part2Content[-2] -notmatch '^\s*}\s*$') {
    $part2Content += "}"
}

# Write the files
Write-Host "Writing DocumentationGenerator.Core.cs..."
$part1Content | Set-Content -Path $part1File -Encoding UTF8

Write-Host "Writing DocumentationGenerator.Helpers.cs..."
$part2Content | Set-Content -Path $part2File -Encoding UTF8

# Update project file
Write-Host "Updating project file..."
$projectFile = "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapAnalyzer.csproj"
$projectContent = Get-Content -Path $projectFile -Raw
$projectContent = $projectContent.Replace('<Compile Include="DocumentationGenerator.cs" />', 
    '<Compile Include="DocumentationGenerator.Core.cs" />
    <Compile Include="DocumentationGenerator.Helpers.cs" />')
$projectContent | Set-Content -Path $projectFile -Encoding UTF8

# Remove original file
Write-Host "Removing original file..."
Remove-Item -Path $originalFile

Write-Host ""
Write-Host "🎉 Successfully split DocumentationGenerator.cs intelligently:"
Write-Host "   📄 DocumentationGenerator.Core.cs    - Lines 1-$splitLine (Core functionality)"
Write-Host "   📄 DocumentationGenerator.Helpers.cs - Lines $($splitLine+1)-end (Helper methods)"
Write-Host ""

# Show file sizes
$part1Size = (Get-Item $part1File).Length
$part2Size = (Get-Item $part2File).Length
Write-Host "File sizes:"
Write-Host "   Part 1 (Core):    $([math]::Round($part1Size/1024, 1)) KB"
Write-Host "   Part 2 (Helpers): $([math]::Round($part2Size/1024, 1)) KB"
