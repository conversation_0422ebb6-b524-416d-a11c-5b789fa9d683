using System;

class SimpleConditionTest
{
    static void Main()
    {
        Console.WriteLine("Testing snap type detection conditions");
        Console.WriteLine("=====================================");
        
        // Test with different types
        string[] snapTypes = {
            "com.snaplogic.snaps.transform.datatransform.DataMapper",
            "com-snaplogic-snaps-transform-datatransform",
            "com.snaplogic.snaps.mapper.JSONMapper",
            "com.snaplogic.snaps.transform.generictransform",
            "com.snaplogic.snaps.transform.sorter"
        };
        
        foreach (string snapType in snapTypes)
        {
            bool isMapper = snapType.ToLower().Contains("map") || snapType.ToLower().Contains("datatransform");
            bool shouldSkipSection = isMapper; // Skip section if it's a mapper
            bool shouldShowSection = !shouldSkipSection; // Only show for non-mappers
            
            Console.WriteLine($"\nTesting snap type: '{snapType}'");
            Console.WriteLine($"Contains 'map': {snapType.ToLower().Contains("map")}");
            Console.WriteLine($"Contains 'datatransform': {snapType.ToLower().Contains("datatransform")}");
            Console.WriteLine($"Is detected as mapper: {isMapper}");
            Console.WriteLine($"Should skip redundant section: {shouldSkipSection}");
            Console.WriteLine($"Should show redundant section: {shouldShowSection}");
            
            if (shouldShowSection)
            {
                Console.WriteLine("WOULD SHOW: Best Practices for this Transformation Snap");
            }
            else
            {
                Console.WriteLine("WOULD SKIP: Best Practices for this Transformation Snap");
            }
        }
        
        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }
}
