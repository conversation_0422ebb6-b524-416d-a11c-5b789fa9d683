using Newtonsoft.Json.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;

namespace SnapAnalyser
{
    public partial class DocumentationGenerator
    {
        private readonly AIDescriptionGenerator _aiGenerator;
        private readonly PipelinePatternAnalyzer _patternAnalyzer;
        private bool _useAI;
        private HashSet<string> _processedSnapTypes = new HashSet<string>();
        private PipelineData _currentPipeline; // Stores the current pipeline being processed

        // Logging infrastructure
        private string _logPath;
        private bool _isLoggingEnabled = true; // Enable logging by default
        
        private void InitializeLogging()
        {
            // Create a marker file on the desktop to help locate the log
            string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
            string markerPath = Path.Combine(desktopPath, "SnapDocumenter_LogLocation.txt");
            
            try 
            {
                Console.WriteLine("Initializing logging...");
                
                // Get the solution directory (two levels up from bin/Debug)
                string baseDir = AppDomain.CurrentDomain.BaseDirectory;
                string solutionDir = Directory.GetParent(Directory.GetParent(Directory.GetParent(baseDir).FullName).FullName).FullName;
                string docsDir = Path.Combine(solutionDir, "Generated Documentation");
                
                Console.WriteLine($"Base directory: {baseDir}");
                Console.WriteLine($"Solution directory: {solutionDir}");
                Console.WriteLine($"Using documentation directory: {docsDir}");
                
                // Ensure the directory exists
                if (!Directory.Exists(docsDir))
                {
                    Console.WriteLine("Documentation directory doesn't exist, creating it...");
                    Directory.CreateDirectory(docsDir);
                    Console.WriteLine("Documentation directory created successfully");
                }
                else
                {
                    Console.WriteLine("Documentation directory already exists");
                }
                
                // Set the log path and test writing
                _logPath = Path.Combine(docsDir, "SnapDocumenter_MapperDebug.log");
                string testMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] Test write to log file{Environment.NewLine}";
                
                Console.WriteLine($"Attempting to write to: {_logPath}");
                File.AppendAllText(_logPath, testMessage);
                
                // Write the initialization message
                string initMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] Logging initialized. Log directory: {docsDir}{Environment.NewLine}";
                File.AppendAllText(_logPath, initMessage);
                
                Console.WriteLine($"Successfully initialized logging to: {_logPath}");
                
                // Write log location to desktop marker file
                File.WriteAllText(markerPath, $"SnapDocumenter log file is located at:\n{_logPath}");
                Console.WriteLine($"Log location written to: {markerPath}");
                
                LogToFile($"Application started. Process ID: {System.Diagnostics.Process.GetCurrentProcess().Id}");
                LogToFile($"Log file location: {_logPath}");
                LogToFile($"This log location has been written to: {markerPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Falling back to application directory. Error: {ex.Message}");
                
                // Fall back to application directory if Generated Documentation folder fails
                try 
                {
                    string appDir = AppDomain.CurrentDomain.BaseDirectory;
                    _logPath = Path.Combine(appDir, "SnapDocumenter_MapperDebug.log");
                    
                    Console.WriteLine($"Attempting to write to application directory: {_logPath}");
                    
                    string initMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] Failed to initialize logging in Generated Documentation folder. Using app directory: {appDir}{Environment.NewLine}";
                    File.WriteAllText(_logPath, initMessage);
                    File.AppendAllText(_logPath, $"Error: {ex}{Environment.NewLine}");
                    
                    Console.WriteLine($"Successfully initialized logging to application directory: {_logPath}");
                    
                    LogToFile($"Application started (fallback location). Process ID: {System.Diagnostics.Process.GetCurrentProcess().Id}");
                }
                catch (Exception ex2)
                {
                    Console.WriteLine($"Falling back to temp directory. Error: {ex2.Message}");
                    
                    // Last resort - try temp directory
                    try 
                    {
                        string tempPath = Path.GetTempPath();
                        _logPath = Path.Combine(tempPath, "SnapDocumenter_MapperDebug.log");
                        
                        Console.WriteLine($"Attempting to write to temp directory: {_logPath}");
                        
                        string errorMsg = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] Failed to create log in app directory. Using temp directory: {_logPath}{Environment.NewLine}";
                        File.AppendAllText(_logPath, errorMsg);
                        File.AppendAllText(_logPath, $"Original Error: {ex}{Environment.NewLine}");
                        File.AppendAllText(_logPath, $"Secondary Error: {ex2}{Environment.NewLine}");
                        
                        Console.WriteLine($"Successfully initialized logging to temp directory: {_logPath}");
                    }
                    catch (Exception ex3)
                    {
                        Console.WriteLine($"CRITICAL: Failed to initialize logging in any location. Final error: {ex3.Message}");
                        _logPath = null;
                    }
                }
            }
        }
        
        private void LogToFile(string message)
        {
            if (string.IsNullOrEmpty(_logPath) || !_isLoggingEnabled) return;
            
            try 
            {
                File.AppendAllText(_logPath, $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] {message}{Environment.NewLine}");
            }
            catch
            {
                // Silently fail - we can't log the error if logging fails
            }
        }

        // Property to check if AI capability is available
        public bool HasAICapability => _aiGenerator != null;
        public DocumentationGenerator(AIDescriptionGenerator aiGenerator = null)
        {
            _aiGenerator = aiGenerator;
            _patternAnalyzer = new PipelinePatternAnalyzer();
            _useAI = _aiGenerator != null;
            InitializeLogging();
            LogToFile("DocumentationGenerator initialized");
        }

        // Enable logging for detailed trace information
        public void EnableLogging(string logFilePath)
        {
            _logPath = logFilePath;
            _isLoggingEnabled = true;
        }

        // Log a message with timestamp
        private void LogMessage(string message)
        {
            if (!_isLoggingEnabled || string.IsNullOrEmpty(_logPath))
                return;

            try            {
                string logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}";
                File.AppendAllText(_logPath, logEntry + Environment.NewLine);
            }
            catch
            {
                // Ignore logging errors to prevent affecting main functionality
            }
        }
        
        /// <summary>
        /// Reset the processed snap types when generating a new pipeline documentation
        /// </summary>
        private void ResetProcessedSnapTypes()
        {
            Console.WriteLine("Resetting processed snap types for new pipeline documentation");
            _processedSnapTypes.Clear();
        }
          /// <summary>
        /// Helper method to convert markdown to HTML with improved handling for markdown elements and placeholder protection
        /// </summary>
        private string ConvertMarkdownToHtml(string markdown)
        {
            if (string.IsNullOrEmpty(markdown))
                return string.Empty;

            // FIRST: Extract and protect ALL placeholder patterns before any processing
            var placeholderProtection = new Dictionary<string, string>();
            var placeholderCounter = 0;
            string text = markdown;
            
            // Extract all placeholder formats before any other processing
            text = Regex.Replace(text, @"##INLINE_?CODE_?([a-zA-Z0-9]+)##", match =>
            {
                var protectionKey = $"PLACEHOLDER_PROTECTION_{placeholderCounter++}";
                placeholderProtection[protectionKey] = match.Value;
                return protectionKey;
            });
            
            // Also extract any already-corrupted placeholders with HTML tags
            text = Regex.Replace(text, @"##INLINE<[^>]*>CODE<[^>]*>([a-zA-Z0-9]+)##", match =>
            {
                string id = match.Groups[1].Value;
                string cleanPlaceholder = $"##INLINECODE{id}##";
                var protectionKey = $"PLACEHOLDER_PROTECTION_{placeholderCounter++}";
                placeholderProtection[protectionKey] = cleanPlaceholder;
                return protectionKey;
            });

            // Check if the content is already HTML (starting with any HTML tag)
            if (Regex.IsMatch(text, @"^\s*<([a-z][a-z0-9]*)\b[^>]*>"))
            {
                // Content is already in HTML format, restore placeholders and return
                foreach (var placeholder in placeholderProtection)
                {
                    text = text.Replace(placeholder.Key, placeholder.Value);
                }
                return text;
            }
            
            // Check if content is already HTML (containing HTML tags) - if so, don't process as markdown
            if (Regex.IsMatch(text, @"<([a-z][a-z0-9]*)\b[^>]*>.*?</\1>|<([a-z][a-z0-9]*)\b[^>]*/>", RegexOptions.Singleline))
            {
                // Content appears to contain HTML tags, restore placeholders and return
                foreach (var placeholder in placeholderProtection)
                {
                    text = text.Replace(placeholder.Key, placeholder.Value);
                }
                return text;
            }

            // HTML encode the text to prevent injection, but preserve protected placeholders
            text = System.Net.WebUtility.HtmlEncode(text);
            
            // Pre-process code blocks to protect them from other replacements
            var codeBlocks = new Dictionary<string, string>();
            
            // Extract and save code blocks
            text = Regex.Replace(text, @"```(.+?)```", match =>
            {
                var placeholder = $"CODE_BLOCK_{placeholderCounter++}";
                codeBlocks[placeholder] = match.Groups[1].Value;
                return placeholder;
            }, RegexOptions.Singleline);

            // Handle paragraphs (ensure double newlines become paragraph breaks)
            var paragraphs = Regex.Split(text, @"\r\n\r\n|\n\n");

            // Process each paragraph
            for (int i = 0; i < paragraphs.Length; i++)
            {
                var paragraph = paragraphs[i];
                
                // Handle headings - must be done at the start of a paragraph
                if (Regex.IsMatch(paragraph, @"^#{1,6} "))
                {
                    // h1-h3 get mapped to h3-h5 to maintain hierarchy in the document
                    paragraph = Regex.Replace(paragraph, @"^# (.+?)$", "<h3>$1</h3>", RegexOptions.Multiline);
                    paragraph = Regex.Replace(paragraph, @"^## (.+?)$", "<h4>$1</h4>", RegexOptions.Multiline);
                    paragraph = Regex.Replace(paragraph, @"^### (.+?)$", "<h5>$1</h5>", RegexOptions.Multiline);
                    paragraph = Regex.Replace(paragraph, @"^#{4,6} (.+?)$", "<h6>$1</h6>", RegexOptions.Multiline);
                }
                // Handle unordered lists
                else if (Regex.IsMatch(paragraph, @"^\s*[-*+]\s+.*$", RegexOptions.Multiline))
                {
                    var listItems = Regex.Split(paragraph, @"\r\n|\n").Where(line => !string.IsNullOrWhiteSpace(line));
                    var listContent = new StringBuilder("<ul>");

                    foreach (var item in listItems)
                    {
                        var listItem = Regex.Replace(item, @"^\s*[-*+]\s+(.+)$", "$1");
                        listItem = ProcessInlineFormatting(listItem, placeholderProtection);
                        listContent.AppendLine($"<li>{listItem}</li>");
                    }

                    listContent.Append("</ul>");
                    paragraph = listContent.ToString();
                }
                // Handle ordered lists
                else if (Regex.IsMatch(paragraph, @"^\s*\d+\.\s+.*$", RegexOptions.Multiline))
                {
                    var listItems = Regex.Split(paragraph, @"\r\n|\n").Where(line => !string.IsNullOrWhiteSpace(line));
                    var listContent = new StringBuilder("<ol>");

                    foreach (var item in listItems)
                    {
                        var listItem = Regex.Replace(item, @"^\s*\d+\.\s+(.+)$", "$1");
                        listItem = ProcessInlineFormatting(listItem, placeholderProtection);
                        listContent.AppendLine($"<li>{listItem}</li>");
                    }

                    listContent.Append("</ol>");
                    paragraph = listContent.ToString();
                }
                // Regular paragraph
                else if (!paragraph.StartsWith("<h") && !paragraph.StartsWith("<ul") && !paragraph.StartsWith("<ol") && !Regex.IsMatch(paragraph, @"<h[1-6][^>]*>"))
                {
                    // Process inline formatting
                    paragraph = ProcessInlineFormatting(paragraph, placeholderProtection);
                    
                    // Handle soft breaks (single newlines within paragraphs)
                    paragraph = paragraph.Replace("\r\n", "<br/>").Replace("\n", "<br/>");

                    // Wrap in paragraph tags
                    paragraph = $"<p>{paragraph}</p>";
                }

                paragraphs[i] = paragraph;
            }

            // Join the processed paragraphs
            text = string.Join("\n", paragraphs);
            
            // Restore code blocks with proper HTML
            foreach (var codeBlock in codeBlocks)
            {
                if (codeBlock.Key.StartsWith("CODE_BLOCK_"))
                {
                    text = text.Replace(codeBlock.Key, $"<pre><code>{codeBlock.Value}</code></pre>");
                }
            }
            
            // Restore protected placeholders
            foreach (var placeholder in placeholderProtection)
            {
                text = text.Replace(placeholder.Key, placeholder.Value);
            }

            return text;
        }

        private string ProcessInlineFormatting(string text, Dictionary<string, string> placeholderProtection)
        {
            // Handle inline code first to protect it from other formatting
            text = Regex.Replace(text, @"`([^`]+?)`", "<code>$1</code>");

            // Handle bold text - process these before italic to handle nested formatting
            text = Regex.Replace(text, @"\*\*(.*?)\*\*", "<strong>$1</strong>");
            
            // Handle bold with underscores - BUT protect placeholders first
            // Only apply underscore bold formatting if it's NOT part of a placeholder
            text = Regex.Replace(text, @"(?<!##[A-Z]*[_]?)__(.+?)__(?![A-Z0-9]*##)", "<strong>$1</strong>");

            // Handle italic text - use a more specific pattern to avoid greedy matching
            text = Regex.Replace(text, @"\*([^*\r\n]*?)\*", "<em>$1</em>");
            
            // Handle italic with underscores - BUT protect placeholders
            // Only apply underscore italic formatting if it's NOT part of a placeholder
            text = Regex.Replace(text, @"(?<!##[A-Z]*[_]?)_([^_\r\n]*?)_(?![A-Z0-9]*##)", "<em>$1</em>");

            // Handle links
            text = Regex.Replace(text, @"\[([^\]]+?)\]\(([^\)]+?)\)", "<a href=\"$2\">$1</a>");

            return text;
        }/// <summary>
        /// Replaces all placeholders in a text with their actual values
        /// </summary>
        /// <param name="text">The text containing placeholders</param>
        /// <param name="codeBlocks">Dictionary containing the code block mappings</param>
        /// <returns>Text with placeholders replaced</returns>
        private string ReplaceAllPlaceholders(string text, Dictionary<string, string> codeBlocks = null)
        {
            if (string.IsNullOrEmpty(text))
                return text;
                
            // If no codeBlocks provided, create empty dictionary to avoid null reference
            if (codeBlocks == null)
                codeBlocks = new Dictionary<string, string>();
                
            // Process both placeholder formats
            // 1. Standard format with underscore: ##INLINE_CODE_xxxxxxxx##
            text = Regex.Replace(text, @"##INLINE_CODE_([a-zA-Z0-9]+)##", match =>
            {
                string id = match.Groups[1].Value;
                string currentPlaceholder = match.Value;
                
                // If we have this placeholder in our dictionary, replace it
                if (codeBlocks.ContainsKey(currentPlaceholder))
                {
                    return $"<code>{codeBlocks[currentPlaceholder]}</code>";
                }
                
                // For unmatched placeholders, try to provide intelligent fallback
                var fallback = GetFallbackForOrphanedPlaceholder(id, text);
                return $"<code class=\"inferred-placeholder\">{fallback}</code>";
            });
            
            // 2. Format without underscore: ##INLINECODExxxxxxxx##
            text = Regex.Replace(text, @"##INLINECODE([a-zA-Z0-9]+)##", match =>
            {
                string id = match.Groups[1].Value;
                string alternateFormat = $"##INLINE_CODE_{id}##";
                
                // Check if we have the alternate format in our dictionary
                if (codeBlocks.ContainsKey(alternateFormat))
                {
                    return $"<code>{codeBlocks[alternateFormat]}</code>";
                }
                
                // For unmatched placeholders, try to provide intelligent context-aware fallback
                var fallback = GetContextAwareFallback(id, text);
                return $"<code class=\"inferred-placeholder\">{fallback}</code>";
            });
            
            return text;
        }        /// <summary>
        /// Provides context-aware fallbacks for AI-generated placeholders based on surrounding text
        /// </summary>
        /// <param name="id">The placeholder ID</param>
        /// <param name="context">The surrounding text context</param>
        /// <returns>A meaningful replacement value</returns>
        private string GetContextAwareFallback(string id, string context)
        {
            string contextLower = context.ToLower();
            
            // Router-specific logic with more detailed analysis
            if (contextLower.Contains("router") || contextLower.Contains("route"))
            {
                // Look for specific patterns in the context
                if (contextLower.Contains("presence of the") && contextLower.Contains("field"))
                    return "field_name";
                if (contextLower.Contains("is not null"))
                    return "field_value";
                if (contextLower.Contains("is null"))
                    return "field_value";
                if (contextLower.Contains("sent to output") || contextLower.Contains("to output"))
                    return "output_view";
                if (contextLower.Contains("property is set to"))
                    return "routing_mode";
                if (contextLower.Contains("validate") && contextLower.Contains("execute"))
                    return "Validate & Execute";
                if (contextLower.Contains("execution mode"))
                    return "execution_mode";
                
                // General router context
                if (contextLower.Contains("sent to") || contextLower.Contains("output"))
                    return "output_view";
                if (contextLower.Contains("property") && contextLower.Contains("set"))
                    return "routing_property";
                if (contextLower.Contains("condition"))
                    return "condition_expr";
            }
            
            // Join-specific logic
            if (contextLower.Contains("join"))
            {
                if (contextLower.Contains("key") || contextLower.Contains("field"))
                    return "join_key";
                if (contextLower.Contains("condition"))
                    return "join_condition";
            }
            
            // Look for specific output patterns
            if (contextLower.Contains("output0") || contextLower.Contains("output1"))
                return "output_view";
            
            // Look for mode/execution patterns
            if (contextLower.Contains("mode") || contextLower.Contains("execution"))
                return "execution_mode";
            
            // General field/property logic
            if (contextLower.Contains("field") && (contextLower.Contains("name") || contextLower.Contains("presence")))
                return "field_name";
            if (contextLower.Contains("value") || contextLower.Contains("data"))
                return "field_value";
            if (contextLower.Contains("property"))
                return "property_name";
            if (contextLower.Contains("output") || contextLower.Contains("view"))
                return "output_view";
            if (contextLower.Contains("input"))
                return "input_view";
            if (contextLower.Contains("path"))
                return "output_path";
            if (contextLower.Contains("condition") || contextLower.Contains("expression"))
                return "condition";
                
            // Use the original GetFallbackForOrphanedPlaceholder as final fallback
            return GetFallbackForOrphanedPlaceholder(id, context);
        }

        public async Task<string> GenerateHtmlDocumentationAsync(PipelineData pipeline, string diagramSvg, CancellationToken cancellationToken = default)
        {
            return await GenerateHtmlDocumentationAsync(pipeline, diagramSvg, null, cancellationToken).ConfigureAwait(false);
        }

        public async Task<string> GenerateHtmlDocumentationAsync(PipelineData pipeline, string diagramSvg, ProjectData project, CancellationToken cancellationToken = default)
        {
            LogMessage($"[HTML-GEN] Starting HTML generation for pipeline: {pipeline.Name}");
            LogMessage($"[HTML-GEN] Project context available: {project != null}");

            // Reset the tracking of processed snap types to ensure a fresh start for each pipeline
            ResetProcessedSnapTypes();

            // Set the current pipeline for use in other methods
            _currentPipeline = pipeline;

            var html = new StringBuilder();

            // Add AI-generated overview if available
            string aiPipelineDescription = "";
            if (_useAI)
            {                try
                {
                    LogMessage($"[HTML-GEN] Calling AI generator for pipeline description");
                    aiPipelineDescription = await _aiGenerator.GeneratePipelineDescription(pipeline, cancellationToken).ConfigureAwait(false);
                    LogMessage($"[HTML-GEN] AI description generated, length: {aiPipelineDescription?.Length ?? 0} characters");
                }
                catch (Exception ex)
                {
                    LogMessage($"[HTML-GEN] AI description generation failed: {ex.Message}");
                    aiPipelineDescription = $"AI description generation failed: {ex.Message}";
                }
            }

            // HTML header
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html lang=\"en\">");
            html.AppendLine("<head>");
            html.AppendLine("  <meta charset=\"UTF-8\">");
            html.AppendLine("  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">");
            html.AppendLine($"  <title>{pipeline.Name} - SnapLogic Pipeline Documentation</title>"); html.AppendLine("  <style>");            html.AppendLine("    body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; color: #333; }");
            html.AppendLine("    .container { max-width: none; width: 100%; margin: 0; padding: 0 20px; }");
            html.AppendLine("    h1 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }");
            html.AppendLine("    h2 { color: #2c3e50; margin-top: 30px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }");
            html.AppendLine("    h3 { color: #3498db; }");
            html.AppendLine("    h4 { color: #3498db; margin-top: 20px; margin-bottom: 10px; }"); html.AppendLine("    .section { margin-bottom: 30px; }");
            html.AppendLine("    table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }");
            html.AppendLine("    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
            html.AppendLine("    th { background-color: #f2f2f2; }");            html.AppendLine("    tr:nth-child(even) { background-color: #f9f9f9; }"); html.AppendLine("    .diagram-container { overflow: auto; border: 1px solid #ddd; margin: 20px 0; padding: 10px; text-align: left; width: 100%; display: block; }");
            html.AppendLine("    .diagram-container svg { display: block; margin: 0; position: relative; }");
            html.AppendLine("    .snap-category { display: inline-block; width: 16px; height: 16px; margin-right: 5px; vertical-align: middle; border: 1px solid #333; }");
            html.AppendLine("    .flow-control { background-color: #ffffcc; }");
            html.AppendLine("    .transformation { background-color: #ccffcc; }");
            html.AppendLine("    .database { background-color: #ccccff; }");
            html.AppendLine("    .external-system { background-color: #ffcccc; }");
            html.AppendLine("    .file-operation { background-color: #ffccff; }");
            html.AppendLine("    .error-handling { background-color: #ffdddd; }");
            html.AppendLine("    .toc { background-color: #f5f5f5; padding: 15px; margin-bottom: 20px; border-radius: 5px; }");
            html.AppendLine("    .toc ul { list-style-type: none; padding-left: 20px; }");
            html.AppendLine("    .toc a { text-decoration: none; color: #3498db; }");
            html.AppendLine("    .toc a:hover { text-decoration: underline; }");
            html.AppendLine("    .ai-description { background-color: #f8f9fa; border-left: 4px solid #3498db; padding: 15px; margin: 15px 0; }");
            html.AppendLine("    .function-details { background-color: #f0f7fb; border-left: 5px solid #72b7e5; padding: 10px 15px; margin: 5px 0; line-height: 1.6; }");
            html.AppendLine("    .snap-details { background-color: #f9f9f9; }");
            html.AppendLine("    .property-list { margin: 0; padding: 0; list-style-type: none; }");
            html.AppendLine("    .property-list li { margin-bottom: 5px; }");
            html.AppendLine("    .sql-preview { font-family: monospace; background-color: #f5f5f5; padding: 2px 4px; border-radius: 3px; }"); html.AppendLine("    .category-description { background-color: #f9f9f9; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px; }");
            html.AppendLine("    .snap-count { font-weight: bold; }");
            html.AppendLine("    .with-columns { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }"); html.AppendLine("    .code-sample { background-color: #f5f5f5; padding: 10px; border-left: 4px solid #72b7e5; font-family: Consolas, Monaco, 'Courier New', monospace; white-space: pre; font-size: 13px; overflow-x: auto; line-height: 1.4; color: #333; }");
            html.AppendLine("    pre { margin: 0; }");
            html.AppendLine("    .technical-details { background-color: #f0f7fb; border-left: 5px solid #3498db; padding: 10px; margin: 10px 0; }");
            html.AppendLine("    .pattern-details { background-color: #f6f6f6; border-left: 3px solid #72b7e5; padding: 8px; margin: 8px 0; }"); html.AppendLine("    .category-overview { margin-bottom: 15px; }");            html.AppendLine("    .code-sample .comment { color: #008000; }");
            html.AppendLine("    .code-sample .keyword { color: #0000ff; }");
            html.AppendLine("    .code-sample .string { color: #a31515; }");
            html.AppendLine("    .code-sample .number { color: #09885a; }");
            // CSS for placeholder handling
            html.AppendLine("    .missing-placeholder { background-color: #ffebee; color: #c62828; border: 1px solid #f8bbd9; }");
            html.AppendLine("    .inferred-placeholder { background-color: #fff3e0; color: #ef6c00; border: 1px solid #ffcc02; }");
            html.AppendLine("    .code-sample .function { color: #795e26; }"); html.AppendLine("    .code-example { margin: 15px 0; border: 1px solid #ddd; border-radius: 4px; overflow: hidden; }");
            html.AppendLine("    .code-example .code-title { background-color: #f8f8f8; padding: 5px 10px; border-bottom: 1px solid #ddd; font-weight: bold; }");
            html.AppendLine("    .code-example .code-content { padding: 0; }");
            html.AppendLine("    .best-practices { background-color: #f9fff9; border: 1px solid #d4e8d4; border-radius: 4px; margin-top: 15px; padding: 10px; }");
            html.AppendLine("    .best-practices h5 { color: #2e7d32; margin-top: 0; margin-bottom: 8px; }");
            html.AppendLine("    .best-practices ul { margin-top: 0; padding-left: 20px; }");
            html.AppendLine("    .best-practices code { background: #f0f0f0; padding: 1px 4px; border-radius: 3px; font-size: 90%; }");
            html.AppendLine("    .collapsible { cursor: pointer; padding: 10px; border: 1px solid #ddd; text-align: left; outline: none; width: 100%; background-color: #f8f9fa; border-radius: 4px; margin: 10px 0; font-weight: bold; }");
            html.AppendLine("    .collapsible:hover { background-color: #e9ecef; }");
            html.AppendLine("    .collapsible:after { content: '\\002B'; float: right; font-weight: bold; }");
            html.AppendLine("    .collapsible.active:after { content: '\\2212'; }");
            html.AppendLine("    .collapsible-content { max-height: 0; overflow: hidden; transition: max-height 0.3s ease-out; background-color: #ffffff; border: 1px solid #ddd; border-top: none; border-radius: 0 0 4px 4px; }");
            html.AppendLine("    .pattern-container { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }");
            html.AppendLine("    .pattern-card { background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 15px; margin-bottom: 20px; }");
            html.AppendLine("    .pattern-card h3 { color: #0056b3; margin-top: 0; }");
            html.AppendLine("    .pattern-card p { margin-bottom: 8px; }"); html.AppendLine("    .pattern-card ul { margin-top: 0; padding-left: 20px; }");
            html.AppendLine("    @media (max-width: 800px) { .pattern-container { grid-template-columns: 1fr; } }");
            html.AppendLine("    .pattern-analysis { margin: 20px 0; border: 1px solid #e9ecef; border-radius: 4px; padding: 20px; background-color: #f8f9fa; }");
            html.AppendLine("    .detected-pattern { margin-bottom: 25px; border-bottom: 1px solid #e9ecef; padding-bottom: 15px; }");
            html.AppendLine("    .detected-pattern:last-child { border-bottom: none; }");
            html.AppendLine("    .confidence-meter { margin: 10px 0; }");
            html.AppendLine("    .confidence-label { margin-bottom: 5px; font-weight: bold; }");
            html.AppendLine("    .confidence-bar { height: 15px; background-color: #e9ecef; border-radius: 10px; overflow: hidden; }");
            html.AppendLine("    .confidence-fill { height: 100%; background-color: #4caf50; transition: width 0.5s ease-in-out; }");
            html.AppendLine("    .confidence-meter-small { display: inline-block; width: 100px; height: 8px; background-color: #e9ecef; border-radius: 4px; overflow: hidden; vertical-align: middle; margin-left: 8px; }");
            html.AppendLine("    .pattern-features, .pattern-missing { margin-top: 10px; }");            html.AppendLine("    .pattern-missing li { color: #856404; }");
            html.AppendLine("    .pattern-analysis-overview { background-color: #f0f7fb; border-left: 5px solid #3498db; padding: 10px 15px; margin: 15px 0; }");
            html.AppendLine("    .pattern-analysis-list { list-style-type: none; padding-left: 0; }");            html.AppendLine("    .pattern-analysis-list li { margin-bottom: 8px; }");            // Add cytoscape.js specific styles            html.AppendLine("    .cytoscape-container { margin: 0; text-align: left; border: none; padding: 0; border-radius: 0; width: 100%; max-width: none; display: block; }");
            html.AppendLine("    .cytoscape-title { color: #2c3e50; margin-bottom: 15px; font-size: 18px; font-weight: bold; text-align: left; }");
            html.AppendLine("    .cytoscape-diagram { min-height: 500px; height: 600px; width: 100%; margin: 0; display: block; position: relative; }");
            
            // Snap List CSS Styles
            html.AppendLine("    /* Snap List Styles */");
            html.AppendLine("    .snap-list-controls { margin: 15px 0; text-align: center; }");
            html.AppendLine("    .snap-list-toggle { background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 8px 16px; margin: 0 5px; cursor: pointer; border-radius: 4px; font-size: 14px; transition: all 0.2s; }");
            html.AppendLine("    .snap-list-toggle:hover { background-color: #e9ecef; }");
            html.AppendLine("    .snap-list-toggle.active { background-color: #3498db; color: white; border-color: #3498db; }");
            html.AppendLine("    .snap-list-view { margin: 20px 0; }");
            html.AppendLine("    .snap-flow-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 10px; margin: 15px 0; }");
            html.AppendLine("    .snap-flow-item { display: flex; align-items: center; padding: 8px 12px; background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; transition: background-color 0.2s; }");
            html.AppendLine("    .snap-flow-item:hover { background-color: #e9ecef; }");
            html.AppendLine("    .snap-number { background-color: #6c757d; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; margin-right: 10px; flex-shrink: 0; }");
            html.AppendLine("    .snap-link { color: #3498db; text-decoration: none; font-weight: 500; margin-left: 5px; margin-right: 8px; }");
            html.AppendLine("    .snap-link:hover { text-decoration: underline; }");
            html.AppendLine("    .snap-type { color: #6c757d; font-size: 12px; font-style: italic; }");
            html.AppendLine("    .snap-category-group { margin-bottom: 25px; }");
            html.AppendLine("    .snap-category-group h4 { display: flex; align-items: center; margin-bottom: 10px; color: #2c3e50; }");
            html.AppendLine("    .snap-category-items { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 8px; margin-left: 20px; }");
            html.AppendLine("    .snap-category-item { display: flex; align-items: center; justify-content: space-between; padding: 6px 10px; background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 3px; transition: background-color 0.2s; }");
            html.AppendLine("    .snap-category-item:hover { background-color: #e9ecef; }");
            html.AppendLine("    @media (max-width: 768px) { .snap-flow-list, .snap-category-items { grid-template-columns: 1fr; } }");
            
            html.AppendLine("  </style>");

            html.AppendLine("  <script>");
            html.AppendLine("    document.addEventListener('DOMContentLoaded', function() {");
            html.AppendLine("      // Animate confidence meters");
            html.AppendLine("      const confidenceMeters = document.querySelectorAll('.confidence-fill');");
            html.AppendLine("      setTimeout(() => {");
            html.AppendLine("        confidenceMeters.forEach(meter => {");
            html.AppendLine("          const width = meter.getAttribute('style').replace('width: ', '').replace('%;', '');");
            html.AppendLine("          meter.style.width = '0%';");
            html.AppendLine("          setTimeout(() => { meter.style.width = width + '%'; }, 100);");
            html.AppendLine("        });");
            html.AppendLine("      }, 300);");
            html.AppendLine("      ");
            html.AppendLine("      // Add collapsible functionality");
            html.AppendLine("      const collapsibles = document.getElementsByClassName('collapsible');");
            html.AppendLine("      for (let i = 0; i < collapsibles.length; i++) {");
            html.AppendLine("        collapsibles[i].addEventListener('click', function() {");
            html.AppendLine("          this.classList.toggle('active');");
            html.AppendLine("          const content = this.nextElementSibling;");
            html.AppendLine("          if (content.style.maxHeight) {");
            html.AppendLine("            content.style.maxHeight = null;");
            html.AppendLine("          } else {");
            html.AppendLine("            content.style.maxHeight = content.scrollHeight + 'px';");
            html.AppendLine("          }");            html.AppendLine("        });");            html.AppendLine("      }");            html.AppendLine("    });");
            html.AppendLine("  </script>");
            
            html.AppendLine("</head>");
            html.AppendLine("<body>");
            html.AppendLine("  <div class=\"container\">");

            // Header
            html.AppendLine($"    <h1>{pipeline.Name} - SnapLogic Pipeline Documentation</h1>");
            html.AppendLine($"    <p><strong>Author:</strong> {pipeline.Author}</p>");
            html.AppendLine($"    <p><strong>Generated:</strong> {DateTime.Now}</p>");
            // Table of Contents
            html.AppendLine("    <div class=\"toc\">");
            html.AppendLine("      <h2>Table of Contents</h2>");
            html.AppendLine("      <ul>"); html.AppendLine("        <li><a href=\"#overview\">Pipeline Overview</a>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><a href=\"#overview\">Architecture & Purpose</a></li>");
            html.AppendLine("            <li><a href=\"#overview\">Pipeline Functions</a></li>");
            html.AppendLine("            <li><a href=\"#overview\">SnapLogic Concepts</a></li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </li>"); html.AppendLine("        <li><a href=\"#glossary\">Technical Glossary</a></li>");
            html.AppendLine("        <li><a href=\"#flow-diagram\">Flow Diagram</a></li>");
            html.AppendLine("        <li><a href=\"#snap-list\">Snap List</a></li>");
            html.AppendLine("        <li><a href=\"#enhanced-flow-control\">Enhanced Flow Control Diagrams</a></li>");
            html.AppendLine("        <li><a href=\"#parameters\">Pipeline Parameters</a></li>");
            html.AppendLine("        <li><a href=\"#snaps\">Pipeline Snaps</a>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><a href=\"#flow-control\">Flow Control</a></li>");
            html.AppendLine("            <li><a href=\"#transformations\">Transformations</a></li>");
            html.AppendLine("            <li><a href=\"#database\">Database Operations</a></li>");
            html.AppendLine("            <li><a href=\"#external\">External Systems</a></li>");
            html.AppendLine("            <li><a href=\"#file\">File Operations</a></li>");
            html.AppendLine("            <li><a href=\"#error\">Error Handling</a></li>");
            html.AppendLine("            <li><a href=\"#other\">Other Snaps</a></li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </li>"); html.AppendLine("        <li><a href=\"#best-practices\">Pipeline Best Practices</a>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><a href=\"#best-practices\">General Pipeline Design</a></li>");
            html.AppendLine("            <li><a href=\"#best-practices\">Performance Optimization</a></li>");
            html.AppendLine("            <li><a href=\"#best-practices\">Security Considerations</a></li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </li>");
            html.AppendLine("        <li><a href=\"#patterns\">Integration Patterns</a>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><a href=\"#patterns\">ETL Pattern</a></li>");
            html.AppendLine("            <li><a href=\"#patterns\">API Integration</a></li>");
            html.AppendLine("            <li><a href=\"#patterns\">Data Synchronization</a></li>");
            html.AppendLine("            <li><a href=\"#patterns\">Event Processing</a></li>");
            html.AppendLine("            <li><a href=\"#patterns\">Data Validation</a></li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </li>");
            html.AppendLine("        <li><a href=\"#execution-flow\">Execution Flow</a>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><a href=\"#execution-flow\">Pipeline Function Summary</a></li>");
            html.AppendLine("            <li><a href=\"#execution-flow\">Pipeline Entry Points</a></li>");
            html.AppendLine("            <li><a href=\"#execution-flow\">Main Execution Paths</a></li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </li>");
            html.AppendLine("      </ul>");
            html.AppendLine("    </div>");
            // Overview Section
            html.AppendLine("    <div class=\"section\" id=\"overview\">");
            html.AppendLine("      <h2>Pipeline Overview</h2>");

            // Add AI-generated description ONLY if AI was selected (_useAI is true) and we have a description            if (_useAI && !string.IsNullOrEmpty(aiPipelineDescription))
            {
                html.AppendLine("      <div class=\"ai-description\">");
                html.AppendLine("        <h3>AI-Generated Description</h3>");
                html.AppendLine($"        <div>{ConvertMarkdownToHtml(aiPipelineDescription)}</div>");
                html.AppendLine("      </div>");
            }

            html.AppendLine($"      <p>This document provides comprehensive documentation for the SnapLogic pipeline <strong>{pipeline.Name}</strong>, " +
                           $"authored by <strong>{pipeline.Author}</strong>. A SnapLogic pipeline is an integration workflow that processes, transforms, " +
                           "and moves data between different systems using pre-built connectors called \"snaps\".</p>");

            // Add information about enhanced documentation when AI is enabled
            if (_useAI)
            {
                html.AppendLine("      <div class=\"technical-details\">");
                html.AppendLine("        <h3>Enhanced Documentation Features</h3>");
                html.AppendLine("        <p>This documentation includes AI-enhanced content to improve understanding:</p>");
                html.AppendLine("        <ul>");
                html.AppendLine("          <li><strong>Enhanced Snap Descriptions</strong>: Detailed technical explanations of snap functionality</li>");
                html.AppendLine("          <li><strong>Pseudocode Implementation</strong>: Readable representations of snap logic to understand data flow</li>");
                html.AppendLine("          <li><strong>AI-Generated Overview</strong>: Business-focused description of the pipeline's purpose</li>");
                html.AppendLine("        </ul>");
                html.AppendLine("      </div>");
            }

            // Add pipeline architecture insights
            html.AppendLine("      <div class=\"category-description\">");
            html.AppendLine("        <h3>Pipeline Architecture</h3>");

            // Determine if this is an ETL, data synchronization, or API pipeline
            bool hasExternalDataSource = pipeline.Snaps.Any(s => s.Category == SnapCategory.ExternalSystem);
            bool hasDbOperations = pipeline.Snaps.Any(s => s.Category == SnapCategory.Database);
            bool hasTransformations = pipeline.Snaps.Any(s => s.Category == SnapCategory.Transformation);
            bool hasFileOperations = pipeline.Snaps.Any(s => s.Category == SnapCategory.FileOperation);
            bool hasErrorHandlers = pipeline.Snaps.Any(s => s.Category == SnapCategory.ErrorHandling);

            // Pipeline complexity assessment
            int totalSnaps = pipeline.Snaps.Count;
            string complexity = totalSnaps < 5 ? "Simple" : (totalSnaps < 15 ? "Moderate" : "Complex");

            html.AppendLine($"        <p><strong>Pipeline Complexity:</strong> {complexity} ({totalSnaps} snaps)</p>");

            // Architecture pattern identification
            html.AppendLine("        <p><strong>Architecture Pattern:</strong> ");
            if (hasExternalDataSource && hasDbOperations && hasTransformations)
            {
                html.AppendLine("ETL (Extract, Transform, Load) - This pipeline extracts data from external systems, transforms it, and loads it into a database.</p>");
            }
            else if (hasExternalDataSource && hasExternalDataSource && hasTransformations)
            {
                html.AppendLine("System Integration - This pipeline facilitates data exchange between different external systems.</p>");
            }
            else if (hasDbOperations && hasTransformations)
            {
                html.AppendLine("Data Processing - This pipeline performs database operations with transformations on the data.</p>");
            }
            else if (hasFileOperations && hasTransformations)
            {
                html.AppendLine("File Processing - This pipeline processes file content and performs transformations.</p>");
            }
            else
            {
                html.AppendLine("Custom Integration - This pipeline implements a custom integration flow.</p>");
            }

            // Error handling assessment
            if (hasErrorHandlers)
            {
                html.AppendLine("        <p><strong>Error Handling:</strong> This pipeline includes explicit error handling mechanisms for robustness.</p>");
            }
            else
            {
                html.AppendLine("        <p><strong>Error Handling:</strong> This pipeline does not contain explicit error handling snaps.</p>");
            }

            html.AppendLine("      </div>");

            // Add pattern analysis results
            var patternAnalysisResults = _patternAnalyzer.AnalyzePipeline(pipeline);
            if (patternAnalysisResults.Any(p => p.ConfidenceScore >= 0.5f))
            {
                html.AppendLine("      <div class=\"pattern-analysis-overview\">");
                html.AppendLine("        <h3>Pattern Analysis</h3>");
                html.AppendLine("        <p>Based on analysis, this pipeline implements the following integration patterns:</p>");
                html.AppendLine("        <ul class=\"pattern-analysis-list\">");

                foreach (var pattern in patternAnalysisResults.Where(p => p.ConfidenceScore >= 0.5f).Take(3))
                {
                    int confidencePercent = (int)(pattern.ConfidenceScore * 100);
                    html.AppendLine($"          <li>");
                    html.AppendLine($"            <strong>{pattern.PatternName}</strong> ({confidencePercent}% confidence)");
                    html.AppendLine($"            <div class=\"confidence-meter-small\">");
                    html.AppendLine($"              <div class=\"confidence-fill\" style=\"width: {confidencePercent}%\"></div>");
                    html.AppendLine($"            </div>");
                    html.AppendLine($"          </li>");
                }

                html.AppendLine("        </ul>");
                html.AppendLine("        <p><em>See the Integration Patterns section for more details.</em></p>");
                html.AppendLine("      </div>");
            }

            // Pipeline Function section
            html.AppendLine("      <h3>Pipeline Functions</h3>");
            html.AppendLine("      <p>This pipeline performs the following key operations:</p>");
            html.AppendLine("      <div class=\"with-columns\">");
            html.AppendLine("        <ul>");

            // Data Sources
            html.AppendLine("          <li><strong>Data Sources:</strong>");
            html.AppendLine("            <ul>");
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.ExternalSystem && s.Type.Contains("dynamics")))
            {
                html.AppendLine("              <li>Retrieves data from Dynamics 365 CRM system</li>");
            }
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.ExternalSystem && s.Type.Contains("salesforce")))
            {
                html.AppendLine("              <li>Connects to Salesforce for CRM data</li>");
            }
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.ExternalSystem && (s.Type.Contains("rest") || s.Type.Contains("http"))))
            {
                html.AppendLine("              <li>Calls REST API endpoints</li>");
            }
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.Database && s.Type.Contains("select")))
            {
                html.AppendLine("              <li>Queries data from database tables</li>");
            }
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.FileOperation && s.Type.Contains("read")))
            {
                html.AppendLine("              <li>Reads data from files</li>");
            }
            html.AppendLine("            </ul>");
            html.AppendLine("          </li>");

            // Data Transformations
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.Transformation))
            {
                html.AppendLine("          <li><strong>Data Transformations:</strong>");
                html.AppendLine("            <ul>");
                if (pipeline.Snaps.Any(s => s.Type.Contains("datatransform") || s.Type.Contains("map")))
                {
                    html.AppendLine("              <li>Maps and transforms field values</li>");
                }
                if (pipeline.Snaps.Any(s => s.Type.Contains("json")))
                {
                    html.AppendLine("              <li>Parses or formats JSON data</li>");
                }
                if (pipeline.Snaps.Any(s => s.Type.Contains("xml")))
                {
                    html.AppendLine("              <li>Processes XML content</li>");
                }
                if (pipeline.Snaps.Any(s => s.Category == SnapCategory.FlowControl && s.Type.Contains("filter")))
                {
                    html.AppendLine("              <li>Filters data based on conditions</li>");
                }
                if (pipeline.Snaps.Any(s => s.Category == SnapCategory.FlowControl && s.Type.Contains("join")))
                {
                    html.AppendLine("              <li>Joins multiple data streams</li>");
                }
                html.AppendLine("            </ul>");
                html.AppendLine("          </li>");
            }

            html.AppendLine("        </ul>");
            html.AppendLine("        <ul>");

            // Data Destinations
            html.AppendLine("          <li><strong>Data Destinations:</strong>");
            html.AppendLine("            <ul>");
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.Database && s.Type.Contains("insert")))
            {
                html.AppendLine("              <li>Inserts records into database tables</li>");
            }
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.Database && s.Type.Contains("update")))
            {
                html.AppendLine("              <li>Updates existing database records</li>");
            }
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.ExternalSystem))
            {
                html.AppendLine("              <li>Writes data to external systems</li>");
            }
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.FileOperation && s.Type.Contains("write")))
            {
                html.AppendLine("              <li>Writes data to files</li>");
            }
            html.AppendLine("            </ul>");
            html.AppendLine("          </li>");

            // Flow Control
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.FlowControl))
            {
                html.AppendLine("          <li><strong>Flow Control:</strong>");
                html.AppendLine("            <ul>");
                if (pipeline.Snaps.Any(s => s.Type.Contains("router")))
                {
                    html.AppendLine("              <li>Routes data through conditional paths</li>");
                }
                if (pipeline.Snaps.Any(s => s.Type.Contains("union")))
                {
                    html.AppendLine("              <li>Combines multiple data streams</li>");
                }
                if (pipeline.Snaps.Any(s => s.Type.Contains("sort")))
                {
                    html.AppendLine("              <li>Sorts data based on specified criteria</li>");
                }
                html.AppendLine("            </ul>");
                html.AppendLine("          </li>");
            }

            html.AppendLine("        </ul>");
            html.AppendLine("      </div>");

            // SnapLogic Concepts
            html.AppendLine("      <div class=\"category-description\">");
            html.AppendLine("        <h3>SnapLogic Pipeline Concepts</h3>");
            html.AppendLine("        <p>SnapLogic pipelines are composed of the following key components:</p>");
            html.AppendLine("        <ul>");
            html.AppendLine("          <li><strong>Snaps</strong>: Pre-built, configurable components that perform specific functions like reading data, transforming it, or writing it to a destination.</li>");
            html.AppendLine("          <li><strong>Documents</strong>: The data units that flow through the pipeline, similar to JSON objects. Each document contains fields and values.</li>");
            html.AppendLine("          <li><strong>Views</strong>: Input or output ports on snaps that allow them to be connected.</li>");
            html.AppendLine("          <li><strong>Links</strong>: Connections between snaps that define the path for documents to flow.</li>");
            html.AppendLine("          <li><strong>Parameters</strong>: Pipeline-level variables that can be set at runtime to control pipeline behavior.</li>");
            html.AppendLine("        </ul>");
            html.AppendLine("        <p>Pipelines process data by passing documents from one snap to another along the connected links. Each snap performs its specific function on the documents it receives before passing them to the next snap in the pipeline.</p>");
            html.AppendLine("      </div>");

            html.AppendLine("    </div>");
            // Flow Diagram Section            html.AppendLine("    <div class=\"section\" id=\"flow-diagram\">");
            html.AppendLine("      <h2>Flow Diagram</h2>"); html.AppendLine("      <div class=\"diagram-container\">");
            html.AppendLine(diagramSvg);
            html.AppendLine("      </div>");
            html.AppendLine("      <div>");
            html.AppendLine("        <h3>Legend</h3>");
            html.AppendLine("        <p><span class=\"snap-category flow-control\"></span> Flow Control</p>");
            html.AppendLine("        <p><span class=\"snap-category transformation\"></span> Transformation</p>");
            html.AppendLine("        <p><span class=\"snap-category database\"></span> Database Operation</p>");
            html.AppendLine("        <p><span class=\"snap-category external-system\"></span> External System</p>");
            html.AppendLine("        <p><span class=\"snap-category file-operation\"></span> File Operation</p>");
            html.AppendLine("        <p><span class=\"snap-category error-handling\"></span> Error Handling</p>"); html.AppendLine("      </div>");
            html.AppendLine("    </div>");

            // Snap List Section
            html.AppendLine("    <div class=\"section\" id=\"snap-list\">");
            html.AppendLine("      <h2>Pipeline Snap List</h2>");
            html.AppendLine("      <p>This section provides a quick overview of all snaps in the pipeline. Click on any snap name to jump to its detailed description.</p>");
            
            // Add toggle buttons for different views
            html.AppendLine("      <div class=\"snap-list-controls\">");
            html.AppendLine("        <button class=\"snap-list-toggle active\" data-view=\"flow-order\">Flow Order</button>");
            html.AppendLine("        <button class=\"snap-list-toggle\" data-view=\"categories\">By Category</button>");
            html.AppendLine("      </div>");

            // Flow Order View
            html.AppendLine("      <div id=\"snap-list-flow-order\" class=\"snap-list-view\">");
            html.AppendLine("        <h3>Snaps in Flow Order</h3>");
            html.AppendLine("        <p>Snaps listed in the order they appear in the pipeline flow:</p>");
            html.AppendLine("        <div class=\"snap-flow-list\">");
            
            // Create flow-ordered list using helper class
            var flowOrderedSnaps = GetSnapFlowOrder(pipeline);
            int snapIndex = 1;
            
            foreach (var snap in flowOrderedSnaps)
            {
                string categoryClass = GetCategoryClass(snap.Category);
                string snapId = GenerateSnapId(snap);
                
                html.AppendLine($"          <div class=\"snap-flow-item\">");
                html.AppendLine($"            <span class=\"snap-number\">{snapIndex}</span>");
                html.AppendLine($"            <span class=\"snap-category {categoryClass}\"></span>");
                html.AppendLine($"            <a href=\"#{snapId}\" class=\"snap-link\">{snap.Label}</a>");
                html.AppendLine($"            <span class=\"snap-type\">({snap.Type})</span>");
                html.AppendLine($"          </div>");
                snapIndex++;
            }
            
            html.AppendLine("        </div>");
            html.AppendLine("      </div>");

            // Category View
            html.AppendLine("      <div id=\"snap-list-categories\" class=\"snap-list-view\" style=\"display: none;\">");
            html.AppendLine("        <h3>Snaps by Category</h3>");
            html.AppendLine("        <p>Snaps organized by their functional categories:</p>");
            
            // Group snaps by category for the list
            var snapsByCategory = pipeline.Snaps.GroupBy(s => s.Category).OrderBy(g => g.Key.ToString());
            
            foreach (var categoryGroup in snapsByCategory)
            {
                if (!categoryGroup.Any()) continue;
                
                string categoryName = GetCategoryDisplayName(categoryGroup.Key);
                string categoryClass = GetCategoryClass(categoryGroup.Key);
                
                html.AppendLine($"        <div class=\"snap-category-group\">");
                html.AppendLine($"          <h4><span class=\"snap-category {categoryClass}\"></span>{categoryName} ({categoryGroup.Count()})</h4>");
                html.AppendLine($"          <div class=\"snap-category-items\">");
                
                foreach (var snap in categoryGroup.OrderBy(s => s.Label))
                {
                    string snapId = GenerateSnapId(snap);
                    html.AppendLine($"            <div class=\"snap-category-item\">");
                    html.AppendLine($"              <a href=\"#{snapId}\" class=\"snap-link\">{snap.Label}</a>");
                    html.AppendLine($"              <span class=\"snap-type\">({snap.Type})</span>");
                    html.AppendLine($"            </div>");
                }
                
                html.AppendLine($"          </div>");
                html.AppendLine($"        </div>");
            }
            
            html.AppendLine("      </div>");
            html.AppendLine("    </div>");

            // Enhanced Flow Control Diagrams Section
            var enhancedFlowControlSnaps = pipeline.Snaps.Where(s =>
                s.Type.Equals("Router", StringComparison.OrdinalIgnoreCase) ||
                s.Type.Equals("Join", StringComparison.OrdinalIgnoreCase) ||
                s.Type.Equals("Union", StringComparison.OrdinalIgnoreCase))
                .ToList();

            if (enhancedFlowControlSnaps.Any())
            {
                LogMessage($"[FLOW-DIAGRAM] Starting Enhanced Flow Control Diagrams section with {enhancedFlowControlSnaps.Count} flow control snaps");
                LogMessage($"[FLOW-DIAGRAM] Flow control snaps found: {string.Join(", ", enhancedFlowControlSnaps.Select(s => $"{s.Type}:{s.Label}"))}");

                html.AppendLine("    <div class=\"section\" id=\"enhanced-flow-control\">");
                html.AppendLine("      <h2>Enhanced Flow Control Diagrams</h2>");
                html.AppendLine("      <p>These diagrams show the detailed flow paths and conditions for Router, Join, and Union snaps in your pipeline.</p>");

                var flowControlDiagramGenerator = new FlowControlDiagramGenerator();
                foreach (var snap in enhancedFlowControlSnaps)
                {
                    try
                    {
                        string enhancedDiagram = "";

                        if (snap.Type.Equals("Router", StringComparison.OrdinalIgnoreCase))
                        {
                            LogMessage($"[FLOW-DIAGRAM] Generating Router flow diagram for snap '{snap.Label}'");
                            enhancedDiagram = flowControlDiagramGenerator.GenerateRouterFlowDiagram(snap, pipeline.Snaps);
                            LogMessage($"[FLOW-DIAGRAM] Router flow diagram generated for '{snap.Label}', diagram length: {enhancedDiagram?.Length ?? 0} characters");
                        }
                        else if (snap.Type.Equals("Join", StringComparison.OrdinalIgnoreCase))
                        {
                            LogMessage($"[FLOW-DIAGRAM] Generating Join flow diagram for snap '{snap.Label}'");
                            enhancedDiagram = flowControlDiagramGenerator.GenerateJoinFlowDiagram(snap, pipeline.Snaps);
                            LogMessage($"[FLOW-DIAGRAM] Join flow diagram generated for '{snap.Label}', diagram length: {enhancedDiagram?.Length ?? 0} characters");
                        }
                        else if (snap.Type.Equals("Union", StringComparison.OrdinalIgnoreCase))
                        {
                            enhancedDiagram = flowControlDiagramGenerator.GenerateUnionFlowDiagram(snap, pipeline.Snaps);
                        }

                        if (!string.IsNullOrEmpty(enhancedDiagram))
                        {
                            // Add specific logging for Router and Join diagram insertion
                            if (snap.Type.Equals("Router", StringComparison.OrdinalIgnoreCase))
                            {
                                LogMessage($"[FLOW-DIAGRAM] Inserting Router flow diagram into HTML documentation for snap '{snap.Label}'");
                            }
                            else if (snap.Type.Equals("Join", StringComparison.OrdinalIgnoreCase))
                            {
                                LogMessage($"[FLOW-DIAGRAM] Inserting Join flow diagram into HTML documentation for snap '{snap.Label}'");
                            }
                            html.AppendLine($"      <div class=\"flow-control-snap\">");
                            html.AppendLine($"        <h3>{snap.Type}: {snap.Label}</h3>");
                            html.AppendLine("        <div class=\"diagram-container\">");
                            html.AppendLine(enhancedDiagram);
                            html.AppendLine("        </div>");
                            html.AppendLine("      </div>");
                        }
                    }
                    catch (Exception ex)
                    {
                        html.AppendLine($"      <div class=\"flow-control-snap\">");
                        html.AppendLine($"        <h3>{snap.Type}: {snap.Label}</h3>");
                        html.AppendLine($"        <p class=\"error\">Unable to generate enhanced diagram: {ex.Message}</p>");
                        html.AppendLine("      </div>");
                    }
                }

                html.AppendLine("    </div>");
            }

            // Parameters Section
            html.AppendLine("    <div class=\"section\" id=\"parameters\">");
            html.AppendLine("      <h2>Pipeline Parameters</h2>");

            if (pipeline.Parameters.Any())
            {
                html.AppendLine("      <table>");
                html.AppendLine("        <tr><th>Parameter</th><th>Type</th><th>Default Value</th><th>Required</th><th>Description</th></tr>");

                foreach (var param in pipeline.Parameters)
                {
                    html.AppendLine($"        <tr><td>{param.Key}</td><td>{param.DataType}</td><td>{param.Value}</td><td>{(param.Required ? "Yes" : "No")}</td><td>{param.Description}</td></tr>");
                }

                html.AppendLine("      </table>");
            }
            else
            {
                html.AppendLine("      <p>This pipeline does not define any parameters.</p>");
            }

            html.AppendLine("    </div>");

            // Snaps Section
            html.AppendLine("    <div class=\"section\" id=\"snaps\">");
            html.AppendLine("      <h2>Pipeline Snaps</h2>");

            // Group snaps by category
            Console.WriteLine($"[DEBUG-FILTER] Total snaps in pipeline: {pipeline.Snaps.Count}");
            foreach (var snap in pipeline.Snaps)
            {
                Console.WriteLine($"[DEBUG-FILTER] Snap '{snap.Label}' has Category: {snap.Category} (Type: {snap.Type})");
            }
            
            var flowControlSnaps = pipeline.Snaps.Where(s => s.Category == SnapCategory.FlowControl).ToList();
            var transformationSnaps = pipeline.Snaps.Where(s => s.Category == SnapCategory.Transformation).ToList();
            var databaseSnaps = pipeline.Snaps.Where(s => s.Category == SnapCategory.Database).ToList();
            Console.WriteLine($"[DEBUG-FILTER-RESULT] Database snaps filtered: {databaseSnaps.Count}");
            foreach (var dbSnap in databaseSnaps)
            {
                Console.WriteLine($"[DEBUG-FILTER-RESULT] Database snap: '{dbSnap.Label}' (Category: {dbSnap.Category})");
            }
            var externalSystemSnaps = pipeline.Snaps.Where(s => s.Category == SnapCategory.ExternalSystem).ToList();
            var fileOperationSnaps = pipeline.Snaps.Where(s => s.Category == SnapCategory.FileOperation).ToList();
            var errorHandlingSnaps = pipeline.Snaps.Where(s => s.Category == SnapCategory.ErrorHandling).ToList();
            var otherSnaps = pipeline.Snaps.Where(s => s.Category == SnapCategory.Other).ToList();            // The current pipeline has already been set in the _currentPipeline field            // Flow Control Snaps
            LogMessage($"[SNAP-CATEGORY] Starting Flow Control section with {flowControlSnaps.Count} snaps");

            // DEBUG: Show all snaps in this category to help identify condition snaps
            Console.WriteLine($"[DEBUG] ===== ALL SNAPS IN CATEGORY 'Flow Control' =====");
            for (int i = 0; i < flowControlSnaps.Count; i++)
            {
                var snap = flowControlSnaps[i];
                Console.WriteLine($"[DEBUG] Snap {i + 1}: '{snap.Label}' (Type: '{snap.Type}', ID: {snap.Id})");
                
                // Check if this looks like a condition snap
                bool couldBeCondition = snap.Type?.ToLower().Contains("condition") == true ||
                    snap.Type?.ToLower().Contains("router") == true ||
                    snap.Type?.ToLower().Contains("branch") == true ||
                    snap.Type?.ToLower().Contains("switch") == true ||
                    snap.Type?.ToLower().Contains("gate") == true ||
                    snap.Type?.ToLower().Contains("flow") == true ||
                    snap.Type?.ToLower().Contains("decision") == true ||
                    snap.Type?.ToLower().Contains("choice") == true ||
                    snap.Type?.ToLower().Contains("if") == true ||
                    snap.Type?.ToLower().Contains("filter") == true ||
                    snap.Type?.ToLower().Contains("-gate") == true ||
                    snap.Type?.ToLower().Contains("-router") == true ||
                    snap.Type?.ToLower().Contains("-branch") == true ||
                    snap.Type?.ToLower().Contains("-switch") == true ||
                    snap.Type?.ToLower().Contains("-condition") == true ||
                    snap.Type?.ToLower().Contains("snaps-flow-") == true ||
                    snap.Type?.ToLower().Contains("snaps-transform-gate") == true ||
                    snap.Type?.ToLower().Contains("snaps-control-") == true;
                
                if (couldBeCondition)
                {
                    Console.WriteLine($"[DEBUG] *** POTENTIAL CONDITION SNAP FOUND: '{snap.Label}' (Type: '{snap.Type}') ***");
                }
            }
            Console.WriteLine($"[DEBUG] ===== END CATEGORY 'Flow Control' =====");

            if (!flowControlSnaps.Any())
            {
                LogMessage($"[SNAP-CATEGORY] No snaps in Flow Control, skipping section");
            }
            else
            {
                await AddSnapCategorySection(html, "Flow Control", "flow-control", flowControlSnaps, cancellationToken).ConfigureAwait(false);
            }

            // Transformation Snaps
            LogMessage($"[SNAP-CATEGORY] Starting Transformations section with {transformationSnaps.Count} snaps");
            await AddSnapCategorySection(html, "Transformations", "transformations", transformationSnaps, cancellationToken).ConfigureAwait(false);

            // Database Snaps
            LogMessage($"[SNAP-CATEGORY] Starting Database Operations section with {databaseSnaps.Count} snaps");
            await AddSnapCategorySection(html, "Database Operations", "database", databaseSnaps, cancellationToken).ConfigureAwait(false);

            // External System Snaps
            LogMessage($"[SNAP-CATEGORY] Starting External Systems section with {externalSystemSnaps.Count} snaps");
            await AddSnapCategorySection(html, "External Systems", "external", externalSystemSnaps, cancellationToken).ConfigureAwait(false);

            // File Operation Snaps
            LogMessage($"[SNAP-CATEGORY] Starting File Operations section with {fileOperationSnaps.Count} snaps");
            await AddSnapCategorySection(html, "File Operations", "file", fileOperationSnaps, cancellationToken).ConfigureAwait(false);

            // Error Handling Snaps
            LogMessage($"[SNAP-CATEGORY] Starting Error Handling section with {errorHandlingSnaps.Count} snaps");
            await AddSnapCategorySection(html, "Error Handling", "error", errorHandlingSnaps, cancellationToken).ConfigureAwait(false);

            // Other Snaps
            LogMessage($"[SNAP-CATEGORY] Starting Other Snaps section with {otherSnaps.Count} snaps");
            await AddSnapCategorySection(html, "Other Snaps", "other", otherSnaps, cancellationToken).ConfigureAwait(false);

            html.AppendLine("    </div>");            // Best Practices Section
            html.AppendLine("    <div class=\"section\" id=\"best-practices\">");
            html.AppendLine("      <h2>Pipeline Best Practices</h2>");
            html.AppendLine("      <div class=\"category-description\">");
            html.AppendLine("        <p>SnapLogic pipelines follow certain best practices for optimal performance, maintainability, and reliability:</p>");
            html.AppendLine("        <h3>General Pipeline Design</h3>");
            html.AppendLine("        <ul>");
            html.AppendLine("          <li><strong>Modularization</strong>: Break complex workflows into smaller, reusable pipelines for easier maintenance and troubleshooting.</li>");
            html.AppendLine("          <li><strong>Parameterization</strong>: Use pipeline parameters for values that might change between environments or executions.</li>");
            html.AppendLine("          <li><strong>Documentation</strong>: Use descriptive snap labels and pipeline notes to explain complex logic and business requirements.</li>");
            html.AppendLine("          <li><strong>Error Handling</strong>: Include proper error handling snaps to catch and manage exceptions gracefully.</li>");
            html.AppendLine("        </ul>");
            html.AppendLine("        <h3>Performance Optimization</h3>");
            html.AppendLine("        <ul>");
            html.AppendLine("          <li><strong>Filtering Early</strong>: Filter data as early as possible in the pipeline to reduce processing load on downstream snaps.</li>");
            html.AppendLine("          <li><strong>Batch Processing</strong>: Configure database operations to use batch mode when processing large datasets.</li>");
            html.AppendLine("          <li><strong>Connection Pooling</strong>: Reuse database connections instead of creating new connections for each operation.</li>");
            html.AppendLine("          <li><strong>Pipeline Splitting</strong>: Use pipeline executes for parallel processing of large workloads.</li>");
            html.AppendLine("        </ul>");
            html.AppendLine("        <h3>Security Considerations</h3>");
            html.AppendLine("        <ul>");
            html.AppendLine("          <li><strong>Credential Management</strong>: Store credentials in account settings rather than hardcoded in the pipeline.</li>");
            html.AppendLine("          <li><strong>Data Encryption</strong>: Enable encryption for sensitive data at rest and in transit.</li>");
            html.AppendLine("          <li><strong>Access Control</strong>: Apply appropriate access controls to pipelines handling sensitive information.</li>");
            html.AppendLine("        </ul>");
            html.AppendLine("      </div>");
            html.AppendLine("    </div>");

            // Execution Flow Section
            html.AppendLine("    <div class=\"section\" id=\"execution-flow\">");
            html.AppendLine("      <h2>Execution Flow</h2>");

            // Add overall pipeline functional description
            html.AppendLine("      <div class=\"category-description\">");
            html.AppendLine("        <h3>Pipeline Function Summary</h3>");
            html.AppendLine("        <p>This section describes how data flows through the pipeline, from start to finish. Understanding the sequence of operations helps identify " +
                           "the main data processing pathways and decision points in the pipeline.</p>");
            html.AppendLine("      </div>");

            // Add entry points section
            html.AppendLine("      <h3>Pipeline Entry Points</h3>");
            html.AppendLine("      <p>The pipeline execution starts at the following entry points:</p>");
            html.AppendLine("      <ul>");

            foreach (var startNode in pipeline.GetStartPoints())
            {
                html.AppendLine($"        <li><strong>{startNode.Label}</strong> ({GetFriendlySnapType(startNode.Type)}) - {GetSnapDescription(startNode)}</li>");
            }

            html.AppendLine("      </ul>");

            // Generate execution flow paths with enhanced descriptions
            html.AppendLine("      <h3>Main Execution Paths</h3>");
            html.AppendLine("      <p>The following sections outline the main paths that data takes through this pipeline:</p>");

            var startPoints = pipeline.GetStartPoints();
            int pathNumber = 0;

            foreach (var startPoint in startPoints)
            {
                html.AppendLine($"      <h4>Paths from {startPoint.Label}</h4>");
                var paths = FindExecutionPaths(pipeline, startPoint);

                foreach (var path in paths)
                {
                    if (path.Count > 1)
                    {
                        pathNumber++;
                        html.AppendLine("      <div class=\"execution-path\">");
                        html.AppendLine($"        <p><strong>Execution Path {pathNumber}:</strong></p>");
                        html.AppendLine("        <ol>");

                        for (int i = 0; i < path.Count; i++)
                        {
                            var node = path[i];
                            if (i == 0)
                            {
                                // Starting node
                                html.AppendLine($"          <li><strong>{node.Label}</strong> - Starts the pipeline execution</li>");
                            }
                            else if (i == path.Count - 1)
                            {
                                // Ending node
                                html.AppendLine($"          <li><strong>{node.Label}</strong> - Final operation in this path</li>");
                            }
                            else
                            {
                                // Intermediate node - add condensed description
                                html.AppendLine($"          <li><strong>{node.Label}</strong> - {GetSnapDescription(node)}</li>");
                            }
                        }

                        html.AppendLine("        </ol>");
                        // Add functional summary of this path
                        html.AppendLine("        <div class=\"function-details\">");
                        html.AppendLine("          <strong>Path Summary:</strong> ");

                        // Generate summary based on the types of nodes in the path
                        string pathSummary = GeneratePathSummary(path);
                        html.AppendLine($"          {pathSummary}");
                        html.AppendLine("        </div>");
                        // Add detailed path analysis
                        html.AppendLine("        <div class=\"technical-details\">");
                        string pathAnalysis = ProvideAdvancedPathAnalysis(path);
                        html.AppendLine($"          {pathAnalysis}");
                        html.AppendLine("        </div>");

                        html.AppendLine("      </div>");

                        // Limit to a reasonable number of paths
                        if (paths.IndexOf(path) >= 2)
                        {
                            html.AppendLine("      <p>Additional paths exist but are not shown for brevity...</p>");
                            break;
                        }
                    }
                }
            }
            html.AppendLine("    </div>");
            // Integration Patterns Section
            html.AppendLine("    <div class=\"section\" id=\"patterns\">");
            html.AppendLine("      <h2>Integration Patterns</h2>");
            html.AppendLine("      <div class=\"category-description\">");
            html.AppendLine("        <p>SnapLogic pipelines commonly implement the following integration patterns. Understanding these patterns helps in designing efficient and maintainable pipelines.</p>");
            html.AppendLine("      </div>");

            // Add the pattern analysis results
            html.AppendLine(_patternAnalyzer.GeneratePatternAnalysisHtml(pipeline));

            html.AppendLine("      <h3>Common Integration Pattern Reference</h3>");
            html.AppendLine("      <div class=\"pattern-container\">");
            html.AppendLine("        <div class=\"pattern-card\">");
            html.AppendLine("          <h3>ETL (Extract, Transform, Load)</h3>");
            html.AppendLine("          <p><strong>Use case:</strong> Data warehousing, reporting, analytics</p>");
            html.AppendLine("          <p><strong>Pattern:</strong> Extract data from source systems â†’ Transform data to meet target requirements â†’ Load data into destination systems</p>");
            html.AppendLine("          <p><strong>Key snaps:</strong> Database Select/Query, REST Get, File Reader, Mapper, Join, Filter, Database Insert/Update, File Writer</p>");
            html.AppendLine("          <p><strong>Best practices:</strong></p>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><strong>Filter data at the source to reduce processing volume</li>");
            html.AppendLine("            <li>Use batch operations for database writes</li>");
            html.AppendLine("            <li>Consider incremental loading for large datasets</li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </div>");

            html.AppendLine("        <div class=\"pattern-card\">");
            html.AppendLine("          <h3>API Integration</h3>");
            html.AppendLine("          <p><strong>Use case:</strong> SaaS integration, microservices communication</p>");
            html.AppendLine("          <p><strong>Pattern:</strong> API Request â†’ Response Parsing â†’ Data Processing â†’ API Response / Database Update</p>");
            html.AppendLine("          <p><strong>Key snaps:</strong> REST Get/Post/Put, SOAP, JSON Parser/Generator, XML Parser/Generator, Mapper</p>");
            html.AppendLine("          <p><strong>Best practices:</strong></p>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><strong>Handle API authentication properly (OAuth, API Keys)</li>");
            html.AppendLine("            <li>Implement retry logic for transient failures</li>");
            html.AppendLine("            <li>Respect API rate limits with throttling</li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </div>");

            html.AppendLine("        <div class=\"pattern-card\">");
            html.AppendLine("          <h3>Data Synchronization</h3>");
            html.AppendLine("          <p><strong>Use case:</strong> Multi-system consistency, master data management</p>");
            html.AppendLine("          <p><strong>Pattern:</strong> Source Change Detection â†’ Lookup Existing Records â†’ Apply Delta Updates â†’ Log Results</p>");
            html.AppendLine("          <p><strong>Key snaps:</strong> Database Select with timestamps/watermarks, Join, Router, Database Upsert, Error Handler</p>");
            html.AppendLine("          <p><strong>Best practices:</strong></p>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li>Track synchronization timestamps for incremental processing</li>");
            html.AppendLine("            <li>Implement conflict resolution strategies</li>");
            html.AppendLine("            <li>Log synchronization results for auditing</li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </div>");

            html.AppendLine("        <div class=\"pattern-card\">");
            html.AppendLine("          <h3>Event Processing</h3>");
            html.AppendLine("          <p><strong>Use case:</strong> Real-time analytics, notifications, workflow triggers</p>");
            html.AppendLine("          <p><strong>Pattern:</strong> Event Capture â†’ Event Filtering/Enrichment â†’ Parallel Processing â†’ Multiple Destinations</p>");
            html.AppendLine("          <p><strong>Key snaps:</strong> REST Listener, Message Queue Consumer, Router, Copy, Mapper, Database Operations</p>");
            html.AppendLine("          <p><strong>Best practices:</strong></p>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><strong>Process events asynchronously when possible</li>");
            html.AppendLine("            <li>Implement idempotent handling for duplicate events</li>");
            html.AppendLine("            <li>Use error handling for graceful failure recovery</li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </div>");

            html.AppendLine("        <div class=\"pattern-card\">");
            html.AppendLine("          <h3>Data Validation & Cleansing</h3>");
            html.AppendLine("          <p><strong>Use case:</strong> Data quality assurance, compliance, governance</p>");
            html.AppendLine("          <p><strong>Pattern:</strong> Data Source â†’ Schema Validation â†’ Business Rule Validation â†’ Routing (Valid/Invalid) â†’ Processing/Notification</p>");
            html.AppendLine("          <p><strong>Key snaps:</strong> JSON/XML Validator, Script, Router, Error Handler, Email</p>");
            html.AppendLine("          <p><strong>Best practices:</strong></p>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><strong>Define clear validation schemas and rules</li>");
            html.AppendLine("            <li>Separate structural validation from business rule validation</li>");
            html.AppendLine("            <li>Create detailed error logs for invalid data</li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </div>");

            html.AppendLine("        <div class=\"pattern-card\">");
            html.AppendLine("          <h3>Batch File Processing</h3>");
            html.AppendLine("          <p><strong>Use case:</strong> Scheduled data exchange, report generation, bulk operations</p>");
            html.AppendLine("          <p><strong>Pattern:</strong> File Detection â†’ File Reading â†’ Processing â†’ Output Generation â†’ File Archiving</p>");
            html.AppendLine("          <p><strong>Key snaps:</strong> File Reader, CSV/JSON/XML Parser, Mapper, Sort, Aggregate, File Writer</p>");
            html.AppendLine("          <p><strong>Best practices:</strong></p>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><strong>Implement file naming conventions for processing order</li>");
            html.AppendLine("            <li>Use staging areas for in-process files</li>");
            html.AppendLine("            <li>Archive processed files with timestamps</li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </div>");
            html.AppendLine("      </div>");
            html.AppendLine("    </div>");

            // Technical Glossary Section
            html.AppendLine("    <div class=\"section\" id=\"glossary\">");
            html.AppendLine("      <h2>Technical Glossary</h2>");
            html.AppendLine("      <div class=\"category-description\">");
            html.AppendLine("        <p>This glossary explains key technical terms and concepts used in SnapLogic pipelines and this documentation.</p>");
            html.AppendLine("      </div>");
            html.AppendLine("      <table>");
            html.AppendLine("        <tr><th>Term</th><th>Definition</th><th>Example</th></tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Snap</td>");
            html.AppendLine("          <td>Pre-built, configurable components that perform specific functions within a pipeline. Each snap is designed for a particular task such as reading data, transforming it, or writing it to a destination.</td>");
            html.AppendLine("          <td>Database - MySQL Select, File - CSV Parser, Transform - Mapper</td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Pipeline</td>");
            html.AppendLine("          <td>A collection of snaps connected together to implement an integration workflow, data process, or automation task.</td>");
            html.AppendLine("          <td>A pipeline that extracts customer data from CRM, transforms it, and loads it into a data warehouse</td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Document</td>");
            html.AppendLine("          <td>The fundamental data unit in SnapLogic, similar to a JSON object. Documents flow through pipelines and contain the data being processed.</td>");
            html.AppendLine("          <td><pre class=\"code-sample\">{\n  \"customer_id\": \"C-1234\",\n  \"name\": \"John Smith\",\n  \"email\": \"<EMAIL>\",\n  \"orders\": [\n    { \"id\": \"O-789\", \"amount: 99.95 }\n  ]\n}</pre></td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>View</td>");
            html.AppendLine("          <td>Input or output ports on snaps that allow them to be connected.</td>");
            html.AppendLine("          <td>A Database Select snap has an output view, while a Mapper snap has both input and output views</td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Link</td>");
            html.AppendLine("          <td>A connection between the output view of one snap to the input view of another, defining the flow of documents.</td>");
            html.AppendLine("          <td>The line connecting a Database Select snap's output to a Mapper snap's input</td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Parameter</td>");
            html.AppendLine("          <td>Named values defined at the pipeline level that can be referenced by snaps, allowing for configurability without modifying pipeline structure.</td>");
            html.AppendLine("          <td><pre class=\"code-sample\">// Define a parameter\nName: database_table\nDefault value: \"customers\"\n\n// Use in a snap\nSELECT * FROM ${database_table}</pre></td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Expression</td>");
            html.AppendLine("          <td>JavaScript-like code snippets used in snaps for data transformation, field mapping, and conditional logic.</td>");
            html.AppendLine("          <td><pre class=\"code-sample\">// Convert name to uppercase\n$customer.name.toUpperCase()\n\n// Calculate total\n$items.reduce((sum, item) => \n  sum + item.price * item.quantity, 0)</pre></td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Mapper</td>");
            html.AppendLine("          <td>A snap that transforms documents by mapping source fields to target fields, often with transformation expressions.</td>");
            html.AppendLine("          <td><pre class=\"code-sample\">// Mapping fields with transformation\ntarget.full_name = $source.first_name + \" \" + $source.last_name;\ntarget.age = new Date().getFullYear() - $source.birth_year;</pre></td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Router</td>");
            html.AppendLine("          <td>A flow control snap that directs documents to different paths based on conditional expressions.</td>");
            html.AppendLine("          <td><pre class=\"code-sample\">// Router conditions\nPath 1: $order.total > 1000  // High-value orders\nPath 2: $order.items.length > 10  // Large orders\nPath 3: true  // Default path</pre></td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Join</td>");
            html.AppendLine("          <td>A flow control snap that combines documents from multiple streams based on matching key values, similar to SQL joins.</td>");
            html.AppendLine("          <td><pre class=\"code-sample\">// Join configuration\nJoin Type: Inner\nJoin Key: $customer_id\nResult: Merged documents with matching IDs</pre></td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Ultra Task</td>");
            html.AppendLine("          <td>A serverless execution environment for running SnapLogic pipelines, automatically scaling resources as needed.</td>");
            html.AppendLine("          <td>Running a nightly data sync pipeline that scales up resources during heavy processing periods</td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Groundplex</td>");
            html.AppendLine("          <td>An on-premises runtime environment for SnapLogic pipelines, used for accessing systems behind firewalls.</td>");
            html.AppendLine("          <td>Using Groundplex to connect to an on-premises database that is not accessible from the public internet</td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Cloudplex</td>");
            html.AppendLine("          <td>A cloud-based runtime environment for SnapLogic pipelines, hosted and managed by SnapLogic.</td>");
            html.AppendLine("          <td>Running pipelines in Cloudplex to integrate SaaS applications like Salesforce and Workday</td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>ETL</td>");
            html.AppendLine("          <td>Extract, Transform, Load - A common integration pattern where data is extracted from source systems, transformed to meet target requirements, and loaded into destination systems.</td>");
            html.AppendLine("          <td>A pipeline that extracts customer data from CRM, transforms fields to match a data warehouse schema, then loads it into the warehouse</td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Batch Processing</td>");
            html.AppendLine("          <td>Processing multiple documents together in a single operation for improved performance, particularly for database operations.</td>");
            html.AppendLine("          <td><pre class=\"code-sample\">// Database Insert configuration\nBatch Size: 100\nBatch documents together for a single database operation</pre></td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Binary Data</td>");
            html.AppendLine("          <td>Non-textual data such as files or images, typically handled as base64-encoded strings within SnapLogic documents.</td>");
            html.AppendLine("          <td><pre class=\"code-sample\">// Binary data in a document\n{\n  \"filename\": \"report.pdf\",\n  \"content_type\": \"application/pdf\",\n  \"data\": \"JVBERi0xLjUNCiW1tbW1DQoxIDAgb2JqDQo...\"\n}</pre></td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Pipeline Execute</td>");
            html.AppendLine("          <td>A snap that triggers execution of another pipeline, allowing for modular design and reuse of common processes.</td>");
            html.AppendLine("          <td>A master pipeline that executes multiple child pipelines for different data sources, then aggregates their results</td>");
            html.AppendLine("        </tr>");
            html.AppendLine("      </table>");
            html.AppendLine("    </div>");
            // HTML footer
            html.AppendLine("  </div>");

            // JavaScript for interactive elements is already included in the head section
            
            // Add snap list toggle functionality
            html.AppendLine("  <script>");
            html.AppendLine("    document.addEventListener('DOMContentLoaded', function() {");
            html.AppendLine("      // Snap list toggle functionality");
            html.AppendLine("      const snapListToggles = document.querySelectorAll('.snap-list-toggle');");
            html.AppendLine("      snapListToggles.forEach(toggle => {");
            html.AppendLine("        toggle.addEventListener('click', function() {");
            html.AppendLine("          // Remove active class from all toggles");
            html.AppendLine("          snapListToggles.forEach(t => t.classList.remove('active'));");
            html.AppendLine("          // Add active class to clicked toggle");
            html.AppendLine("          this.classList.add('active');");
            html.AppendLine("          ");
            html.AppendLine("          // Hide all views");
            html.AppendLine("          document.getElementById('snap-list-flow-order').style.display = 'none';");
            html.AppendLine("          document.getElementById('snap-list-categories').style.display = 'none';");
            html.AppendLine("          ");
            html.AppendLine("          // Show selected view");
            html.AppendLine("          const viewType = this.getAttribute('data-view');");
            html.AppendLine("          if (viewType === 'flow-order') {");
            html.AppendLine("            document.getElementById('snap-list-flow-order').style.display = 'block';");
            html.AppendLine("          } else if (viewType === 'categories') {");
            html.AppendLine("            document.getElementById('snap-list-categories').style.display = 'block';");
            html.AppendLine("          }");
            html.AppendLine("        });");
            html.AppendLine("      });");
            html.AppendLine("    });");
            html.AppendLine("  </script>");
            
            // Add cytoscape.js library
            html.AppendLine("  <!-- Cytoscape.js dependencies -->");
            html.AppendLine("  <script src=\"https://unpkg.com/cytoscape@3.30.3/dist/cytoscape.min.js\"></script>");
            html.AppendLine("  <script src=\"https://unpkg.com/dagre@0.8.5/dist/dagre.min.js\"></script>");
            html.AppendLine("  <script src=\"https://unpkg.com/cytoscape-dagre@2.5.0/cytoscape-dagre.js\"></script>");
            
            html.AppendLine("</body>");
            html.AppendLine("</html>");

            return html.ToString();
        }          // Add non-async version that calls the async version and waits for the result
        public string GenerateHtmlDocumentation(PipelineData pipeline, string diagramSvg)
        {
            return GenerateHtmlDocumentationAsync(pipeline, diagramSvg, null, CancellationToken.None).GetAwaiter().GetResult();
        }

        public string GenerateHtmlDocumentation(PipelineData pipeline, string diagramSvg, ProjectData project)
        {
            return GenerateHtmlDocumentationAsync(pipeline, diagramSvg, project, CancellationToken.None).GetAwaiter().GetResult();
        }
        private async Task AddSnapCategorySection(StringBuilder html, string categoryTitle, string cssClass, List<SnapNode> snaps, CancellationToken cancellationToken = default)
        {
            Console.WriteLine($"[DEBUG-SECTION-START] AddSnapCategorySection called for '{categoryTitle}' with {snaps.Count} snaps");
            foreach (var snap in snaps)
            {
                Console.WriteLine($"[DEBUG-SECTION-START] Snap in {categoryTitle}: '{snap.Label}' (Category: {snap.Category})");
            }
            
            LogMessage($"[SNAP-CATEGORY] Starting {categoryTitle} section with {snaps.Count} snaps");

            // DEBUG: Show all snaps in this category to help identify condition snaps
            Console.WriteLine($"[DEBUG] ===== ALL SNAPS IN CATEGORY '{categoryTitle}' =====");
            for (int i = 0; i < snaps.Count; i++)
            {
                var snap = snaps[i];
                Console.WriteLine($"[DEBUG] Snap {i + 1}: '{snap.Label}' (Type: '{snap.Type}', ID: {snap.Id})");
                
                // Check if this looks like a condition snap
                bool couldBeCondition = snap.Type?.ToLower().Contains("condition") == true ||
                    snap.Type?.ToLower().Contains("router") == true ||
                    snap.Type?.ToLower().Contains("branch") == true ||
                    snap.Type?.ToLower().Contains("switch") == true ||
                    snap.Type?.ToLower().Contains("gate") == true ||
                    snap.Type?.ToLower().Contains("flow") == true ||
                    snap.Type?.ToLower().Contains("decision") == true ||
                    snap.Type?.ToLower().Contains("choice") == true ||
                    snap.Type?.ToLower().Contains("if") == true ||
                    snap.Type?.ToLower().Contains("filter") == true ||
                    snap.Type?.ToLower().Contains("-gate") == true ||
                    snap.Type?.ToLower().Contains("-router") == true ||
                    snap.Type?.ToLower().Contains("-branch") == true ||
                    snap.Type?.ToLower().Contains("-switch") == true ||
                    snap.Type?.ToLower().Contains("-condition") == true ||
                    snap.Type?.ToLower().Contains("snaps-flow-") == true ||
                    snap.Type?.ToLower().Contains("snaps-transform-gate") == true ||
                    snap.Type?.ToLower().Contains("snaps-control-") == true;
                
                if (couldBeCondition)
                {
                    Console.WriteLine($"[DEBUG] *** POTENTIAL CONDITION SNAP FOUND: '{snap.Label}' (Type: '{snap.Type}') ***");
                }
            }
            Console.WriteLine($"[DEBUG] ===== END CATEGORY '{categoryTitle}' =====");

            if (!snaps.Any())
            {
                LogMessage($"[SNAP-CATEGORY] No snaps in {categoryTitle}, skipping section");
                return;
            }

            html.AppendLine($"      <div class=\"snap-category-section {cssClass}\" id=\"{cssClass}\">");
            html.AppendLine($"        <h3>{categoryTitle} ({snaps.Count})</h3>");
            // Add detailed category description
            AddSnapCategoryDocumentation(html, cssClass);
            LogMessage($"[SNAP-CATEGORY] Added category documentation for {categoryTitle}");

            Console.WriteLine($"[DEBUG-BEFORE-LOOP] About to process {snaps.Count} snaps in {categoryTitle}");
            foreach (var snap in snaps)
            {
                Console.WriteLine($"[DEBUG-IN-LOOP] Processing snap: '{snap.Label}' in {categoryTitle}");
                LogMessage($"[SNAP-PROCESS] Processing snap: {snap.Label} (Type: {snap.Type})");
                Console.WriteLine($"[DEBUG-AFTER-LOG] After LogMessage for snap: '{snap.Label}'");
                
                html.AppendLine($"        <div class=\"snap-details\" id=\"{GenerateSnapId(snap)}\">");
                html.AppendLine($"          <h4>{snap.Label}</h4>");
                html.AppendLine($"          <p><strong>Type:</strong> {GetFriendlySnapType(snap.Type)}</p>");

                // Add database connection information for all snaps (excluding mapper/transformation snaps)
                Console.WriteLine($"[DEBUG-CONNECTION-CHECK] Checking connection info for snap '{snap.Label}'. Category: {snap.Category}, Type: {snap.Type}");
                LogMessage($"[CONNECTION] Processing snap for connection info: {snap.Label} (Type: {snap.Type}, Category: {snap.Category})");
                
                // Skip connection info for mapper/transformation type snaps
                bool isMapperOrTransform = snap.Type?.ToLower().Contains("datatransform") == true ||
                                         snap.Type?.ToLower().Contains("mapper") == true ||
                                         snap.Type?.ToLower().Contains("transform") == true ||
                                         snap.Category == SnapCategory.Transformation;
                
                if (!isMapperOrTransform)
                {
                    Console.WriteLine($"[DEBUG-BEFORE-GET-INFO] About to call GetConnectionInfo for: '{snap.Label}'");
                    Console.WriteLine($"[DEBUG-RAW-JSON] RawPipelineJson is null: {_currentPipeline?.RawPipelineJson == null}");
                    if (_currentPipeline?.RawPipelineJson != null)
                    {
                        Console.WriteLine($"[DEBUG-RAW-JSON] RawPipelineJson has snaps: {_currentPipeline.RawPipelineJson["snaps"] != null}");
                    }
                    string connectionInfo = GetConnectionInfo(snap, _currentPipeline?.RawPipelineJson);
                    Console.WriteLine($"[DEBUG-AFTER-GET-INFO] GetConnectionInfo returned: '{connectionInfo ?? "NULL"}'");
                    if (!string.IsNullOrEmpty(connectionInfo) && connectionInfo != "NULL")
                    {
                        LogMessage($"[CONNECTION] Found connection info: {connectionInfo}");
                        string displayLabel = snap.Category == SnapCategory.Database ? "Database/Connector" : "Account/Connection";
                        html.AppendLine($"          <p><strong>{displayLabel}:</strong> {System.Net.WebUtility.HtmlEncode(connectionInfo)}</p>");
                    }
                    else
                    {
                        LogMessage($"[CONNECTION] No connection info found for: {snap.Label}");
                    }
                }
                else
                {
                    Console.WriteLine($"[DEBUG-SKIP-CONNECTION] Skipping connection info for mapper/transform snap: '{snap.Label}'");
                    LogMessage($"[CONNECTION] Skipping connection info for mapper/transform snap: {snap.Label}");
                }
                
                // Add Dynamics365ForSales-specific details
                if (snap.Type?.ToLower().Contains("dynamics365forsales") == true)
                {
                    Console.WriteLine($"[DEBUG-DYNAMICS365] Adding Dynamics365ForSales details for: '{snap.Label}'");
                    LogMessage($"[DYNAMICS365] Adding detailed configuration for snap: {snap.Label}");
                    string dynamics365Details = GetDynamics365ForSalesDetails(snap);
                    if (!string.IsNullOrEmpty(dynamics365Details))
                    {
                        html.AppendLine(dynamics365Details);
                    }
                }
                
                // Add SQL statement for SQL execute snaps
                if (snap.Category == SnapCategory.Database && snap.Type?.ToLower().Contains("execute") == true)
                {
                    LogMessage($"[SQL-EXTRACT] Processing SQL execute snap: {snap.Label}");
                    string sqlStatement = GetSqlStatement(snap);
                    if (!string.IsNullOrEmpty(sqlStatement))
                    {
                        html.AppendLine($"          <div style=\"margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-left: 4px solid #28a745;\">");
                        html.AppendLine($"            <h5 style=\"color: #28a745; margin: 0 0 8px 0;\">SQL Statement</h5>");
                        html.AppendLine($"            <pre style=\"background-color: #f1f3f4; padding: 8px; border-radius: 4px; overflow-x: auto; font-family: 'Courier New', monospace; white-space: pre-wrap;\">");
                        html.AppendLine($"              <code>{System.Net.WebUtility.HtmlEncode(sqlStatement)}</code>");
                        html.AppendLine($"            </pre>");
                        html.AppendLine($"          </div>");
                    }
                }
                
                // Description
                string description = GetSnapDescription(snap);
                if (!string.IsNullOrEmpty(description))
                {
                    html.AppendLine($"          <p><strong>Function:</strong> {description}</p>");
                }

                // Add AI-enhanced description if available
                if (_useAI)
                {
                    LogMessage($"[AI-PROCESS] Starting AI processing for snap: {snap.Label}");
                    try
                    {                        // Use the current pipeline that was already set
                        if (_currentPipeline != null)
                        {
                            // Track unique snap types to avoid generating descriptions for identical snap types
                            string snapTypeKey = $"{snap.Category}-{snap.Type}";
                            LogMessage($"[AI-PROCESS] Checking if snap type {snapTypeKey} already processed");

                            if (!_processedSnapTypes.Contains(snapTypeKey))
                            {
                                LogMessage($"[AI-PROCESS] Snap type {snapTypeKey} not processed yet, adding to processed list");
                                _processedSnapTypes.Add(snapTypeKey);

                                LogMessage($"[AI-PROCESS] Calling GenerateEnhancedSnapDescription for snap: {snap.Label}");
                                string enhancedDescription = await _aiGenerator.GenerateEnhancedSnapDescription(snap, _currentPipeline, cancellationToken).ConfigureAwait(false);
                                LogMessage($"[AI-PROCESS] GenerateEnhancedSnapDescription completed for snap: {snap.Label}");                                if (!string.IsNullOrEmpty(enhancedDescription))
                                {
                                    LogMessage($"[AI-PROCESS] Adding enhanced description to HTML for snap: {snap.Label}");
                                    
                                    // Use the improved context-aware placeholder processing
                                    string processedDescription = ReplaceAllPlaceholders(enhancedDescription);
                                    
                                    html.AppendLine($"          <div class=\"ai-description\">");
                                    html.AppendLine($"            <h5>Enhanced Description</h5>");
                                    html.AppendLine($"            <div>{ConvertMarkdownToHtml(processedDescription)}</div>");
                                    html.AppendLine($"          </div>");
                                }// Generate pseudocode for the snap logic, but skip for certain snap types
                                string snapTypeLower = snap.Type.ToLower();
                                bool isMapperType = snapTypeLower.Contains("map") || snapTypeLower.Contains("datatransform") || snapTypeLower.Contains("transform");
                                bool isExitOrUnionType = snapTypeLower.Contains("exit") || snapTypeLower.Contains("union");
                                bool isCopyType = snapTypeLower.Contains("copy");
                                // Check for router snaps which should use explicit pseudocode generation
                                bool isRouterType = snap.Category == SnapCategory.FlowControl && snapTypeLower.Contains("router");
                                bool isDatabase = snap.Category == SnapCategory.Database;
                                bool isDynamics365 = snap.Type?.ToLower().Contains("dynamics365forsales") == true;

                                LogMessage($"[AI-PROCESS] Checking pseudocode eligibility for snap: {snap.Label} (isMapper: {isMapperType}, isRouter: {isRouterType}, isExitOrUnion: {isExitOrUnionType}, isCopy: {isCopyType}, isDatabase: {isDatabase}, isDynamics365: {isDynamics365})");
                                // Skip pseudocode generation for mapper, router, exit, union, copy, database, and dynamics365 snaps
                                if (!isMapperType && !isRouterType && !isExitOrUnionType && !isCopyType && !isDatabase && !isDynamics365)
                                {
                                    LogMessage($"[AI-CALL] Calling GenerateSnapPseudocode for snap: {snap.Label}"); string pseudocode = await _aiGenerator.GenerateSnapPseudocode(snap, _currentPipeline, cancellationToken).ConfigureAwait(false);
                                    LogMessage($"[AI-CALL] GenerateSnapPseudocode completed for snap: {snap.Label}");
                                    LogMessage($"[AI-CALL] Pseudocode result length: {pseudocode?.Length ?? 0}");

                                    if (!string.IsNullOrEmpty(pseudocode))
                                    {
                                        LogMessage($"[AI-PROCESS] Adding pseudocode to HTML for snap: {snap.Label}");
                                        html.AppendLine($"          <div class=\"code-example\">");
                                        html.AppendLine($"            <div class=\"code-title\">Pseudocode Implementation</div>");                                        html.AppendLine($"            <div class=\"code-content\">");
                                        html.AppendLine($"              <div class=\"code-sample\">");
                                        html.AppendLine($"<pre>{ConvertMarkdownToHtml(pseudocode)}</pre>");
                                        html.AppendLine($"              </div>");
                                        html.AppendLine($"            </div>");
                                        html.AppendLine($"          </div>");
                                    }
                                }
                                else
                                {
                                    LogMessage($"[AI-PROCESS] Skipping pseudocode generation for snap type: {snap.Type}");
                                }
                            }
                            else
                            {
                                LogMessage($"[AI-PROCESS] Snap type {snapTypeKey} already processed, skipping AI generation");
                            }
                        }
                        else
                        {
                            LogMessage($"[AI-ERROR] Current pipeline is null, cannot process AI for snap: {snap.Label}");
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log the error but don't disrupt the document generation
                        LogMessage($"[AI-ERROR] Error generating AI content for snap {snap.Label}: {ex.Message}");
                        Console.WriteLine($"Error generating AI content for snap {snap.Label}: {ex.Message}");
                    }
                }
                else
                {
                    LogMessage($"[AI-PROCESS] AI not enabled, skipping AI processing for snap: {snap.Label}");
                }

                LogMessage($"[SNAP-PROCESS] Completed AI processing section for snap: {snap.Label}");
                
                // Add functional details if available - prioritize showing actual mappings
                // Note: functionDetails will be populated later after isFlowControlSnap is determined
                string functionDetails = "";

                // Add best practices if available - pass flag to avoid duplicate mapping extraction                
                bool hasMappings = !string.IsNullOrEmpty(functionDetails) &&
                                 (snap.Category == SnapCategory.Transformation &&
                                 (snap.Type?.ToLower().Contains("map") == true ||
                                  snap.Type?.ToLower().Contains("transform") == true ||
                                  snap.Type?.ToLower().Contains("script") == true));

                Console.WriteLine($"[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if '{snap.Label}' is flow control snap");
                LogMessage($"[SNAP-PROCESS] About to check flow control status for snap: {snap.Label}");
                
                // Check if this is a flow control snap that should use enhanced configuration
                bool isFlowControlSnap = IsFlowControlSnap(snap);

                Console.WriteLine($"[DEBUG-AFTER-FLOW-CONTROL-CHECK] '{snap.Label}' isFlowControlSnap: {isFlowControlSnap}");
                LogMessage($"[SNAP-PROCESS] Adding properties for snap: {snap.Label} (hasMappings: {hasMappings}, isFlowControlSnap: {isFlowControlSnap})");

                // Debug check for Join snap detection
                if (snap.Type?.ToLower().Contains("join") == true)
                {
                    Console.WriteLine($"[DEBUG-JOIN-DETECTION] Found Join snap: '{snap.Label}' (Type: {snap.Type}), isFlowControlSnap: {isFlowControlSnap}");
                    LogMessage($"[JOIN-DEBUG] Found potential Join snap: {snap.Label} (Type: {snap.Type})");
                    LogMessage($"[JOIN-DEBUG] IsFlowControlSnap result: {isFlowControlSnap}");
                }
                
                Console.WriteLine($"[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for '{snap.Label}'");
                // For flow control snaps (Router, Join, Union), use enhanced configuration instead of raw properties
                if (isFlowControlSnap)
                {
                    Console.WriteLine($"[DEBUG-FLOW-CONTROL-ENTERED] *** ENTERED FLOW CONTROL CONFIGURATION SECTION FOR '{snap.Label}' ***");
                    LogMessage($"[FLOW-CONTROL-CONFIG] Generating enhanced configuration for {snap.Type} snap: {snap.Label}");
                    try
                    {
                        Console.WriteLine($"[DEBUG-FLOW-CONTROL-TRY] *** ENTERED TRY BLOCK FOR FLOW CONTROL CONFIG: '{snap.Label}' ***");
                        LogMessage($"[FLOW-CONTROL-TRY] Starting flow control configuration generation for: {snap.Label}");
                        
                        var flowControlGenerator = new FlowControlConfigurationGenerator();
                        Console.WriteLine($"[DEBUG-FLOW-CONTROL-GENERATOR] FlowControlConfigurationGenerator created successfully for: '{snap.Label}'");
                        
                        // Set up the snap with connections for the configuration generator
                        Console.WriteLine($"[DEBUG-SETUP-CONNECTIONS] About to call SetupSnapConnections for: '{snap.Label}'");
                        LogMessage($"[FLOW-CONTROL-SETUP] Setting up snap connections for: {snap.Label}");
                        
                        SetupSnapConnections(snap);
                        
                        Console.WriteLine($"[DEBUG-SETUP-CONNECTIONS] SetupSnapConnections completed for: '{snap.Label}'");
                        LogMessage($"[FLOW-CONTROL-SETUP] Snap connections setup completed for: {snap.Label}");
                        
                        // Generate the appropriate configuration based on snap type
                        Console.WriteLine($"[DEBUG-FLOW-CONTROL] About to start snap type detection for: '{snap.Label}'");
                        LogMessage($"[FLOW-CONTROL-START] Starting snap type detection for: {snap.Label}");
                        
                        string flowControlConfig = "";
                        Console.WriteLine($"[DEBUG-FLOW-CONTROL] flowControlConfig variable initialized for: '{snap.Label}'");
                        
                        string snapTypeLower = snap.Type?.ToLower() ?? "";
                        Console.WriteLine($"[DEBUG-FLOW-CONTROL] snapTypeLower = '{snapTypeLower}' for: '{snap.Label}'");
                        
                        Console.WriteLine($"[DEBUG-FLOW-CONTROL] About to call LogMessage for snap type detection: '{snap.Label}'");
                        LogMessage($"[FLOW-CONTROL-CONFIG] Snap type detection: '{snapTypeLower}' for snap: {snap.Label}");
                        
                        bool hasRouter = snapTypeLower.Contains("router");
                        bool hasJoin = snapTypeLower.Contains("join");
                        bool hasUnion = snapTypeLower.Contains("union");
                        
                        Console.WriteLine($"[DEBUG-FLOW-CONTROL] Computed boolean values - router: {hasRouter}, join: {hasJoin}, union: {hasUnion}");
                        
                        LogMessage($"[FLOW-CONTROL-CONFIG] Contains 'router'? {hasRouter}");
                        LogMessage($"[FLOW-CONTROL-CONFIG] Contains 'join'? {hasJoin}");
                        LogMessage($"[FLOW-CONTROL-CONFIG] Contains 'union'? {hasUnion}");
                        
                        Console.WriteLine($"[DEBUG-FLOW-CONTROL] About to check configuration type for: '{snap.Label}'");
                        
                        if (hasRouter)
                        {
                            Console.WriteLine($"[DEBUG-FLOW-CONTROL] Detected ROUTER snap: '{snap.Label}'");
                            LogMessage($"[FLOW-CONTROL-CONFIG] Generating router configuration for: {snap.Label}");
                            flowControlConfig = flowControlGenerator.GenerateRouterConfiguration(snap, _currentPipeline.Snaps);
                        }
                        else if (hasJoin)
                        {
                            Console.WriteLine($"[DEBUG-FLOW-CONTROL] Detected JOIN snap: '{snap.Label}'");
                            Console.WriteLine($"[FLOW-CONTROL-CONFIG] *** GENERATING JOIN CONFIGURATION *** for: {snap.Label}");
                            
                            Console.WriteLine($"[DEBUG-JOIN-CALL] About to call GenerateJoinConfiguration for: '{snap.Label}'");
                            flowControlConfig = flowControlGenerator.GenerateJoinConfiguration(snap, _currentPipeline.Snaps);
                            
                            Console.WriteLine($"[DEBUG-JOIN-RESULT] GenerateJoinConfiguration returned {flowControlConfig?.Length ?? 0} characters for: '{snap.Label}'");
                        }
                        else if (hasUnion)
                        {
                            Console.WriteLine($"[DEBUG-FLOW-CONTROL] Detected UNION snap: '{snap.Label}'");
                            LogMessage($"[FLOW-CONTROL-CONFIG] Generating union configuration for: {snap.Label}");
                            flowControlConfig = flowControlGenerator.GenerateUnionConfiguration(snap, _currentPipeline.Snaps);
                        }
                        else
                        {
                            LogMessage($"[FLOW-CONTROL-CONFIG] Using router configuration as fallback for: {snap.Label}");
                            flowControlConfig = flowControlGenerator.GenerateRouterConfiguration(snap, _currentPipeline.Snaps);
                        }
                        
                        html.AppendLine(flowControlConfig);
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"[FLOW-CONTROL-CONFIG] Error generating enhanced configuration for {snap.Type}: {ex.Message}");
                    }
                }

                // Generate function details only for non-flow control snaps
                if (!isFlowControlSnap)
                {
                    LogMessage($"[SNAP-PROCESS] Starting function details for non-flow control snap: {snap.Label}");
                    functionDetails = ProvideSnapFunctionDetails(snap);
                    LogMessage($"[SNAP-PROCESS] Completed function details for snap: {snap.Label}, result length: {functionDetails?.Length ?? 0}");

                    if (!string.IsNullOrEmpty(functionDetails))
                    {
                        html.AppendLine($"          <div class=\"function-details\">");
                        html.AppendLine($"            {functionDetails}");
                        html.AppendLine($"          </div>");
                    }
                }
                else
                {
                    LogMessage($"[SNAP-PROCESS] Skipping function details for flow control snap: {snap.Label} (already has enhanced configuration)");
                }

                // Properties - Display important configuration settings (skip for mapper snaps and flow control snaps)
                if (snap.Properties.Any() && !hasMappings && !isFlowControlSnap)
                {
                }
                
                // Add collapsible section for raw properties (except for router and condition snaps)
                bool skipRawProperties = (snap.Type?.ToLower()?.Contains("router") ?? false) || 
                                        (snap.Type?.ToLower()?.Contains("condition") ?? false);
                if (!skipRawProperties)
                {
                    html.AppendLine($"          <button class=\"collapsible\">View Raw Properties</button>");
                    html.AppendLine($"          <div class=\"collapsible-content\">");
                    DisplayRawProperties(html, snap);
                    html.AppendLine($"          </div>");
                }
                else
                {
                    LogMessage($"[SNAP-PROPERTIES] Skipping collapsible raw properties section for snap: {snap.Label} as it is a router snap");
                }

                LogMessage($"[SNAP-PROCESS] Starting best practices for snap: {snap.Label}");
                string bestPractices = ""; // Temporarily disabled until method signature is fixed
                LogMessage($"[SNAP-PROCESS] Completed best practices for snap: {snap.Label}, result length: {bestPractices?.Length ?? 0}");
                if (!string.IsNullOrEmpty(bestPractices))
                {
                    html.AppendLine($"          {bestPractices}");
                }

                html.AppendLine($"        </div>");
                LogMessage($"[SNAP-PROCESS] Completed processing snap: {snap.Label}");
            }

            html.AppendLine($"      </div>");
            LogMessage($"[SNAP-CATEGORY] Completed {categoryTitle} section");
        }
      
    }
}
