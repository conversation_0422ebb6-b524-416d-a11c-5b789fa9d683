using System;
using System.Collections.Generic;
using System.Linq;

namespace SnapDocumenter
{
    class TestJoinFix
    {
        static void Main(string[] args)
        {
            Console.WriteLine("Testing Join snap enhanced configuration fix...");
            
            // Create a test Join snap with the actual property structure from pipeline JSON
            var testJoinSnap = new SnapNode
            {
                SnapType = "join",
                InstanceId = "test_join",
                Properties = new Dictionary<string, string>
                {
                    // Simulate the actual nested property structure
                    {"settings.joinPaths.value.0.leftPath.value", "$nch_propertyid"},
                    {"settings.joinPaths.value.0.rightPath.value", "$_nch_relatedproperty_value"},
                    {"settings.joinType.value", "Inner"},
                    {"settings.joinPaths.value.0.leftPath.expression", "true"},
                    {"settings.joinPaths.value.0.rightPath.expression", "true"}
                }
            };

            try
            {
                var generator = new FlowControlConfigurationGenerator();
                var enhancedConfig = generator.GenerateJoinConfiguration(testJoinSnap);
                
                Console.WriteLine("Enhanced Join Configuration Generated:");
                Console.WriteLine("=====================================");
                Console.WriteLine(enhancedConfig);
                
                if (enhancedConfig.Contains("$nch_propertyid") && 
                    enhancedConfig.Contains("$_nch_relatedproperty_value") &&
                    enhancedConfig.Contains("Inner"))
                {
                    Console.WriteLine("\n✅ SUCCESS: Join conditions and type extracted correctly!");
                }
                else
                {
                    Console.WriteLine("\n❌ FAILED: Join conditions or type not extracted properly");
                    Console.WriteLine("Looking for: $nch_propertyid, $_nch_relatedproperty_value, Inner");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
