# PowerShell script to fix the main display logic to show account info for all snaps
# The issue is in the main snap processing loop where connection info is only shown for Database category

$filePath = "DocumentationGenerator.cs"

Write-Host "Fixing main display logic for account info..." -ForegroundColor Green

$content = Get-Content $filePath -Raw

# Fix the main condition that restricts account display to database snaps only
# We need to change this logic to show connection info for ANY snap that has account info

# Find and replace the database-only check in the main display logic
$oldDisplayLogic = @'
                // Add database connection information for database snaps
                Console.WriteLine($"[DEBUG-DB-CHECK] Checking if snap '{snap.Label}' is database category. Category: {snap.Category}");
                if (snap.Category == SnapCategory.Database)
'@

$newDisplayLogic = @'
                // Add connection information for any snap that has account info (expanded from database-only)
                Console.WriteLine($"[DEBUG-ACCOUNT-CHECK] Checking if snap '{snap.Label}' has account info. Category: {snap.Category}, Type: {snap.Type}");
                
                // Check if this snap might have account/connection information
                bool mightHaveAccountInfo = 
                    snap.Category == SnapCategory.Database ||  // Database snaps
                    (snap.Type?.ToLower().Contains("dynamics365forsales") == true) ||  // Dynamics365 snaps
                    snap.Properties.ContainsKey("account") ||  // Has account property
                    snap.Properties.Any(p => p.Key.ToLower().Contains("connect") || p.Key.ToLower().Contains("database") || p.Key.ToLower().Contains("server"));  // Has connection-related properties
                
                if (mightHaveAccountInfo)
'@

if ($content.Contains($oldDisplayLogic)) {
    $content = $content.Replace($oldDisplayLogic, $newDisplayLogic)
    Write-Host "✅ Updated main display logic to check for account info in all snaps" -ForegroundColor Yellow
} else {
    Write-Host "❌ Old display logic not found exactly - trying pattern match" -ForegroundColor Yellow
    
    # Try a more flexible pattern
    $pattern = 'if \(snap\.Category == SnapCategory\.Database\)\s*\{'
    $replacement = @'
// Check if this snap might have account/connection information
                bool mightHaveAccountInfo = 
                    snap.Category == SnapCategory.Database ||  // Database snaps
                    (snap.Type?.ToLower().Contains("dynamics365forsales") == true) ||  // Dynamics365 snaps
                    snap.Properties.ContainsKey("account") ||  // Has account property
                    snap.Properties.Any(p => p.Key.ToLower().Contains("connect") || p.Key.ToLower().Contains("database") || p.Key.ToLower().Contains("server"));  // Has connection-related properties
                
                if (mightHaveAccountInfo)
                {
'@
    
    if ($content -match $pattern) {
        $content = $content -replace $pattern, $replacement
        Write-Host "✅ Updated main display logic using pattern match" -ForegroundColor Yellow
    } else {
        Write-Host "❌ Could not find database category check pattern" -ForegroundColor Red
    }
}

# Also update the else condition message
$oldElseMessage = 'Console.WriteLine($"[CONNECTION] Snap {snap.Label} is not categorized as Database (Category: {snap.Category})");'
$newElseMessage = 'Console.WriteLine($"[CONNECTION] Snap {snap.Label} does not appear to have account info (Category: {snap.Category}, Type: {snap.Type})");'

if ($content.Contains($oldElseMessage)) {
    $content = $content.Replace($oldElseMessage, $newElseMessage)
    Write-Host "✅ Updated else condition message" -ForegroundColor Yellow
}

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "✅ Main display logic fix applied!" -ForegroundColor Green
Write-Host "Now connection info will be checked for:" -ForegroundColor Cyan
Write-Host "  • Database snaps (as before)" -ForegroundColor White
Write-Host "  • Dynamics365ForSales snaps" -ForegroundColor White  
Write-Host "  • Any snap with account property" -ForegroundColor White
Write-Host "  • Any snap with connection-related properties" -ForegroundColor White
