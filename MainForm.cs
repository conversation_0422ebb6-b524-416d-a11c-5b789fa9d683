using System.Diagnostics;
using System.Runtime.Versioning;
using System.Text;

[assembly: SupportedOSPlatform("windows")]

namespace SnapAnalyser
{
    public partial class MainForm : Form
    {
        private readonly SlpAnalyzer _analyzer;
        private readonly DiagramGenerator _diagramGenerator;
        private DocumentationGenerator _docGenerator;

        public MainForm()
        {
            InitializeComponent();
            _analyzer = new SlpAnalyzer();
            _diagramGenerator = new DiagramGenerator();
            _docGenerator = new DocumentationGenerator();
        }

        private void InitializeComponent()
        {
            btnBrowse = new Button();
            btnAnalyze = new Button();
            btnBatchProcess = new Button();
            txtFilePath = new TextBox();
            lblStatus = new Label();
            progressBar = new ProgressBar();
            chkHtml = new CheckBox();
            chkPdf = new CheckBox();
            lblOutputFolder = new Label();
            txtOutputFolder = new TextBox();
            btnBrowseOutput = new Button();
            chkUseAI = new CheckBox();
            chkUseCachedDescriptions = new CheckBox();
            btnTestAzureAPI = new Button();
            btnOpenSourceFolder = new Button();
            btnOpenOutputFolder = new Button();
            SuspendLayout();
            // 
            // btnBrowse
            // 
            btnBrowse.Location = new Point(507, 31);
            btnBrowse.Name = "btnBrowse";
            btnBrowse.Size = new Size(75, 23);
            btnBrowse.TabIndex = 0;
            btnBrowse.Text = "Browse...";
            btnBrowse.UseVisualStyleBackColor = true;
            btnBrowse.Click += btnBrowse_Click;
            //
            // btnOpenSourceFolder
            //
            btnOpenSourceFolder.Location = new Point(588, 31);
            btnOpenSourceFolder.Name = "btnOpenSourceFolder";
            btnOpenSourceFolder.Size = new Size(85, 23);
            btnOpenSourceFolder.TabIndex = 15;
            btnOpenSourceFolder.Text = "Open Folder";
            btnOpenSourceFolder.UseVisualStyleBackColor = true;
            btnOpenSourceFolder.Click += btnOpenSourceFolder_Click;

            // btnAnalyze
            // 
            btnAnalyze.Enabled = false;
            btnAnalyze.Location = new Point(249, 240);
            btnAnalyze.Name = "btnAnalyze";
            btnAnalyze.Size = new Size(118, 31);
            btnAnalyze.TabIndex = 1;
            btnAnalyze.Text = "Analyze";
            btnAnalyze.UseVisualStyleBackColor = true;
            btnAnalyze.Click += btnAnalyze_Click;
            // 
            // btnBatchProcess
            // 
            btnBatchProcess.Location = new Point(377, 240);
            btnBatchProcess.Name = "btnBatchProcess";
            btnBatchProcess.Size = new Size(118, 31);
            btnBatchProcess.TabIndex = 2;
            btnBatchProcess.Text = "Batch Process";
            btnBatchProcess.UseVisualStyleBackColor = true;
            btnBatchProcess.Click += btnBatchProcess_Click;
            // 
            // txtFilePath
            // 
            txtFilePath.Location = new Point(41, 33);
            txtFilePath.Name = "txtFilePath";
            txtFilePath.Size = new Size(460, 23);
            txtFilePath.TabIndex = 2;
            txtFilePath.Text = "Select SLP file...";
            txtFilePath.TextChanged += txtFilePath_TextChanged;
            //            // lblStatus
            // 
            lblStatus.AutoSize = true;
            lblStatus.Location = new Point(41, 280);
            lblStatus.Name = "lblStatus";
            lblStatus.Size = new Size(39, 15);
            lblStatus.TabIndex = 3;
            lblStatus.Text = "Ready";
            //            // progressBar
            // 
            progressBar.Location = new Point(41, 310);
            progressBar.Name = "progressBar";
            progressBar.Size = new Size(541, 23);
            progressBar.TabIndex = 4;
            // 
            // chkHtml
            // 
            chkHtml.AutoSize = true;
            chkHtml.Checked = true;
            chkHtml.CheckState = CheckState.Checked;
            chkHtml.Location = new Point(41, 133);
            chkHtml.Name = "chkHtml";
            chkHtml.Size = new Size(167, 19);
            chkHtml.TabIndex = 5;
            chkHtml.Text = "Generate HTML Document";
            chkHtml.UseVisualStyleBackColor = true;
            // 
            // chkPdf
            // 
            chkPdf.AutoSize = true;
            chkPdf.Location = new Point(249, 133);
            chkPdf.Name = "chkPdf";
            chkPdf.Size = new Size(156, 19);
            chkPdf.TabIndex = 6;
            chkPdf.Text = "Generate PDF Document";
            chkPdf.UseVisualStyleBackColor = true;
            // 
            // lblOutputFolder
            // 
            lblOutputFolder.AutoSize = true;
            lblOutputFolder.Location = new Point(41, 75);
            lblOutputFolder.Name = "lblOutputFolder";
            lblOutputFolder.Size = new Size(84, 15);
            lblOutputFolder.TabIndex = 7;
            lblOutputFolder.Text = "Output Folder:";
            // 
            // txtOutputFolder
            // 
            txtOutputFolder.Location = new Point(41, 91);
            txtOutputFolder.Name = "txtOutputFolder";
            txtOutputFolder.Size = new Size(460, 23);
            txtOutputFolder.TabIndex = 8;
            // 
            // btnBrowseOutput
            // 
            btnBrowseOutput.Location = new Point(507, 89);
            btnBrowseOutput.Name = "btnBrowseOutput";
            btnBrowseOutput.Size = new Size(75, 23);
            btnBrowseOutput.TabIndex = 9;
            btnBrowseOutput.Text = "Browse...";
            btnBrowseOutput.UseVisualStyleBackColor = true;
            btnBrowseOutput.Click += btnBrowseOutput_Click;
            //
            // btnOpenOutputFolder
            //
            btnOpenOutputFolder.Location = new Point(588, 89);
            btnOpenOutputFolder.Name = "btnOpenOutputFolder";
            btnOpenOutputFolder.Size = new Size(85, 23);
            btnOpenOutputFolder.TabIndex = 16;
            btnOpenOutputFolder.Text = "Open Folder";
            btnOpenOutputFolder.UseVisualStyleBackColor = true;
            btnOpenOutputFolder.Click += btnOpenOutputFolder_Click;

            // chkUseAI
            //            chkUseAI.AutoSize = true;
            chkUseAI.Location = new Point(41, 165);
            chkUseAI.Name = "chkUseAI";
            chkUseAI.Size = new Size(364, 19);
            chkUseAI.TabIndex = 10;
            chkUseAI.Text = "Use AI for Pipeline Overview (Falls back to free service if needed)";
            chkUseAI.UseVisualStyleBackColor = true;
            chkUseAI.Checked = true;

            // Add the checkbox to toggle cached descriptions
            chkUseCachedDescriptions = new CheckBox();
            chkUseCachedDescriptions.AutoSize = true;
            chkUseCachedDescriptions.Location = new Point(41, 190);
            chkUseCachedDescriptions.Name = "chkUseCachedDescriptions";
            chkUseCachedDescriptions.Size = new Size(350, 19);
            chkUseCachedDescriptions.TabIndex = 11;
            chkUseCachedDescriptions.Text = "Use cached AI descriptions (faster but may use outdated content)";
            chkUseCachedDescriptions.Checked = true;
            chkUseCachedDescriptions.UseVisualStyleBackColor = true;
            Controls.Add(chkUseCachedDescriptions);
            //// btnTestAzureAPI
            // 
            btnTestAzureAPI.Location = new Point(410, 190);
            btnTestAzureAPI.Name = "btnTestAzureAPI";
            btnTestAzureAPI.Size = new Size(202, 23);
            btnTestAzureAPI.TabIndex = 14;
            btnTestAzureAPI.Text = "Test Azure OpenAI Connection";
            btnTestAzureAPI.UseVisualStyleBackColor = true;
            btnTestAzureAPI.Click += btnTestAzureAPI_Click;
            //            // MainForm
            // 
            ClientSize = new Size(684, 360); Controls.Add(btnOpenOutputFolder);
            Controls.Add(btnOpenSourceFolder);
            Controls.Add(btnTestAzureAPI);
            Controls.Add(chkUseAI);
            Controls.Add(btnBrowseOutput);
            Controls.Add(txtOutputFolder);
            Controls.Add(lblOutputFolder);
            Controls.Add(chkPdf);
            Controls.Add(chkHtml);
            Controls.Add(progressBar);
            Controls.Add(lblStatus);
            Controls.Add(txtFilePath);
            Controls.Add(btnAnalyze);
            Controls.Add(btnBrowse);
            Controls.Add(btnBatchProcess);
            FormBorderStyle = FormBorderStyle.FixedSingle;
            MaximizeBox = false;
            Name = "MainForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "SnapLogic Pipeline Analyzer";
            Load += MainForm_Load;
            ResumeLayout(false);
            PerformLayout();
        }
        private System.Windows.Forms.Button btnBrowse;
        private System.Windows.Forms.Button btnAnalyze;
        private System.Windows.Forms.Button btnBatchProcess;
        private System.Windows.Forms.TextBox txtFilePath;
        private System.Windows.Forms.Label lblStatus;
        private System.Windows.Forms.ProgressBar progressBar;
        private System.Windows.Forms.CheckBox chkHtml;
        private System.Windows.Forms.CheckBox chkPdf;
        private System.Windows.Forms.Label lblOutputFolder;
        private System.Windows.Forms.TextBox txtOutputFolder;
        private System.Windows.Forms.Button btnBrowseOutput;
        private System.Windows.Forms.CheckBox chkUseAI;
        private System.Windows.Forms.CheckBox chkUseCachedDescriptions;
        private System.Windows.Forms.Button btnTestAzureAPI;
        private System.Windows.Forms.Button btnOpenSourceFolder;
        private System.Windows.Forms.Button btnOpenOutputFolder;

        private void MainForm_Load(object sender, EventArgs e)
        {
            // Ensure configuration is loaded (should already be done in Program.cs)
            ConfigManager.LoadConfig();

            // Set default output folder if no saved location exists
            if (string.IsNullOrEmpty(ConfigManager.LastOutputFolderLocation))
            {
                txtOutputFolder.Text = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "SnapAnalyzer");
                ConfigManager.LastOutputFolderLocation = txtOutputFolder.Text;
            }
            else
            {
                txtOutputFolder.Text = ConfigManager.LastOutputFolderLocation;
            }            // Ensure output directory exists
            try
            {
                // Validate the path before attempting to create directory
                if (string.IsNullOrEmpty(txtOutputFolder.Text) || !IsValidPath(txtOutputFolder.Text))
                {
                    txtOutputFolder.Text = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "SnapAnalyzer");
                    ConfigManager.LastOutputFolderLocation = txtOutputFolder.Text;
                }

                Directory.CreateDirectory(txtOutputFolder.Text);
            }
            catch (Exception ex)
            {
                // If directory creation fails, use a safe default path
                string defaultPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "SnapAnalyzer");
                txtOutputFolder.Text = defaultPath;
                ConfigManager.LastOutputFolderLocation = defaultPath;
                Directory.CreateDirectory(defaultPath);

                MessageBox.Show($"Unable to create output directory. Using default location: {defaultPath}\nOriginal error: {ex.Message}",
                    "Directory Creation Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }

            // Pre-fill SLP file path if available
            if (!string.IsNullOrEmpty(ConfigManager.LastSlpFolderLocation))
            {
                txtFilePath.Text = ConfigManager.LastSlpFolderLocation;
                // Check if it's a valid SLP file to enable analyze button
                btnAnalyze.Enabled = File.Exists(txtFilePath.Text) && txtFilePath.Text.EndsWith(".slp", StringComparison.OrdinalIgnoreCase);
            }
        }

        private void btnBrowse_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "SnapLogic Pipeline Files (*.slp)|*.slp|All files (*.*)|*.*";
                openFileDialog.RestoreDirectory = true;

                // Set initial directory if we have a saved location
                if (!string.IsNullOrEmpty(ConfigManager.LastSlpFolderLocation))
                {
                    try
                    {
                        string folder = Path.GetDirectoryName(ConfigManager.LastSlpFolderLocation);
                        if (Directory.Exists(folder))
                        {
                            openFileDialog.InitialDirectory = folder;
                        }
                    }
                    catch { }
                }

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    txtFilePath.Text = openFileDialog.FileName;
                    ConfigManager.LastSlpFolderLocation = openFileDialog.FileName;
                }
            }
        }

        private void btnBrowseOutput_Click(object sender, EventArgs e)
        {
            using (FolderBrowserDialog folderDialog = new FolderBrowserDialog())
            {
                folderDialog.Description = "Select output folder for documentation";
                folderDialog.SelectedPath = txtOutputFolder.Text;

                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    txtOutputFolder.Text = folderDialog.SelectedPath;
                    ConfigManager.LastOutputFolderLocation = folderDialog.SelectedPath;
                }
            }
        }

        private void txtFilePath_TextChanged(object sender, EventArgs e)
        {
            btnAnalyze.Enabled = File.Exists(txtFilePath.Text) && txtFilePath.Text.EndsWith(".slp", StringComparison.OrdinalIgnoreCase);
        }

        private async void btnAnalyze_Click(object sender, EventArgs e)
        {
            if (!File.Exists(txtFilePath.Text))
            {
                MessageBox.Show("Please select a valid SLP file.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (!Directory.Exists(txtOutputFolder.Text))
            {
                try
                {
                    Directory.CreateDirectory(txtOutputFolder.Text);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error creating output directory: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
            }

            if (!chkHtml.Checked && !chkPdf.Checked)
            {
                MessageBox.Show("Please select at least one output format.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // Save the output folder location
            ConfigManager.LastOutputFolderLocation = txtOutputFolder.Text;

            DisableUI();

            try
            {
                await ProcessFileAsync().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error processing file: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "Error processing file.";
            }
            finally
            {
                EnableUI();
            }
        }

        private async Task ProcessFileAsync()
        {
            UpdateStatus("Reading file...");
            UpdateProgress(10);

            string fileContent = File.ReadAllText(txtFilePath.Text);
            string fileName = Path.GetFileNameWithoutExtension(txtFilePath.Text);

            UpdateStatus("Parsing SLP file...");
            UpdateProgress(20);

            var pipelineData = _analyzer.AnalyzePipeline(fileContent);

            UpdateStatus("Generating flow diagram...");
            UpdateProgress(40);

            string diagramSvg = _diagramGenerator.GenerateDiagram(pipelineData);            // Initialize AI generator if enabled
            AIDescriptionGenerator aiGenerator = null;
            if (chkUseAI.Checked)
            {
                UpdateStatus("Initializing AI...");
                
                try
                {
                    aiGenerator = new AIDescriptionGenerator(null); // Pass null to use the API key from ConfigManager
                }
                catch (Exception aiEx)
                {
                    throw new Exception($"Failed to create AIDescriptionGenerator: {aiEx.Message}", aiEx);
                }

                // Configure caching based on the checkbox
                bool useCaching = chkUseCachedDescriptions.Checked;
                aiGenerator.UseCachedDescriptions = useCaching;

                // Enable AI logging to save logs in the output folder
                aiGenerator.EnableLogging(txtOutputFolder.Text);

                _docGenerator = new DocumentationGenerator(aiGenerator);
            }

            UpdateStatus("Generating documentation...");
            UpdateProgress(60);

            if (chkHtml.Checked)
            {
                UpdateStatus("Creating HTML documentation...");
                UpdateProgress(75); 
                string htmlDoc; 
                
                if (aiGenerator != null)
                {
                    htmlDoc = await _docGenerator.GenerateHtmlDocumentationAsync(pipelineData, diagramSvg).ConfigureAwait(false);
                }
                else
                {
                    htmlDoc = _docGenerator.GenerateHtmlDocumentation(pipelineData, diagramSvg);
                }

                string htmlPath = Path.Combine(txtOutputFolder.Text, $"{fileName}_documentation.html");
                File.WriteAllText(htmlPath, htmlDoc, Encoding.UTF8);
            }

            if (chkPdf.Checked)
            {
                UpdateStatus("Creating PDF documentation...");
                UpdateProgress(90);

                string pdfPath = Path.Combine(txtOutputFolder.Text, $"{fileName}_documentation.pdf");
                _docGenerator.GeneratePdfDocumentation(pipelineData, diagramSvg, pdfPath);
            }

            UpdateStatus("Complete!");
            UpdateProgress(100);

            // Play completion sound instead of showing popup
            InvokeOnUI(() =>
            {
                try
                {
                    System.Media.SystemSounds.Asterisk.Play();
                }
                catch
                {
                    // Ignore sound errors - don't let audio issues affect functionality
                }
            });
        }

        private void UpdateStatus(string status)
        {
            InvokeOnUI(() => lblStatus.Text = status);
        }

        private void UpdateProgress(int value)
        {
            InvokeOnUI(() => progressBar.Value = value);
        }

        private void InvokeOnUI(Action action)
        {
            if (InvokeRequired)
            {
                Invoke(action);
            }
            else
            {
                action();
            }
        }
        private void DisableUI()
        {
            InvokeOnUI(() =>
            {
                btnAnalyze.Enabled = false;
                btnBrowse.Enabled = false;
                btnBrowseOutput.Enabled = false;
                btnOpenSourceFolder.Enabled = false;
                btnOpenOutputFolder.Enabled = false;
                chkHtml.Enabled = false;
                chkPdf.Enabled = false;
                chkUseAI.Enabled = false;
                txtFilePath.Enabled = false;
                txtOutputFolder.Enabled = false;
                progressBar.Value = 0;
            });
        }
        private void EnableUI()
        {
            InvokeOnUI(() =>
            {
                btnAnalyze.Enabled = true;
                btnBrowse.Enabled = true;
                btnBrowseOutput.Enabled = true;
                btnOpenSourceFolder.Enabled = true;
                btnOpenOutputFolder.Enabled = true;
                chkHtml.Enabled = true;
                chkPdf.Enabled = true;
                chkUseAI.Enabled = true;
                txtFilePath.Enabled = true;
                txtOutputFolder.Enabled = true;
            });
        }

        private async void btnTestAzureAPI_Click(object sender, EventArgs e)
        {
            btnTestAzureAPI.Enabled = false;
            lblStatus.Text = "Testing Azure OpenAI connection...";
            progressBar.Style = ProgressBarStyle.Marquee;

            try
            {
                await Task.Run(async () =>
                {
                    try
                    {
                        var tester = new AzureOpenAITester();

                        // First test basic connection
                        bool isConnected = await tester.TestConnection();

                        if (isConnected)
                        {
                            // If basic connection works, get a detailed response
                            string detailedResponse = await tester.TestWithResponse();

                            // Get additional deployment info
                            string deploymentInfo = await tester.GetDeploymentInfo();

                            InvokeOnUI(() =>
                            {
                                MessageBox.Show($"Successfully connected to Azure OpenAI API!\n\n{detailedResponse}\n\n{deploymentInfo}",
                                    "Connection Test", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                lblStatus.Text = "Azure OpenAI connection successful.";

                                // Enable AI checkbox since we've confirmed it works
                                chkUseAI.Checked = true;
                            });
                        }
                        else
                        {
                            InvokeOnUI(() =>
                            {
                                MessageBox.Show(
                                    "Failed to connect to Azure OpenAI API. Please check your configuration.",
                                    "Connection Test",
                                    MessageBoxButtons.OK,
                                    MessageBoxIcon.Warning);

                                lblStatus.Text = "Azure OpenAI connection failed.";
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        InvokeOnUI(() =>
                        {
                            string errorMessage = $"Error testing Azure OpenAI connection: {ex.Message}";

                            // Check for common error types and provide more helpful messages
                            if (ex is HttpRequestException)
                            {
                                errorMessage += "\n\nThis may be due to network issues or proxy settings.";
                            }
                            else if (ex.Message.Contains("Unauthorized") || ex.Message.Contains("401"))
                            {
                                errorMessage += "\n\nThe API key appears to be invalid or has expired.";
                            }

                            MessageBox.Show(errorMessage,
                                "Connection Test Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            lblStatus.Text = "Azure OpenAI connection error.";
                        });
                    }
                });
            }
            finally
            {
                btnTestAzureAPI.Enabled = true;
                progressBar.Style = ProgressBarStyle.Blocks;
                progressBar.Value = 0;
            }
        }

        private void btnBatchProcess_Click(object sender, EventArgs e)
        {
            // Show batch processing form
            using (var batchForm = new BatchDocumentationForm())
            {
                batchForm.ShowDialog(this);
            }
        }

        private void btnOpenSourceFolder_Click(object sender, EventArgs e)
        {
            try
            {
                string folderPath = Path.GetDirectoryName(txtFilePath.Text);
                if (Directory.Exists(folderPath))
                {
                    Process.Start(new ProcessStartInfo("explorer.exe", folderPath) { UseShellExecute = true });
                }
                else
                {
                    MessageBox.Show("Source folder does not exist.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening source folder: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private void btnOpenOutputFolder_Click(object sender, EventArgs e)
        {
            try
            {
                string outputPath = txtOutputFolder.Text;
                if (Directory.Exists(outputPath))
                {
                    Process.Start(new ProcessStartInfo("explorer.exe", outputPath) { UseShellExecute = true });
                }
                else
                {
                    MessageBox.Show("Output folder does not exist.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening output folder: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool IsValidPath(string path)
        {
            try
            {
                // Check if path is null or empty
                if (string.IsNullOrWhiteSpace(path))
                    return false;

                // Check if path contains invalid characters
                if (path.IndexOfAny(Path.GetInvalidPathChars()) >= 0)
                    return false;

                // Check if the drive exists (for absolute paths)
                if (Path.IsPathRooted(path))
                {
                    string root = Path.GetPathRoot(path);
                    if (!Directory.Exists(root))
                        return false;
                }

                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}