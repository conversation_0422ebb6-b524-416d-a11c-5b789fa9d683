using System;
using System.Collections.Generic;
using System.IO;
using Newtonsoft.Json.Linq;

class TestJoinFixSimple
{
    static void Main()
    {
        Console.WriteLine("Testing Join snap property extraction fix...");
        
        // Test data mimicking actual Join snap properties
        var testProperties = new Dictionary<string, object>
        {
            ["settings.joinPaths.value.0.leftPath.value"] = "customer_id",
            ["settings.joinPaths.value.0.rightPath.value"] = "id", 
            ["settings.joinType.value"] = "Inner Join",
            ["settings.someOtherProperty"] = "ignored",
            ["datatype"] = "should be ignored"
        };
        
        Console.WriteLine("\nTest Properties:");
        foreach (var prop in testProperties)
        {
            Console.WriteLine($"  {prop.Key}: {prop.Value}");
        }
        
        // Test ExtractJoinConditions logic
        Console.WriteLine("\nTesting Join Conditions extraction:");
        var joinConditions = new List<string>();
        
        foreach (var prop in testProperties)
        {
            if (prop.Key.Contains("joinPaths.value") && prop.Key.Contains("leftPath"))
            {
                Console.WriteLine($"  Found leftPath: {prop.Key} = {prop.Value}");
                joinConditions.Add($"Left: {prop.Value}");
            }
            else if (prop.Key.Contains("joinPaths.value") && prop.Key.Contains("rightPath"))
            {
                Console.WriteLine($"  Found rightPath: {prop.Key} = {prop.Value}");
                joinConditions.Add($"Right: {prop.Value}");
            }
        }
        
        // Test ExtractJoinType logic
        Console.WriteLine("\nTesting Join Type extraction:");
        string joinType = null;
        
        foreach (var prop in testProperties)
        {
            if (prop.Key.Contains("joinType.value") || 
                prop.Key.Contains("settings.joinType") ||
                (prop.Key.ToLower().Contains("type") && !prop.Key.ToLower().Contains("datatype")))
            {
                Console.WriteLine($"  Found joinType: {prop.Key} = {prop.Value}");
                joinType = prop.Value?.ToString();
                break;
            }
        }
        
        Console.WriteLine($"\nResults:");
        Console.WriteLine($"  Join Type: {joinType ?? "NOT FOUND"}");
        Console.WriteLine($"  Join Conditions: {string.Join(", ", joinConditions)}");
        
        bool success = !string.IsNullOrEmpty(joinType) && joinConditions.Count > 0;
        Console.WriteLine($"\nTest Result: {(success ? "SUCCESS" : "FAILED")}");
        
        if (success)
        {
            Console.WriteLine("✓ Property extraction logic is working correctly!");
        }
        else
        {
            Console.WriteLine("✗ Property extraction logic needs further adjustment");
        }
    }
}
