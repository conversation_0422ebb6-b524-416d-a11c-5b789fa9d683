# PowerShell script to fix the specific corrupted line 3108

$filePath = "DocumentationGenerator.cs"

Write-Host "Fixing corrupted line 3108..." -ForegroundColor Green

# Read the file as lines
$lines = Get-Content $filePath

# Find the corrupted line and replace it
for ($i = 0; $i -lt $lines.Count; $i++) {
    if ($lines[$i] -like "*LogMessage\\*CONNECTION\\*No\\*connection\\*info\\*found*") {
        Write-Host "Found corrupted line at $($i+1)" -ForegroundColor Yellow
        
        # Replace the corrupted line with simple correct implementation
        $lines[$i] = '            LogMessage($"[CONNECTION] No connection info found for snap: {snap.Label}");'
        $lines[$i+1] = '            return null;'
        $lines[$i+2] = '        }'
        $lines[$i+3] = ''
        $lines[$i+4] = '        /// <summary>'
        $lines[$i+5] = '        /// Extracts and formats Dynamics365ForSales-specific details'
        $lines[$i+6] = '        /// </summary>'
        $lines[$i+7] = '        private string GetDynamics365ForSalesDetails(SnapNode snap)'
        $lines[$i+8] = '        {'
        $lines[$i+9] = '            var html = new StringBuilder();'
        $lines[$i+10] = '            LogMessage($"[DYNAMICS365] Extracting details for snap: {snap.Label}");'
        $lines[$i+11] = ''
        $lines[$i+12] = '            try'
        $lines[$i+13] = '            {'
        $lines[$i+14] = '                var details = new Dictionary<string, string>();'
        $lines[$i+15] = ''
        $lines[$i+16] = '                // Look for specific Dynamics365ForSales properties'
        $lines[$i+17] = '                foreach (var prop in snap.Properties)'
        $lines[$i+18] = '                {'
        $lines[$i+19] = '                    string key = prop.Key.ToLower();'
        $lines[$i+20] = '                    string value = prop.Value;'
        $lines[$i+21] = ''
        $lines[$i+22] = '                    // Skip empty or boolean values'
        $lines[$i+23] = '                    if (string.IsNullOrEmpty(value) ||'
        $lines[$i+24] = '                        value.Equals("true", StringComparison.OrdinalIgnoreCase) ||'
        $lines[$i+25] = '                        value.Equals("false", StringComparison.OrdinalIgnoreCase))'
        $lines[$i+26] = '                        continue;'
        $lines[$i+27] = ''
        $lines[$i+28] = '                    // Filter Condition'
        $lines[$i+29] = '                    if (key.Contains("filter") && (key.Contains("condition") || key.Contains("criteria") || key.Contains("where")))'
        $lines[$i+30] = '                    {'
        $lines[$i+31] = '                        details["Filter Condition"] = value;'
        $lines[$i+32] = '                        LogMessage($"[DYNAMICS365] Found Filter Condition: {value}");'
        $lines[$i+33] = '                    }'
        $lines[$i+34] = '                    // Query Parameters'
        $lines[$i+35] = '                    else if (key.Contains("query") && (key.Contains("parameter") || key.Contains("param") || key.Contains("variable")))'
        $lines[$i+36] = '                    {'
        $lines[$i+37] = '                        details["Query Parameters"] = value;'
        $lines[$i+38] = '                        LogMessage($"[DYNAMICS365] Found Query Parameters: {value}");'
        $lines[$i+39] = '                    }'
        $lines[$i+40] = '                    // Output Attributes'
        $lines[$i+41] = '                    else if (key.Contains("output") && (key.Contains("attribute") || key.Contains("field") || key.Contains("column")))'
        $lines[$i+42] = '                    {'
        $lines[$i+43] = '                        details["Output Attributes"] = value;'
        $lines[$i+44] = '                        LogMessage($"[DYNAMICS365] Found Output Attributes: {value}");'
        $lines[$i+45] = '                    }'
        $lines[$i+46] = '                    // Order By'
        $lines[$i+47] = '                    else if (key.Contains("order") && (key.Contains("by") || key.Contains("sort")))'
        $lines[$i+48] = '                    {'
        $lines[$i+49] = '                        details["Order By"] = value;'
        $lines[$i+50] = '                        LogMessage($"[DYNAMICS365] Found Order By: {value}");'
        $lines[$i+51] = '                    }'
        $lines[$i+52] = '                }'
        $lines[$i+53] = ''
        $lines[$i+54] = '                // Format the details for HTML output'
        $lines[$i+55] = '                if (details.Any())'
        $lines[$i+56] = '                {'
        $lines[$i+57] = '                    html.AppendLine($"          <div style=\"margin: 10px 0; padding: 10px; background-color: #f0f8ff; border-left: 4px solid #0066cc;\">");'
        $lines[$i+58] = '                    html.AppendLine($"            <h5 style=\"color: #0066cc; margin: 0 0 8px 0;\">Dynamics365 Configuration</h5>");'
        $lines[$i+59] = ''
        $lines[$i+60] = '                    foreach (var detail in details)'
        $lines[$i+61] = '                    {'
        $lines[$i+62] = '                        string displayValue = detail.Value;'
        $lines[$i+63] = '                        html.AppendLine($"            <p style=\"margin: 4px 0;\"><strong>{detail.Key}:</strong> {System.Net.WebUtility.HtmlEncode(displayValue)}</p>");'
        $lines[$i+64] = '                        LogMessage($"[DYNAMICS365] Added to output: {detail.Key} = {displayValue}");'
        $lines[$i+65] = '                    }'
        $lines[$i+66] = ''
        $lines[$i+67] = '                    html.AppendLine($"          </div>");'
        $lines[$i+68] = '                    LogMessage($"[DYNAMICS365] Generated HTML details block for: {snap.Label}");'
        $lines[$i+69] = '                }'
        $lines[$i+70] = '                else'
        $lines[$i+71] = '                {'
        $lines[$i+72] = '                    LogMessage($"[DYNAMICS365] No specific details found for: {snap.Label}");'
        $lines[$i+73] = '                }'
        $lines[$i+74] = '            }'
        $lines[$i+75] = '            catch (Exception ex)'
        $lines[$i+76] = '            {'
        $lines[$i+77] = '                LogMessage($"[DYNAMICS365] Error processing details for {snap.Label}: {ex.Message}");'
        $lines[$i+78] = '            }'
        $lines[$i+79] = ''
        $lines[$i+80] = '            return html.ToString();'
        $lines[$i+81] = '        }'
        
        # Clear the rest of the corrupted line that might extend beyond
        $lines = $lines[0..$($i+81)]
        
        Write-Host "✅ Replaced corrupted line with correct implementation" -ForegroundColor Green
        break
    }
}

# Write the corrected content
$lines | Set-Content $filePath -Encoding UTF8

Write-Host "✅ Fixed line 3108!" -ForegroundColor Green
