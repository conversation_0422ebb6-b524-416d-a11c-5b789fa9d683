using System;
using System.IO;
using System.Windows.Forms;
using System.Text;

namespace SnapAnalyser
{
    static class DebugProgram
    {
        [STAThread]
        static void Main()
        {
            // Create a log file to capture any errors
            string logPath = Path.Combine(Environment.CurrentDirectory, "startup_debug.log");
            
            try
            {
                File.WriteAllText(logPath, $"=== Startup Debug Log - {DateTime.Now} ===\n");
                
                LogMessage(logPath, "Starting application...");
                
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                LogMessage(logPath, "Application settings configured");
                
                // Test if we can load ConfigManager
                LogMessage(logPath, "Loading ConfigManager...");
                ConfigManager.LoadConfig();
                LogMessage(logPath, "ConfigManager loaded successfully");
                
                // Test if we can create MainForm
                LogMessage(logPath, "Creating MainForm...");
                var mainForm = new MainForm();
                LogMessage(logPath, "MainForm created successfully");
                
                LogMessage(logPath, "Starting Application.Run...");
                Application.Run(mainForm);
                
                LogMessage(logPath, "Application.Run completed normally");
            }
            catch (Exception ex)
            {
                string errorMsg = $"FATAL ERROR: {ex.Message}\n\nStack Trace:\n{ex.StackTrace}";
                
                try
                {
                    LogMessage(logPath, errorMsg);
                }
                catch
                {
                    // If we can't write to log, at least show the error
                }
                
                MessageBox.Show(errorMsg, "Application Startup Error", 
                               MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private static void LogMessage(string logPath, string message)
        {
            try
            {
                File.AppendAllText(logPath, $"[{DateTime.Now:HH:mm:ss.fff}] {message}\n");
            }
            catch
            {
                // Ignore logging errors
            }
        }
    }
}
