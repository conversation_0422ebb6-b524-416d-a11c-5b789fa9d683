using System;
using System.Threading;
using System.Threading.Tasks;

namespace SnapAnalyser
{
    class QuickCrashTest
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("Testing AddSnapCategorySection crash...");
            
            try
            {
                // Test AIDescriptionGenerator creation
                Console.WriteLine("Step 1: Creating AIDescriptionGenerator...");
                var aiGenerator = new AIDescriptionGenerator(null);
                Console.WriteLine("✅ AIDescriptionGenerator created successfully!");

                // Test if it's a cache initialization issue
                Console.WriteLine("Step 2: Testing cache access...");
                // Try to trigger cache initialization
                
                Console.WriteLine("Step 3: Creating test snap...");
                var testSnap = new SnapNode
                {
                    Id = "test-snap",
                    Label = "Test Snap",
                    Type = "sl::File Reader",
                    Category = SnapCategory.Transformation
                };

                var testPipeline = new PipelineData();
                
                Console.WriteLine("Step 4: Testing GenerateEnhancedSnapDescription...");
                var cancellationToken = new CancellationTokenSource(TimeSpan.FromSeconds(30)).Token;
                
                string result = await aiGenerator.GenerateEnhancedSnapDescription(testSnap, testPipeline, cancellationToken);
                Console.WriteLine($"✅ GenerateEnhancedSnapDescription completed! Result length: {result?.Length ?? 0}");
                
                Console.WriteLine("Step 5: Testing GenerateSnapPseudocode...");
                string pseudocode = await aiGenerator.GenerateSnapPseudocode(testSnap, testPipeline, cancellationToken);
                Console.WriteLine($"✅ GenerateSnapPseudocode completed! Result length: {pseudocode?.Length ?? 0}");
                
                Console.WriteLine("🎉 All tests passed! The issue might be elsewhere.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ CRASH DETECTED:");
                Console.WriteLine($"   Message: {ex.Message}");
                Console.WriteLine($"   Type: {ex.GetType().Name}");
                Console.WriteLine($"   Stack Trace: {ex.StackTrace}");
                
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"   Inner Exception: {ex.InnerException.Message}");
                    Console.WriteLine($"   Inner Stack Trace: {ex.InnerException.StackTrace}");
                }
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
