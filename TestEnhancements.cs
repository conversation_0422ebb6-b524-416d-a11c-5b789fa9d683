using System;
using System.IO;
using System.Threading.Tasks;
using System.Reflection;
using System.Text;

namespace SnapAnalyser
{
    public class TestEnhancements
    {
        public static async Task TestAIDescriptionCaching()
        {
            Console.WriteLine("==== Testing AI Description Caching ====");
            
            // Initialize components
            var slpAnalyzer = new SlpAnalyzer();
            var aiGenerator = new AIDescriptionGenerator();
            var docGenerator = new DocumentationGenerator(aiGenerator);
            
            // Set up test parameters
            string testSlpFile = Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory, 
                "..", "..", "..", "Snap-Pipelines", 
                "CH16 - Update URM Property Tenants_2025_05_16.slp");
            
            string outputFolder = Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory, 
                "..", "..", "..", "TestOutput");
            
            Directory.CreateDirectory(outputFolder);
            
            // Test with caching enabled
            Console.WriteLine("Test 1: With caching enabled");
            aiGenerator.UseCachedDescriptions = true;
            await RunTestWithFile(testSlpFile, outputFolder, slpAnalyzer, docGenerator, aiGenerator, "cache_enabled");
            
            // Test with caching disabled
            Console.WriteLine("\nTest 2: With caching disabled");
            aiGenerator.UseCachedDescriptions = false; 
            await RunTestWithFile(testSlpFile, outputFolder, slpAnalyzer, docGenerator, aiGenerator, "cache_disabled");
            
            Console.WriteLine("\nTest complete! Review the output files in the TestOutput folder.");
        }
        
        private static async Task RunTestWithFile(
            string slpFilePath,
            string outputFolder,
            SlpAnalyzer slpAnalyzer,
            DocumentationGenerator docGenerator,
            AIDescriptionGenerator aiGenerator,
            string testSuffix)
        {
            Console.WriteLine($"Processing file: {Path.GetFileName(slpFilePath)}");
            
            try
            {
                // Read SLP file
                string slpContent = File.ReadAllText(slpFilePath);
                
                // Parse pipeline
                var pipeline = slpAnalyzer.AnalyzePipeline(slpContent);
                Console.WriteLine($"Pipeline loaded: {pipeline.Name} with {pipeline.Snaps.Count} snaps");
                
                // Enable logging
                aiGenerator.EnableLogging(outputFolder);
                
                // Count snaps by category
                int copySnaps = 0, unionSnaps = 0, exitSnaps = 0;
                foreach (var snap in pipeline.Snaps)
                {
                    string snapType = snap.Type.ToLowerInvariant();
                    if (snapType.Contains("copy")) copySnaps++;
                    if (snapType.Contains("union")) unionSnaps++;
                    if (snapType.Contains("exit")) exitSnaps++; 
                }
                
                Console.WriteLine($"Found special snap types: Copy={copySnaps}, Union={unionSnaps}, Exit={exitSnaps}");
                
                // Test generating descriptions for specific snap types
                foreach (var snap in pipeline.Snaps)
                {
                    string snapType = snap.Type.ToLowerInvariant();
                    if (snapType.Contains("copy") || snapType.Contains("union") || snapType.Contains("exit"))
                    {
                        // Generate description
                        string description = await aiGenerator.GenerateSnapDescription(snap, pipeline);
                        Console.WriteLine($"- {snap.Label} ({snap.Type}): {description.Substring(0, Math.Min(60, description.Length))}...");
                    }
                }
                
                // Generate SVG diagram (optional for testing)
                var diagramGenerator = new DiagramGenerator();
                string diagramSvg = diagramGenerator.GenerateDiagram(pipeline);
                
                // Test full documentation generation
                string htmlContent = await docGenerator.GenerateHtmlDocumentationAsync(pipeline, diagramSvg);
                string outputFile = Path.Combine(outputFolder, $"Test_{testSuffix}_{DateTime.Now:yyyyMMdd_HHmmss}.html");
                File.WriteAllText(outputFile, htmlContent);
                
                Console.WriteLine($"Documentation saved to: {outputFile}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during test: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
            }
        }
        
        public static void TestMarkdownToHtmlConversion()
        {
            Console.WriteLine("==== Testing Markdown to HTML Conversion ====");
            
            // Create a new instance of DocumentationGenerator
            var docGenerator = new DocumentationGenerator();
            
            // Get private method using reflection
            var methodInfo = typeof(DocumentationGenerator).GetMethod(
                "ConvertMarkdownToHtml", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            if (methodInfo == null)
            {
                Console.WriteLine("ERROR: Could not find ConvertMarkdownToHtml method");
                return;
            }
            
            // Test with a sample markdown that includes complex formatting
            string testMarkdown = @"# Test Heading

This paragraph has **bold** and *italic* text.

## Second Level Heading

Here is a list of items:
- Item 1
- Item 2 with **bold text**
- Item 3 with `inline code`

And an ordered list:
1. First item
2. Second item

```
// This is a code block
function example() {
  console.log('Hello, world!');
}
```

[Link text](https://example.com)";
            // Invoke the method
            var result = (string)methodInfo.Invoke(docGenerator, new object[] { testMarkdown });
            
            // Output path
            string outputFolder = Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory, 
                "..", "..", "..", "TestOutput");
                
            Directory.CreateDirectory(outputFolder);
            
            // Create an HTML file to view the result
            string outputFile = Path.Combine(outputFolder, $"MarkdownTest_{DateTime.Now:yyyyMMdd_HHmmss}.html");
            
            var html = new StringBuilder();
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html>");
            html.AppendLine("<head>");
            html.AppendLine("  <title>Markdown to HTML Conversion Test</title>");
            html.AppendLine("  <style>");
            html.AppendLine("    body { font-family: Arial, sans-serif; margin: 20px; }");
            html.AppendLine("    .container { max-width: 800px; margin: 0 auto; }");
            html.AppendLine("    .result { padding: 15px; border: 1px solid #ddd; background-color: #f9f9f9; }");
            html.AppendLine("    .source, .html { margin-top: 20px; padding: 15px; border: 1px solid #ddd; }");
            html.AppendLine("    pre { background-color: #f5f5f5; padding: 10px; overflow: auto; }");
            html.AppendLine("    code { font-family: Consolas, monospace; }");
            html.AppendLine("  </style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");
            html.AppendLine("  <div class=\"container\">");
            html.AppendLine("    <h1>Markdown to HTML Conversion Test</h1>");
            
            html.AppendLine("    <h2>Source Markdown</h2>");
            html.AppendLine("    <div class=\"source\">");
            html.AppendLine($"      <pre>{System.Web.HttpUtility.HtmlEncode(testMarkdown)}</pre>");
            html.AppendLine("    </div>");
            
            html.AppendLine("    <h2>Generated HTML</h2>");
            html.AppendLine("    <div class=\"html\">");
            html.AppendLine($"      <pre>{System.Web.HttpUtility.HtmlEncode(result)}</pre>");
            html.AppendLine("    </div>");
            
            html.AppendLine("    <h2>Rendered Result</h2>");
            html.AppendLine("    <div class=\"result\">");
            html.AppendLine($"      {result}");
            html.AppendLine("    </div>");
            
            html.AppendLine("  </div>");
            html.AppendLine("</body>");
            html.AppendLine("</html>");
            
            File.WriteAllText(outputFile, html.ToString());
            
            Console.WriteLine($"Test completed. Results saved to: {outputFile}");
        }
    }
}
