using System;
using System.Collections.Generic;
using System.IO;

namespace SnapAnalyser
{
    class CDNFixFinalTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== FINAL CDN FIX TEST ===");
            Console.WriteLine("Testing the corrected CDN placement in DocumentationGenerator");
            Console.WriteLine();

            try
            {
                // Create test pipeline
                var pipeline = CreateTestPipeline();
                Console.WriteLine("✓ Test pipeline created");

                // Generate diagram
                var diagramGen = new DiagramGenerator();
                string diagram = diagramGen.GenerateDiagram(pipeline);
                Console.WriteLine("✓ Diagram generated");

                // Generate full documentation
                var docGen = new DocumentationGenerator();
                string fullDoc = docGen.GenerateHtmlDocumentationAsync(pipeline, diagram, new ProjectData()).Result;
                Console.WriteLine("✓ Full documentation generated");

                // Check CDN placement
                bool hasRaphaelCDN = fullDoc.Contains("https://cdnjs.cloudflare.com/ajax/libs/raphael/2.3.0/raphael.min.js");
                bool hasFlowchartCDN = fullDoc.Contains("https://cdn.jsdelivr.net/npm/flowchart.js@1.17.1/release/flowchart.min.js");
                bool hasFlowchartStyles = fullDoc.Contains(".flowchart-container");
                
                // Check if CDN is placed before </body>
                int bodyCloseIndex = fullDoc.IndexOf("</body>");
                int raphaelIndex = fullDoc.IndexOf("raphael.min.js");
                int flowchartIndex = fullDoc.IndexOf("flowchart.min.js");
                
                bool cdnBeforeBodyClose = (raphaelIndex < bodyCloseIndex && raphaelIndex > 0) && 
                                        (flowchartIndex < bodyCloseIndex && flowchartIndex > 0);

                Console.WriteLine();
                Console.WriteLine("=== VERIFICATION RESULTS ===");
                Console.WriteLine($"✓ Raphael CDN included: {hasRaphaelCDN}");
                Console.WriteLine($"✓ Flowchart CDN included: {hasFlowchartCDN}");
                Console.WriteLine($"✓ Flowchart styles included: {hasFlowchartStyles}");
                Console.WriteLine($"✓ CDN scripts before </body>: {cdnBeforeBodyClose}");

                // Save test file
                string outputPath = "final_cdn_fix_test_result.html";
                File.WriteAllText(outputPath, fullDoc);
                Console.WriteLine($"✓ Test documentation saved: {outputPath}");

                Console.WriteLine();
                if (hasRaphaelCDN && hasFlowchartCDN && hasFlowchartStyles && cdnBeforeBodyClose)
                {
                    Console.WriteLine("🎉 SUCCESS! CDN fix is complete and correctly implemented!");
                    Console.WriteLine();
                    Console.WriteLine("The fix includes:");
                    Console.WriteLine("- Raphael.js CDN properly included");
                    Console.WriteLine("- Flowchart.js CDN properly included");
                    Console.WriteLine("- CDN scripts placed before </body> tag");
                    Console.WriteLine("- Flowchart styles included in <head>");
                    Console.WriteLine();
                    Console.WriteLine("Flow diagrams should now render correctly in generated documentation.");
                }
                else
                {
                    Console.WriteLine("❌ ISSUE DETECTED:");
                    if (!hasRaphaelCDN) Console.WriteLine("  - Missing Raphael CDN");
                    if (!hasFlowchartCDN) Console.WriteLine("  - Missing Flowchart CDN");
                    if (!hasFlowchartStyles) Console.WriteLine("  - Missing Flowchart styles");
                    if (!cdnBeforeBodyClose) Console.WriteLine("  - CDN scripts not properly placed");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        static PipelineData CreateTestPipeline()
        {
            var snaps = new List<SnapNode>
            {
                new SnapNode
                {
                    Id = "test-start",
                    Label = "Test Start",
                    Type = "com-snaplogic-snaps-read",
                    Category = SnapCategory.ExternalSystem,
                    Position = new Position { X = 0, Y = 0 },
                    IsStartPoint = true
                },
                new SnapNode
                {
                    Id = "test-transform",
                    Label = "Test Transform",
                    Type = "com-snaplogic-snaps-transform",
                    Category = SnapCategory.Transformation,
                    Position = new Position { X = 1, Y = 0 }
                },
                new SnapNode
                {
                    Id = "test-end",
                    Label = "Test End",
                    Type = "com-snaplogic-snaps-write",
                    Category = SnapCategory.ExternalSystem,
                    Position = new Position { X = 2, Y = 0 },
                    IsEndPoint = true
                }
            };

            var links = new List<Link>
            {
                new Link { FromSnapId = "test-start", ToSnapId = "test-transform" },
                new Link { FromSnapId = "test-transform", ToSnapId = "test-end" }
            };

            return new PipelineData
            {
                Name = "CDN Fix Test Pipeline",
                Author = "CDN Fix Test",
                Snaps = snaps,
                Links = links
            };
        }
    }
}
