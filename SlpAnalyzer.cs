﻿using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace SnapAnalyser
{
    public class PipelineData
    {
        public string Name { get; set; }
        public string Author { get; set; }
        public List<PipelineParameter> Parameters { get; set; } = new List<PipelineParameter>();
        public List<SnapNode> Snaps { get; set; } = new List<SnapNode>();
        public List<SnapLink> Links { get; set; } = new List<SnapLink>();
        public JObject RawPipelineJson { get; set; } // Store raw pipeline JSON for account extraction

        public List<SnapNode> GetStartPoints()
        {
            return Snaps.Where(s => s.IsStartPoint).ToList();
        }

        public List<SnapNode> GetEndPoints()
        {
            return Snaps.Where(s => s.IsEndPoint).ToList();
        }
    }

    public class PipelineParameter
    {
        public string Key { get; set; }
        public string Value { get; set; }
        public string DataType { get; set; }
        public bool Required { get; set; }
        public string Description { get; set; }
    }

    public class SnapNode
    {
        public string Id { get; set; }
        public string Label { get; set; }
        public string Type { get; set; }
        public Dictionary<string, string> Properties { get; set; } = new Dictionary<string, string>();
        public Position Position { get; set; }
        public List<SnapLink> InputConnections { get; set; } = new List<SnapLink>();
        public List<SnapLink> OutputConnections { get; set; } = new List<SnapLink>();
        public bool IsStartPoint { get; set; }
        public bool IsEndPoint { get; set; }
        public SnapCategory Category { get; set; }
    }

    public class SnapLink
    {
        public string Id { get; set; }
        public string SourceId { get; set; }
        public string TargetId { get; set; }
        public string SourceViewId { get; set; }
        public string TargetViewId { get; set; }
        public bool IsGoto { get; set; }
    }

    public class Position
    {
        public int X { get; set; }
        public int Y { get; set; }
    }

    public enum SnapCategory
    {
        FlowControl,
        Transformation,
        Database,
        ExternalSystem,
        FileOperation,
        ErrorHandling,
        Other
    }

    public class SlpAnalyzer
    {
        public PipelineData AnalyzePipeline(string slpContent)
        {
            try
            {
                JObject pipelineJson = JObject.Parse(slpContent);
                
                // Store raw JSON for account extraction
                
                PipelineData pipeline = new PipelineData
                {
                    Name = GetPipelineName(pipelineJson),
                    Author = GetPipelineAuthor(pipelineJson),
                    Parameters = GetPipelineParameters(pipelineJson),
                    Snaps = GetSnaps(pipelineJson),
                    Links = GetLinks(pipelineJson),
                    RawPipelineJson = pipelineJson
                };
                
                // Post-processing to enrich the model
                EnrichSnapData(pipeline);
                CalculateSnapCategories(pipeline);
                
                return pipeline;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error analyzing pipeline: {ex.Message}", ex);
            }
        }

        private string GetPipelineName(JObject pipelineJson)
        {
            return pipelineJson.SelectToken("property_map.info.label.value")?.ToString() ?? "Unnamed Pipeline";
        }

        private string GetPipelineAuthor(JObject pipelineJson)
        {
            return pipelineJson.SelectToken("property_map.info.author.value")?.ToString() ?? "Unknown";
        }

        private List<PipelineParameter> GetPipelineParameters(JObject pipelineJson)
        {
            var parameters = new List<PipelineParameter>();
            var paramsArray = pipelineJson.SelectToken("property_map.settings.param_table.value") as JArray;

            if (paramsArray != null)
            {
                foreach (var param in paramsArray)
                {
                    parameters.Add(new PipelineParameter
                    {
                        Key = param.SelectToken("key.value")?.ToString(),
                        Value = param.SelectToken("value.value")?.ToString(),
                        DataType = param.SelectToken("data_type.value")?.ToString(),
                        Required = param.SelectToken("required.value")?.ToString() == "true",
                        Description = param.SelectToken("description.value")?.ToString()
                    });
                }
            }

            return parameters;
        }

        private List<SnapNode> GetSnaps(JObject pipelineJson)
        {
            var snaps = new List<SnapNode>();
            var snapMap = pipelineJson.SelectToken("snap_map") as JObject;
            var renderMap = pipelineJson.SelectToken("render_map.detail_map") as JObject;

            if (snapMap != null)
            {
                foreach (var snapPair in snapMap)
                {
                    string snapId = snapPair.Key;
                    JObject snapData = snapPair.Value as JObject;

                    if (snapData != null)
                    {
                        var snap = new SnapNode
                        {
                            Id = snapId,
                            Label = snapData.SelectToken("property_map.info.label.value")?.ToString() ?? snapId,
                            Type = snapData.SelectToken("class_id")?.ToString() ?? "unknown",
                            Properties = ExtractSnapProperties(snapData),
                            Position = GetSnapPosition(renderMap, snapId)
                        };

                        snaps.Add(snap);
                    }
                }
            }

            return snaps;
        }        
        private Dictionary<string, string> ExtractSnapProperties(JObject snapData)
        {
            var properties = new Dictionary<string, string>();
            var settings = snapData.SelectToken("property_map.settings");

            if (settings != null)
            {
                foreach (var setting in settings)
                {
                    if (setting is JProperty prop)
                    {
                        // Extract simple value properties
                        if (prop.Value["value"] != null)
                        {
                            var value = prop.Value["value"];
                            // Handle non-complex values directly
                            if (value.Type != JTokenType.Object && value.Type != JTokenType.Array)
                            {
                                properties[$"settings.{prop.Name}.value"] = value.ToString();
                            }
                            // Special handling for routes array in Router snaps
                            else if (prop.Name == "routes" && value.Type == JTokenType.Array)
                            {
                                // Store the routes array as a serialized JSON string
                                properties["settings.routes.value"] = value.ToString(Formatting.None);
                                
                                // Also extract individual route elements to handle hierarchical structure
                                var routesArray = value as JArray;
                                if (routesArray != null)
                                {
                                    for (int i = 0; i < routesArray.Count; i++)
                                    {
                                        var route = routesArray[i];
                                        foreach (JProperty routeProp in route.Children<JProperty>())
                                        {
                                            // Extract expression and outputViewName values
                                            if (routeProp.Value["value"] != null)
                                            {
                                                string propKey = $"settings.routes.value[{i}].{routeProp.Name}.value";
                                                properties[propKey] = routeProp.Value["value"].ToString();
                                            }
                                            
                                            // Also extract expression flag if present
                                            if (routeProp.Name == "expression" && routeProp.Value["expression"] != null)
                                            {
                                                string exprKey = $"settings.routes.value[{i}].expression.expression";
                                                properties[exprKey] = routeProp.Value["expression"].ToString();
                                            }
                                        }
                                    }
                                }
                            }
                            // Special handling for joinPaths array in Join snaps
                            else if (prop.Name == "joinPaths" && value.Type == JTokenType.Array)
                            {
                                // Store the joinPaths array as a serialized JSON string
                                properties["settings.joinPaths.value"] = value.ToString(Formatting.None);

                                // Also extract individual join path elements
                                var joinPathsArray = value as JArray;
                                if (joinPathsArray != null)
                                {
                                    for (int i = 0; i < joinPathsArray.Count; i++)
                                    {
                                        var joinPath = joinPathsArray[i];
                                        if (joinPath != null)
                                        {
                                            // Extract leftPath
                                            var leftPath = joinPath["leftPath"];
                                            if (leftPath != null)
                                            {
                                                var leftPathValue = leftPath["value"]?.ToString();
                                                if (!string.IsNullOrEmpty(leftPathValue))
                                                {
                                                    properties[$"settings.joinPaths.value.{i}.leftPath.value"] = leftPathValue;
                                                }

                                                var leftPathExpression = leftPath["expression"]?.ToString();
                                                if (!string.IsNullOrEmpty(leftPathExpression))
                                                {
                                                    properties[$"settings.joinPaths.value.{i}.leftPath.expression"] = leftPathExpression;
                                                }
                                            }

                                            // Extract rightPath
                                            var rightPath = joinPath["rightPath"];
                                            if (rightPath != null)
                                            {
                                                var rightPathValue = rightPath["value"]?.ToString();
                                                if (!string.IsNullOrEmpty(rightPathValue))
                                                {
                                                    properties[$"settings.joinPaths.value.{i}.rightPath.value"] = rightPathValue;
                                                }

                                                var rightPathExpression = rightPath["expression"]?.ToString();
                                                if (!string.IsNullOrEmpty(rightPathExpression))
                                                {
                                                    properties[$"settings.joinPaths.value.{i}.rightPath.expression"] = rightPathExpression;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            // Special handling for Dynamics 365 search criteria
                            else if (prop.Name == "filterCriteria" || prop.Name == "searchCriteria" || prop.Name == "queryFilter" || prop.Name == "filter_prop")
                            {
                                // Store the search criteria as a serialized JSON string
                                properties[$"settings.{prop.Name}.value"] = value.ToString(Formatting.None);

                                // Try to extract individual filter components
                                ExtractDynamics365FilterProperties(value, properties, $"settings.{prop.Name}");
                            }
                            // Special handling for Dynamics 365 select conditions
                            else if (prop.Name == "select_condition_prop" || prop.Name == "selectCondition")
                            {
                                // Store the select conditions as a serialized JSON string
                                properties[$"settings.{prop.Name}.value"] = value.ToString(Formatting.None);

                                // Try to extract individual select attributes
                                ExtractDynamics365SelectProperties(value, properties, $"settings.{prop.Name}");
                            }
                            // Special handling for output attributes (legacy)
                            else if (prop.Name == "outputAttributes" || prop.Name == "selectAttributes")
                            {
                                properties[$"settings.{prop.Name}.value"] = value.ToString(Formatting.None);
                            }
                            // Special handling for order by criteria
                            else if (prop.Name == "orderBy" || prop.Name == "sortBy")
                            {
                                properties[$"settings.{prop.Name}.value"] = value.ToString(Formatting.None);
                            }
                            // For transformations, include the full path to help with mapping extraction
                            else if (prop.Name == "transformations")
                            {
                                ExtractTransformationProperties(value, properties, "settings.transformations");
                            }
                            // Special handling for mapping configurations
                            else if (prop.Name == "mappingTable" || prop.Name == "transformersList" || prop.Name == "mappingDefinition")
                            {
                                ExtractMapperExpressions(prop.Value, properties, prop.Name);
                            }
                            // Special handling for conditional tables
                            else if (prop.Name == "conditionalTable" || prop.Name.ToLower().Contains("conditionaltable") ||
                                   (prop.Name.ToLower().Contains("condition") && prop.Name.ToLower().Contains("table")) ||
                                   (prop.Name.ToLower().Contains("branch") && prop.Name.ToLower().Contains("table")))
                            {
                                Console.WriteLine($"Found conditionalTable in snap properties: {prop.Name}");
                                // Store the full JSON content
                                properties[$"settings.{prop.Name}.value"] = value.ToString(Formatting.None);
                                
                                // Also extract individual condition entries if it's an array
                                if (value is JArray conditionsArray)
                                {
                                    for (int i = 0; i < conditionsArray.Count; i++)
                                    {
                                        var condition = conditionsArray[i];
                                        foreach (JProperty condProp in condition.Children<JProperty>())
                                        {
                                            // Extract key fields from condition entries (expression, condition, description, etc.)
                                            if (condProp.Value != null)
                                            {
                                                string propKey = $"settings.{prop.Name}.value[{i}].{condProp.Name}";
                                                properties[propKey] = condProp.Value.ToString();
                                                Console.WriteLine($"Extracted condition property: {propKey}");
                                            }
                                        }
                                    }
                                }
                                // Handle single object case
                                else if (value is JObject conditionObj)
                                {
                                    foreach (JProperty condProp in conditionObj.Properties())
                                    {
                                        string propKey = $"settings.{prop.Name}.value.{condProp.Name}";
                                        properties[propKey] = condProp.Value.ToString();
                                        Console.WriteLine($"Extracted single condition property: {propKey}");
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return properties;
        }

        private void ExtractDynamics365FilterProperties(JToken filterCriteria, Dictionary<string, string> properties, string basePath)
        {
            if (filterCriteria == null) return;

            try
            {
                // Handle the specific Dynamics 365 filter_prop structure
                if (filterCriteria.Type == JTokenType.Object && filterCriteria["value"] != null)
                {
                    var valueArray = filterCriteria["value"];
                    if (valueArray.Type == JTokenType.Array)
                    {
                        var filterArray = valueArray as JArray;
                        for (int i = 0; i < filterArray.Count; i++)
                        {
                            var filter = filterArray[i];
                            if (filter != null)
                            {
                                // Extract filter components using the _prop structure
                                var typeValue = filter["type_prop"]?["value"]?.ToString();
                                var attributeValue = filter["filterAttribute_prop"]?["value"]?.ToString();
                                var operatorValue = filter["filterOperator_prop"]?["value"]?.ToString();
                                var filterValue = filter["filterValue_prop"]?["value"]?.ToString();

                                if (!string.IsNullOrEmpty(typeValue))
                                    properties[$"{basePath}.{i}.type"] = typeValue;
                                if (!string.IsNullOrEmpty(attributeValue))
                                    properties[$"{basePath}.{i}.attribute"] = attributeValue;
                                if (!string.IsNullOrEmpty(operatorValue))
                                    properties[$"{basePath}.{i}.operator"] = operatorValue;
                                if (!string.IsNullOrEmpty(filterValue))
                                    properties[$"{basePath}.{i}.value"] = filterValue;

                                Console.WriteLine($"[DEBUG] Extracted D365 filter {i}: type={typeValue}, attribute={attributeValue}, operator={operatorValue}, value={filterValue}");
                            }
                        }
                    }
                }
                // Fallback to handle other formats
                else if (filterCriteria.Type == JTokenType.Array)
                {
                    var filterArray = filterCriteria as JArray;
                    for (int i = 0; i < filterArray.Count; i++)
                    {
                        var filter = filterArray[i];
                        if (filter != null)
                        {
                            // Extract filter components (legacy format)
                            var attribute = filter["attribute"]?.ToString();
                            var operatorType = filter["operator"]?.ToString();
                            var value = filter["value"]?.ToString();
                            var type = filter["type"]?.ToString();

                            if (!string.IsNullOrEmpty(attribute))
                                properties[$"{basePath}.{i}.attribute"] = attribute;
                            if (!string.IsNullOrEmpty(operatorType))
                                properties[$"{basePath}.{i}.operator"] = operatorType;
                            if (!string.IsNullOrEmpty(value))
                                properties[$"{basePath}.{i}.value"] = value;
                            if (!string.IsNullOrEmpty(type))
                                properties[$"{basePath}.{i}.type"] = type;
                        }
                    }
                }
                else if (filterCriteria.Type == JTokenType.Object)
                {
                    // Handle single filter object (legacy format)
                    var attribute = filterCriteria["attribute"]?.ToString();
                    var operatorType = filterCriteria["operator"]?.ToString();
                    var value = filterCriteria["value"]?.ToString();
                    var type = filterCriteria["type"]?.ToString();

                    if (!string.IsNullOrEmpty(attribute))
                        properties[$"{basePath}.attribute"] = attribute;
                    if (!string.IsNullOrEmpty(operatorType))
                        properties[$"{basePath}.operator"] = operatorType;
                    if (!string.IsNullOrEmpty(value))
                        properties[$"{basePath}.value"] = value;
                    if (!string.IsNullOrEmpty(type))
                        properties[$"{basePath}.type"] = type;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] Error extracting Dynamics 365 filter properties: {ex.Message}");
            }
        }

        private void ExtractDynamics365SelectProperties(JToken selectConditions, Dictionary<string, string> properties, string basePath)
        {
            if (selectConditions == null) return;

            try
            {
                // Handle the specific Dynamics 365 select_condition_prop structure
                if (selectConditions.Type == JTokenType.Object && selectConditions["value"] != null)
                {
                    var valueArray = selectConditions["value"];
                    if (valueArray.Type == JTokenType.Array)
                    {
                        var selectArray = valueArray as JArray;
                        for (int i = 0; i < selectArray.Count; i++)
                        {
                            var selectItem = selectArray[i];
                            if (selectItem != null)
                            {
                                // Extract select components using the _prop structure
                                var selectValue = selectItem["select_prop"]?["value"]?.ToString();
                                var expressionValue = selectItem["select_prop"]?["expression"]?.ToString();

                                if (!string.IsNullOrEmpty(selectValue))
                                    properties[$"{basePath}.{i}.attribute"] = selectValue;
                                if (!string.IsNullOrEmpty(expressionValue))
                                    properties[$"{basePath}.{i}.expression"] = expressionValue;

                                Console.WriteLine($"[DEBUG] Extracted D365 select {i}: attribute={selectValue}, expression={expressionValue}");
                            }
                        }
                    }
                }
                // Fallback to handle other formats
                else if (selectConditions.Type == JTokenType.Array)
                {
                    var selectArray = selectConditions as JArray;
                    for (int i = 0; i < selectArray.Count; i++)
                    {
                        var selectItem = selectArray[i];
                        if (selectItem != null)
                        {
                            // Extract select components (legacy format)
                            var attribute = selectItem["attribute"]?.ToString() ?? selectItem["field"]?.ToString() ?? selectItem.ToString();

                            if (!string.IsNullOrEmpty(attribute))
                                properties[$"{basePath}.{i}.attribute"] = attribute;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] Error extracting Dynamics 365 select properties: {ex.Message}");
            }
        }

        private void ExtractTransformationProperties(JToken transformations, Dictionary<string, string> properties, string prefix)
        {
            if (transformations == null) return;

            Console.WriteLine($"Extracting transformation properties from prefix: {prefix}");

            if (transformations is JObject transformObj)
            {
                foreach (var prop in transformObj.Properties())
                {
                    string propPath = $"{prefix}.{prop.Name}";
                    Console.WriteLine($"Processing property: {propPath}");
                    
                    // Handle structure: prop -> valueObj -> value
                    if (prop.Value is JObject valueObj && valueObj["value"] != null)
                    {
                        var value = valueObj["value"];
                        if (value.Type != JTokenType.Object && value.Type != JTokenType.Array)
                        {
                            properties[$"{propPath}.value"] = value.ToString();
                            Console.WriteLine($"  Simple value: {propPath}.value = {value}");
                        }
                        else
                        {
                            // Store the full JSON for complex objects
                            properties[$"{propPath}.value"] = value.ToString(Formatting.None);
                            Console.WriteLine($"  Complex value at {propPath}.value (length: {value.ToString().Length})");
                            
                            // For mappingTable specifically, do deeper extraction
                            if (prop.Name == "mappingTable" || propPath.EndsWith(".mappingTable"))
                            {
                                Console.WriteLine("  Found mappingTable, doing deeper extraction");
                                ExtractMapperExpressions(valueObj, properties, propPath);
                            }
                            
                            // For array values, process each item looking for mappings
                            if (value.Type == JTokenType.Array)
                            {
                                int index = 0;
                                foreach (var item in (JArray)value)
                                {
                                    // Check for targetPath/expression pairs
                                    var targetPath = item["targetPath"]?.ToString() ?? item["target"]?.ToString();
                                    var expression = item["expression"]?.ToString() ?? item["source"]?.ToString();
                                    
                                    if (!string.IsNullOrEmpty(targetPath) && !string.IsNullOrEmpty(expression))
                                    {
                                        properties[$"{propPath}.{index}.targetPath"] = targetPath;
                                        properties[$"{propPath}.{index}.expression"] = expression;
                                        Console.WriteLine($"  Found mapping pair: {targetPath} = {expression}");
                                    }
                                    
                                    // If item is a nested object with value properties, extract them
                                    if (item is JObject itemObj)
                                    {
                                        foreach (var itemProp in itemObj.Properties())
                                        {
                                            if (itemProp.Value is JObject valObj && valObj["value"] != null)
                                            {
                                                properties[$"{propPath}.{index}.{itemProp.Name}.value"] = valObj["value"].ToString();
                                            }
                                        }
                                    }
                                    
                                    index++;
                                }
                            }
                            
                            // For object values, process nested properties 
                            if (value.Type == JTokenType.Object)
                            {
                                JObject valueAsObj = (JObject)value;
                                // Recursively extract from nested objects
                                ExtractTransformationProperties(valueAsObj, properties, propPath + ".value");
                            }
                        }
                    }
                    // Handle direct property values (without nested .value)
                    else if (prop.Value is JObject propValueObj)
                    {
                        // Recursively extract from nested objects
                        ExtractTransformationProperties(propValueObj, properties, propPath);
                    }
                    else if (prop.Value != null && 
                             prop.Value.Type != JTokenType.Object && 
                             prop.Value.Type != JTokenType.Array)
                    {
                        // Store simple direct values
                        properties[propPath] = prop.Value.ToString();
                        Console.WriteLine($"  Direct value: {propPath} = {prop.Value}");
                    }
                }
            }
            else if (transformations is JArray transformArray)
            {
                // For array properties, process each item
                for (int i = 0; i < transformArray.Count; i++)
                {
                    JToken item = transformArray[i];
                    string itemPath = $"{prefix}[{i}]";
                    
                    // Extract mapping patterns if they exist
                    var targetPath = item["targetPath"]?.ToString() ?? item["target"]?.ToString();
                    var expression = item["expression"]?.ToString() ?? item["source"]?.ToString();
                    
                    if (!string.IsNullOrEmpty(targetPath) && !string.IsNullOrEmpty(expression))
                    {
                        properties[$"{itemPath}.targetPath"] = targetPath;
                        properties[$"{itemPath}.expression"] = expression;
                        Console.WriteLine($"Found array mapping: {targetPath} = {expression}");
                    }
                    
                    // If item is an object, recursively extract properties
                    if (item is JObject itemObj)
                    {
                        ExtractTransformationProperties(itemObj, properties, itemPath);
                    }
                }
            }
        }// Helper method to extract detailed mapping expressions from mapper snaps
        private void ExtractMapperExpressions(JToken mappingToken, Dictionary<string, string> properties, string propertyPrefix)
        {
            try
            {
                if (mappingToken == null) return;
                
                // Log the token type we're processing
                Console.WriteLine($"Processing mapper expressions from {propertyPrefix}, token type: {mappingToken.Type}");
                    
                // Handle mappingTable format (common in datatransform snaps)
                var mappingTable = mappingToken.SelectToken("value") as JArray;
                if (mappingTable != null)
                {
                    // Store the full mappingTable for higher-level processing
                    properties[$"{propertyPrefix}.value"] = mappingTable.ToString(Formatting.None);
                    
                    int index = 0;
                    foreach (var mapping in mappingTable)
                    {
                        Console.WriteLine($"Processing mapping table entry {index}");
                        
                        // Check various property paths used in SnapLogic
                        var targetPath = mapping.SelectToken("targetPath.value")?.ToString() ??
                                        mapping.SelectToken("targetPath")?.ToString() ??
                                        mapping.SelectToken("target.value")?.ToString() ??
                                        mapping.SelectToken("target")?.ToString();
                        
                        var expression = mapping.SelectToken("expression.value")?.ToString() ??
                                       mapping.SelectToken("expression")?.ToString() ??
                                       mapping.SelectToken("source.value")?.ToString() ??
                                       mapping.SelectToken("source")?.ToString();
                        
                        if (!string.IsNullOrEmpty(targetPath) && !string.IsNullOrEmpty(expression))
                        {
                            Console.WriteLine($"Found mapping: {targetPath} = {expression}");
                            properties[$"{propertyPrefix}.{index}.targetPath.value"] = targetPath;
                            properties[$"{propertyPrefix}.{index}.expression.value"] = expression;
                        }
                        else {
                            // If we can't find the standard paths, log all properties in this mapping
                            Console.WriteLine("Could not find standard targetPath/expression, listing all properties:");
                            foreach (JProperty prop in mapping.Children<JProperty>())
                            {
                                Console.WriteLine($"  {prop.Name}: {prop.Value}");
                                
                                if (prop.Value is JObject childObj)
                                {
                                    foreach (JProperty childProp in childObj.Properties())
                                    {
                                        if (childProp.Name == "value")
                                        {
                                            properties[$"{propertyPrefix}.{index}.{prop.Name}.value"] = childProp.Value.ToString();
                                        }
                                    }
                                }
                                else
                                {
                                    properties[$"{propertyPrefix}.{index}.{prop.Name}"] = prop.Value.ToString();
                                }
                            }
                        }
                        index++;
                    }
                }
                // Handle transformersList format 
                else if (mappingToken["value"] is JObject transformers)
                {
                    properties[$"{propertyPrefix}.value"] = transformers.ToString(Formatting.None);
                    
                    foreach (JProperty transformer in transformers.Properties())
                    {
                        var expressions = transformer.Value.SelectToken("expressions");
                        if (expressions != null && expressions is JObject expObj)
                        {
                            foreach (JProperty exp in expObj.Properties())
                            {
                                string targetPath = $"{transformer.Name}.{exp.Name}";
                                string expression = exp.Value.ToString();
                                properties[$"{propertyPrefix}.{targetPath}"] = expression;
                            }
                        }
                        else 
                        {
                            // If no expressions property, check for direct targetPath/expression properties
                            var targetPath = transformer.Value.SelectToken("targetPath")?.ToString();
                            var expression = transformer.Value.SelectToken("expression")?.ToString();
                            
                            if (!string.IsNullOrEmpty(targetPath) && !string.IsNullOrEmpty(expression))
                            {
                                properties[$"{propertyPrefix}.{transformer.Name}.targetPath"] = targetPath;
                                properties[$"{propertyPrefix}.{transformer.Name}.expression"] = expression;
                            }
                        }
                    }
                }
                // Handle direct object format (might be an array or object directly)
                else if (mappingToken["value"] is JArray valueArray)
                {
                    Console.WriteLine("Processing value as JArray");
                    properties[$"{propertyPrefix}.value"] = valueArray.ToString(Formatting.None);
                    
                    int index = 0;
                    foreach (var item in valueArray)
                    {
                        // Try to extract mappings from each array item
                        var targetPath = item.SelectToken("targetPath")?.ToString() ?? 
                                        item.SelectToken("target")?.ToString();
                        var expression = item.SelectToken("expression")?.ToString() ?? 
                                       item.SelectToken("source")?.ToString();
                                       
                        if (!string.IsNullOrEmpty(targetPath) && !string.IsNullOrEmpty(expression))
                        {
                            properties[$"{propertyPrefix}.{index}.targetPath"] = targetPath;
                            properties[$"{propertyPrefix}.{index}.expression"] = expression;
                        }
                        index++;
                    }
                }
                // Handle mappingDefinition format or any other format
                else
                {
                    var value = mappingToken.SelectToken("value")?.ToString();
                    if (!string.IsNullOrEmpty(value))
                    {
                        properties[$"{propertyPrefix}.value"] = value;
                        
                        // Try to parse the value as JSON if it looks like it might be
                        if (value.TrimStart().StartsWith("[") || value.TrimStart().StartsWith("{"))
                        {
                            try
                            {
                                JToken parsedValue = JToken.Parse(value);
                                
                                // If it's an array, process each item
                                if (parsedValue is JArray arr)
                                {
                                    int index = 0;
                                    foreach (var item in arr)
                                    {
                                        // Try common property names
                                        var targetPath = item.SelectToken("targetPath")?.ToString() ?? 
                                                      item.SelectToken("target")?.ToString();
                                        var expression = item.SelectToken("expression")?.ToString() ?? 
                                                      item.SelectToken("source")?.ToString();
                                                      
                                        if (!string.IsNullOrEmpty(targetPath) && !string.IsNullOrEmpty(expression))
                                        {
                                            properties[$"{propertyPrefix}.parsed.{index}.targetPath"] = targetPath;
                                            properties[$"{propertyPrefix}.parsed.{index}.expression"] = expression;
                                        }
                                        index++;
                                    }
                                }
                                // If it's an object, check if it has mappings directly or nested
                                else if (parsedValue is JObject obj)
                                {
                                    // Check if this is a direct mapping object
                                    var targetPath = obj.SelectToken("targetPath")?.ToString() ?? 
                                                  obj.SelectToken("target")?.ToString();
                                    var expression = obj.SelectToken("expression")?.ToString() ?? 
                                                   obj.SelectToken("source")?.ToString();
                                                   
                                    if (!string.IsNullOrEmpty(targetPath) && !string.IsNullOrEmpty(expression))
                                    {
                                        properties[$"{propertyPrefix}.parsed.targetPath"] = targetPath;
                                        properties[$"{propertyPrefix}.parsed.expression"] = expression;
                                    }
                                    else
                                    {
                                        // Check each property for nested mappings
                                        foreach (JProperty prop in obj.Properties())
                                        {
                                            if (prop.Value is JObject propObj)
                                            {
                                                targetPath = propObj.SelectToken("targetPath")?.ToString() ?? 
                                                         propObj.SelectToken("target")?.ToString();
                                                expression = propObj.SelectToken("expression")?.ToString() ?? 
                                                          propObj.SelectToken("source")?.ToString();
                                                          
                                                if (!string.IsNullOrEmpty(targetPath) && !string.IsNullOrEmpty(expression))
                                                {
                                                    properties[$"{propertyPrefix}.parsed.{prop.Name}.targetPath"] = targetPath;
                                                    properties[$"{propertyPrefix}.parsed.{prop.Name}.expression"] = expression;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error parsing nested JSON value: {ex.Message}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error extracting mapper expressions: {ex.Message}");
                // Store the error but continue processing
                properties[$"{propertyPrefix}.error"] = ex.Message;
            }
        }

        private Position GetSnapPosition(JObject renderMap, string snapId)
        {
            var position = new Position { X = 0, Y = 0 };

            if (renderMap != null && renderMap[snapId] != null)
            {
                position.X = renderMap[snapId]["grid_x_int"]?.ToObject<int>() ?? 0;
                position.Y = renderMap[snapId]["grid_y_int"]?.ToObject<int>() ?? 0;
            }

            return position;
        }

        private List<SnapLink> GetLinks(JObject pipelineJson)
        {
            var links = new List<SnapLink>();
            var linkMap = pipelineJson.SelectToken("link_map") as JObject;

            if (linkMap != null)
            {
                foreach (var linkPair in linkMap)
                {
                    string linkId = linkPair.Key;
                    JObject linkData = linkPair.Value as JObject;

                    if (linkData != null)
                    {
                        var link = new SnapLink
                        {
                            Id = linkId,
                            SourceId = linkData.SelectToken("src_id")?.ToString(),
                            TargetId = linkData.SelectToken("dst_id")?.ToString(),
                            SourceViewId = linkData.SelectToken("src_view_id")?.ToString(),
                            TargetViewId = linkData.SelectToken("dst_view_id")?.ToString(),
                            IsGoto = linkData.SelectToken("isGoto")?.ToObject<bool>() ?? false
                        };

                        links.Add(link);
                    }
                }
            }

            return links;
        }

        private void EnrichSnapData(PipelineData pipeline)
        {
            // Add source and target connections to each snap
            foreach (var link in pipeline.Links)
            {
                var sourceSnap = pipeline.Snaps.FirstOrDefault(s => s.Id == link.SourceId);
                var targetSnap = pipeline.Snaps.FirstOrDefault(s => s.Id == link.TargetId);

                if (sourceSnap != null)
                {
                    sourceSnap.OutputConnections.Add(link);
                }

                if (targetSnap != null)
                {
                    targetSnap.InputConnections.Add(link);
                }
            }

            // Identify start and end points of the pipeline
            foreach (var snap in pipeline.Snaps)
            {
                if (snap.InputConnections.Count == 0)
                {
                    snap.IsStartPoint = true;
                }

                if (snap.OutputConnections.Count == 0)
                {
                    snap.IsEndPoint = true;
                }
            }
        }

        private void CalculateSnapCategories(PipelineData pipeline)
        {
            Console.WriteLine("[DEBUG] === SNAP CATEGORIZATION ===");
            Console.WriteLine($"[DEBUG] Processing {pipeline.Snaps.Count} snaps for categorization");
            
            foreach (var snap in pipeline.Snaps)
            {
                Console.WriteLine($"[DEBUG] Processing snap: '{snap.Label}' (Type: '{snap.Type}')");
                
                // Check for router/flow control snaps FIRST (pure flow control)
                if (snap.Type.Contains("router") || 
                    snap.Type.Contains("flow") || 
                    snap.Type.Contains("branch") || 
                    snap.Type.Contains("switch") || 
                    snap.Type.Contains("gate") || 
                    snap.Type.Contains("join") ||
                    snap.Type.Contains("copy") ||
                    snap.Type.Contains("filter"))
                {
                    snap.Category = SnapCategory.FlowControl;
                    Console.WriteLine($"[DEBUG] CATEGORIZED as FlowControl: '{snap.Label}' (Type: {snap.Type})");
                }
                // Check for transformation snaps (including conditional transforms)
                else if (snap.Type.Contains("transform") || 
                         snap.Type.Contains("map") || 
                         snap.Type.Contains("conditional"))
                {
                    snap.Category = SnapCategory.Transformation;
                    Console.WriteLine($"[DEBUG] CATEGORIZED as Transformation: '{snap.Label}' (Type: {snap.Type})");
                }
                else if (snap.Type.Contains("sql") || 
                         snap.Type.Contains("sqlserver") || 
                         snap.Type.Contains("database") || 
                         snap.Type.Contains("insert") || 
                         snap.Type.Contains("update") || 
                         snap.Type.Contains("select") ||
                         snap.Type.Contains("execute") ||
                         snap.Type.Contains("mysql") ||
                         snap.Type.Contains("oracle") ||
                         snap.Type.Contains("postgres"))
                {
                    snap.Category = SnapCategory.Database;
                    Console.WriteLine($"[DEBUG] CATEGORIZED as Database: '{snap.Label}' (Type: {snap.Type})");
                }
                else if (snap.Type.Contains("dynamics"))
                {
                    snap.Category = SnapCategory.ExternalSystem;
                    Console.WriteLine($"[DEBUG] CATEGORIZED as ExternalSystem: '{snap.Label}' (Type: {snap.Type})");
                }
                else if (snap.Type.Contains("binary") || snap.Type.Contains("file"))
                {
                    snap.Category = SnapCategory.FileOperation;
                    Console.WriteLine($"[DEBUG] CATEGORIZED as FileOperation: '{snap.Label}' (Type: {snap.Type})");
                }
                else if (snap.Type.Contains("error") || snap.Type.Contains("exit"))
                {
                    snap.Category = SnapCategory.ErrorHandling;
                    Console.WriteLine($"[DEBUG] CATEGORIZED as ErrorHandling: '{snap.Label}' (Type: {snap.Type})");
                }
                else
                {
                    snap.Category = SnapCategory.Other;
                    Console.WriteLine($"[DEBUG] CATEGORIZED as Other: '{snap.Label}' (Type: {snap.Type})");
                }
            }
            Console.WriteLine("[DEBUG] === END SNAP CATEGORIZATION ===");
        }
    }
}
