using System;
using System.Collections.Generic;
using System.Linq;

namespace SnapAnalyser
{
    /// <summary>
    /// Specialized diagram generator for Router, Join, and Union snaps that creates
    /// detailed flow diagrams using cytoscape.js showing conditions, connected snaps, and data flow paths.
    /// </summary>
    public class FlowControlDiagramGenerator
    {
        private readonly CytoscapeJsGenerator _cytoscapeGenerator;        public FlowControlDiagramGenerator()
        {
            _cytoscapeGenerator = new CytoscapeJsGenerator();
        }

        /// <summary>
        /// Generates an enhanced flow diagram specifically for Router snaps showing routing conditions and connected snaps
        /// </summary>
        public string GenerateRouterFlowDiagram(SnapNode routerSnap, List<SnapNode> allSnaps)
        {
            try
            {
                // Get connected snaps
                var connectedSnaps = GetConnectedSnaps(routerSnap, allSnaps);
                
                // Create a mini pipeline for this router flow
                var routerPipeline = new PipelineData
                {
                    Name = $"Router Flow: {routerSnap.Label}",
                    Snaps = connectedSnaps,
                    Links = GetLinksForSnaps(connectedSnaps, allSnaps)
                };

                // Generate cytoscape elements
                string elements = _cytoscapeGenerator.GenerateCytoscapeElements(routerPipeline);
                
                // Generate HTML with router-specific styling
                return _cytoscapeGenerator.GenerateCytoscapeHtml(elements, 
                    $"router-flow-{_cytoscapeGenerator.SanitizeId(routerSnap.Id)}", 
                    $"Router Flow Diagram: {routerSnap.Label}");
            }
            catch (Exception ex)
            {
                return $"<div class=\"error\">Error generating router diagram: {ex.Message}</div>";
            }
        }

        /// <summary>
        /// Generates an enhanced flow diagram for Join snaps showing input sources and join conditions
        /// </summary>
        public string GenerateJoinFlowDiagram(SnapNode joinSnap, List<SnapNode> allSnaps)
        {
            try
            {
                // Get connected snaps
                var connectedSnaps = GetConnectedSnaps(joinSnap, allSnaps);
                
                // Create a mini pipeline for this join flow
                var joinPipeline = new PipelineData
                {
                    Name = $"Join Flow: {joinSnap.Label}",
                    Snaps = connectedSnaps,
                    Links = GetLinksForSnaps(connectedSnaps, allSnaps)
                };

                // Generate cytoscape elements
                string elements = _cytoscapeGenerator.GenerateCytoscapeElements(joinPipeline);
                
                // Generate HTML with join-specific styling
                return _cytoscapeGenerator.GenerateCytoscapeHtml(elements, 
                    $"join-flow-{_cytoscapeGenerator.SanitizeId(joinSnap.Id)}", 
                    $"Join Flow Diagram: {joinSnap.Label}");
            }
            catch (Exception ex)
            {
                return $"<div class=\"error\">Error generating join diagram: {ex.Message}</div>";
            }
        }

        /// <summary>
        /// Generates an enhanced flow diagram for Union snaps showing input sources being merged
        /// </summary>
        public string GenerateUnionFlowDiagram(SnapNode unionSnap, List<SnapNode> allSnaps)
        {
            try
            {
                // Get connected snaps
                var connectedSnaps = GetConnectedSnaps(unionSnap, allSnaps);
                
                // Create a mini pipeline for this union flow
                var unionPipeline = new PipelineData
                {
                    Name = $"Union Flow: {unionSnap.Label}",
                    Snaps = connectedSnaps,
                    Links = GetLinksForSnaps(connectedSnaps, allSnaps)
                };

                // Generate cytoscape elements
                string elements = _cytoscapeGenerator.GenerateCytoscapeElements(unionPipeline);
                
                // Generate HTML with union-specific styling
                return _cytoscapeGenerator.GenerateCytoscapeHtml(elements, 
                    $"union-flow-{_cytoscapeGenerator.SanitizeId(unionSnap.Id)}", 
                    $"Union Flow Diagram: {unionSnap.Label}");
            }
            catch (Exception ex)
            {
                return $"<div class=\"error\">Error generating union diagram: {ex.Message}</div>";
            }
        }

        /// <summary>
        /// Gets all snaps connected to the specified snap (both input and output)
        /// </summary>
        private List<SnapNode> GetConnectedSnaps(SnapNode centralSnap, List<SnapNode> allSnaps)
        {
            var connectedSnaps = new List<SnapNode>();
            
            // Add the central snap itself
            connectedSnaps.Add(centralSnap);
            
            // Find input snaps (snaps that connect TO the central snap)
            foreach (var snap in allSnaps)
            {
                if (snap.OutputConnections?.Any(conn => conn.TargetId == centralSnap.Id) == true)
                {
                    connectedSnaps.Add(snap);
                }
            }
            
            // Find output snaps (snaps that the central snap connects TO)
            if (centralSnap.OutputConnections != null)
            {
                foreach (var connection in centralSnap.OutputConnections)
                {
                    var targetSnap = allSnaps.FirstOrDefault(s => s.Id == connection.TargetId);
                    if (targetSnap != null)
                    {
                        connectedSnaps.Add(targetSnap);
                    }
                }
            }
            
            return connectedSnaps.Distinct().ToList();
        }

        /// <summary>
        /// Gets the links between the connected snaps
        /// </summary>
        private List<SnapLink> GetLinksForSnaps(List<SnapNode> snaps, List<SnapNode> allSnaps)
        {
            var links = new List<SnapLink>();
            
            foreach (var snap in snaps)
            {
                if (snap.OutputConnections != null)
                {
                    foreach (var connection in snap.OutputConnections)
                    {
                        // Only include links where both source and target are in our snap list
                        if (snaps.Any(s => s.Id == connection.TargetId))
                        {
                            links.Add(new SnapLink
                            {
                                SourceId = snap.Id,
                                TargetId = connection.TargetId
                            });
                        }
                    }
                }
            }
            
            return links;
        }
    }
}