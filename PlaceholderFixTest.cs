using System;
using SnapAnalyser;

class PlaceholderFixTest
{
    static void Main(string[] args)
    {
        Console.WriteLine("Testing Improved Placeholder Handling");
        Console.WriteLine("====================================");
        
        // Create a DocumentationGenerator instance
        var docGen = new DocumentationGenerator();
        
        // Test the AI-generated content with placeholders similar to your example
        string testContent = "This Snap routes input documents based on the presence of the ##INLINECODE35ff60d6## field. " +
                           "It is configured with two routes: if ##INLINECODE6333c9ec## is not null, the document is sent to ##INLINECODE5dee338b##; " +
                           "if ##INLINECODEc63766be## is null, it is sent to ##INLINECODEf86a59b2##. " +
                           "The ##INLINECODEa5461794## property is set to ##INLINECODE45522aba##, so each document may be sent to multiple outputs " +
                           "if it matches multiple conditions (though these are mutually exclusive here). Execution mode is set to \"Validate & Execute\".";
        
        Console.WriteLine("Original content:");
        Console.WriteLine(testContent);
        Console.WriteLine();
        
        // Use reflection to access the private method for testing
        var methodInfo = typeof(DocumentationGenerator).GetMethod("ConvertMarkdownToHtml", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (methodInfo != null)
        {
            string processedContent = (string)methodInfo.Invoke(docGen, new object[] { testContent });
            
            Console.WriteLine("Processed content with context-aware placeholders:");
            Console.WriteLine(processedContent);
            Console.WriteLine();
            
            // Check if placeholders were replaced
            bool hasUnprocessedPlaceholders = processedContent.Contains("##INLINECODE");
            Console.WriteLine($"Unprocessed placeholders remaining: {hasUnprocessedPlaceholders}");
            
            if (!hasUnprocessedPlaceholders)
            {
                Console.WriteLine("✅ SUCCESS: All placeholders were processed with context-aware fallbacks!");
            }
            else
            {
                Console.WriteLine("❌ ISSUE: Some placeholders were not processed.");
            }
        }
        else
        {
            Console.WriteLine("❌ Could not access ConvertMarkdownToHtml method for testing");
        }
        
        Console.WriteLine();
        Console.WriteLine("Test completed. Press any key to exit...");
        Console.ReadKey();
    }
}
