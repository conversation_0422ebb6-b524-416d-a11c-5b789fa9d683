using System;
using System.IO;

namespace SnapAnalyser
{
    class QuickCytoscapeValidation
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("Quick Cytoscape.js Validation Test");
            Console.WriteLine("===================================");
            
            try
            {
                // Test basic instantiation
                var generator = new CytoscapeJsGenerator();
                Console.WriteLine("✅ CytoscapeJsGenerator created successfully");
                
                var diagramGen = new DiagramGenerator();
                Console.WriteLine("✅ DiagramGenerator created successfully");
                
                var flowControlGen = new FlowControlDiagramGenerator();
                Console.WriteLine("✅ FlowControlDiagramGenerator created successfully");
                
                // Test a simple pipeline creation
                var pipeline = new PipelineData
                {
                    Name = "Test Pipeline",
                    Snaps = new List<SnapNode>
                    {
                        new SnapNode { Id = "node1", Label = "Start", Type = "com.snaplogic.snap.api.InputSetting" },
                        new SnapNode { Id = "node2", Label = "Transform", Type = "com.snaplogic.snap.api.Transformer" },
                        new SnapNode { Id = "node3", Label = "Output", Type = "com.snaplogic.snap.api.OutputSetting" }
                    },
                    Links = new List<SnapLink>
                    {
                        new SnapLink { SourceId = "node1", TargetId = "node2" },
                        new SnapLink { SourceId = "node2", TargetId = "node3" }
                    }
                };
                
                // Test elements generation
                string elements = generator.GenerateCytoscapeElements(pipeline);
                Console.WriteLine($"✅ Generated cytoscape elements: {elements.Length} characters");
                
                // Test HTML generation
                string html = generator.GenerateCytoscapeHtml(elements, "test-diagram", "Test Pipeline");
                Console.WriteLine($"✅ Generated cytoscape HTML: {html.Length} characters");
                
                // Test diagram generation
                string diagram = diagramGen.GenerateDiagram(pipeline);
                Console.WriteLine($"✅ Generated diagram through DiagramGenerator: {diagram.Length} characters");
                
                // Check if it contains cytoscape.js instead of flowchart.js
                if (diagram.Contains("cytoscape"))
                {
                    Console.WriteLine("✅ Diagram contains cytoscape.js integration");
                }
                else
                {
                    Console.WriteLine("❌ Diagram does not contain cytoscape.js");
                }
                
                if (diagram.Contains("flowchart"))
                {
                    Console.WriteLine("❌ Diagram still contains flowchart.js references");
                }
                else
                {
                    Console.WriteLine("✅ Diagram has no flowchart.js references");
                }
                
                // Save test output
                string testFile = "cytoscape_validation_test.html";
                File.WriteAllText(testFile, diagram);
                Console.WriteLine($"✅ Test output saved to: {testFile}");
                
                Console.WriteLine();
                Console.WriteLine("🎉 ALL BASIC VALIDATIONS PASSED!");
                Console.WriteLine("The cytoscape.js implementation is ready for use.");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ VALIDATION FAILED: {ex.Message}");
                Console.WriteLine($"Details: {ex.StackTrace}");
            }
            
            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
