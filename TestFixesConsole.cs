// Test program for verifying the heading and inline code fixes
using System;
using System.IO;
using System.Reflection;
using System.Collections.Generic;
using SnapAnalyser;

namespace SnapAnalyser
{
    public class TestFixesConsole
    {        public static void Main()
        {
            Console.WriteLine("Testing Markdown to HTML fixes...");
            TestHeadingFix();
            Console.WriteLine("\nTesting Inline Code Placeholder Fix...");
            TestInlineCodeFix();
            Console.WriteLine("\nTesting Duplicate Mapping Table Fix...");
            SimpleMappingTest.TestMappingFix();
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
        
        private static void TestHeadingFix()
        {
            try
            {
                // Initialize the DocumentationGenerator
                var docGen = new DocumentationGenerator();
                
                // Get the private ConvertMarkdownToHtml method using reflection
                var docGenType = typeof(DocumentationGenerator);
                var convertMethod = docGenType.GetMethod("ConvertMarkdownToHtml", 
                    BindingFlags.NonPublic | BindingFlags.Instance);
                
                if (convertMethod == null)
                {
                    Console.WriteLine("ERROR: Could not find ConvertMarkdownToHtml method");
                    return;
                }
                
                // Test data with 1 to 6 hash marks for headings
                string markdown = 
                    "# Heading 1\n\n" +
                    "## Heading 2\n\n" +
                    "### Heading 3\n\n" +
                    "#### Heading 4\n\n" +
                    "##### Heading 5\n\n" +
                    "###### Heading 6";
                
                // Convert the markdown to HTML
                var html = (string)convertMethod.Invoke(docGen, new object[] { markdown });
                
                // Output the results
                Console.WriteLine("Input Markdown:");
                Console.WriteLine(markdown);
                Console.WriteLine("\nOutput HTML:");
                Console.WriteLine(html);
                
                // Check if the 4-hash heading was converted correctly
                bool heading4Success = html.Contains("<h6>Heading 4</h6>");
                Console.WriteLine($"\nLevel 4 heading conversion: {(heading4Success ? "SUCCESS ✓" : "FAILED ✗")}");
                
                // Check if the 5-hash heading was converted correctly
                bool heading5Success = html.Contains("<h6>Heading 5</h6>");
                Console.WriteLine($"Level 5 heading conversion: {(heading5Success ? "SUCCESS ✓" : "FAILED ✗")}");
                
                // Check if the 6-hash heading was converted correctly
                bool heading6Success = html.Contains("<h6>Heading 6</h6>");
                Console.WriteLine($"Level 6 heading conversion: {(heading6Success ? "SUCCESS ✓" : "FAILED ✗")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
            }
        }
        
        private static void TestInlineCodeFix()
        {
            try
            {
                // Initialize the AIDescriptionGenerator
                var aiGen = new AIDescriptionGenerator();
                
                // Get the private ProcessAIResponse method using reflection
                var aiGenType = typeof(AIDescriptionGenerator);
                var processMethod = aiGenType.GetMethod("ProcessAIResponse", 
                    BindingFlags.NonPublic | BindingFlags.Instance);
                
                if (processMethod == null)
                {
                    Console.WriteLine("ERROR: Could not find ProcessAIResponse method");
                    return;
                }
                
                // Test data with various inline code placeholders
                string aiResponse = "Example with simple placeholder: INLINE_CODE_0\n" +
                    "Example with formatted placeholder: INLINE<em>CODE</em>1\n" +
                    "Example with spaces: INLINE CODE 2";
                
                // Process the AI response
                var processedResponse = (string)processMethod.Invoke(aiGen, new object[] { aiResponse });
                
                // Output the results
                Console.WriteLine("Input Response:");
                Console.WriteLine(aiResponse);
                Console.WriteLine("\nProcessed Response:");
                Console.WriteLine(processedResponse);
                
                // Check if the simple placeholder was fixed
                bool simpleSuccess = !processedResponse.Contains("INLINE_CODE_0") && 
                    processedResponse.Contains("`code_placeholder_0`");
                Console.WriteLine($"\nSimple placeholder conversion: {(simpleSuccess ? "SUCCESS ✓" : "FAILED ✗")}");
                
                // Check if the formatted placeholder was fixed
                bool formattedSuccess = !processedResponse.Contains("INLINE<em>CODE</em>1") && 
                    processedResponse.Contains("`code_placeholder_1`");
                Console.WriteLine($"Formatted placeholder conversion: {(formattedSuccess ? "SUCCESS ✓" : "FAILED ✗")}");
                
                // Check if the spaced placeholder was fixed
                bool spacedSuccess = !processedResponse.Contains("INLINE CODE 2") && 
                    processedResponse.Contains("`code_placeholder_2`");
                Console.WriteLine($"Spaced placeholder conversion: {(spacedSuccess ? "SUCCESS ✓" : "FAILED ✗")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
            }
        }
        
        private static void TestMappingTableFix()
        {
            try
            {
                Console.WriteLine("Testing Duplicate Mapping Table Fix");
                Console.WriteLine("===================================");
                
                // Test 1: hasMappings = true (should NOT generate mapping tables)
                Console.WriteLine("\nTest 1: hasMappings = true (should suppress mapping tables)");
                var snap1 = CreateMockMapperSnap();
                var allSnaps1 = new List<SnapNode> { snap1 };
                string result1 = SnapBestPractices.GetSnapBestPractices(snap1, allSnaps1, hasMappings: true);
                
                bool hasFieldMappingsTable1 = result1.Contains("Field mappings defined in this snap:");
                bool hasExpressionCode1 = result1.Contains("SnapLogic Expression Language for advanced transformations:");
                
                Console.WriteLine($"  Contains field mappings table: {hasFieldMappingsTable1}");
                Console.WriteLine($"  Contains expression code: {hasExpressionCode1}");
                
                if (!hasFieldMappingsTable1 && !hasExpressionCode1)
                {
                    Console.WriteLine("  ✓ PASS: No mapping tables generated when hasMappings = true");
                }
                else
                {
                    Console.WriteLine("  ✗ FAIL: Mapping tables were generated when hasMappings = true");
                }
                
                // Test 2: hasMappings = false (should generate mapping tables)
                Console.WriteLine("\nTest 2: hasMappings = false (should generate mapping tables)");
                var snap2 = CreateMockMapperSnap();
                var allSnaps2 = new List<SnapNode> { snap2 };
                string result2 = SnapBestPractices.GetSnapBestPractices(snap2, allSnaps2, hasMappings: false);
                
                bool hasFieldMappingsTable2 = result2.Contains("Field mappings defined in this snap:");
                bool hasExpressionCode2 = result2.Contains("SnapLogic Expression Language for advanced transformations:");
                
                Console.WriteLine($"  Contains field mappings table: {hasFieldMappingsTable2}");
                Console.WriteLine($"  Contains expression code: {hasExpressionCode2}");
                
                if (hasFieldMappingsTable2 || hasExpressionCode2)
                {
                    Console.WriteLine("  ✓ PASS: Mapping tables generated when hasMappings = false");
                }
                else
                {
                    Console.WriteLine("  ✗ FAIL: No mapping tables were generated when hasMappings = false");
                }
                
                // Summary
                bool allTestsPassed = (!hasFieldMappingsTable1 && !hasExpressionCode1) && 
                                    (hasFieldMappingsTable2 || hasExpressionCode2);
                                    
                Console.WriteLine($"\nOverall Result: {(allTestsPassed ? "✓ ALL TESTS PASSED" : "✗ SOME TESTS FAILED")}");
                
                if (allTestsPassed)
                {
                    Console.WriteLine("The duplicate mapping table fix is working correctly!");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
            }
        }
        
        private static SnapNode CreateMockMapperSnap()
        {
            var snap = new SnapNode
            {
                Name = "Test Mapper",
                Type = "com.snaplogic.snaps.transform.datatransform.DataMapper",
                Properties = new List<KeyValuePair<string, string>>
                {
                    new KeyValuePair<string, string>("execution_mode", "Validate & Execute"),
                    new KeyValuePair<string, string>("customer.name.expression", "$customer_data.name"),
                    new KeyValuePair<string, string>("customer.email.expression", "$customer_data.email"),
                    new KeyValuePair<string, string>("order.id.targetPath", "order_id"),
                    new KeyValuePair<string, string>("order.total.expression", "$order_data.amount * 1.1"),
                    new KeyValuePair<string, string>("mapping.simple", "direct_mapping_value")
                }
            };
            
            return snap;
        }
    }
}
