<Project Sdk="Microsoft.NET.Sdk">  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows7.0</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
  </PropertyGroup>
  <ItemGroup>    <!-- Explicitly include main source files -->    <Compile Include="AddCategoryDescription.cs" />
    <Compile Include="AIDescriptionGenerator.cs" />
    <Compile Include="AzureConfigForm.cs" />
    <Compile Include="AzureOpenAITester.cs" />
    <Compile Include="BatchDocumentationForm.cs" />
    <Compile Include="ConfigManager.cs" />
    <Compile Include="DebugConnections.cs" />
    <Compile Include="DescriptionCache.cs" />    <Compile Include="DiagramGenerator.cs" />    
    <Compile Include="DocumentationGenerator.cs" />    
    <Compile Include="DocumentationGenerator.Helpers.cs" />
    <Compile Include="FlowchartJsGenerator.cs" />
    <Compile Include="CytoscapeJsGenerator.cs" />    <Compile Include="FlowControlConfigurationGenerator.cs" />
    <Compile Include="FlowControlDiagramGenerator.cs" />
    <Compile Include="MainForm.cs" />
    <Compile Include="PathAnalysis.cs" />
    <Compile Include="PipelinePatternAnalyzer.cs" />    <Compile Include="Program.cs" />
    <Compile Include="ProjectData.cs" />
    <Compile Include="ProjectForm.cs" />
    <Compile Include="SlpAnalyzer.cs" />
    <Compile Include="SnapBestPractices.cs" />
    <Compile Include="SnapLogicClient.cs" />
    <Compile Include="TestFlowchartJs.cs" />
    <Compile Include="TestCytoscapeJs.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    
    <!-- Exclude test files -->
    <None Include="InlineCodeTest.cs" />
    <None Include="TestFormatFixes.cs" />
    <None Include="TestFixesConsole.cs" />
    <None Include="Program.TestMarkdownConversion.cs" />
    <None Include="TestMarkdownConversion.cs" />
    <None Include="TestInlineCode.cs" />
    <None Include="TestInlineCodeFixConsole.cs" />
    <None Include="TestEnhancements.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Select.HtmlToPdf.NetCore" Version="22.2.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="SnapLogic.Documentation.Shared\SnapLogic.Documentation.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Resources\" />
  </ItemGroup>

</Project>
