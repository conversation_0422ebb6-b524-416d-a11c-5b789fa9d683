using System.Text;

namespace SnapLogic.Documentation.Shared
{
    public class DiagramGenerator
    {
        public string GenerateDiagram(PipelineData pipeline)
        {
            // Generate a simple SVG diagram for now
            // In a full implementation, this would use cytoscape.js or similar
            var svg = new StringBuilder();
            
            svg.AppendLine("<svg width=\"800\" height=\"600\" xmlns=\"http://www.w3.org/2000/svg\">");
            svg.AppendLine("<style>");
            svg.AppendLine(".snap-node { fill: #e1f5fe; stroke: #0277bd; stroke-width: 2; }");
            svg.AppendLine(".snap-text { font-family: Arial; font-size: 12px; text-anchor: middle; }");
            svg.AppendLine(".snap-link { stroke: #666; stroke-width: 2; marker-end: url(#arrowhead); }");
            svg.AppendLine("</style>");
            
            // Define arrowhead marker
            svg.AppendLine("<defs>");
            svg.AppendLine("<marker id=\"arrowhead\" markerWidth=\"10\" markerHeight=\"7\" refX=\"9\" refY=\"3.5\" orient=\"auto\">");
            svg.AppendLine("<polygon points=\"0 0, 10 3.5, 0 7\" fill=\"#666\" />");
            svg.AppendLine("</marker>");
            svg.AppendLine("</defs>");
            
            // Calculate positions for nodes
            int nodeWidth = 120;
            int nodeHeight = 60;
            int spacing = 150;
            int startX = 50;
            int startY = 50;
            
            // Draw nodes
            for (int i = 0; i < pipeline.Snaps.Count; i++)
            {
                var snap = pipeline.Snaps[i];
                int x = startX + (i % 5) * spacing;
                int y = startY + (i / 5) * spacing;
                
                // Draw node rectangle
                svg.AppendLine($"<rect x=\"{x}\" y=\"{y}\" width=\"{nodeWidth}\" height=\"{nodeHeight}\" class=\"snap-node\" />");
                
                // Draw node label
                svg.AppendLine($"<text x=\"{x + nodeWidth/2}\" y=\"{y + nodeHeight/2}\" class=\"snap-text\">");
                svg.AppendLine($"<tspan x=\"{x + nodeWidth/2}\" dy=\"0\">{EscapeXml(snap.Label)}</tspan>");
                svg.AppendLine($"<tspan x=\"{x + nodeWidth/2}\" dy=\"15\" font-size=\"10\" fill=\"#666\">{EscapeXml(snap.Type)}</tspan>");
                svg.AppendLine("</text>");
            }
            
            // Draw links (simplified - just connecting sequential nodes for now)
            for (int i = 0; i < pipeline.Snaps.Count - 1; i++)
            {
                int x1 = startX + (i % 5) * spacing + nodeWidth;
                int y1 = startY + (i / 5) * spacing + nodeHeight/2;
                int x2 = startX + ((i+1) % 5) * spacing;
                int y2 = startY + ((i+1) / 5) * spacing + nodeHeight/2;
                
                svg.AppendLine($"<line x1=\"{x1}\" y1=\"{y1}\" x2=\"{x2}\" y2=\"{y2}\" class=\"snap-link\" />");
            }
            
            svg.AppendLine("</svg>");
            
            return svg.ToString();
        }
        
        private string EscapeXml(string text)
        {
            if (string.IsNullOrEmpty(text))
                return "";
                
            return text.Replace("&", "&amp;")
                      .Replace("<", "&lt;")
                      .Replace(">", "&gt;")
                      .Replace("\"", "&quot;")
                      .Replace("'", "&apos;");
        }
    }
}
