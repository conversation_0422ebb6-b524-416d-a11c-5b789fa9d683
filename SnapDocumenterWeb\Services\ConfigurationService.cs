using System.Text.Json;
using SnapLogic.Documentation.Shared;

namespace SnapDocumenterWeb.Services
{
    public class ConfigurationService
    {
        private readonly ILogger<ConfigurationService> _logger;
        private readonly IConfiguration _configuration;
        private AppSettings _settings;

        public ConfigurationService(ILogger<ConfigurationService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _settings = LoadSettings();
        }

        public AppSettings Settings => _settings;

        private AppSettings LoadSettings()
        {
            try
            {
                var settings = new AppSettings();
                _configuration.GetSection("AppSettings").Bind(settings);
                
                // Set defaults if not configured
                if (string.IsNullOrEmpty(settings.DefaultOutputFormat))
                    settings.DefaultOutputFormat = "HTML";
                
                if (settings.MaxFileSize <= 0)
                    settings.MaxFileSize = 10 * 1024 * 1024; // 10MB
                
                if (settings.MaxFilesPerBatch <= 0)
                    settings.MaxFilesPerBatch = 10;
                
                if (settings.ProcessingTimeoutMinutes <= 0)
                    settings.ProcessingTimeoutMinutes = 30;

                return settings;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading configuration, using defaults");
                return GetDefaultSettings();
            }
        }

        private AppSettings GetDefaultSettings()
        {
            return new AppSettings
            {
                DefaultOutputFormat = "HTML",
                EnableAI = false,
                UseCachedDescriptions = true,
                MaxFileSize = 10 * 1024 * 1024, // 10MB
                MaxFilesPerBatch = 10,
                ProcessingTimeoutMinutes = 30,
                EnableLogging = true,
                LogLevel = "Information"
            };
        }

        public void UpdateSettings(AppSettings newSettings)
        {
            _settings = newSettings;
            _logger.LogInformation("Application settings updated");
        }

        public string ExportSettings()
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                return JsonSerializer.Serialize(_settings, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting settings");
                throw;
            }
        }

        public bool ImportSettings(string json)
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                var importedSettings = JsonSerializer.Deserialize<AppSettings>(json, options);
                if (importedSettings != null)
                {
                    _settings = importedSettings;
                    _logger.LogInformation("Settings imported successfully");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing settings");
                return false;
            }
        }

        public ProcessingOptions GetDefaultProcessingOptions()
        {
            return new ProcessingOptions
            {
                GenerateHtml = _settings.DefaultOutputFormat.Contains("HTML", StringComparison.OrdinalIgnoreCase),
                GeneratePdf = _settings.DefaultOutputFormat.Contains("PDF", StringComparison.OrdinalIgnoreCase),
                UseAI = _settings.EnableAI,
                UseCachedDescriptions = _settings.UseCachedDescriptions
            };
        }

        public bool ValidateFileSize(long fileSize)
        {
            return fileSize <= _settings.MaxFileSize;
        }

        public bool ValidateBatchSize(int fileCount)
        {
            return fileCount <= _settings.MaxFilesPerBatch;
        }

        public TimeSpan GetProcessingTimeout()
        {
            return TimeSpan.FromMinutes(_settings.ProcessingTimeoutMinutes);
        }
    }

    public class AppSettings
    {
        public string DefaultOutputFormat { get; set; } = "HTML";
        public bool EnableAI { get; set; } = false;
        public bool UseCachedDescriptions { get; set; } = true;
        public long MaxFileSize { get; set; } = 10 * 1024 * 1024; // 10MB
        public int MaxFilesPerBatch { get; set; } = 10;
        public int ProcessingTimeoutMinutes { get; set; } = 30;
        public bool EnableLogging { get; set; } = true;
        public string LogLevel { get; set; } = "Information";
        
        // AI Configuration
        public string? AzureOpenAIEndpoint { get; set; }
        public string? AzureOpenAIApiKey { get; set; }
        public string? AzureOpenAIDeploymentName { get; set; }
        
        // UI Preferences
        public string Theme { get; set; } = "light";
        public bool ShowAdvancedOptions { get; set; } = false;
        public bool AutoSaveProjects { get; set; } = true;
        
        // Output Preferences
        public bool IncludeDiagrams { get; set; } = true;
        public bool IncludeSnapDetails { get; set; } = true;
        public bool IncludeParameterDetails { get; set; } = true;
        public string DiagramFormat { get; set; } = "SVG";
    }
}
