# Debug script to print the raw JSON structure for database snaps

$filePath = "DocumentationGenerator.cs"

Write-Host "Adding raw JSON structure debugging..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Add JSON structure debugging after finding the raw JSON
$oldDebugLine = 'Console.WriteLine($"[DB-CONNECTION] Found raw JSON for snap: {snap.Label} (ID: {snapId})");'
$newDebugCode = @'
Console.WriteLine($"[DB-CONNECTION] Found raw JSON for snap: {snap.Label} (ID: {snapId})");
                                        
                                        // DEBUG: Print the raw JSON structure to understand the layout
                                        Console.WriteLine($"[DB-CONNECTION-DEBUG] Raw JSON Keys for {snap.Label}:");
                                        foreach (var prop in rawSnapJson.Properties())
                                        {
                                            Console.WriteLine($"[DB-CONNECTION-DEBUG]   Key: {prop.Name}");
                                            if (prop.Name == "property_map" && prop.Value is JObject propMap)
                                            {
                                                Console.WriteLine($"[DB-CONNECTION-DEBUG]     property_map keys:");
                                                foreach (var subProp in propMap.Properties())
                                                {
                                                    Console.WriteLine($"[DB-CONNECTION-DEBUG]       {subProp.Name}");
                                                    if (subProp.Name == "account" && subProp.Value is JObject accountObj)
                                                    {
                                                        Console.WriteLine($"[DB-CONNECTION-DEBUG]         account structure: {accountObj.ToString()}");
                                                    }
                                                }
                                            }
                                        }
'@

if ($content.Contains($oldDebugLine)) {
    $content = $content.Replace($oldDebugLine, $newDebugCode)
    Write-Host "Added JSON structure debugging" -ForegroundColor Yellow
} else {
    Write-Host "Debug line not found" -ForegroundColor Red
}

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Added raw JSON structure debugging!" -ForegroundColor Green
