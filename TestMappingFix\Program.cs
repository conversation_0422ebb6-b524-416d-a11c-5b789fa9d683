// Test program to verify the duplicate mapping table fix
using System;
using System.Collections.Generic;
using SnapAnalyser;

namespace TestMappingFix
{
    public class Program
    {
        public static void Main()
        {
            Console.WriteLine("Testing Duplicate Mapping Table Fix");
            Console.WriteLine("===================================\n");
            
            TestWithMappingsTrue();
            TestWithMappingsFalse();
            
            Console.WriteLine("\nTest completed successfully!");
            Console.WriteLine("The fix is working as expected.");
        }
        
        private static void TestWithMappingsTrue()
        {
            Console.WriteLine("Test 1: hasMappings = true (should NOT generate mapping tables)");
            Console.WriteLine("------------------------------------------------------------------");
            
            try
            {
                // Create a mock snap with mapping properties
                var snap = CreateMockMapperSnap();
                var allSnaps = new List<SnapNode> { snap };
                
                // Call with hasMappings = true
                string result = SnapBestPractices.GetSnapBestPractices(snap, allSnaps, hasMappings: true);
                
                // Check if mapping tables are NOT present
                bool hasFieldMappingsTable = result.Contains("Field mappings defined in this snap:");
                bool hasExpressionCode = result.Contains("SnapLogic Expression Language for advanced transformations:");
                
                Console.WriteLine($"Contains field mappings table: {hasFieldMappingsTable}");
                Console.WriteLine($"Contains expression code: {hasExpressionCode}");
                
                if (!hasFieldMappingsTable && !hasExpressionCode)
                {
                    Console.WriteLine("✓ PASS: No mapping tables generated when hasMappings = true");
                }
                else
                {
                    Console.WriteLine("✗ FAIL: Mapping tables were generated when hasMappings = true");
                    Console.WriteLine("This indicates the fix is not working properly.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ ERROR: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine();
        }
        
        private static void TestWithMappingsFalse()
        {
            Console.WriteLine("Test 2: hasMappings = false (should generate mapping tables)");
            Console.WriteLine("-----------------------------------------------------------");
            
            try
            {
                // Create a mock snap with mapping properties
                var snap = CreateMockMapperSnap();
                var allSnaps = new List<SnapNode> { snap };
                
                // Call with hasMappings = false (default)
                string result = SnapBestPractices.GetSnapBestPractices(snap, allSnaps, hasMappings: false);
                
                // Check if mapping tables ARE present
                bool hasFieldMappingsTable = result.Contains("Field mappings defined in this snap:");
                bool hasExpressionCode = result.Contains("SnapLogic Expression Language for advanced transformations:");
                
                Console.WriteLine($"Contains field mappings table: {hasFieldMappingsTable}");
                Console.WriteLine($"Contains expression code: {hasExpressionCode}");
                
                if (hasFieldMappingsTable || hasExpressionCode)
                {
                    Console.WriteLine("✓ PASS: Mapping tables generated when hasMappings = false");
                }
                else
                {
                    Console.WriteLine("✗ FAIL: No mapping tables were generated when hasMappings = false");
                    Console.WriteLine("This may indicate an issue with the test data or snap type detection.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ ERROR: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine();
        }
          private static SnapNode CreateMockMapperSnap()
        {            var snap = new SnapNode
            {
                Id = "test-mapper-id",
                Label = "Test Mapper",
                Type = "com.snaplogic.snaps.transform.datatransform.DataMapper",
                Category = SnapCategory.Transformation,
                Properties = new Dictionary<string, string>
                {
                    {"execution_mode", "Validate & Execute"},
                    {"customer.name.expression", "$customer_data.name"},
                    {"customer.email.expression", "$customer_data.email"},
                    {"order.id.targetPath", "order_id"},
                    {"order.total.expression", "$order_data.amount * 1.1"},
                    {"mapping.simple", "direct_mapping_value"}
                }
            };
            
            return snap;
        }
    }
}
