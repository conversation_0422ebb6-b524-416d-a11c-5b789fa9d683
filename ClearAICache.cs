using System;
using SnapAnalyser;

namespace ClearAICache
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== AI Cache Clearing Tool ===");
            Console.WriteLine($"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine();

            try
            {
                Console.WriteLine("Initializing DescriptionCache...");
                var cache = new DescriptionCache();
                
                Console.WriteLine("Clearing all AI cache entries...");
                cache.ClearAllEntries();
                
                Console.WriteLine();
                Console.WriteLine("✅ AI cache cleared successfully!");
                Console.WriteLine();
                Console.WriteLine("All cached descriptions and pseudocode have been removed.");
                Console.WriteLine("The next time you analyze pipelines, all descriptions will be regenerated fresh.");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR clearing cache: {ex.GetType().Name}: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.GetType().Name}: {ex.InnerException.Message}");
                }
            }
            
            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
