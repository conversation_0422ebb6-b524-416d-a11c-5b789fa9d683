# PowerShell script to add SQL statement display for execute snaps

$filePath = "DocumentationGenerator.cs"

Write-Host "Adding SQL statement display for execute snaps..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Define the SQL extraction method to add before the GetDatabaseConnectionInfo method
$sqlMethod = @'

        /// <summary>
        /// Extracts SQL statement from SQL execute snaps
        /// </summary>
        private string GetSqlStatement(SnapNode snap)
        {
            if (snap.Category != SnapCategory.Database)
                return null;

            // Only get SQL for execute snaps
            if (!snap.SnapType.Contains("execute"))
                return null;

            Console.WriteLine($"[SQL-EXTRACT] Checking SQL for snap: {snap.Label}");

            // Look for SQL statement in flattened properties
            var sqlProp = snap.Properties.FirstOrDefault(p => 
                p.Key.ToLower().Contains("sqlstatement") && 
                !string.IsNullOrEmpty(p.Value));

            if (sqlProp.Key != null)
            {
                Console.WriteLine($"[SQL-EXTRACT] Found SQL statement: {sqlProp.Value}");
                return sqlProp.Value;
            }

            Console.WriteLine($"[SQL-EXTRACT] No SQL statement found for snap: {snap.Label}");
            return null;
        }
'@

# Add the method before GetDatabaseConnectionInfo
$pattern = '(\s+/// <summary>\s+/// Extracts database connection information from snap properties\s+/// </summary>\s+private string GetDatabaseConnectionInfo\(SnapNode snap\))'
$replacement = $sqlMethod + "`r`n$1"
$content = $content -replace $pattern, $replacement

# Add SQL statement display after connection info display
$sqlDisplayCode = @'
                
                    // Add SQL statement for execute snaps
                    if (snap.SnapType.Contains("execute"))
                    {
                        string sqlStatement = GetSqlStatement(snap);
                        if (!string.IsNullOrEmpty(sqlStatement))
                        {
                            html.AppendLine($"          <p><strong>SQL Statement:</strong></p>");
                            html.AppendLine($"          <pre style='background-color: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; white-space: pre-wrap;'><code>{System.Net.WebUtility.HtmlEncode(sqlStatement)}</code></pre>");
                        }
                    }
'@

# Find where to insert SQL display (after the connection info display block)
$insertPattern = '(\s+else\s*\{\s*Console\.WriteLine\(\$"\[DB-CONNECTION\] No connection info found for: \{snap\.Label\}"\);\s*\}\s*\})'
$insertReplacement = $1 + $sqlDisplayCode

$content = $content -replace $insertPattern, $insertReplacement

# Write the updated file
$content | Set-Content $filePath -Encoding UTF8

Write-Host "SQL statement display added successfully!" -ForegroundColor Green
Write-Host "Execute snaps will now show their SQL statements in the documentation." -ForegroundColor Yellow
'@
<parameter name="EmptyFile">false
