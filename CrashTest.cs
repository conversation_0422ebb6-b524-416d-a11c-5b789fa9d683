using System;
using System.IO;

namespace SnapAnalyser
{
    class CrashTest
    {        static void Main(string[] args)
        {
            Console.WriteLine("Testing Cytoscape.js Implementation...");
            
            try
            {
                TestCytoscapeJs.RunCytoscapeTest();
                Console.WriteLine();
                Console.WriteLine("Original test follows:");
                Console.WriteLine("Testing AIDescriptionGenerator creation...");
                
                Console.WriteLine("Step 1: Loading configuration...");
                string configPath = "config.json";
                if (!File.Exists(configPath))
                {
                    configPath = Path.Combine("bin", "Debug", "net9.0-windows7.0", "config.json");
                }
                Console.WriteLine($"Config file exists: {File.Exists(configPath)}");
                
                Console.WriteLine("Step 2: Testing ConfigManager...");
                string apiKey = ConfigManager.OpenAIApiKey;
                string endpoint = ConfigManager.AzureOpenAIEndpoint;
                Console.WriteLine($"API Key loaded: {!string.IsNullOrEmpty(apiKey)}");
                Console.WriteLine($"Endpoint loaded: {!string.IsNullOrEmpty(endpoint)}");
                
                Console.WriteLine("Step 3: Creating AIDescriptionGenerator...");
                var aiGenerator = new AIDescriptionGenerator(null);
                Console.WriteLine("SUCCESS: AIDescriptionGenerator created!");
                
                Console.WriteLine("Step 4: Testing basic functionality...");
                Console.WriteLine($"AI Generator initialized: {aiGenerator != null}");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"CRASH at: {ex.Message}");
                Console.WriteLine($"Exception type: {ex.GetType().Name}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                    Console.WriteLine($"Inner stack trace: {ex.InnerException.StackTrace}");
                }
            }
            
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
