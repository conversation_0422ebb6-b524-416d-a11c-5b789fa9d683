using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json;

namespace Snap_Documenter
{
    class DebugSnapTypes
    {
        static void Main(string[] args)
        {
            Console.WriteLine("Debug: Analyzing snap types in test files...");
            
            string testDirectory = @"c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\TestRedundantSection";
            
            if (!Directory.Exists(testDirectory))
            {
                Console.WriteLine($"Test directory not found: {testDirectory}");
                return;
            }
            
            var jsonFiles = Directory.GetFiles(testDirectory, "*.json");
            Console.WriteLine($"Found {jsonFiles.Length} JSON files");
            
            foreach (var file in jsonFiles)
            {
                try
                {
                    var content = File.ReadAllText(file);
                    dynamic pipeline = JsonConvert.DeserializeObject(content);
                    
                    Console.WriteLine($"\nFile: {Path.GetFileName(file)}");
                    
                    if (pipeline?.snap_map != null)
                    {
                        foreach (var snapEntry in pipeline.snap_map)
                        {
                            var snap = snapEntry.Value;
                            if (snap?.class_id != null)
                            {
                                string snapType = snap.class_id.ToString();
                                string snapLabel = snap.label?.ToString() ?? "Unknown";
                                
                                Console.WriteLine($"  Snap: {snapLabel}");
                                Console.WriteLine($"    Type: '{snapType}'");
                                Console.WriteLine($"    Contains 'map': {snapType.ToLower().Contains("map")}");
                                Console.WriteLine($"    Contains 'datatransform': {snapType.ToLower().Contains("datatransform")}");
                                Console.WriteLine($"    Should show redundant section: {!snapType.ToLower().Contains("map") && !snapType.ToLower().Contains("datatransform")}");
                                Console.WriteLine();
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error processing {file}: {ex.Message}");
                }
            }
            
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
