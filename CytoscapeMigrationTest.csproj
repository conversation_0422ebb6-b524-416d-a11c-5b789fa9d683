<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Text.Json" Version="8.0.4" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CytoscapeMigrationTest.cs" />
    <Compile Include="CytoscapeJsGenerator.cs" />
    <Compile Include="DocumentationGenerator.cs" />
    <Compile Include="DiagramGenerator.cs" />
    <Compile Include="FlowControlDiagramGenerator.cs" />
    <Compile Include="SlpAnalyzer.cs" />
    <Compile Include="AIDescriptionGenerator.cs" />
    <Compile Include="DescriptionCache.cs" />
    <Compile Include="ConfigManager.cs" />
  </ItemGroup>

</Project>
