using System;

namespace SnapAnalyser
{
    class SimpleFlowTest
    {
        static void Main()
        {
            Console.WriteLine("Testing FlowControlDiagramGenerator instantiation...");
            
            try
            {
                var generator = new FlowControlDiagramGenerator();
                Console.WriteLine("✅ FlowControlDiagramGenerator created successfully!");
                
                var cytoGen = new CytoscapeJsGenerator();
                Console.WriteLine("✅ CytoscapeJsGenerator created successfully!");
                
                var diagGen = new DiagramGenerator();
                Console.WriteLine("✅ DiagramGenerator created successfully!");
                
                Console.WriteLine("🎉 All classes are working correctly!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
