using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SnapAnalyser
{
    public class PipelinePatternAnalyzer
    {
        public class PatternResult
        {
            public string PatternName { get; set; }
            public string Description { get; set; }
            public float ConfidenceScore { get; set; } // 0-1 scale
            public List<string> DetectedFeatures { get; set; } = new List<string>();
            public List<string> MissingFeatures { get; set; } = new List<string>();
        }
        
        public List<PatternResult> AnalyzePipeline(PipelineData pipeline)
        {
            var results = new List<PatternResult>();
            
            // Analyze for various patterns
            results.Add(AnalyzeEtlPattern(pipeline));
            results.Add(AnalyzeApiIntegrationPattern(pipeline));
            results.Add(AnalyzeDataSyncPattern(pipeline));
            results.Add(AnalyzeDataValidationPattern(pipeline));
            results.Add(AnalyzeBatchFilePattern(pipeline));
            
            // Sort by confidence score descending
            return results.OrderByDescending(r => r.ConfidenceScore).ToList();
        }
        
        private PatternResult AnalyzeEtlPattern(PipelineData pipeline)
        {
            var result = new PatternResult 
            {
                PatternName = "ETL (Extract, Transform, Load)",
                Description = "This pipeline extracts data from sources, applies transformations, and loads it into destination systems."
            };
            
            // Check for Extract component
            bool hasDataSource = pipeline.Snaps.Any(s => 
                (s.Category == SnapCategory.Database && s.Type.Contains("select")) ||
                (s.Category == SnapCategory.FileOperation && s.Type.Contains("read")) ||
                (s.Category == SnapCategory.ExternalSystem));
                
            if (hasDataSource)
                result.DetectedFeatures.Add("Data extraction from source systems");
            else
                result.MissingFeatures.Add("No clear data source extraction identified");
                
            // Check for Transform component  
            bool hasTransformation = pipeline.Snaps.Any(s => s.Category == SnapCategory.Transformation);
                
            if (hasTransformation)
                result.DetectedFeatures.Add("Data transformation logic");
            else
                result.MissingFeatures.Add("No data transformation operations identified");
                
            // Check for Load component
            bool hasDataTarget = pipeline.Snaps.Any(s => 
                (s.Category == SnapCategory.Database && (s.Type.Contains("insert") || s.Type.Contains("update"))) ||
                (s.Category == SnapCategory.FileOperation && s.Type.Contains("write")) ||
                (s.Category == SnapCategory.ExternalSystem && !s.Type.Contains("get") && !s.Type.Contains("read")));
                
            if (hasDataTarget)
                result.DetectedFeatures.Add("Data loading to destination systems");
            else
                result.MissingFeatures.Add("No data loading to destination systems identified");
            
            // Calculate confidence score
            int features = 0;
            if (hasDataSource) features++;
            if (hasTransformation) features++;
            if (hasDataTarget) features++;
            
            result.ConfidenceScore = features / 3.0f;
            
            return result;
        }
        
        private PatternResult AnalyzeApiIntegrationPattern(PipelineData pipeline)
        {
            var result = new PatternResult 
            {
                PatternName = "API Integration",
                Description = "This pipeline integrates with external APIs or exposes API endpoints."
            };
            
            // Check for API request/response components
            bool hasApiRequests = pipeline.Snaps.Any(s => 
                s.Category == SnapCategory.ExternalSystem && 
                (s.Type.Contains("rest") || s.Type.Contains("soap") || s.Type.Contains("http")));
                
            if (hasApiRequests)
                result.DetectedFeatures.Add("API request operations");
            else
                result.MissingFeatures.Add("No API requests identified");
                
            // Check for JSON/XML processing
            bool hasJsonXmlProcessing = pipeline.Snaps.Any(s => 
                s.Category == SnapCategory.Transformation && 
                (s.Type.Contains("json") || s.Type.Contains("xml")));
                
            if (hasJsonXmlProcessing)
                result.DetectedFeatures.Add("JSON/XML data processing");
                
            // Check for authentication handling
            bool hasAuthHandling = pipeline.Snaps.Any(s => 
                s.Type.ToLower().Contains("auth") || 
                s.Properties.Any(p => p.Key.ToLower().Contains("auth") || p.Key.ToLower().Contains("token")));
                
            if (hasAuthHandling)
                result.DetectedFeatures.Add("Authentication handling");
                
            // Calculate confidence score
            float score = 0;
            if (hasApiRequests) score += 0.6f;
            if (hasJsonXmlProcessing) score += 0.3f;
            if (hasAuthHandling) score += 0.1f;
            
            result.ConfidenceScore = Math.Min(score, 1.0f);
            
            return result;
        }
        
        private PatternResult AnalyzeDataSyncPattern(PipelineData pipeline)
        {
            var result = new PatternResult 
            {
                PatternName = "Data Synchronization",
                Description = "This pipeline maintains data consistency between multiple systems."
            };
            
            // Check for data source and target of same type
            bool hasSimilarSourceAndTarget = 
                (pipeline.Snaps.Any(s => s.Category == SnapCategory.Database && s.Type.Contains("select")) &&
                 pipeline.Snaps.Any(s => s.Category == SnapCategory.Database && (s.Type.Contains("insert") || s.Type.Contains("update")))) ||
                (pipeline.Snaps.Any(s => s.Category == SnapCategory.ExternalSystem && s.Type.Contains("get")) &&
                 pipeline.Snaps.Any(s => s.Category == SnapCategory.ExternalSystem && (s.Type.Contains("put") || s.Type.Contains("post"))));
                 
            if (hasSimilarSourceAndTarget)
                result.DetectedFeatures.Add("Data read and write operations on similar systems");
                
            // Check for timestamp or watermark filtering
            bool hasTimestampFiltering = pipeline.Snaps.Any(s =>
                s.Properties.Any(p => p.Value != null && 
                (p.Value.Contains("timestamp") || p.Value.Contains("last_update") || 
                 p.Value.Contains("modified") || p.Value.Contains("date") ||
                 p.Value.Contains(">=") || p.Value.Contains("between"))));
                 
            if (hasTimestampFiltering)
                result.DetectedFeatures.Add("Timestamp or change-based filtering");
            else
                result.MissingFeatures.Add("No incremental processing identified");
                
            // Check for existence checks / lookups
            bool hasLookups = pipeline.Snaps.Any(s => s.Category == SnapCategory.FlowControl && s.Type.Contains("join"));
            bool hasRouting = pipeline.Snaps.Any(s => s.Category == SnapCategory.FlowControl && s.Type.Contains("router"));
            
            if (hasLookups)
                result.DetectedFeatures.Add("Record matching/lookup operations");
                
            if (hasRouting)
                result.DetectedFeatures.Add("Conditional processing paths");
                
            // Calculate confidence score
            float score = 0;
            if (hasSimilarSourceAndTarget) score += 0.4f;
            if (hasTimestampFiltering) score += 0.3f;
            if (hasLookups) score += 0.2f;
            if (hasRouting) score += 0.1f;
            
            result.ConfidenceScore = Math.Min(score, 1.0f);
            
            return result;
        }
        
        private PatternResult AnalyzeDataValidationPattern(PipelineData pipeline)
        {
            var result = new PatternResult 
            {
                PatternName = "Data Validation & Cleansing",
                Description = "This pipeline implements data quality validation and cleansing operations."
            };
            
            // Check for validation components
            bool hasValidation = pipeline.Snaps.Any(s => 
                s.Type.ToLower().Contains("valid") || 
                s.Type.ToLower().Contains("schema") ||
                s.Type.Contains("script"));
                
            if (hasValidation)
                result.DetectedFeatures.Add("Validation operations");
            else
                result.MissingFeatures.Add("No explicit validation operations identified");
                
            // Check for routing based on validation
            bool hasRoutingAfterValidation = 
                hasValidation && 
                pipeline.Snaps.Any(s => s.Category == SnapCategory.FlowControl && s.Type.Contains("router"));
                
            if (hasRoutingAfterValidation)
                result.DetectedFeatures.Add("Routing based on validation results");
                
            // Check for error handling
            bool hasErrorHandling = pipeline.Snaps.Any(s => s.Category == SnapCategory.ErrorHandling);
            
            if (hasErrorHandling)
                result.DetectedFeatures.Add("Error handling mechanisms");
            else
                result.MissingFeatures.Add("No error handling for validation failures");
                
            // Calculate confidence score
            float score = 0;
            if (hasValidation) score += 0.5f;
            if (hasRoutingAfterValidation) score += 0.3f;
            if (hasErrorHandling) score += 0.2f;
            
            result.ConfidenceScore = Math.Min(score, 1.0f);
            
            return result;
        }
        
        private PatternResult AnalyzeBatchFilePattern(PipelineData pipeline)
        {
            var result = new PatternResult 
            {
                PatternName = "Batch File Processing",
                Description = "This pipeline processes files in batch mode."
            };
            
            // Check for file operations
            bool hasFileReading = pipeline.Snaps.Any(s => 
                s.Category == SnapCategory.FileOperation && s.Type.Contains("read"));
                
            if (hasFileReading)
                result.DetectedFeatures.Add("File reading operations");
            else
                result.MissingFeatures.Add("No file reading operations identified");
                
            // Check for CSV/JSON/XML parsing
            bool hasFileFormatParsing = pipeline.Snaps.Any(s => 
                s.Type.Contains("csv") || s.Type.Contains("json") || 
                s.Type.Contains("xml") || s.Type.Contains("excel"));
                
            if (hasFileFormatParsing)
                result.DetectedFeatures.Add("File format parsing");
                
            // Check for batch operations
            bool hasBatchProcessing = pipeline.Snaps.Any(s => 
                s.Properties.Any(p => p.Key.ToLower().Contains("batch") || 
                                    p.Value != null && p.Value.ToLower().Contains("batch")));
                
            if (hasBatchProcessing)
                result.DetectedFeatures.Add("Batch processing configuration");
                
            // Check for file writing operations
            bool hasFileWriting = pipeline.Snaps.Any(s => 
                s.Category == SnapCategory.FileOperation && s.Type.Contains("write"));
                
            if (hasFileWriting)
                result.DetectedFeatures.Add("File writing operations");
                
            // Calculate confidence score
            float score = 0;
            if (hasFileReading) score += 0.4f;
            if (hasFileFormatParsing) score += 0.3f;
            if (hasBatchProcessing) score += 0.2f;
            if (hasFileWriting) score += 0.1f;
            
            result.ConfidenceScore = Math.Min(score, 1.0f);
            
            return result;
        }
        
        public string GeneratePatternAnalysisHtml(PipelineData pipeline)
        {
            var results = AnalyzePipeline(pipeline);
            var significantPatterns = results.Where(r => r.ConfidenceScore >= 0.5f).ToList();
            
            var html = new StringBuilder();
            
            if (significantPatterns.Any())
            {
                html.AppendLine("<div class=\"pattern-analysis\">");
                html.AppendLine("  <h3>Pipeline Pattern Analysis</h3>");
                
                foreach (var pattern in significantPatterns)
                {
                    int confidencePercent = (int)(pattern.ConfidenceScore * 100);
                    
                    html.AppendLine("  <div class=\"detected-pattern\">");
                    html.AppendLine($"    <h4>{pattern.PatternName}</h4>");
                    html.AppendLine($"    <p>{pattern.Description}</p>");
                    html.AppendLine($"    <div class=\"confidence-meter\">");
                    html.AppendLine($"      <div class=\"confidence-label\">Confidence: {confidencePercent}%</div>");
                    html.AppendLine($"      <div class=\"confidence-bar\">");
                    html.AppendLine($"        <div class=\"confidence-fill\" style=\"width: {confidencePercent}%\"></div>");
                    html.AppendLine($"      </div>");
                    html.AppendLine($"    </div>");
                    
                    if (pattern.DetectedFeatures.Any())
                    {
                        html.AppendLine("    <div class=\"pattern-features\">");
                        html.AppendLine("      <p><strong>Detected Features:</strong></p>");
                        html.AppendLine("      <ul>");
                        foreach (var feature in pattern.DetectedFeatures)
                        {
                            html.AppendLine($"        <li>{feature}</li>");
                        }
                        html.AppendLine("      </ul>");
                        html.AppendLine("    </div>");
                    }
                    
                    if (pattern.MissingFeatures.Any())
                    {
                        html.AppendLine("    <div class=\"pattern-missing\">");
                        html.AppendLine("      <p><strong>Recommendations:</strong></p>");
                        html.AppendLine("      <ul>");
                        foreach (var missing in pattern.MissingFeatures)
                        {
                            html.AppendLine($"        <li>Consider adding: {missing}</li>");
                        }
                        html.AppendLine("      </ul>");
                        html.AppendLine("    </div>");
                    }
                    
                    html.AppendLine("  </div>");
                }
                
                html.AppendLine("</div>");
            }
            else
            {
                html.AppendLine("<div class=\"pattern-analysis\">");
                html.AppendLine("  <p>No significant integration patterns were detected in this pipeline.</p>");
                html.AppendLine("</div>");
            }
            
            return html.ToString();
        }
    }
}
