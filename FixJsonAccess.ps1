# Script to fix JSON access for snap_map iteration

$filePath = "DocumentationGenerator.cs"

Write-Host "Fixing JSON access for snap_map..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Fix the snap_map iteration - it's a JProperty collection, not JObject array
$oldJsonIteration = @'
var snapsArray = pipelineJson["snap_map"];
                            if (snapsArray != null)
                            {
                                foreach (var snapEntry in snapsArray)
                                {
                                    var snapId = snapEntry["class_id"]?.ToString();
                                    if (snap.Id == snapId)
                                    {
                                        rawSnapJson = (JObject)snapEntry;
                                        Console.WriteLine($"[DB-CONNECTION] Found raw JSON for snap: {snap.Label}");
                                        break;
                                    }
                                }
                            }
'@

$newJsonIteration = @'
var snapMap = pipelineJson["snap_map"] as JObject;
                            if (snapMap != null)
                            {
                                foreach (var snapEntry in snapMap)
                                {
                                    var snapId = snapEntry.Key;
                                    if (snap.Id == snapId)
                                    {
                                        rawSnapJson = (JObject)snapEntry.Value;
                                        Console.WriteLine($"[DB-CONNECTION] Found raw JSON for snap: {snap.Label} (ID: {snapId})");
                                        break;
                                    }
                                }
                            }
'@

if ($content.Contains($oldJsonIteration)) {
    $content = $content.Replace($oldJsonIteration, $newJsonIteration)
    Write-Host "Fixed JSON iteration - using KeyValuePair approach" -ForegroundColor Yellow
} else {
    Write-Host "JSON iteration pattern not found" -ForegroundColor Red
}

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Fixed JSON access!" -ForegroundColor Green
