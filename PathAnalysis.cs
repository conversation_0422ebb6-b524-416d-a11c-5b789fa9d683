using System.Text;

namespace SnapAnalyser
{
    public partial class DocumentationGenerator
    {        // Add advanced path analysis for execution paths
        private string ProvideAdvancedPathAnalysis(List<SnapNode> path)
        {
            if (path == null || path.Count < 2)
                return "Path is too short for meaningful analysis.";
                
            StringBuilder analysis = new StringBuilder();
            
            // Identify key characteristics of this path
            var snapsByCategory = path.GroupBy(n => n.Category)
                .ToDictionary(g => g.Key, g => g.Count());
                
            // Identify the pattern of operations
            analysis.AppendLine("<strong>Path Architecture Analysis:</strong>");
            
            // Pattern detection
            if (path.Any(n => n.Category == SnapCategory.Database && n.Type.Contains("select")) &&
                path.Any(n => n.Category == SnapCategory.ExternalSystem) &&
                path.Any(n => n.Category == SnapCategory.Transformation))
            {
                analysis.AppendLine("<br/>This path implements a <strong>System Integration Pattern</strong> that extracts data " +
                                   "from a database, transforms it, and sends it to an external system.");
            }
            else if (path.Any(n => n.Category == SnapCategory.ExternalSystem && path.IndexOf(path.First(n => n.Category == SnapCategory.ExternalSystem)) < path.Count / 2) &&
                     path.Any(n => n.Category == SnapCategory.Database && n.Type.Contains("insert") && path.IndexOf(path.First(n => n.Category == SnapCategory.Database)) > path.Count / 2))
            {
                analysis.AppendLine("<br/>This path implements an <strong>API-to-Database Integration Pattern</strong> that " +
                                   "consumes data from an external API and stores it in a database.");
            }
            else if (path.Any(n => n.Category == SnapCategory.FileOperation && n.Type.Contains("read")) &&
                     path.Any(n => n.Category == SnapCategory.Database && 
                              (n.Type.Contains("insert") || n.Type.Contains("update"))))
            {
                analysis.AppendLine("<br/>This path implements a <strong>File Import Pattern</strong> that reads " +
                                   "data from files and loads it into database tables.");
            }
            else if (snapsByCategory.ContainsKey(SnapCategory.Transformation) && 
                     snapsByCategory[SnapCategory.Transformation] >= Math.Max(1, path.Count / 3))
            {
                analysis.AppendLine("<br/>This path implements a <strong>Complex Transformation Pattern</strong> with " +
                                   "multiple transformation steps to reshape and enrich data.");
            }
            else if (path.Any(n => n.Category == SnapCategory.FlowControl && n.Type.Contains("router")))
            {
                analysis.AppendLine("<br/>This path implements a <strong>Conditional Processing Pattern</strong> that " +
                                   "routes documents through different processing branches based on content.");
            }
            
            // Data complexity analysis
            var transformationSnaps = path.Where(n => n.Category == SnapCategory.Transformation).ToList();
            int transformationComplexity = transformationSnaps.Count;
            
            if (transformationComplexity > 3)
            {
                analysis.AppendLine("<br/><strong>Data Transformation Complexity:</strong> High - This path contains multiple " +
                                   "transformation steps suggesting complex data mapping or business logic.");
            }
            else if (transformationComplexity > 0)
            {
                analysis.AppendLine("<br/><strong>Data Transformation Complexity:</strong> Medium - This path contains standard " +
                                   "data transformations for field mapping and formatting.");
            }
            else
            {
                analysis.AppendLine("<br/><strong>Data Transformation Complexity:</strong> Low - This path moves data with " +
                                   "minimal transformation, focusing on data movement rather than restructuring.");
            }
            
            // Performance considerations
            if (path.Any(n => n.Type.Contains("batch")))
            {
                analysis.AppendLine("<br/><strong>Performance Optimization:</strong> Uses batch processing for improved throughput.");
            }
            
            if (path.Any(n => n.Category == SnapCategory.FlowControl && n.Type.Contains("join")))
            {
                analysis.AppendLine("<br/><strong>Performance Consideration:</strong> Contains join operations that may require " +
                                   "memory for caching data and could be a performance bottleneck with large datasets.");
            }
            
            // Error handling assessment
            if (path.Any(n => n.Category == SnapCategory.ErrorHandling))
            {
                analysis.AppendLine("<br/><strong>Error Handling:</strong> This path includes explicit error handling mechanisms " +
                                   "for improved robustness and fault tolerance.");
            }
            else
            {
                analysis.AppendLine("<br/><strong>Error Handling:</strong> No explicit error handling detected in this path. " +
                                   "Consider adding error handling for improved robustness.");
            }
            
            return analysis.ToString();
        }
    }
}
