using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using SnapAnalyser;

class TestDeployments
{
    static async Task Main()
    {
        Console.WriteLine("=== Quick Azure OpenAI Deployment Test ===");
        Console.WriteLine($"Current deployment: {ConfigManager.AzureOpenAIDeploymentName}");
        Console.WriteLine();
        
        await TestDeployment("gpt-4");
        await TestDeployment("gpt-4-turbo");
        await TestDeployment("gpt-35-turbo");
        await TestDeployment("gpt-4o");
        
        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }
    
    static async Task TestDeployment(string deploymentName)
    {
        try
        {
            Console.WriteLine($"🔍 Testing deployment: {deploymentName}");
            
            string apiKey = ConfigManager.OpenAIApiKey;
            string endpoint = ConfigManager.AzureOpenAIEndpoint;
            
            var testUrl = $"{endpoint.TrimEnd('/')}/openai/deployments/{deploymentName}/chat/completions?api-version=2023-05-15";
            
            using var client = new HttpClient();
            client.DefaultRequestHeaders.Add("api-key", apiKey);
            client.Timeout = TimeSpan.FromSeconds(10);
            
            var content = new StringContent(
                JsonSerializer.Serialize(new
                {
                    messages = new[]
                    {
                        new { role = "user", content = "Hello" }
                    },
                    max_tokens = 5
                }),
                Encoding.UTF8,
                "application/json"
            );
            
            var response = await client.PostAsync(testUrl, content);
            
            if (response.IsSuccessStatusCode)
            {
                Console.WriteLine($"✅ SUCCESS: {deploymentName} is available!");
                var responseBody = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"   Response: {response.StatusCode}");
            }
            else
            {
                Console.WriteLine($"❌ FAILED: {deploymentName} - {response.StatusCode}");
                var error = await response.Content.ReadAsStringAsync();
                if (error.Contains("DeploymentNotFound") || error.Contains("404"))
                {
                    Console.WriteLine($"   Deployment '{deploymentName}' doesn't exist");
                }
                else
                {
                    Console.WriteLine($"   Error: {error.Substring(0, Math.Min(100, error.Length))}...");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ ERROR testing {deploymentName}: {ex.Message}");
        }
        
        Console.WriteLine();
    }
}
