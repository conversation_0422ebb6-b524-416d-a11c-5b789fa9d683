using System;
using System.Collections.Generic;
using SnapAnalyser;

class BasicConditionTest
{
    static void Main()
    {
        Console.WriteLine("====== CONDITION SNAP EXTRACTION TEST ======");

        // Create test condition snap with indexed expression properties
        var snap = new SnapNode
        {
            Id = "condition-1",
            Label = "BlockTypes Condition",
            Type = "com-snaplogic-snaps-transform-conditional",
            Properties = new Dictionary<string, string>
            {
                { "expression_0", "$blocktypes.blockId == null || $blocktypes.blockId == ''" },
                { "expression_1", "$blocktypes.parentBlock == null || $blocktypes.parentBlock == ''" },
                { "expression_2", "$blocktypes.blocktype == 'District'" },
                { "output_0", "Remove District" },
                { "output_1", "Collapse Parent" },
                { "output_2", "Keep District" },
                { "settings.evaluateAll.value", "False" },
                { "settings.execution_mode.value", "Validate & Execute" },
                { "settings.nullSafeAccess.value", "False" }
            }
        };

        // Extract condition expressions
        var conditions = SnapBestPractices.ExtractConditions(snap);
        
        // Display results
        Console.WriteLine($"Found {conditions.Count} condition expressions:");
        foreach (var condition in conditions)
        {
            Console.WriteLine($"- {condition.Description}: {condition.Expression}");
        }
        
        Console.WriteLine("\n====== TEST COMPLETE ======");
    }
}
