using System;
using System.IO;
using SnapAnalyser;

public class VerifyAzureAI
{
    public static void Main()
    {
        try
        {
            string logPath = Path.Combine(Environment.CurrentDirectory, "azure_ai_verification.log");
            
            // Get current timestamp
            string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            
            // Load configuration
            string apiKey = ConfigManager.OpenAIApiKey;
            string endpoint = ConfigManager.AzureOpenAIEndpoint;
            string deployment = ConfigManager.AzureOpenAIDeploymentName;
            
            // Check if Azure AI will be enabled
            bool useAzureAI = !string.IsNullOrEmpty(apiKey) && !string.IsNullOrEmpty(endpoint);
            
            // Write verification results to log file
            string logContent = $@"Azure AI Configuration Verification
Timestamp: {timestamp}

Configuration Values:
- API Key: {(string.IsNullOrEmpty(apiKey) ? "MISSING" : $"Present (length: {apiKey.Length})")}
- Endpoint: {endpoint ?? "MISSING"}
- Deployment: {deployment ?? "MISSING"}

Azure AI Status: {(useAzureAI ? "ENABLED" : "DISABLED")}

{(useAzureAI ? "✅ SUCCESS: Azure AI will be enabled!" : "❌ FAILED: Azure AI is disabled")}

Verification completed at {timestamp}
";

            File.WriteAllText(logPath, logContent);
            
            // Also try to write to the Generated Documentation folder
            string docPath = @"c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Generated Documentation\azure_verification.log";
            try
            {
                File.WriteAllText(docPath, logContent);
            }
            catch
            {
                // Ignore if can't write to that location
            }
        }
        catch (Exception ex)
        {
            // Write error to a simple log
            File.WriteAllText("verification_error.log", $"Error: {ex.Message}\nStack: {ex.StackTrace}");
        }
    }
}
