using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace SnapAnalyser
{
    /// <summary>
    /// Test program to verify the enhanced flow control documentation functionality
    /// </summary>
    class TestEnhancedFlowControl
    {
        static void Main(string[] args)
        {
            try
            {
                Console.WriteLine("Testing Enhanced Flow Control Documentation...");
                
                // Test with the sample pipeline file if it exists
                string testFile = "test_ch03.slp";
                if (!File.Exists(testFile))
                {
                    Console.WriteLine($"Test file {testFile} not found. Please provide a pipeline file with Router/Join/Union snaps.");
                    return;
                }

                Console.WriteLine($"Loading pipeline from {testFile}...");
                var analyzer = new SlpAnalyzer();
                var pipeline = analyzer.AnalyzePipeline(testFile);

                Console.WriteLine($"Pipeline loaded: {pipeline.Name}");
                Console.WriteLine($"Total snaps: {pipeline.Snaps.Count}");

                // Check for flow control snaps
                var flowControlSnaps = pipeline.Snaps.Where(s => 
                    s.Type.Equals("Router", StringComparison.OrdinalIgnoreCase) ||
                    s.Type.Equals("Join", StringComparison.OrdinalIgnoreCase) ||
                    s.Type.Equals("Union", StringComparison.OrdinalIgnoreCase))
                    .ToList();

                Console.WriteLine($"Flow control snaps found: {flowControlSnaps.Count}");
                
                foreach (var snap in flowControlSnaps)
                {
                    Console.WriteLine($"  - {snap.Type}: {snap.Label}");
                }

                if (flowControlSnaps.Any())
                {
                    Console.WriteLine("\nTesting FlowControlConfigurationGenerator...");
                    var configGenerator = new FlowControlConfigurationGenerator();
                    
                    foreach (var snap in flowControlSnaps)
                    {
                        try
                        {
                            string config = "";
                            if (snap.Type.Equals("Router", StringComparison.OrdinalIgnoreCase))
                            {
                                config = configGenerator.GenerateRouterConfiguration(snap, pipeline.Snaps);
                            }
                            else if (snap.Type.Equals("Join", StringComparison.OrdinalIgnoreCase))
                            {
                                config = configGenerator.GenerateJoinConfiguration(snap, pipeline.Snaps);
                            }
                            else if (snap.Type.Equals("Union", StringComparison.OrdinalIgnoreCase))
                            {
                                config = configGenerator.GenerateUnionConfiguration(snap, pipeline.Snaps);
                            }
                            
                            Console.WriteLine($"✓ Configuration generated for {snap.Type}: {snap.Label}");
                            Console.WriteLine($"  Config length: {config.Length} characters");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"✗ Error generating config for {snap.Type}: {snap.Label} - {ex.Message}");
                        }
                    }

                    Console.WriteLine("\nTesting FlowControlDiagramGenerator...");
                    var diagramGenerator = new FlowControlDiagramGenerator();
                    
                    foreach (var snap in flowControlSnaps)
                    {
                        try
                        {
                            string diagram = "";
                            if (snap.Type.Equals("Router", StringComparison.OrdinalIgnoreCase))
                            {
                                diagram = diagramGenerator.GenerateRouterFlowDiagram(snap, pipeline.Snaps);
                            }
                            else if (snap.Type.Equals("Join", StringComparison.OrdinalIgnoreCase))
                            {
                                diagram = diagramGenerator.GenerateJoinFlowDiagram(snap, pipeline.Snaps);
                            }
                            else if (snap.Type.Equals("Union", StringComparison.OrdinalIgnoreCase))
                            {
                                diagram = diagramGenerator.GenerateUnionFlowDiagram(snap, pipeline.Snaps);
                            }
                            
                            Console.WriteLine($"✓ Diagram generated for {snap.Type}: {snap.Label}");
                            Console.WriteLine($"  Diagram length: {diagram.Length} characters");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"✗ Error generating diagram for {snap.Type}: {snap.Label} - {ex.Message}");
                        }
                    }

                    Console.WriteLine("\nTesting full DocumentationGenerator integration...");
                    var docGenerator = new DocumentationGenerator();
                    string documentation = docGenerator.GenerateDocumentation(pipeline, new System.Threading.CancellationToken());
                    
                    // Check if enhanced flow control section was added
                    if (documentation.Contains("Enhanced Flow Control Diagrams"))
                    {
                        Console.WriteLine("✓ Enhanced Flow Control Diagrams section found in documentation");
                    }
                    else
                    {
                        Console.WriteLine("✗ Enhanced Flow Control Diagrams section NOT found in documentation");
                    }

                    // Save test output
                    string outputFile = "test_enhanced_documentation.html";
                    File.WriteAllText(outputFile, documentation);
                    Console.WriteLine($"✓ Test documentation saved to {outputFile}");
                }
                else
                {
                    Console.WriteLine("No Router, Join, or Union snaps found in the pipeline for testing.");
                }

                Console.WriteLine("\nTest completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Test failed with error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
