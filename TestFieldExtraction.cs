using System;
using System.Text.RegularExpressions;
using System.Linq;

class TestFieldExtraction
{
    static void Main(string[] args)
    {
        Console.WriteLine("Testing Enhanced ExtractTargetFieldName Method");
        Console.WriteLine("===========================================");
        
        string[] testCases = {
            "settings.transformations.mappingTable.0.targetPath.value",
            "settings.transformations.value.mappingTable.1.expression.CustomerName",
            "targetPaths[0].fieldName",
            "mappings.customer.address.expression",
            "output.target.firstName",
            "source.lastName.value",
            "transformations.mappingTable.2.targetPath.email",
            "expression.phoneNumber",
            "targetPath.value.orderDate",
            "someComplexPath.settings.transformations.mappingTable.3.targetPath.value.amount"
        };
        
        foreach (string testCase in testCases)
        {
            string result = ExtractTargetFieldName(testCase);
            Console.WriteLine($"Input:  {testCase}");
            Console.WriteLine($"Output: {result}");
            Console.WriteLine();
        }
        
        Console.WriteLine("Press any key to continue...");
        Console.ReadKey();
    }
    
    private static string ExtractTargetFieldName(string propertyKey)
    {
        // Try to extract a meaningful field name from property keys like "targetPaths[0].fieldName"
        // or "mappings.customer.address.expression" or "settings.transformations.mappingTable.0.targetPath.value"
        
        if (string.IsNullOrWhiteSpace(propertyKey))
            return propertyKey;
            
        // First, handle common complex path patterns
        string cleaned = propertyKey;
        
        // Handle common nested path patterns
        string[] commonPrefixPatterns = {
            "settings.transformations.mappingTable.",
            "settings.transformations.value.mappingTable.",
            "settings.mappingTable.",
            "transformations.value.mappingTable.",
            "transformations.mappingTable."
        };
        
        foreach (var pattern in commonPrefixPatterns)
        {
            if (cleaned.Contains(pattern))
            {
                cleaned = cleaned.Substring(cleaned.IndexOf(pattern) + pattern.Length);
                break;
            }
        }
        
        // Remove array indices anywhere in the path
        cleaned = Regex.Replace(cleaned, @"\[\d+\]\.?", ".");
        
        // Remove common prefixes
        string[] prefixesToRemove = { 
            "targetPath", "target_path", "sourcePath", "source_path", 
            "mapping", "expression", "output", "target", "source" 
        };
        
        foreach (var prefix in prefixesToRemove)
        {
            if (cleaned.ToLower().StartsWith(prefix.ToLower()))
            {
                cleaned = cleaned.Substring(prefix.Length);
                if (cleaned.StartsWith(".") || cleaned.StartsWith("_"))
                    cleaned = cleaned.Substring(1);
            }
        }
        
        // Handle common suffixes
        string[] suffixesToRemove = { ".expression", ".value", ".mapping", ".targetPath", ".sourcePath" };
        foreach (var suffix in suffixesToRemove)
        {
            if (cleaned.ToLower().EndsWith(suffix.ToLower()))
            {
                cleaned = cleaned.Substring(0, cleaned.Length - suffix.Length);
            }
        }
        
        // If we're left with a path (contains dots), take the last part as the field name
        if (cleaned.Contains("."))
        {
            string[] parts = cleaned.Split('.');
            cleaned = parts.LastOrDefault(p => !string.IsNullOrWhiteSpace(p)) ?? cleaned;
        }
        
        // Clean up any remaining dots at start/end
        cleaned = cleaned.Trim('.');
        
        return string.IsNullOrWhiteSpace(cleaned) ? propertyKey : cleaned;
    }
}
