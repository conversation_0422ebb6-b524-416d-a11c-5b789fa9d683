// Add these methods to DocumentationGenerator.cs
        
        private string GetFriendlySnapType(string rawType)
        {
            if (string.IsNullOrEmpty(rawType))
                return "Unknown";
                
            // Remove common prefixes like "snaplogic." or vendor names
            string friendlyType = rawType;
            string[] prefixesToRemove = { "snaplogic.", "dynamics.", "salesforce.", "oracle.", "mssql." };
            
            foreach (var prefix in prefixesToRemove)
            {
                if (friendlyType.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
                {
                    friendlyType = friendlyType.Substring(prefix.Length);
                    break;
                }
            }
            
            // Capitalize and add spaces for better readability
            friendlyType = Regex.Replace(friendlyType, "([a-z])([A-Z])", "$1 $2");
            
            // Title case the result
            TextInfo textInfo = new CultureInfo("en-US", false).TextInfo;
            return textInfo.ToTitleCase(friendlyType.ToLower());
        }
        
        private string GetSnapDescription(SnapNode snap)
        {
            // Check if there's a description property directly in the snap
            if (snap.Properties.TryGetValue("description", out string descriptionValue) && !string.IsNullOrEmpty(descriptionValue))
            {
                return HttpUtility.HtmlEncode(descriptionValue);
            }
            
            // Try to generate a description based on the snap type and category
            switch (snap.Category)
            {
                case SnapCategory.Transformation:
                    if (snap.Type.Contains("map") || snap.Type.Contains("datatransform"))
                        return "Transforms document field values or structure based on mapping rules.";
                    else if (snap.Type.Contains("sort"))
                        return "Orders documents based on specified field values.";
                    else if (snap.Type.Contains("groupby"))
                        return "Groups documents based on shared field values and applies aggregations.";
                    else if (snap.Type.Contains("json"))
                        return snap.Type.Contains("parser") ? 
                            "Parses JSON strings into structured document objects." : 
                            "Converts structured objects into JSON string representation.";
                    else if (snap.Type.Contains("xml"))
                        return snap.Type.Contains("parser") ? 
                            "Parses XML strings into structured document objects." : 
                            "Converts structured objects into XML string representation.";
                    else if (snap.Type.Contains("script"))
                        return "Executes custom code (typically JavaScript) to transform data.";
                    else
                        return "Applies transformations to the document content or structure.";
                    
                case SnapCategory.FlowControl:
                    if (snap.Type.Contains("router"))
                        return "Directs documents to different paths based on conditions.";
                    else if (snap.Type.Contains("filter"))
                        return "Passes through only documents that match specified criteria.";
                    else if (snap.Type.Contains("join"))
                        return "Combines related documents from multiple inputs based on matching key fields.";
                    else if (snap.Type.Contains("union"))
                        return "Merges document streams from multiple inputs into a single output stream.";
                    else if (snap.Type.Contains("copy"))
                        return "Creates multiple copies of each input document.";
                    else
                        return "Controls the flow and routing of documents through the pipeline.";
                    
                case SnapCategory.Database:
                    if (snap.Type.Contains("select"))
                        return "Retrieves data from a database table using SQL query.";
                    else if (snap.Type.Contains("insert"))
                        return "Adds new records to a database table.";
                    else if (snap.Type.Contains("update"))
                        return "Modifies existing records in a database table.";
                    else if (snap.Type.Contains("delete"))
                        return "Removes records from a database table.";
                    else if (snap.Type.Contains("execute"))
                        return "Executes SQL statements or stored procedures against a database.";
                    else
                        return "Interacts with a relational database system.";
                    
                case SnapCategory.ExternalSystem:
                    if (snap.Type.Contains("rest"))
                        return "Communicates with REST API endpoints using HTTP methods.";
                    else if (snap.Type.Contains("soap"))
                        return "Exchanges SOAP messages with web services.";
                    else if (snap.Type.Contains("dynamics"))
                        return "Integrates with Microsoft Dynamics 365 entities and operations.";
                    else if (snap.Type.Contains("salesforce"))
                        return "Connects to Salesforce objects and operations.";
                    else
                        return "Interacts with an external system or service.";
                    
                case SnapCategory.FileOperation:
                    if (snap.Type.Contains("read"))
                        return "Reads content from files in the specified location.";
                    else if (snap.Type.Contains("write"))
                        return "Writes content to files in the specified location.";
                    else if (snap.Type.Contains("list"))
                        return "Lists files available in the specified location.";
                    else if (snap.Type.Contains("delete"))
                        return "Removes files from the specified location.";
                    else
                        return "Performs operations on files or folders.";
                    
                case SnapCategory.ErrorHandling:
                    return "Manages error conditions and provides recovery mechanisms.";
                    
                default:
                    return "Performs specialized operations for this pipeline stage.";
            }
        }
        
        public void GeneratePdfDocumentation(PipelineData pipeline, string diagramSvg, string outputPath)
        {
            try
            {
                // Generate HTML first
                string htmlContent = GenerateHtmlDocumentation(pipeline, diagramSvg);
                
                // Create a PDF converter
                HtmlToPdf converter = new HtmlToPdf();
                
                // Set converter options for better PDF output
                converter.Options.PdfPageSize = PdfPageSize.A4;
                converter.Options.PdfPageOrientation = PdfPageOrientation.Portrait;
                converter.Options.WebPageWidth = 1024;
                converter.Options.MinPageLoadTime = 2;
                converter.Options.MaxPageLoadTime = 30;
                
                // Add custom CSS for PDF layout improvements
                string pdfStyles = @"
                    body { font-family: Arial, sans-serif; font-size: 12px; }
                    h1 { font-size: 24px; color: #333366; }
                    h2 { font-size: 18px; color: #333366; page-break-before: always; }
                    h3 { font-size: 14px; color: #333366; }
                    .snap-details { margin-bottom: 20px; border-bottom: 1px solid #eee; }
                    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
                    th, td { border: 1px solid #ddd; padding: 5px; }
                    th { background-color: #f5f5f5; }
                    .function-details, .snap-properties { margin: 10px 0; }
                    pre { background-color: #f9f9f9; padding: 5px; overflow-x: auto; font-size: 11px; }
                    code { font-family: Consolas, monospace; font-size: 11px; }
                    .diagram { page-break-inside: avoid; margin: 15px 0; }
                ";
                
                converter.Options.AdditionalHtmlHeader = $"<style>{pdfStyles}</style>";
                
                // Convert HTML to PDF
                PdfDocument doc = converter.ConvertHtmlString(htmlContent);
                
                // Save the PDF document
                doc.Save(outputPath);
                doc.Close();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating PDF: {ex.Message}");
                throw;
            }
        }
