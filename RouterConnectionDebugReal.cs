using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Text.Json;

namespace SnapAnalyser
{
    class RouterConnectionDebugReal
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Router Connection Debug - Real Pipeline Data ===");
            
            // Load a real pipeline file that has routers
            string pipelineFile = @"C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Snap-Pipelines\CH28 - Update Kypera Housing Officer_2025_05_16.slp";
            
            if (!File.Exists(pipelineFile))
            {
                Console.WriteLine($"Pipeline file not found: {pipelineFile}");
                Console.WriteLine("Please provide a valid pipeline file path.");
                Console.ReadKey();
                return;
            }
            
            try
            {
                Console.WriteLine($"Loading pipeline: {Path.GetFileName(pipelineFile)}");
                
                // Parse the pipeline
                var analyzer = new SlpAnalyzer();
                var pipeline = analyzer.AnalyzePipeline(pipelineFile);
                
                Console.WriteLine($"Pipeline loaded: {pipeline.Name}");
                Console.WriteLine($"Total snaps: {pipeline.Snaps.Count}");
                Console.WriteLine($"Total links: {pipeline.Links.Count}");
                
                // Find router snaps
                var routerSnaps = pipeline.Snaps.Where(s => 
                    s.Type.ToLower().Contains("router") || 
                    s.Type.ToLower().Contains("flow")).ToList();
                
                Console.WriteLine($"\nFound {routerSnaps.Count} router/flow control snaps:");
                
                foreach (var router in routerSnaps)
                {
                    Console.WriteLine($"\n--- ROUTER: {router.Label} (Type: {router.Type}) ---");
                    Console.WriteLine($"ID: {router.Id}");
                    Console.WriteLine($"Category: {router.Category}");
                    
                    // Check router's connections
                    Console.WriteLine($"OutputConnections: {router.OutputConnections?.Count ?? 0}");
                    if (router.OutputConnections != null && router.OutputConnections.Any())
                    {
                        for (int i = 0; i < router.OutputConnections.Count; i++)
                        {
                            var conn = router.OutputConnections[i];
                            Console.WriteLine($"  Output {i}: {conn.SourceId} → {conn.TargetId}");
                            Console.WriteLine($"    SourceViewId: '{conn.SourceViewId}'");
                            Console.WriteLine($"    TargetViewId: '{conn.TargetViewId}'");
                        }
                    }
                    else
                    {
                        Console.WriteLine("  No OutputConnections found!");
                    }
                    
                    Console.WriteLine($"InputConnections: {router.InputConnections?.Count ?? 0}");
                    if (router.InputConnections != null && router.InputConnections.Any())
                    {
                        for (int i = 0; i < router.InputConnections.Count; i++)
                        {
                            var conn = router.InputConnections[i];
                            Console.WriteLine($"  Input {i}: {conn.SourceId} → {conn.TargetId}");
                            Console.WriteLine($"    SourceViewId: '{conn.SourceViewId}'");
                            Console.WriteLine($"    TargetViewId: '{conn.TargetViewId}'");
                        }
                    }
                    
                    // Check pipeline links that involve this router
                    var routerLinks = pipeline.Links.Where(l => 
                        l.SourceId == router.Id || l.TargetId == router.Id).ToList();
                    
                    Console.WriteLine($"Pipeline links involving this router: {routerLinks.Count}");
                    foreach (var link in routerLinks)
                    {
                        var sourceSnap = pipeline.Snaps.FirstOrDefault(s => s.Id == link.SourceId);
                        var targetSnap = pipeline.Snaps.FirstOrDefault(s => s.Id == link.TargetId);
                        
                        Console.WriteLine($"  Link: {sourceSnap?.Label ?? link.SourceId} → {targetSnap?.Label ?? link.TargetId}");
                        Console.WriteLine($"    SourceViewId: '{link.SourceViewId}'");
                        Console.WriteLine($"    TargetViewId: '{link.TargetViewId}'");
                        
                        if (link.SourceId == router.Id)
                        {
                            Console.WriteLine($"    >>> This is an OUTPUT from the router to '{targetSnap?.Label}'");
                        }
                        if (link.TargetId == router.Id)
                        {
                            Console.WriteLine($"    >>> This is an INPUT to the router from '{sourceSnap?.Label}'");
                        }
                    }
                    
                    // Test the FlowControlConfigurationGenerator
                    Console.WriteLine($"\n--- TESTING ROUTER CONFIGURATION GENERATION ---");
                    var generator = new FlowControlConfigurationGenerator();
                    string routerConfig = generator.GenerateRouterConfiguration(router, pipeline.Snaps);
                    
                    Console.WriteLine("Generated configuration:");
                    Console.WriteLine(routerConfig);
                    
                    Console.WriteLine(new string('=', 60));
                }
                
                // Create detailed HTML report
                CreateDetailedHtmlReport(pipeline, routerSnaps);
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
        
        static void CreateDetailedHtmlReport(PipelineData pipeline, List<SnapNode> routerSnaps)
        {
            var html = $@"<!DOCTYPE html>
<html>
<head>
    <title>Router Connection Debug Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .router {{ border: 1px solid #ddd; margin: 20px 0; padding: 15px; }}
        .connections {{ background: #f8f9fa; padding: 10px; margin: 10px 0; }}
        .error {{ color: red; font-weight: bold; }}
        .success {{ color: green; font-weight: bold; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f8f9fa; }}
        pre {{ background: #f5f5f5; padding: 10px; border-radius: 3px; }}
    </style>
</head>
<body>
    <h1>Router Connection Debug Report</h1>
    <p>Pipeline: {pipeline.Name}</p>
    <p>Generated: {DateTime.Now}</p>
    <p>Total Snaps: {pipeline.Snaps.Count}, Total Links: {pipeline.Links.Count}</p>
    
    <h2>Router Analysis</h2>";
            
            foreach (var router in routerSnaps)
            {
                html += $@"
    <div class='router'>
        <h3>Router: {router.Label}</h3>
        <p><strong>Type:</strong> {router.Type}</p>
        <p><strong>ID:</strong> {router.Id}</p>
        
        <div class='connections'>
            <h4>Output Connections ({router.OutputConnections?.Count ?? 0})</h4>";
            
                if (router.OutputConnections != null && router.OutputConnections.Any())
                {
                    html += "<table><tr><th>Index</th><th>Target ID</th><th>Target Label</th><th>SourceViewId</th><th>TargetViewId</th></tr>";
                    for (int i = 0; i < router.OutputConnections.Count; i++)
                    {
                        var conn = router.OutputConnections[i];
                        var targetSnap = pipeline.Snaps.FirstOrDefault(s => s.Id == conn.TargetId);
                        html += $"<tr><td>{i}</td><td>{conn.TargetId}</td><td>{targetSnap?.Label ?? "Unknown"}</td><td>'{conn.SourceViewId}'</td><td>'{conn.TargetViewId}'</td></tr>";
                    }
                    html += "</table>";
                }
                else
                {
                    html += "<p class='error'>No OutputConnections found!</p>";
                }
                
                html += $@"
            <h4>Pipeline Links FROM this router</h4>";
                
                var outgoingLinks = pipeline.Links.Where(l => l.SourceId == router.Id).ToList();
                if (outgoingLinks.Any())
                {
                    html += "<table><tr><th>Target ID</th><th>Target Label</th><th>SourceViewId</th><th>TargetViewId</th></tr>";
                    foreach (var link in outgoingLinks)
                    {
                        var targetSnap = pipeline.Snaps.FirstOrDefault(s => s.Id == link.TargetId);
                        html += $"<tr><td>{link.TargetId}</td><td>{targetSnap?.Label ?? "Unknown"}</td><td>'{link.SourceViewId}'</td><td>'{link.TargetViewId}'</td></tr>";
                    }
                    html += "</table>";
                }
                else
                {
                    html += "<p class='error'>No outgoing pipeline links found!</p>";
                }
                
                // Test router configuration generation
                var generator = new FlowControlConfigurationGenerator();
                string routerConfig = generator.GenerateRouterConfiguration(router, pipeline.Snaps);
                
                html += $@"
            <h4>Generated Router Configuration</h4>
            <div style='border: 1px solid #ccc; padding: 10px;'>
                {routerConfig}
            </div>
        </div>
    </div>";
            }
            
            html += @"
</body>
</html>";
            
            File.WriteAllText("router_debug_detailed_report.html", html);
            Console.WriteLine("\nDetailed HTML report saved to: router_debug_detailed_report.html");
        }
    }
}
