using System;
using SnapAnalyser;

class QuickConfigTest
{
    static void Main()
    {
        Console.WriteLine("===== AZURE AI CONFIGURATION TEST =====");
        Console.WriteLine($"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        Console.WriteLine();
        
        // Test configuration loading
        Console.WriteLine("Loading configuration from ConfigManager...");
        string apiKey = ConfigManager.OpenAIApiKey;
        string endpoint = ConfigManager.AzureOpenAIEndpoint;
        string deployment = ConfigManager.AzureOpenAIDeploymentName;
        int timeout = ConfigManager.AzureOpenAITimeoutSeconds;
        
        Console.WriteLine($"API Key: {(string.IsNullOrEmpty(apiKey) ? "❌ MISSING" : $"✅ Present (length: {apiKey.Length})")}");
        Console.WriteLine($"Endpoint: {(string.IsNullOrEmpty(endpoint) ? "❌ MISSING" : $"✅ {endpoint}")}");
        Console.WriteLine($"Deployment: {(string.IsNullOrEmpty(deployment) ? "❌ MISSING" : $"✅ {deployment}")}");
        Console.WriteLine($"Timeout: {timeout} seconds");
        
        // Check if Azure AI should be enabled
        bool shouldBeEnabled = !string.IsNullOrEmpty(apiKey) && !string.IsNullOrEmpty(endpoint);
        Console.WriteLine($"\nBased on configuration, Azure AI should be: {(shouldBeEnabled ? "✅ ENABLED" : "❌ DISABLED")}");
        
        if (shouldBeEnabled)
        {
            Console.WriteLine("\n🎯 Configuration looks good! Creating AIDescriptionGenerator...");
            try
            {
                var generator = new AIDescriptionGenerator();
                Console.WriteLine("✅ AIDescriptionGenerator created successfully!");
                Console.WriteLine("   Check the console output above for detailed Azure AI status.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error creating AIDescriptionGenerator: {ex.Message}");
            }
        }
        else
        {
            Console.WriteLine("\n❌ Configuration is incomplete - Azure AI will be disabled");
        }
        
        Console.WriteLine("\n===== TEST COMPLETE =====");
    }
}
