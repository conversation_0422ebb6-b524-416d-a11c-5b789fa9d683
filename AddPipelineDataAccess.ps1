# Script to add pipeline data access for raw JSON extraction

$filePath = "DocumentationGenerator.cs"

Write-Host "Adding pipeline data field for raw JSON access..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Step 1: Add a private field to store current pipeline data
$classDeclaration = 'public class DocumentationGenerator'
$newClassDeclaration = @'
public class DocumentationGenerator
    {
        private PipelineData _currentPipeline; // Store current pipeline for raw JSON access
'@

if ($content.Contains($classDeclaration)) {
    $content = $content.Replace($classDeclaration + "`n    {", $newClassDeclaration)
    Write-Host "Added _currentPipeline field" -ForegroundColor Yellow
} else {
    Write-Host "Class declaration not found" -ForegroundColor Red
}

# Step 2: Find where documentation generation starts and store the pipeline
$documentationStartPattern = 'public async Task<string> GenerateHtmlDocumentationAsync(PipelineData pipeline'
$replacement = @'
public async Task<string> GenerateHtmlDocumentationAsync(PipelineData pipeline
        {
            _currentPipeline = pipeline; // Store pipeline for raw JSON access
'@.Replace("`n        {", ", string diagramSvg, CancellationToken cancellationToken = default)`n        {`n            _currentPipeline = pipeline; // Store pipeline for raw JSON access")

# Find the actual method signature
$methodPattern = 'public async Task<string> GenerateHtmlDocumentationAsync\(PipelineData pipeline[^{]*\{'
if ($content -match $methodPattern) {
    $match = $matches[0]
    $newMatch = $match.Replace("{", "{`n            _currentPipeline = pipeline; // Store pipeline for raw JSON access")
    $content = $content.Replace($match, $newMatch)
    Write-Host "Added pipeline storage in GenerateHtmlDocumentationAsync" -ForegroundColor Yellow
} else {
    Write-Host "GenerateHtmlDocumentationAsync method not found" -ForegroundColor Red
}

# Step 3: Update GetDatabaseConnectionInfo to use the stored pipeline data
$oldMethodCall = 'string connectionInfo = GetDatabaseConnectionInfo(snap);'
$newMethodCall = @'
// Get raw JSON for the current snap
                    JObject rawSnapJson = null;
                    if (_currentPipeline?.RawPipelineJson != null)
                    {
                        try 
                        {
                            var pipelineJson = JObject.Parse(_currentPipeline.RawPipelineJson);
                            var snapsArray = pipelineJson["snap_map"];
                            if (snapsArray != null)
                            {
                                foreach (var snapEntry in snapsArray)
                                {
                                    var snapId = snapEntry["class_id"]?.ToString();
                                    if (snap.Id == snapId)
                                    {
                                        rawSnapJson = (JObject)snapEntry;
                                        Console.WriteLine($"[DB-CONNECTION] Found raw JSON for snap: {snap.Label}");
                                        break;
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"[DB-CONNECTION] Error accessing raw pipeline JSON: {ex.Message}");
                        }
                    }
                    
                    string connectionInfo = GetDatabaseConnectionInfo(snap, rawSnapJson);
'@

if ($content.Contains($oldMethodCall)) {
    $content = $content.Replace($oldMethodCall, $newMethodCall)
    Write-Host "Updated GetDatabaseConnectionInfo call with raw JSON access" -ForegroundColor Yellow
} else {
    Write-Host "GetDatabaseConnectionInfo call not found" -ForegroundColor Red
}

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Enhanced DocumentationGenerator with pipeline data access!" -ForegroundColor Green
