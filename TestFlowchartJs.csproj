<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0-windows7.0</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.AI.OpenAI" Version="1.0.0-beta.17" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="TestFlowchartJs.cs" />
    <Compile Include="FlowchartJsGenerator.cs" />
    <Compile Include="DiagramGenerator.cs" />
    <Compile Include="FlowControlDiagramGenerator.cs" />
    <Compile Include="SlpAnalyzer.cs" />
    <Compile Include="ConfigManager.cs" />
    <Compile Include="AzureOpenAITester.cs" />
  </ItemGroup>
</Project>
