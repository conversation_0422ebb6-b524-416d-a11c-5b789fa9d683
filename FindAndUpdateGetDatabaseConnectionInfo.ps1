# Script to find and update calls to GetDatabaseConnectionInfo to pass raw JSON

$filePath = "DocumentationGenerator.cs"

Write-Host "Searching for GetDatabaseConnectionInfo method calls..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Look for patterns that might indicate where the method is called
$lines = $content.Split("`n")
$foundCalls = @()

for ($i = 0; $i -lt $lines.Length; $i++) {
    $line = $lines[$i]
    if ($line -match "GetDatabaseConnectionInfo") {
        $foundCalls += "Line $($i + 1): $($line.Trim())"
    }
    
    # Also look for database connection related calls
    if ($line -match "Database/Connector" -or $line -match "connectionInfo") {
        $foundCalls += "Database Connection Line $($i + 1): $($line.Trim())"
    }
    
    # Look for the debug messages we see in output
    if ($line -match "Processing database snap" -or $line -match "About to call GetDatabaseConnectionInfo") {
        $foundCalls += "Debug Line $($i + 1): $($line.Trim())"
    }
}

Write-Host "Found potential method calls and related code:" -ForegroundColor Yellow
foreach ($call in $foundCalls) {
    Write-Host $call -ForegroundColor Cyan
}

# Also search for variable assignments that might contain the method call
Write-Host "`nSearching for variable assignments with connection info..." -ForegroundColor Green
for ($i = 0; $i -lt $lines.Length; $i++) {
    $line = $lines[$i]
    if ($line -match "connectionInfo\s*=" -or $line -match "connInfo\s*=" -or $line -match "dbInfo\s*=") {
        Write-Host "Connection Assignment Line $($i + 1): $($line.Trim())" -ForegroundColor Magenta
    }
}
