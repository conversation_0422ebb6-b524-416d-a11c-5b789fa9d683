using System;
using System.IO;
using SnapAnalyser;

namespace TestProjectContext
{
    class Program
    {
        static void Main()
        {
            Console.WriteLine("Testing Project Context Integration...");
            
            // Create test project data
            var project = new ProjectData("Test Project", "Test Description", "Test Purpose");
            project.SlpFiles.Add("P_MainPipeline.slp");
            project.SlpFiles.Add("CH_ChildPipeline.slp");
            project.SlpFiles.Add("CH_AnotherChild.slp");
            
            // Create test pipeline data
            var pipeline = new PipelineData();
            pipeline.Name = "P_MainPipeline.slp";
            pipeline.Snaps = new List<SnapNode>();
            
            // Test AI description generator
            var aiGenerator = new AIDescriptionGenerator(false, false, "");
            
            // Test CreateProjectContext method indirectly
            Console.WriteLine("Project has the following methods:");
            Console.WriteLine($"- GetParentPipelines(): {project.GetParentPipelines().Count} files");
            Console.WriteLine($"- GetChildPipelines(): {project.GetChildPipelines().Count} files");
            Console.WriteLine($"- IsParentPipeline(P_MainPipeline.slp): {project.IsParentPipeline("P_MainPipeline.slp")}");
            Console.WriteLine($"- IsChildPipeline(CH_ChildPipeline.slp): {project.IsChildPipeline("CH_ChildPipeline.slp")}");
            
            Console.WriteLine("\nProject Context:");
            Console.WriteLine(project.GetProjectContext());
            
            Console.WriteLine("\nTest completed successfully!");
        }
    }
}
