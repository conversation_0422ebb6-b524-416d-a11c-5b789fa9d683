using System;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.Json;
using System.Text;

class ManualDeploymentTest
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== Manual Deployment Name Tester ===");
        Console.WriteLine();
        
        // Your configuration
        string endpoint = "https://openai-snaplogic-documenter.openai.azure.com/";
        string apiKey = "6dr7izA3Iuc1qhTiaKMZWnx1iHX0yI0MDx4Hhj1VWkSLHQ7CCPD7JQQJ99BEACmepeSXJ3w3AAABACOG0Qbs";
        
        // Extended list of possible deployment names
        string[] possibleDeployments = {
            // Standard GPT-4 variations
            "gpt-4", "gpt4", "GPT-4", "GPT4",
            
            // GPT-4 with versions
            "gpt-4-32k", "gpt-4-0613", "gpt-4-1106-preview", "gpt-4-0125-preview",
            
            // GPT-4 Turbo variations
            "gpt-4-turbo", "gpt4-turbo", "gpt-4-turbo-preview", "gpt-4-turbo-2024-04-09",
            
            // GPT-3.5 variations
            "gpt-35-turbo", "gpt-3.5-turbo", "gpt35turbo", "gpt-35-turbo-16k",
            "gpt-35-turbo-0613", "gpt-35-turbo-1106", "gpt-35-turbo-0125",
            
            // GPT-4o variations
            "gpt-4o", "gpt4o", "gpt-4o-mini", "gpt-4o-2024-05-13",
            
            // Other common patterns
            "text-davinci-003", "davinci-003", "davinci",
            "deployment-1", "deployment1", "default", "primary",
            
            // Custom naming patterns
            "snaplogic-gpt4", "snaplogic-gpt-4", "documenter-gpt4",
            "openai-gpt4", "gpt4-documenter", "snap-gpt4"
        };
        
        Console.WriteLine($"Testing {possibleDeployments.Length} possible deployment names...");
        Console.WriteLine();
        
        using (var client = new HttpClient())
        {
            client.DefaultRequestHeaders.Add("api-key", apiKey);
            client.Timeout = TimeSpan.FromSeconds(10);
            
            int successCount = 0;
            
            foreach (string deployment in possibleDeployments)
            {
                try
                {
                    string testUrl = $"{endpoint.TrimEnd('/')}/openai/deployments/{deployment}/chat/completions?api-version=2023-05-15";
                    
                    var testRequest = new {
                        messages = new[] {
                            new { role = "user", content = "test" }
                        },
                        max_tokens = 1
                    };
                    
                    var content = new StringContent(JsonSerializer.Serialize(testRequest), Encoding.UTF8, "application/json");
                    var response = await client.PostAsync(testUrl, content);
                    
                    if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                    {
                        Console.WriteLine($"❌ {deployment} - Not Found");
                    }
                    else if (response.IsSuccessStatusCode)
                    {
                        Console.WriteLine($"✅ {deployment} - SUCCESS! This deployment exists and works!");
                        successCount++;
                    }
                    else
                    {
                        Console.WriteLine($"⚠️  {deployment} - {response.StatusCode} (deployment exists but request failed)");
                        successCount++;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"⚠️  {deployment} - Error: {ex.Message}");
                }
                
                // Small delay to avoid rate limiting
                await Task.Delay(100);
            }
            
            Console.WriteLine();
            Console.WriteLine($"Found {successCount} working deployment(s)!");
            
            if (successCount == 0)
            {
                Console.WriteLine();
                Console.WriteLine("No deployments found. Please check your Azure Portal for the exact deployment names.");
            }
        }
        
        Console.WriteLine();
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}
