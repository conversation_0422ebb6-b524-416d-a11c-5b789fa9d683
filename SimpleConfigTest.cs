using System;
using SnapAnalyser;

public class SimpleConfigTest
{
    public static void Main()
    {
        Console.WriteLine("=== Azure AI Configuration Test ===");
        Console.WriteLine($"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        Console.WriteLine();
        
        // Load configuration values
        string apiKey = ConfigManager.OpenAIApiKey;
        string endpoint = ConfigManager.AzureOpenAIEndpoint;
        string deployment = ConfigManager.AzureOpenAIDeploymentName;
        int timeout = ConfigManager.AzureOpenAITimeoutSeconds;
        
        Console.WriteLine("Configuration Values:");
        Console.WriteLine($"  API Key: {(string.IsNullOrEmpty(apiKey) ? "MISSING" : $"Present (length: {apiKey.Length})")}");
        Console.WriteLine($"  Endpoint: {endpoint ?? "MISSING"}");
        Console.WriteLine($"  Deployment: {deployment ?? "MISSING"}");
        Console.WriteLine($"  Timeout: {timeout} seconds");
        Console.WriteLine();
        
        // Simulate the exact check from AIDescriptionGenerator
        bool useAzureAI = !string.IsNullOrEmpty(apiKey) && !string.IsNullOrEmpty(endpoint);
        
        Console.WriteLine($"Azure AI Status: {(useAzureAI ? "ENABLED" : "DISABLED")}");
        
        if (!useAzureAI)
        {
            Console.WriteLine();
            Console.WriteLine("Reasons for disabled status:");
            if (string.IsNullOrEmpty(apiKey))
                Console.WriteLine("  ❌ API key is null or empty");
            if (string.IsNullOrEmpty(endpoint))
                Console.WriteLine("  ❌ Endpoint is null or empty");
        }
        else
        {
            Console.WriteLine();
            Console.WriteLine("✅ Azure AI will be enabled!");
            Console.WriteLine("All required configuration values are present.");
        }
        
        Console.WriteLine();
        Console.WriteLine("Test completed.");
    }
}
