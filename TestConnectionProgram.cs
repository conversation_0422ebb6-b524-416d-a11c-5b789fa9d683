using System;
using System.Threading.Tasks;
using SnapAnalyser;

class TestProgram
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("Testing Azure OpenAI connection with current configuration...");
        Console.WriteLine("=======================================================");
        
        // First show what configuration is being loaded
        Console.WriteLine("Current Configuration:");
        Console.WriteLine($"  API Key: {(!string.IsNullOrEmpty(ConfigManager.OpenAIApiKey) ? "Set (length: " + ConfigManager.OpenAIApiKey.Length + ")" : "Not set")}");
        Console.WriteLine($"  Endpoint: {ConfigManager.AzureOpenAIEndpoint}");
        Console.WriteLine($"  Deployment: {ConfigManager.AzureOpenAIDeploymentName}");
        Console.WriteLine($"  Timeout: {ConfigManager.AzureOpenAITimeoutSeconds} seconds");
        Console.WriteLine();
        
        // Test if Azure AI will be enabled
        string apiKey = ConfigManager.OpenAIApiKey;
        string endpoint = ConfigManager.AzureOpenAIEndpoint;
        bool useAzureAI = !string.IsNullOrEmpty(apiKey) && !string.IsNullOrEmpty(endpoint);
        
        Console.WriteLine($"Azure AI will be enabled: {useAzureAI}");
        
        if (!useAzureAI)
        {
            Console.WriteLine("Azure AI is disabled because:");
            if (string.IsNullOrEmpty(apiKey))
                Console.WriteLine("  - API key is null or empty");
            if (string.IsNullOrEmpty(endpoint))
                Console.WriteLine("  - Endpoint is null or empty");
            return;
        }
        
        Console.WriteLine();
        Console.WriteLine("Testing Azure OpenAI connection...");
        
        // Create tester and test connection
        var tester = new AzureOpenAITester();
        
        try
        {
            // Test basic connection
            Console.WriteLine("1. Testing basic connection...");
            bool isConnected = await tester.TestConnection();
            
            if (isConnected)
            {
                Console.WriteLine("✅ Basic connection test: SUCCESS");
                
                // Test with response
                Console.WriteLine();
                Console.WriteLine("2. Testing with response...");
                string response = await tester.TestWithResponse();
                Console.WriteLine($"Response test result: {response}");
                
                // Get deployment info
                Console.WriteLine();
                Console.WriteLine("3. Getting deployment information...");
                string deploymentInfo = await tester.GetDeploymentInfo();
                Console.WriteLine($"Deployment info: {deploymentInfo}");
            }
            else
            {
                Console.WriteLine("❌ Basic connection test: FAILED");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error during testing: {ex.Message}");
            if (ex.InnerException != null)
            {
                Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
            }
        }
        
        Console.WriteLine();
        Console.WriteLine("Testing complete. Press any key to exit...");
        Console.ReadKey();
    }
}
