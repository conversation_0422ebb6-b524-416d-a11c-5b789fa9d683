# PowerShell script to implement Dynamics365ForSales details display

$filePath = "DocumentationGenerator.cs"

Write-Host "Implementing Dynamics365ForSales details display..." -ForegroundColor Green

$content = Get-Content $filePath -Raw

# Step 1: Extend account display logic to include Dynamics365ForSales snaps
$accountPattern = 'if \(snap\.Category == SnapCategory\.Database\)'
$accountReplacement = @'
// Show account info for Database snaps AND Dynamics365ForSales snaps
                bool shouldShowAccountInfo = snap.Category == SnapCategory.Database || 
                                           (snap.Type?.ToLower().Contains("dynamics365forsales") == true);
                if (shouldShowAccountInfo)
'@

if ($content.Contains($accountPattern)) {
    $content = $content.Replace($accountPattern, $accountReplacement)
    Write-Host "✅ Extended account display logic for Dynamics365ForSales snaps" -ForegroundColor Yellow
}

# Step 2: Update method name and logging for broader scope
$methodPattern = 'string connectionInfo = GetDatabaseConnectionInfo\(snap\);'
$methodReplacement = @'
// Get raw JSON for this snap from the pipeline
                    JObject rawSnapJson = null;
                    if (_currentPipeline?.RawPipelineJson != null)
                    {
                        try
                        {
                            var pipelineObj = JObject.Parse(_currentPipeline.RawPipelineJson);
                            var snapsArray = pipelineObj["snaps"] as JArray;
                            rawSnapJson = snapsArray?.FirstOrDefault(s => s["snap_id"]?.ToString() == snap.Id) as JObject;
                            LogMessage($"[CONNECTION] Raw JSON for snap {snap.Label}: {(rawSnapJson != null ? "Found" : "Not found")}");
                        }
                        catch (Exception ex)
                        {
                            LogMessage($"[CONNECTION] Error getting raw JSON for snap {snap.Label}: {ex.Message}");
                        }
                    }
                    
                    string connectionInfo = GetConnectionInfo(snap, rawSnapJson);
'@

if ($content.Contains($methodPattern)) {
    $content = $content.Replace($methodPattern, $methodReplacement)
    Write-Host "✅ Updated to use GetConnectionInfo with raw JSON support" -ForegroundColor Yellow
}

# Step 3: Update logging prefixes from DB-CONNECTION to CONNECTION
$content = $content -replace '\[DB-CONNECTION\]', '[CONNECTION]'
$content = $content -replace 'LogMessage\(\$"\[DB-CONNECTION\]', 'LogMessage($"[CONNECTION]'

# Step 4: Update the else clause for non-database snaps
$elsePattern = 'LogMessage\(\$"\[CONNECTION\] Snap \{snap\.Label\} is not categorized as Database \(Category: \{snap\.Category\}\)"\);'
$elseReplacement = @'
LogMessage($"[CONNECTION] Snap {snap.Label} does not qualify for account display (Category: {snap.Category}, Type: {snap.Type})");
                }

                // Add Dynamics365ForSales-specific details
                if (snap.Type?.ToLower().Contains("dynamics365forsales") == true)
                {
                    LogMessage($"[DYNAMICS365] Processing Dynamics365ForSales snap details for: {snap.Label}");
                    string dynamics365Details = GetDynamics365ForSalesDetails(snap);
                    if (!string.IsNullOrEmpty(dynamics365Details))
                    {
                        html.AppendLine(dynamics365Details);
                    }
                }
'@

if ($content.Contains($elsePattern)) {
    $content = $content.Replace($elsePattern, $elseReplacement)
    Write-Host "✅ Added Dynamics365ForSales details integration point" -ForegroundColor Yellow
}

# Step 5: Update method signature and scope
$methodSigPattern = 'private string GetDatabaseConnectionInfo\(SnapNode snap\)'
$methodSigReplacement = 'private string GetConnectionInfo(SnapNode snap, JObject rawSnapJson = null)'

if ($content.Contains($methodSigPattern)) {
    $content = $content.Replace($methodSigPattern, $methodSigReplacement)
    Write-Host "✅ Updated method signature to GetConnectionInfo" -ForegroundColor Yellow
}

# Step 6: Update method eligibility check
$eligibilityPattern = 'if \(snap\.Category != SnapCategory\.Database\)\s+return null;'
$eligibilityReplacement = @'
// Show connection info for Database snaps and Dynamics365ForSales snaps
            bool isEligibleForConnectionInfo = snap.Category == SnapCategory.Database || 
                                               (snap.Type?.ToLower().Contains("dynamics365forsales") == true);
            
            if (!isEligibleForConnectionInfo)
            {
                LogMessage($"[CONNECTION] Snap {snap.Label} not eligible for connection info (Category: {snap.Category}, Type: {snap.Type})");
                return null;
            }
'@

$content = $content -replace $eligibilityPattern, $eligibilityReplacement

# Step 7: Add raw JSON parsing at the beginning of GetConnectionInfo
$loggingPattern = 'LogMessage\(\$"\[CONNECTION\] Checking connection info for snap: \{snap\.Label\}, Properties count: \{snap\.Properties\.Count\}"\);'
$loggingReplacement = @'
LogMessage($"[CONNECTION] Checking connection info for snap: {snap.Label}, Properties count: {snap.Properties.Count}");
            
            // First try to extract account from raw JSON if available
            if (rawSnapJson != null)
            {
                var accountNode = rawSnapJson["property_map"]?["account"];
                if (accountNode != null)
                {
                    var accountRef = accountNode["account_ref"];
                    var valueObj = accountRef?["value"];
                    var labelObj = valueObj?["label"];
                    var accountName = labelObj?["value"]?.ToString();
                    if (!string.IsNullOrEmpty(accountName))
                    {
                        LogMessage($"[CONNECTION] Successfully extracted account name from raw JSON: {accountName}");
                        return accountName;
                    }
                }
                LogMessage($"[CONNECTION] No account found in raw JSON structure");
            }
'@

if ($content.Contains($loggingPattern)) {
    $content = $content.Replace($loggingPattern, $loggingReplacement)
    Write-Host "✅ Added raw JSON account extraction logic" -ForegroundColor Yellow
}

# Step 8: Disable pseudocode for Dynamics365ForSales snaps
$pseudocodePattern = 'if \(!isMapperType && !isRouterType && !isExitOrUnionType && !isCopyType\)'
$pseudocodeReplacement = 'if (!isMapperType && !isRouterType && !isExitOrUnionType && !isCopyType && snap.Category != SnapCategory.Database && !snap.Type.ToLower().Contains("dynamics365forsales"))'

if ($content.Contains($pseudocodePattern)) {
    $content = $content.Replace($pseudocodePattern, $pseudocodeReplacement)
    Write-Host "✅ Disabled pseudocode generation for Database and Dynamics365ForSales snaps" -ForegroundColor Yellow
}

# Step 9: Add the GetDynamics365ForSalesDetails method before the closing braces
$methodImplementation = @'

        /// <summary>
        /// Extracts and formats Dynamics365ForSales-specific details
        /// </summary>
        private string GetDynamics365ForSalesDetails(SnapNode snap)
        {
            var html = new StringBuilder();
            LogMessage($"[DYNAMICS365] Extracting details for snap: {snap.Label}");

            try
            {
                var details = new Dictionary<string, string>();

                // Look for specific Dynamics365ForSales properties
                foreach (var prop in snap.Properties)
                {
                    string key = prop.Key.ToLower();
                    string value = prop.Value;

                    // Skip empty or boolean values
                    if (string.IsNullOrEmpty(value) || 
                        value.Equals("true", StringComparison.OrdinalIgnoreCase) || 
                        value.Equals("false", StringComparison.OrdinalIgnoreCase))
                        continue;

                    // Filter Condition
                    if (key.Contains("filter") && (key.Contains("condition") || key.Contains("criteria") || key.Contains("where")))
                    {
                        details["Filter Condition"] = value;
                        LogMessage($"[DYNAMICS365] Found Filter Condition: {value}");
                    }
                    // Query Parameters
                    else if (key.Contains("query") && (key.Contains("parameter") || key.Contains("param") || key.Contains("variable")))
                    {
                        details["Query Parameters"] = value;
                        LogMessage($"[DYNAMICS365] Found Query Parameters: {value}");
                    }
                    // Output Attributes
                    else if (key.Contains("output") && (key.Contains("attribute") || key.Contains("field") || key.Contains("column")))
                    {
                        details["Output Attributes"] = value;
                        LogMessage($"[DYNAMICS365] Found Output Attributes: {value}");
                    }
                    // Order By
                    else if (key.Contains("order") && (key.Contains("by") || key.Contains("sort")))
                    {
                        details["Order By"] = value;
                        LogMessage($"[DYNAMICS365] Found Order By: {value}");
                    }
                    // Generic property matching for these specific terms
                    else if (key.Contains("filtercondition") || key == "filter_condition")
                    {
                        details["Filter Condition"] = value;
                        LogMessage($"[DYNAMICS365] Found Filter Condition (exact match): {value}");
                    }
                    else if (key.Contains("queryparameters") || key == "query_parameters")
                    {
                        details["Query Parameters"] = value;
                        LogMessage($"[DYNAMICS365] Found Query Parameters (exact match): {value}");
                    }
                    else if (key.Contains("outputattributes") || key == "output_attributes")
                    {
                        details["Output Attributes"] = value;
                        LogMessage($"[DYNAMICS365] Found Output Attributes (exact match): {value}");
                    }
                    else if (key.Contains("orderby") || key == "order_by")
                    {
                        details["Order By"] = value;
                        LogMessage($"[DYNAMICS365] Found Order By (exact match): {value}");
                    }
                }

                // Format the details for HTML output
                if (details.Any())
                {
                    html.AppendLine($"          <div style=\"margin: 10px 0; padding: 10px; background-color: #f0f8ff; border-left: 4px solid #0066cc;\">");
                    html.AppendLine($"            <h5 style=\"color: #0066cc; margin: 0 0 8px 0;\">Dynamics365 Configuration</h5>");

                    foreach (var detail in details)
                    {
                        string displayValue = detail.Value;
                        
                        // Try to parse JSON values for better display
                        if (displayValue.StartsWith("{") || displayValue.StartsWith("["))
                        {
                            try
                            {
                                var parsed = JToken.Parse(displayValue);
                                if (parsed is JArray array)
                                {
                                    displayValue = string.Join(", ", array.Select(item => item.ToString()));
                                }
                                else if (parsed is JObject obj)
                                {
                                    displayValue = string.Join(", ", obj.Properties().Select(p => $"{p.Name}: {p.Value}"));
                                }
                            }
                            catch
                            {
                                // Keep original value if JSON parsing fails
                            }
                        }

                        html.AppendLine($"            <p style=\"margin: 4px 0;\"><strong>{detail.Key}:</strong> {System.Net.WebUtility.HtmlEncode(displayValue)}</p>");
                        LogMessage($"[DYNAMICS365] Added to output: {detail.Key} = {displayValue}");
                    }

                    html.AppendLine($"          </div>");
                    LogMessage($"[DYNAMICS365] Generated HTML details block for: {snap.Label}");
                }
                else
                {
                    LogMessage($"[DYNAMICS365] No specific details found for: {snap.Label}");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"[DYNAMICS365] Error processing details for {snap.Label}: {ex.Message}");
            }

            return html.ToString();
        }
'@

# Insert the method before the final closing braces
$closingPattern = '    }\s*}\s*$'
if ($content -match $closingPattern) {
    $content = $content -replace $closingPattern, "$methodImplementation`n    }`n}"
    Write-Host "✅ Added GetDynamics365ForSalesDetails method" -ForegroundColor Yellow
}

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "✅ Successfully implemented Dynamics365ForSales details display!" -ForegroundColor Green
Write-Host ""
Write-Host "Features implemented:" -ForegroundColor Cyan
Write-Host "  ✓ Extended account display for Dynamics365ForSales snaps" -ForegroundColor White
Write-Host "  ✓ Added raw JSON account extraction" -ForegroundColor White
Write-Host "  ✓ Disabled pseudocode for Database and Dynamics365ForSales snaps" -ForegroundColor White
Write-Host "  ✓ Added Dynamics365ForSales details extraction method" -ForegroundColor White
Write-Host "  ✓ Display Filter Condition, Query Parameters, Output Attributes, Order By" -ForegroundColor White
Write-Host "  ✓ Styled HTML output with blue highlight box" -ForegroundColor White
Write-Host "  ✓ JSON parsing for complex property values" -ForegroundColor White
Write-Host "  ✓ Comprehensive logging for debugging" -ForegroundColor White
