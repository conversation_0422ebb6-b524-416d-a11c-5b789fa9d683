using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json.Linq;

namespace SnapLogic.Documentation.Shared
{
    public class PipelineData
    {
        public string Name { get; set; } = "";
        public string Author { get; set; } = "";
        public List<PipelineParameter> Parameters { get; set; } = new List<PipelineParameter>();
        public List<SnapNode> Snaps { get; set; } = new List<SnapNode>();
        public List<SnapLink> Links { get; set; } = new List<SnapLink>();
        public JObject? RawPipelineJson { get; set; } // Store raw pipeline JSON for account extraction

        public List<SnapNode> GetStartPoints()
        {
            return Snaps.Where(s => s.IsStartPoint).ToList();
        }

        public List<SnapNode> GetEndPoints()
        {
            return Snaps.Where(s => s.IsEndPoint).ToList();
        }
    }

    public class PipelineParameter
    {
        public string Key { get; set; } = "";
        public string Value { get; set; } = "";
        public string DataType { get; set; } = "";
        public bool Required { get; set; }
        public string Description { get; set; } = "";
    }

    public class SnapNode
    {
        public string Id { get; set; } = "";
        public string Label { get; set; } = "";
        public string Type { get; set; } = "";
        public Dictionary<string, string> Properties { get; set; } = new Dictionary<string, string>();
        public Position Position { get; set; } = new Position();
        public List<SnapLink> InputConnections { get; set; } = new List<SnapLink>();
        public List<SnapLink> OutputConnections { get; set; } = new List<SnapLink>();
        public bool IsStartPoint { get; set; }
        public bool IsEndPoint { get; set; }
        public SnapCategory Category { get; set; }
    }

    public class SnapLink
    {
        public string Id { get; set; } = "";
        public string SourceId { get; set; } = "";
        public string TargetId { get; set; } = "";
        public string SourceViewId { get; set; } = "";
        public string TargetViewId { get; set; } = "";
        public bool IsGoto { get; set; }
    }

    public class Position
    {
        public int X { get; set; }
        public int Y { get; set; }
    }

    public enum SnapCategory
    {
        FlowControl,
        Transformation,
        Database,
        ExternalSystem,
        FileOperation,
        ErrorHandling,
        Other
    }

    public class ProjectData
    {
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string Purpose { get; set; } = "";
        public List<string> SlpFiles { get; set; } = new List<string>();
        public string OutputFolder { get; set; } = "";
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;
        
        // Project settings
        public bool GenerateHtml { get; set; } = true;
        public bool GeneratePdf { get; set; } = false;
        public bool UseAI { get; set; } = false;
        public bool UseCachedDescriptions { get; set; } = true;

        public ProjectData()
        {
        }

        public ProjectData(string name, string description, string purpose)
        {
            Name = name;
            Description = description;
            Purpose = purpose;
        }

        /// <summary>
        /// Gets the parent pipelines (those starting with 'P')
        /// </summary>
        public List<string> GetParentPipelines()
        {
            var parentPipelines = new List<string>();
            foreach (var file in SlpFiles)
            {
                var fileName = Path.GetFileNameWithoutExtension(file);
                if (fileName.StartsWith("P", StringComparison.OrdinalIgnoreCase))
                {
                    parentPipelines.Add(file);
                }
            }
            return parentPipelines;
        }

        /// <summary>
        /// Gets the child pipelines (those starting with 'CH')
        /// </summary>
        public List<string> GetChildPipelines()
        {
            var childPipelines = new List<string>();
            foreach (var file in SlpFiles)
            {
                var fileName = Path.GetFileNameWithoutExtension(file);
                if (fileName.StartsWith("CH", StringComparison.OrdinalIgnoreCase))
                {
                    childPipelines.Add(file);
                }
            }
            return childPipelines;
        }

        /// <summary>
        /// Gets all other pipelines that don't match parent or child conventions
        /// </summary>
        public List<string> GetOtherPipelines()
        {
            var otherPipelines = new List<string>();
            foreach (var file in SlpFiles)
            {
                var fileName = Path.GetFileNameWithoutExtension(file);
                if (!fileName.StartsWith("P", StringComparison.OrdinalIgnoreCase) && 
                    !fileName.StartsWith("CH", StringComparison.OrdinalIgnoreCase))
                {
                    otherPipelines.Add(file);
                }
            }
            return otherPipelines;
        }

        /// <summary>
        /// Determines if a pipeline is a parent pipeline
        /// </summary>
        public bool IsParentPipeline(string filePath)
        {
            var fileName = Path.GetFileNameWithoutExtension(filePath);
            return fileName.StartsWith("P", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Determines if a pipeline is a child pipeline
        /// </summary>
        public bool IsChildPipeline(string filePath)
        {
            var fileName = Path.GetFileNameWithoutExtension(filePath);
            return fileName.StartsWith("CH", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Gets context information for AI generation
        /// </summary>
        public string GetProjectContext()
        {
            var context = $"Project: {Name}\n";
            context += $"Description: {Description}\n";
            context += $"Purpose: {Purpose}\n\n";
            
            var parentPipelines = GetParentPipelines();
            var childPipelines = GetChildPipelines();
            var otherPipelines = GetOtherPipelines();

            if (parentPipelines.Count > 0)
            {
                context += $"Parent Pipelines ({parentPipelines.Count}): ";
                context += string.Join(", ", parentPipelines.ConvertAll(f => Path.GetFileNameWithoutExtension(f)));
                context += "\n";
            }

            if (childPipelines.Count > 0)
            {
                context += $"Child Pipelines ({childPipelines.Count}): ";
                context += string.Join(", ", childPipelines.ConvertAll(f => Path.GetFileNameWithoutExtension(f)));
                context += "\n";
            }

            if (otherPipelines.Count > 0)
            {
                context += $"Other Pipelines ({otherPipelines.Count}): ";
                context += string.Join(", ", otherPipelines.ConvertAll(f => Path.GetFileNameWithoutExtension(f)));
                context += "\n";
            }

            return context;
        }
    }

    // Additional models for file processing
    public class UploadedFile
    {
        public string FileName { get; set; } = "";
        public string Content { get; set; } = "";
        public long Size { get; set; }
        public DateTime UploadedAt { get; set; } = DateTime.Now;
    }

    public class ProcessingOptions
    {
        public bool GenerateHtml { get; set; } = true;
        public bool GeneratePdf { get; set; } = false;
        public bool UseAI { get; set; } = false;
        public bool UseCachedDescriptions { get; set; } = true;
        public string OutputFormat { get; set; } = "html";
    }

    public class ProcessingResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = "";
        public List<ProcessedFile> ProcessedFiles { get; set; } = new List<ProcessedFile>();
        public string? DownloadUrl { get; set; }
        public TimeSpan ProcessingTime { get; set; }

        // Legacy properties for backward compatibility
        public List<string> Errors { get; set; } = new List<string>();
        public string? ZipFilePath { get; set; }
    }

    public class ProcessedFile
    {
        public string FileName { get; set; } = "";
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public string? HtmlContent { get; set; }
        public string? PdfPath { get; set; }
        public PipelineData? PipelineData { get; set; }
    }

    public class ProcessingProgress
    {
        public int CurrentFile { get; set; }
        public int TotalFiles { get; set; }
        public string CurrentFileName { get; set; } = "";
        public string Status { get; set; } = "";
        public double PercentComplete => TotalFiles > 0 ? (double)CurrentFile / TotalFiles * 100 : 0;

        // Legacy property for backward compatibility
        public int ProcessedFiles { get; set; }
    }
}
