# PowerShell script to update both account display and pseudocode generation logic
# 1. Disable pseudocode for database and dynamics365forsales snaps
# 2. Show accounts for any snap that has an account (not just database snaps)

$filePath = "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\DocumentationGenerator.cs"

Write-Host "Reading DocumentationGenerator.cs..."
$content = Get-Content -Path $filePath -Raw

Write-Host "Making changes..."

# Change 1: Update pseudocode generation to skip database and dynamics365forsales snaps
# Find the line that checks snap types for pseudocode eligibility
$oldPseudocodeCondition = 'if (!isMapperType && !isRouterType && !isExitOrUnionType && !isCopyType)'
$newPseudocodeCondition = 'if (!isMapperType && !isRouterType && !isExitOrUnionType && !isCopyType && snap.Category != SnapCategory.Database && !snap.Type.ToLower().Contains("dynamics365forsales"))'

if ($content -match [regex]::Escape($oldPseudocodeCondition)) {
    $content = $content -replace [regex]::Escape($oldPseudocodeCondition), $newPseudocodeCondition
    Write-Host "✅ Updated pseudocode generation condition to skip database and dynamics365forsales snaps"
} else {
    Write-Host "⚠️  Could not find pseudocode condition to update"
}

# Change 2: Update GetDatabaseConnectionInfo to work for any snap with an account
# Find the line that restricts to database snaps only
$oldAccountCondition = 'if (snap.Category != SnapCategory.Database)'
$newAccountCondition = 'if (false) // Show accounts for any snap that has an account'

if ($content -match [regex]::Escape($oldAccountCondition)) {
    $content = $content -replace [regex]::Escape($oldAccountCondition), $newAccountCondition
    Write-Host "✅ Updated GetDatabaseConnectionInfo to work for any snap with an account"
} else {
    Write-Host "⚠️  Could not find account condition to update"
}

# Change 3: Update the method name and logging to reflect its broader scope
$oldMethodName = 'GetDatabaseConnectionInfo'
$newMethodName = 'GetConnectionInfo'

# Update method signature
$content = $content -replace "private string $oldMethodName\(", "private string $newMethodName("

# Update method calls
$content = $content -replace "$oldMethodName\(", "$newMethodName("

# Update logging messages
$content = $content -replace '\[DB-CONNECTION\]', '[CONNECTION]'

Write-Host "✅ Renamed method from GetDatabaseConnectionInfo to GetConnectionInfo"

Write-Host "Writing updated file..."
$content | Set-Content -Path $filePath -Encoding UTF8

Write-Host ""
Write-Host "🎉 Successfully updated DocumentationGenerator.cs with both changes:"
Write-Host "   1. ✅ Pseudocode generation now skips database and dynamics365forsales snaps"
Write-Host "   2. ✅ Account information now shows for any snap that has an account"
Write-Host "   3. ✅ Method renamed from GetDatabaseConnectionInfo to GetConnectionInfo"
Write-Host ""
Write-Host "The changes made:"
Write-Host "• Pseudocode: Added conditions to skip database snaps and dynamics365forsales snaps"
Write-Host "• Accounts: Removed database-only restriction, now works for all snaps with accounts"
Write-Host "• Logging: Updated to use [CONNECTION] prefix instead of [DB-CONNECTION]"
