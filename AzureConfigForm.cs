using System.Text;
using System.Text.Json;

namespace SnapAnalyser
{
    public partial class AzureConfigForm : Form
    {
        private TextBox txtApiKey;
        private Label lblApiKey;
        private Button btnTest;
        private Button btnSave;
        private Button btnCancel;
        private TextBox txtEndpoint;
        private Label lblEndpoint;
        private TextBox txtDeploymentName;
        private Label lblDeploymentName;
        private Label lblStatus;
        private NumericUpDown numTimeout;
        private Label lblTimeout;
        private Button btnListDeployments;
        private Button btnTestExact;

        public AzureConfigForm()
        {
            InitializeComponent();
            
            // Load non-sensitive settings directly
            txtEndpoint.Text = ConfigManager.AzureOpenAIEndpoint;
            txtDeploymentName.Text = ConfigManager.AzureOpenAIDeploymentName;
            numTimeout.Value = ConfigManager.AzureOpenAITimeoutSeconds;

            // Show indication if API key is already stored, always masking it
            if (!string.IsNullOrEmpty(ConfigManager.OpenAIApiKey))
            {
                // Just show the first few characters and mask the rest
                string maskedKey = ConfigManager.OpenAIApiKey.Substring(0, Math.Min(4, ConfigManager.OpenAIApiKey.Length)) + "..." + 
                                  (ConfigManager.OpenAIApiKey.Length > 8 ? 
                                   ConfigManager.OpenAIApiKey.Substring(ConfigManager.OpenAIApiKey.Length - 4) : "");
                txtApiKey.Text = maskedKey;
                lblStatus.Text = "API key loaded from config file";
            }
            else
            {
                txtApiKey.Text = "";
                lblStatus.Text = "No API key found in config file. Please enter your API key.";
            }
        }

        #region Windows Form Designer generated code
        private void InitializeComponent()
        {
            this.lblApiKey = new System.Windows.Forms.Label();
            this.txtApiKey = new System.Windows.Forms.TextBox();
            this.lblEndpoint = new System.Windows.Forms.Label();
            this.txtEndpoint = new System.Windows.Forms.TextBox();
            this.lblDeploymentName = new System.Windows.Forms.Label();
            this.txtDeploymentName = new System.Windows.Forms.TextBox();
            this.btnTest = new System.Windows.Forms.Button();
            this.btnSave = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.lblStatus = new System.Windows.Forms.Label();
            this.numTimeout = new System.Windows.Forms.NumericUpDown();
            this.lblTimeout = new System.Windows.Forms.Label();
            this.btnListDeployments = new System.Windows.Forms.Button();
            this.btnTestExact = new System.Windows.Forms.Button();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeout)).BeginInit();
            this.SuspendLayout();
            // 
            // lblApiKey
            // 
            this.lblApiKey.AutoSize = true;
            this.lblApiKey.Location = new System.Drawing.Point(12, 15);
            this.lblApiKey.Name = "lblApiKey";
            this.lblApiKey.Size = new System.Drawing.Size(123, 15);
            this.lblApiKey.TabIndex = 0;
            this.lblApiKey.Text = "Azure OpenAI API Key:";
            // 
            // txtApiKey
            // 
            this.txtApiKey.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtApiKey.Location = new System.Drawing.Point(154, 12);
            this.txtApiKey.Name = "txtApiKey";
            this.txtApiKey.PasswordChar = '*';
            this.txtApiKey.Size = new System.Drawing.Size(321, 23);
            this.txtApiKey.TabIndex = 1;
            // 
            // lblEndpoint
            // 
            this.lblEndpoint.AutoSize = true;
            this.lblEndpoint.Location = new System.Drawing.Point(12, 44);
            this.lblEndpoint.Name = "lblEndpoint";
            this.lblEndpoint.Size = new System.Drawing.Size(119, 15);
            this.lblEndpoint.TabIndex = 2;
            this.lblEndpoint.Text = "Azure API Endpoint:";
            // 
            // txtEndpoint
            // 
            this.txtEndpoint.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtEndpoint.Location = new System.Drawing.Point(154, 41);
            this.txtEndpoint.Name = "txtEndpoint";
            this.txtEndpoint.Size = new System.Drawing.Size(321, 23);
            this.txtEndpoint.TabIndex = 3;
            // 
            // lblDeploymentName
            // 
            this.lblDeploymentName.AutoSize = true;
            this.lblDeploymentName.Location = new System.Drawing.Point(12, 73);
            this.lblDeploymentName.Name = "lblDeploymentName";
            this.lblDeploymentName.Size = new System.Drawing.Size(136, 15);
            this.lblDeploymentName.TabIndex = 4;
            this.lblDeploymentName.Text = "Azure Deployment Name:";
            // 
            // txtDeploymentName
            // 
            this.txtDeploymentName.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtDeploymentName.Location = new System.Drawing.Point(154, 70);
            this.txtDeploymentName.Name = "txtDeploymentName";
            this.txtDeploymentName.Size = new System.Drawing.Size(321, 23);
            this.txtDeploymentName.TabIndex = 5;
            // 
            // btnTest
            // 
            this.btnTest.Location = new System.Drawing.Point(12, 110);
            this.btnTest.Name = "btnTest";
            this.btnTest.Size = new System.Drawing.Size(111, 27);
            this.btnTest.TabIndex = 6;
            this.btnTest.Text = "Test Connection";
            this.btnTest.UseVisualStyleBackColor = true;
            this.btnTest.Click += new System.EventHandler(this.btnTest_Click);
            // 
            // btnListDeployments
            // 
            this.btnListDeployments.Location = new System.Drawing.Point(12, 143);
            this.btnListDeployments.Name = "btnListDeployments";
            this.btnListDeployments.Size = new System.Drawing.Size(111, 27);
            this.btnListDeployments.TabIndex = 12;
            this.btnListDeployments.Text = "List Deployments";
            this.btnListDeployments.UseVisualStyleBackColor = true;
            this.btnListDeployments.Click += new System.EventHandler(this.btnListDeployments_Click);
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSave.Location = new System.Drawing.Point(320, 168);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(75, 27);
            this.btnSave.TabIndex = 7;
            this.btnSave.Text = "Save";
            this.btnSave.UseVisualStyleBackColor = true;
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Location = new System.Drawing.Point(400, 168);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 27);
            this.btnCancel.TabIndex = 8;
            this.btnCancel.Text = "Cancel";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // lblStatus
            // 
            this.lblStatus.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lblStatus.Location = new System.Drawing.Point(154, 110);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(321, 46);
            this.lblStatus.TabIndex = 9;
            // 
            // numTimeout
            // 
            this.numTimeout.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.numTimeout.Location = new System.Drawing.Point(400, 70);
            this.numTimeout.Name = "numTimeout";
            this.numTimeout.Size = new System.Drawing.Size(75, 23);
            this.numTimeout.TabIndex = 10;
            this.numTimeout.Value = new decimal(new int[] {
                100,
                0,
                0,
                0});
            // 
            // lblTimeout
            // 
            this.lblTimeout.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lblTimeout.AutoSize = true;
            this.lblTimeout.Location = new System.Drawing.Point(320, 73);
            this.lblTimeout.Name = "lblTimeout";
            this.lblTimeout.Size = new System.Drawing.Size(54, 15);
            this.lblTimeout.TabIndex = 11;
            this.lblTimeout.Text = "Timeout:";
            // 
            // btnTestExact
            // 
            this.btnTestExact.Location = new System.Drawing.Point(12, 176);
            this.btnTestExact.Name = "btnTestExact";
            this.btnTestExact.Size = new System.Drawing.Size(111, 27);
            this.btnTestExact.TabIndex = 13;
            this.btnTestExact.Text = "Test Exact URL";
            this.btnTestExact.UseVisualStyleBackColor = true;
            this.btnTestExact.Click += new System.EventHandler(this.btnTestExact_Click);
            // 
            // AzureConfigForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(487, 240);
            this.Controls.Add(this.btnTestExact);
            this.Controls.Add(this.btnListDeployments);
            this.Controls.Add(this.lblTimeout);
            this.Controls.Add(this.numTimeout);
            this.Controls.Add(this.lblStatus);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.btnTest);
            this.Controls.Add(this.txtDeploymentName);
            this.Controls.Add(this.lblDeploymentName);
            this.Controls.Add(this.txtEndpoint);
            this.Controls.Add(this.lblEndpoint);
            this.Controls.Add(this.txtApiKey);
            this.Controls.Add(this.lblApiKey);
            this.Name = "AzureConfigForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Azure OpenAI Configuration";
            ((System.ComponentModel.ISupportInitialize)(this.numTimeout)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();
        }
        #endregion

        private async void btnTest_Click(object sender, EventArgs e)
        {
            try
            {
                btnTest.Enabled = false;
                btnSave.Enabled = false;
                lblStatus.Text = "Testing connection...";
                
                // Always use API key from ConfigManager, not from the textbox
                var apiKey = ConfigManager.OpenAIApiKey;
                var endpoint = txtEndpoint.Text.Trim();
                var deploymentName = txtDeploymentName.Text.Trim();
                var timeout = (int)numTimeout.Value;
                
                if (string.IsNullOrWhiteSpace(apiKey))
                {
                    lblStatus.Text = "Error: API key not found in config file.";
                    MessageBox.Show("Please enter your API key in the text box and save it first.", "Missing API Key", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                if (string.IsNullOrWhiteSpace(endpoint) || string.IsNullOrWhiteSpace(deploymentName))
                {
                    lblStatus.Text = "Error: Please provide all required fields.";
                    return;
                }
                
                await TestAzureOpenAIConnection(apiKey, endpoint, deploymentName, timeout);
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Error: {ex.Message}";
            }
            finally
            {
                btnTest.Enabled = true;
                btnSave.Enabled = true;
            }
        }

        private async void btnListDeployments_Click(object sender, EventArgs e)
        {
            try
            {
                btnListDeployments.Enabled = false;
                btnSave.Enabled = false;
                lblStatus.Text = "Listing deployments...";

                var apiKey = ConfigManager.OpenAIApiKey;
                var endpoint = txtEndpoint.Text.Trim();
                var timeout = (int)numTimeout.Value;

                if (string.IsNullOrWhiteSpace(apiKey))
                {
                    lblStatus.Text = "Error: API key not found in config file.";
                    MessageBox.Show("Please enter your API key in the text box and save it first.", "Missing API Key", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(endpoint))
                {
                    lblStatus.Text = "Error: Please provide the endpoint URL.";
                    return;
                }

                await ListAvailableDeployments(apiKey, endpoint, timeout);
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Error: {ex.Message}";
            }
            finally
            {
                btnListDeployments.Enabled = true;
                btnSave.Enabled = true;
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            SaveConfig();
            DialogResult = DialogResult.OK;
            Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private async Task TestAzureOpenAIConnection(string apiKey, string endpoint, string deploymentName, int timeout)
        {
            // Ensure the endpoint is properly formatted
            if (!endpoint.StartsWith("https://", StringComparison.OrdinalIgnoreCase))
            {
                endpoint = $"https://{endpoint}.openai.azure.com/";
            }
            else if (!endpoint.EndsWith("/", StringComparison.OrdinalIgnoreCase))
            {
                endpoint += "/";
            }

            using var client = new HttpClient();
            client.DefaultRequestHeaders.Add("api-key", apiKey);
            client.Timeout = TimeSpan.FromSeconds(timeout);

            var testUrl = $"{endpoint}openai/deployments/{deploymentName}/chat/completions?api-version=2023-05-15";
            
            try
            {
                var content = new StringContent(
                    JsonSerializer.Serialize(new
                    {
                        messages = new[]
                        {
                            new { role = "user", content = "Say hi!" }
                        },
                        max_tokens = 10
                    }),
                    Encoding.UTF8,
                    "application/json"
                );

                var response = await client.PostAsync(testUrl, content);
                if (response.IsSuccessStatusCode)
                {
                    lblStatus.Text = "Connection test successful!";
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    lblStatus.Text = $"Error: {response.StatusCode} - {error}";
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Error: {ex.Message}";
            }
        }

        private async Task ListAvailableDeployments(string apiKey, string endpoint, int timeout)
        {
            // Ensure the endpoint is properly formatted
            if (!endpoint.StartsWith("https://", StringComparison.OrdinalIgnoreCase))
            {
                endpoint = $"https://{endpoint}.openai.azure.com/";
            }
            else if (!endpoint.EndsWith("/", StringComparison.OrdinalIgnoreCase))
            {
                endpoint += "/";
            }

            using var client = new HttpClient();
            client.DefaultRequestHeaders.Add("api-key", apiKey);
            client.Timeout = TimeSpan.FromSeconds(timeout);

            var listUrl = $"{endpoint}openai/deployments?api-version=2023-05-15";
            
            try
            {
                var response = await client.GetAsync(listUrl);
                if (response.IsSuccessStatusCode)
                {
                    var jsonResponse = await response.Content.ReadAsStringAsync();
                    var deployments = JsonSerializer.Deserialize<JsonDocument>(jsonResponse);
                    var deploymentList = deployments.RootElement.GetProperty("data");
                    
                    var sb = new StringBuilder("Available deployments:\n");
                    foreach (var deployment in deploymentList.EnumerateArray())
                    {
                        var id = deployment.GetProperty("id").GetString();
                        var model = deployment.GetProperty("model").GetString();
                        sb.AppendLine($"- {id} (Model: {model})");
                    }
                    
                    this.Invoke(new Action(() => {
                        lblStatus.Text = sb.ToString();
                    }));
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    this.Invoke(new Action(() => {
                        lblStatus.Text = $"Error: {response.StatusCode} - {error}";
                    }));
                }
            }
            catch (Exception ex)
            {
                this.Invoke(new Action(() => {
                    lblStatus.Text = $"Error: {ex.Message}";
                }));
            }
        }

        private void SaveConfig()
        {
            var endpoint = txtEndpoint.Text.Trim();
            var deploymentName = txtDeploymentName.Text.Trim();
            var timeout = (int)numTimeout.Value;

            // Save non-sensitive settings
            ConfigManager.AzureOpenAIEndpoint = endpoint;
            ConfigManager.AzureOpenAIDeploymentName = deploymentName;
            ConfigManager.AzureOpenAITimeoutSeconds = timeout;            // Save API key if it's a valid key and different from what's shown in the textbox
            // (we don't want to save the masked version)
            var apiKey = txtApiKey.Text.Trim();
            if (!string.IsNullOrWhiteSpace(apiKey) && !apiKey.Contains("..."))
            {
                ConfigManager.OpenAIApiKey = apiKey;
            }
        }

        private async void btnTestExact_Click(object sender, EventArgs e)
        {
            try
            {
                btnTestExact.Enabled = false;
                btnSave.Enabled = false;
                lblStatus.Text = "Testing exact URL connection...";
                
                var apiKey = ConfigManager.OpenAIApiKey;
                var endpoint = txtEndpoint.Text.Trim();
                var deploymentName = txtDeploymentName.Text.Trim();
                var timeout = (int)numTimeout.Value;
                
                if (string.IsNullOrWhiteSpace(apiKey))
                {
                    lblStatus.Text = "Error: API key not found in config file.";
                    MessageBox.Show("Please enter your API key in the text box and save it first.", "Missing API Key", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                if (string.IsNullOrWhiteSpace(endpoint) || string.IsNullOrWhiteSpace(deploymentName))
                {
                    lblStatus.Text = "Error: Please provide all required fields.";
                    return;
                }

                // Test the exact URL without any formatting
                string testUrl = endpoint;
                if (!testUrl.EndsWith("/"))
                {
                    testUrl += "/";
                }
                testUrl += $"openai/deployments/{deploymentName}/chat/completions?api-version=2023-05-15";

                using var client = new HttpClient();
                client.DefaultRequestHeaders.Add("api-key", apiKey);
                client.Timeout = TimeSpan.FromSeconds(timeout);

                var content = new StringContent(
                    JsonSerializer.Serialize(new
                    {
                        messages = new[]
                        {
                            new { role = "user", content = "Say hi!" }
                        },
                        max_tokens = 10
                    }),
                    Encoding.UTF8,
                    "application/json"
                );

                var response = await client.PostAsync(testUrl, content);
                if (response.IsSuccessStatusCode)
                {
                    lblStatus.Text = "Connection test successful with exact URL!";
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    lblStatus.Text = $"Error with exact URL: {response.StatusCode} - {error}";
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Error with exact URL: {ex.Message}";
            }
            finally
            {
                btnTestExact.Enabled = true;
                btnSave.Enabled = true;
            }
        }
    }
}
