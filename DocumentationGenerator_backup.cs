using Newtonsoft.Json.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;

namespace SnapAnalyser
{
    public partial class DocumentationGenerator
    {
        private readonly AIDescriptionGenerator _aiGenerator;
        private readonly PipelinePatternAnalyzer _patternAnalyzer;
        private bool _useAI;
        private HashSet<string> _processedSnapTypes = new HashSet<string>();
        private PipelineData _currentPipeline; // Stores the current pipeline being processed

        // Logging infrastructure
        private string _logPath;
        private bool _isLoggingEnabled = true; // Enable logging by default
        
        private void InitializeLogging()
        {
            // Create a marker file on the desktop to help locate the log
            string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
            string markerPath = Path.Combine(desktopPath, "SnapDocumenter_LogLocation.txt");
            
            try 
            {
                Console.WriteLine("Initializing logging...");
                
                // Get the solution directory (two levels up from bin/Debug)
                string baseDir = AppDomain.CurrentDomain.BaseDirectory;
                string solutionDir = Directory.GetParent(Directory.GetParent(Directory.GetParent(baseDir).FullName).FullName).FullName;
                string docsDir = Path.Combine(solutionDir, "Generated Documentation");
                
                Console.WriteLine($"Base directory: {baseDir}");
                Console.WriteLine($"Solution directory: {solutionDir}");
                Console.WriteLine($"Using documentation directory: {docsDir}");
                
                // Ensure the directory exists
                if (!Directory.Exists(docsDir))
                {
                    Console.WriteLine("Documentation directory doesn't exist, creating it...");
                    Directory.CreateDirectory(docsDir);
                    Console.WriteLine("Documentation directory created successfully");
                }
                else
                {
                    Console.WriteLine("Documentation directory already exists");
                }
                
                // Set the log path and test writing
                _logPath = Path.Combine(docsDir, "SnapDocumenter_MapperDebug.log");
                string testMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] Test write to log file{Environment.NewLine}";
                
                Console.WriteLine($"Attempting to write to: {_logPath}");
                File.AppendAllText(_logPath, testMessage);
                
                // Write the initialization message
                string initMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] Logging initialized. Log directory: {docsDir}{Environment.NewLine}";
                File.AppendAllText(_logPath, initMessage);
                
                Console.WriteLine($"Successfully initialized logging to: {_logPath}");
                
                // Write log location to desktop marker file
                File.WriteAllText(markerPath, $"SnapDocumenter log file is located at:\n{_logPath}");
                Console.WriteLine($"Log location written to: {markerPath}");
                
                LogToFile($"Application started. Process ID: {System.Diagnostics.Process.GetCurrentProcess().Id}");
                LogToFile($"Log file location: {_logPath}");
                LogToFile($"This log location has been written to: {markerPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Falling back to application directory. Error: {ex.Message}");
                
                // Fall back to application directory if Generated Documentation folder fails
                try 
                {
                    string appDir = AppDomain.CurrentDomain.BaseDirectory;
                    _logPath = Path.Combine(appDir, "SnapDocumenter_MapperDebug.log");
                    
                    Console.WriteLine($"Attempting to write to application directory: {_logPath}");
                    
                    string initMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] Failed to initialize logging in Generated Documentation folder. Using app directory: {appDir}{Environment.NewLine}";
                    File.WriteAllText(_logPath, initMessage);
                    File.AppendAllText(_logPath, $"Error: {ex}{Environment.NewLine}");
                    
                    Console.WriteLine($"Successfully initialized logging to application directory: {_logPath}");
                    
                    LogToFile($"Application started (fallback location). Process ID: {System.Diagnostics.Process.GetCurrentProcess().Id}");
                }
                catch (Exception ex2)
                {
                    Console.WriteLine($"Falling back to temp directory. Error: {ex2.Message}");
                    
                    // Last resort - try temp directory
                    try 
                    {
                        string tempPath = Path.GetTempPath();
                        _logPath = Path.Combine(tempPath, "SnapDocumenter_MapperDebug.log");
                        
                        Console.WriteLine($"Attempting to write to temp directory: {_logPath}");
                        
                        string errorMsg = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] Failed to create log in app directory. Using temp directory: {_logPath}{Environment.NewLine}";
                        File.AppendAllText(_logPath, errorMsg);
                        File.AppendAllText(_logPath, $"Original Error: {ex}{Environment.NewLine}");
                        File.AppendAllText(_logPath, $"Secondary Error: {ex2}{Environment.NewLine}");
                        
                        Console.WriteLine($"Successfully initialized logging to temp directory: {_logPath}");
                    }
                    catch (Exception ex3)
                    {
                        Console.WriteLine($"CRITICAL: Failed to initialize logging in any location. Final error: {ex3.Message}");
                        _logPath = null;
                    }
                }
            }
        }
        
        private void LogToFile(string message)
        {
            if (string.IsNullOrEmpty(_logPath) || !_isLoggingEnabled) return;
            
            try 
            {
                File.AppendAllText(_logPath, $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] {message}{Environment.NewLine}");
            }
            catch
            {
                // Silently fail - we can't log the error if logging fails
            }
        }

        // Property to check if AI capability is available
        public bool HasAICapability => _aiGenerator != null;
        public DocumentationGenerator(AIDescriptionGenerator aiGenerator = null)
        {
            _aiGenerator = aiGenerator;
            _patternAnalyzer = new PipelinePatternAnalyzer();
            _useAI = _aiGenerator != null;
            InitializeLogging();
            LogToFile("DocumentationGenerator initialized");
        }

        // Enable logging for detailed trace information
        public void EnableLogging(string logFilePath)
        {
            _logPath = logFilePath;
            _isLoggingEnabled = true;
        }

        // Log a message with timestamp
        private void LogMessage(string message)
        {
            if (!_isLoggingEnabled || string.IsNullOrEmpty(_logPath))
                return;

            try            {
                string logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}";
                File.AppendAllText(_logPath, logEntry + Environment.NewLine);
            }
            catch
            {
                // Ignore logging errors to prevent affecting main functionality
            }
        }
        
        /// <summary>
        /// Reset the processed snap types when generating a new pipeline documentation
        /// </summary>
        private void ResetProcessedSnapTypes()
        {
            Console.WriteLine("Resetting processed snap types for new pipeline documentation");
            _processedSnapTypes.Clear();
        }
          /// <summary>
        /// Helper method to convert markdown to HTML with improved handling for markdown elements and placeholder protection
        /// </summary>
        private string ConvertMarkdownToHtml(string markdown)
        {
            if (string.IsNullOrEmpty(markdown))
                return string.Empty;

            // FIRST: Extract and protect ALL placeholder patterns before any processing
            var placeholderProtection = new Dictionary<string, string>();
            var placeholderCounter = 0;
            string text = markdown;
            
            // Extract all placeholder formats before any other processing
            text = Regex.Replace(text, @"##INLINE_?CODE_?([a-zA-Z0-9]+)##", match =>
            {
                var protectionKey = $"PLACEHOLDER_PROTECTION_{placeholderCounter++}";
                placeholderProtection[protectionKey] = match.Value;
                return protectionKey;
            });
            
            // Also extract any already-corrupted placeholders with HTML tags
            text = Regex.Replace(text, @"##INLINE<[^>]*>CODE<[^>]*>([a-zA-Z0-9]+)##", match =>
            {
                string id = match.Groups[1].Value;
                string cleanPlaceholder = $"##INLINECODE{id}##";
                var protectionKey = $"PLACEHOLDER_PROTECTION_{placeholderCounter++}";
                placeholderProtection[protectionKey] = cleanPlaceholder;
                return protectionKey;
            });

            // Check if the content is already HTML (starting with any HTML tag)
            if (Regex.IsMatch(text, @"^\s*<([a-z][a-z0-9]*)\b[^>]*>"))
            {
                // Content is already in HTML format, restore placeholders and return
                foreach (var placeholder in placeholderProtection)
                {
                    text = text.Replace(placeholder.Key, placeholder.Value);
                }
                return text;
            }
            
            // Check if content is already HTML (containing HTML tags) - if so, don't process as markdown
            if (Regex.IsMatch(text, @"<([a-z][a-z0-9]*)\b[^>]*>.*?</\1>|<([a-z][a-z0-9]*)\b[^>]*/>", RegexOptions.Singleline))
            {
                // Content appears to contain HTML tags, restore placeholders and return
                foreach (var placeholder in placeholderProtection)
                {
                    text = text.Replace(placeholder.Key, placeholder.Value);
                }
                return text;
            }

            // HTML encode the text to prevent injection, but preserve protected placeholders
            text = System.Net.WebUtility.HtmlEncode(text);
            
            // Pre-process code blocks to protect them from other replacements
            var codeBlocks = new Dictionary<string, string>();
            
            // Extract and save code blocks
            text = Regex.Replace(text, @"```(.+?)```", match =>
            {
                var placeholder = $"CODE_BLOCK_{placeholderCounter++}";
                codeBlocks[placeholder] = match.Groups[1].Value;
                return placeholder;
            }, RegexOptions.Singleline);

            // Handle paragraphs (ensure double newlines become paragraph breaks)
            var paragraphs = Regex.Split(text, @"\r\n\r\n|\n\n");

            // Process each paragraph
            for (int i = 0; i < paragraphs.Length; i++)
            {
                var paragraph = paragraphs[i];
                
                // Handle headings - must be done at the start of a paragraph
                if (Regex.IsMatch(paragraph, @"^#{1,6} "))
                {
                    // h1-h3 get mapped to h3-h5 to maintain hierarchy in the document
                    paragraph = Regex.Replace(paragraph, @"^# (.+?)$", "<h3>$1</h3>", RegexOptions.Multiline);
                    paragraph = Regex.Replace(paragraph, @"^## (.+?)$", "<h4>$1</h4>", RegexOptions.Multiline);
                    paragraph = Regex.Replace(paragraph, @"^### (.+?)$", "<h5>$1</h5>", RegexOptions.Multiline);
                    paragraph = Regex.Replace(paragraph, @"^#{4,6} (.+?)$", "<h6>$1</h6>", RegexOptions.Multiline);
                }
                // Handle unordered lists
                else if (Regex.IsMatch(paragraph, @"^\s*[-*+]\s+.*$", RegexOptions.Multiline))
                {
                    var listItems = Regex.Split(paragraph, @"\r\n|\n").Where(line => !string.IsNullOrWhiteSpace(line));
                    var listContent = new StringBuilder("<ul>");

                    foreach (var item in listItems)
                    {
                        var listItem = Regex.Replace(item, @"^\s*[-*+]\s+(.+)$", "$1");
                        listItem = ProcessInlineFormatting(listItem, placeholderProtection);
                        listContent.AppendLine($"<li>{listItem}</li>");
                    }

                    listContent.Append("</ul>");
                    paragraph = listContent.ToString();
                }
                // Handle ordered lists
                else if (Regex.IsMatch(paragraph, @"^\s*\d+\.\s+.*$", RegexOptions.Multiline))
                {
                    var listItems = Regex.Split(paragraph, @"\r\n|\n").Where(line => !string.IsNullOrWhiteSpace(line));
                    var listContent = new StringBuilder("<ol>");

                    foreach (var item in listItems)
                    {
                        var listItem = Regex.Replace(item, @"^\s*\d+\.\s+(.+)$", "$1");
                        listItem = ProcessInlineFormatting(listItem, placeholderProtection);
                        listContent.AppendLine($"<li>{listItem}</li>");
                    }

                    listContent.Append("</ol>");
                    paragraph = listContent.ToString();
                }
                // Regular paragraph
                else if (!paragraph.StartsWith("<h") && !paragraph.StartsWith("<ul") && !paragraph.StartsWith("<ol") && !Regex.IsMatch(paragraph, @"<h[1-6][^>]*>"))
                {
                    // Process inline formatting
                    paragraph = ProcessInlineFormatting(paragraph, placeholderProtection);
                    
                    // Handle soft breaks (single newlines within paragraphs)
                    paragraph = paragraph.Replace("\r\n", "<br/>").Replace("\n", "<br/>");

                    // Wrap in paragraph tags
                    paragraph = $"<p>{paragraph}</p>";
                }

                paragraphs[i] = paragraph;
            }

            // Join the processed paragraphs
            text = string.Join("\n", paragraphs);
            
            // Restore code blocks with proper HTML
            foreach (var codeBlock in codeBlocks)
            {
                if (codeBlock.Key.StartsWith("CODE_BLOCK_"))
                {
                    text = text.Replace(codeBlock.Key, $"<pre><code>{codeBlock.Value}</code></pre>");
                }
            }
            
            // Restore protected placeholders
            foreach (var placeholder in placeholderProtection)
            {
                text = text.Replace(placeholder.Key, placeholder.Value);
            }

            return text;
        }

        private string ProcessInlineFormatting(string text, Dictionary<string, string> placeholderProtection)
        {
            // Handle inline code first to protect it from other formatting
            text = Regex.Replace(text, @"`([^`]+?)`", "<code>$1</code>");

            // Handle bold text - process these before italic to handle nested formatting
            text = Regex.Replace(text, @"\*\*(.*?)\*\*", "<strong>$1</strong>");
            
            // Handle bold with underscores - BUT protect placeholders first
            // Only apply underscore bold formatting if it's NOT part of a placeholder
            text = Regex.Replace(text, @"(?<!##[A-Z]*[_]?)__(.+?)__(?![A-Z0-9]*##)", "<strong>$1</strong>");

            // Handle italic text - use a more specific pattern to avoid greedy matching
            text = Regex.Replace(text, @"\*([^*\r\n]*?)\*", "<em>$1</em>");
            
            // Handle italic with underscores - BUT protect placeholders
            // Only apply underscore italic formatting if it's NOT part of a placeholder
            text = Regex.Replace(text, @"(?<!##[A-Z]*[_]?)_([^_\r\n]*?)_(?![A-Z0-9]*##)", "<em>$1</em>");

            // Handle links
            text = Regex.Replace(text, @"\[([^\]]+?)\]\(([^\)]+?)\)", "<a href=\"$2\">$1</a>");

            return text;
        }/// <summary>
        /// Replaces all placeholders in a text with their actual values
        /// </summary>
        /// <param name="text">The text containing placeholders</param>
        /// <param name="codeBlocks">Dictionary containing the code block mappings</param>
        /// <returns>Text with placeholders replaced</returns>
        private string ReplaceAllPlaceholders(string text, Dictionary<string, string> codeBlocks = null)
        {
            if (string.IsNullOrEmpty(text))
                return text;
                
            // If no codeBlocks provided, create empty dictionary to avoid null reference
            if (codeBlocks == null)
                codeBlocks = new Dictionary<string, string>();
                
            // Process both placeholder formats
            // 1. Standard format with underscore: ##INLINE_CODE_xxxxxxxx##
            text = Regex.Replace(text, @"##INLINE_CODE_([a-zA-Z0-9]+)##", match =>
            {
                string id = match.Groups[1].Value;
                string currentPlaceholder = match.Value;
                
                // If we have this placeholder in our dictionary, replace it
                if (codeBlocks.ContainsKey(currentPlaceholder))
                {
                    return $"<code>{codeBlocks[currentPlaceholder]}</code>";
                }
                
                // For unmatched placeholders, try to provide intelligent fallback
                var fallback = GetFallbackForOrphanedPlaceholder(id, text);
                return $"<code class=\"inferred-placeholder\">{fallback}</code>";
            });
            
            // 2. Format without underscore: ##INLINECODExxxxxxxx##
            text = Regex.Replace(text, @"##INLINECODE([a-zA-Z0-9]+)##", match =>
            {
                string id = match.Groups[1].Value;
                string alternateFormat = $"##INLINE_CODE_{id}##";
                
                // Check if we have the alternate format in our dictionary
                if (codeBlocks.ContainsKey(alternateFormat))
                {
                    return $"<code>{codeBlocks[alternateFormat]}</code>";
                }
                
                // For unmatched placeholders, try to provide intelligent context-aware fallback
                var fallback = GetContextAwareFallback(id, text);
                return $"<code class=\"inferred-placeholder\">{fallback}</code>";
            });
            
            return text;
        }        /// <summary>
        /// Provides context-aware fallbacks for AI-generated placeholders based on surrounding text
        /// </summary>
        /// <param name="id">The placeholder ID</param>
        /// <param name="context">The surrounding text context</param>
        /// <returns>A meaningful replacement value</returns>
        private string GetContextAwareFallback(string id, string context)
        {
            string contextLower = context.ToLower();
            
            // Router-specific logic with more detailed analysis
            if (contextLower.Contains("router") || contextLower.Contains("route"))
            {
                // Look for specific patterns in the context
                if (contextLower.Contains("presence of the") && contextLower.Contains("field"))
                    return "field_name";
                if (contextLower.Contains("is not null"))
                    return "field_value";
                if (contextLower.Contains("is null"))
                    return "field_value";
                if (contextLower.Contains("sent to output") || contextLower.Contains("to output"))
                    return "output_view";
                if (contextLower.Contains("property is set to"))
                    return "routing_mode";
                if (contextLower.Contains("validate") && contextLower.Contains("execute"))
                    return "Validate & Execute";
                if (contextLower.Contains("execution mode"))
                    return "execution_mode";
                
                // General router context
                if (contextLower.Contains("sent to") || contextLower.Contains("output"))
                    return "output_view";
                if (contextLower.Contains("property") && contextLower.Contains("set"))
                    return "routing_property";
                if (contextLower.Contains("condition"))
                    return "condition_expr";
            }
            
            // Join-specific logic
            if (contextLower.Contains("join"))
            {
                if (contextLower.Contains("key") || contextLower.Contains("field"))
                    return "join_key";
                if (contextLower.Contains("condition"))
                    return "join_condition";
            }
            
            // Look for specific output patterns
            if (contextLower.Contains("output0") || contextLower.Contains("output1"))
                return "output_view";
            
            // Look for mode/execution patterns
            if (contextLower.Contains("mode") || contextLower.Contains("execution"))
                return "execution_mode";
            
            // General field/property logic
            if (contextLower.Contains("field") && (contextLower.Contains("name") || contextLower.Contains("presence")))
                return "field_name";
            if (contextLower.Contains("value") || contextLower.Contains("data"))
                return "field_value";
            if (contextLower.Contains("property"))
                return "property_name";
            if (contextLower.Contains("output") || contextLower.Contains("view"))
                return "output_view";
            if (contextLower.Contains("input"))
                return "input_view";
            if (contextLower.Contains("path"))
                return "output_path";
            if (contextLower.Contains("condition") || contextLower.Contains("expression"))
                return "condition";
                
            // Use the original GetFallbackForOrphanedPlaceholder as final fallback
            return GetFallbackForOrphanedPlaceholder(id, context);
        }

        public async Task<string> GenerateHtmlDocumentationAsync(PipelineData pipeline, string diagramSvg, CancellationToken cancellationToken = default)
        {
            return await GenerateHtmlDocumentationAsync(pipeline, diagramSvg, null, cancellationToken).ConfigureAwait(false);
        }

        public async Task<string> GenerateHtmlDocumentationAsync(PipelineData pipeline, string diagramSvg, ProjectData project, CancellationToken cancellationToken = default)
        {
            LogMessage($"[HTML-GEN] Starting HTML generation for pipeline: {pipeline.Name}");
            LogMessage($"[HTML-GEN] Project context available: {project != null}");

            // Reset the tracking of processed snap types to ensure a fresh start for each pipeline
            ResetProcessedSnapTypes();

            // Set the current pipeline for use in other methods
            _currentPipeline = pipeline;

            var html = new StringBuilder();

            // Add AI-generated overview if available
            string aiPipelineDescription = "";
            if (_useAI)
            {                try
                {
                    LogMessage($"[HTML-GEN] Calling AI generator for pipeline description");
                    aiPipelineDescription = await _aiGenerator.GeneratePipelineDescription(pipeline, cancellationToken).ConfigureAwait(false);
                    LogMessage($"[HTML-GEN] AI description generated, length: {aiPipelineDescription?.Length ?? 0} characters");
                }
                catch (Exception ex)
                {
                    LogMessage($"[HTML-GEN] AI description generation failed: {ex.Message}");
                    aiPipelineDescription = $"AI description generation failed: {ex.Message}";
                }
            }

            // HTML header
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html lang=\"en\">");
            html.AppendLine("<head>");
            html.AppendLine("  <meta charset=\"UTF-8\">");
            html.AppendLine("  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">");
            html.AppendLine($"  <title>{pipeline.Name} - SnapLogic Pipeline Documentation</title>"); html.AppendLine("  <style>");            html.AppendLine("    body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; color: #333; }");
            html.AppendLine("    .container { max-width: none; width: 100%; margin: 0; padding: 0 20px; }");
            html.AppendLine("    h1 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }");
            html.AppendLine("    h2 { color: #2c3e50; margin-top: 30px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }");
            html.AppendLine("    h3 { color: #3498db; }");
            html.AppendLine("    h4 { color: #3498db; margin-top: 20px; margin-bottom: 10px; }"); html.AppendLine("    .section { margin-bottom: 30px; }");
            html.AppendLine("    table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }");
            html.AppendLine("    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
            html.AppendLine("    th { background-color: #f2f2f2; }");            html.AppendLine("    tr:nth-child(even) { background-color: #f9f9f9; }"); html.AppendLine("    .diagram-container { overflow: auto; border: 1px solid #ddd; margin: 20px 0; padding: 10px; text-align: left; width: 100%; display: block; }");
            html.AppendLine("    .diagram-container svg { display: block; margin: 0; position: relative; }");
            html.AppendLine("    .snap-category { display: inline-block; width: 16px; height: 16px; margin-right: 5px; vertical-align: middle; border: 1px solid #333; }");
            html.AppendLine("    .flow-control { background-color: #ffffcc; }");
            html.AppendLine("    .transformation { background-color: #ccffcc; }");
            html.AppendLine("    .database { background-color: #ccccff; }");
            html.AppendLine("    .external-system { background-color: #ffcccc; }");
            html.AppendLine("    .file-operation { background-color: #ffccff; }");
            html.AppendLine("    .error-handling { background-color: #ffdddd; }");
            html.AppendLine("    .toc { background-color: #f5f5f5; padding: 15px; margin-bottom: 20px; border-radius: 5px; }");
            html.AppendLine("    .toc ul { list-style-type: none; padding-left: 20px; }");
            html.AppendLine("    .toc a { text-decoration: none; color: #3498db; }");
            html.AppendLine("    .toc a:hover { text-decoration: underline; }");
            html.AppendLine("    .ai-description { background-color: #f8f9fa; border-left: 4px solid #3498db; padding: 15px; margin: 15px 0; }");
            html.AppendLine("    .function-details { background-color: #f0f7fb; border-left: 5px solid #72b7e5; padding: 10px 15px; margin: 5px 0; line-height: 1.6; }");
            html.AppendLine("    .snap-details { background-color: #f9f9f9; }");
            html.AppendLine("    .property-list { margin: 0; padding: 0; list-style-type: none; }");
            html.AppendLine("    .property-list li { margin-bottom: 5px; }");
            html.AppendLine("    .sql-preview { font-family: monospace; background-color: #f5f5f5; padding: 2px 4px; border-radius: 3px; }"); html.AppendLine("    .category-description { background-color: #f9f9f9; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px; }");
            html.AppendLine("    .snap-count { font-weight: bold; }");
            html.AppendLine("    .with-columns { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }"); html.AppendLine("    .code-sample { background-color: #f5f5f5; padding: 10px; border-left: 4px solid #72b7e5; font-family: Consolas, Monaco, 'Courier New', monospace; white-space: pre; font-size: 13px; overflow-x: auto; line-height: 1.4; color: #333; }");
            html.AppendLine("    pre { margin: 0; }");
            html.AppendLine("    .technical-details { background-color: #f0f7fb; border-left: 5px solid #3498db; padding: 10px; margin: 10px 0; }");
            html.AppendLine("    .pattern-details { background-color: #f6f6f6; border-left: 3px solid #72b7e5; padding: 8px; margin: 8px 0; }"); html.AppendLine("    .category-overview { margin-bottom: 15px; }");            html.AppendLine("    .code-sample .comment { color: #008000; }");
            html.AppendLine("    .code-sample .keyword { color: #0000ff; }");
            html.AppendLine("    .code-sample .string { color: #a31515; }");
            html.AppendLine("    .code-sample .number { color: #09885a; }");
            // CSS for placeholder handling
            html.AppendLine("    .missing-placeholder { background-color: #ffebee; color: #c62828; border: 1px solid #f8bbd9; }");
            html.AppendLine("    .inferred-placeholder { background-color: #fff3e0; color: #ef6c00; border: 1px solid #ffcc02; }");
            html.AppendLine("    .code-sample .function { color: #795e26; }"); html.AppendLine("    .code-example { margin: 15px 0; border: 1px solid #ddd; border-radius: 4px; overflow: hidden; }");
            html.AppendLine("    .code-example .code-title { background-color: #f8f8f8; padding: 5px 10px; border-bottom: 1px solid #ddd; font-weight: bold; }");
            html.AppendLine("    .code-example .code-content { padding: 0; }");
            html.AppendLine("    .best-practices { background-color: #f9fff9; border: 1px solid #d4e8d4; border-radius: 4px; margin-top: 15px; padding: 10px; }");
            html.AppendLine("    .best-practices h5 { color: #2e7d32; margin-top: 0; margin-bottom: 8px; }");
            html.AppendLine("    .best-practices ul { margin-top: 0; padding-left: 20px; }");
            html.AppendLine("    .best-practices code { background: #f0f0f0; padding: 1px 4px; border-radius: 3px; font-size: 90%; }");
            html.AppendLine("    .collapsible { cursor: pointer; padding: 10px; border: none; text-align: left; outline: none; width: 100%; }");
            html.AppendLine("    .collapsible:after { content: '\\002B'; float: right; }");
            html.AppendLine("    .active:after { content: '\\2212'; }");
            html.AppendLine("    .collapsible-content { max-height: 0; overflow: hidden; transition: max-height 0.2s ease-out; }");
            html.AppendLine("    .pattern-container { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }");
            html.AppendLine("    .pattern-card { background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 15px; margin-bottom: 20px; }");
            html.AppendLine("    .pattern-card h3 { color: #0056b3; margin-top: 0; }");
            html.AppendLine("    .pattern-card p { margin-bottom: 8px; }"); html.AppendLine("    .pattern-card ul { margin-top: 0; padding-left: 20px; }");
            html.AppendLine("    @media (max-width: 800px) { .pattern-container { grid-template-columns: 1fr; } }");
            
            html.AppendLine("    .pattern-analysis { margin: 20px 0; border: 1px solid #e9ecef; border-radius: 4px; padding: 20px; background-color: #f8f9fa; }");
            html.AppendLine("    .detected-pattern { margin-bottom: 25px; border-bottom: 1px solid #e9ecef; padding-bottom: 15px; }");
            html.AppendLine("    .detected-pattern:last-child { border-bottom: none; }");
            html.AppendLine("    .confidence-meter { margin: 10px 0; }");
            html.AppendLine("    .confidence-label { margin-bottom: 5px; font-weight: bold; }");
            html.AppendLine("    .confidence-bar { height: 15px; background-color: #e9ecef; border-radius: 10px; overflow: hidden; }");
            html.AppendLine("    .confidence-fill { height: 100%; background-color: #4caf50; transition: width 0.5s ease-in-out; }");
            html.AppendLine("    .confidence-meter-small { display: inline-block; width: 100px; height: 8px; background-color: #e9ecef; border-radius: 4px; overflow: hidden; vertical-align: middle; margin-left: 8px; }");
            html.AppendLine("    .pattern-features, .pattern-missing { margin-top: 10px; }");            html.AppendLine("    .pattern-missing li { color: #856404; }");
            html.AppendLine("    .pattern-analysis-overview { background-color: #f0f7fb; border-left: 5px solid #3498db; padding: 10px 15px; margin: 15px 0; }");
            html.AppendLine("    .pattern-analysis-list { list-style-type: none; padding-left: 0; }");            html.AppendLine("    .pattern-analysis-list li { margin-bottom: 8px; }");            // Add cytoscape.js specific styles            html.AppendLine("    .cytoscape-container { margin: 0; text-align: left; border: none; padding: 0; border-radius: 0; width: 100%; max-width: none; display: block; }");
            html.AppendLine("    .cytoscape-title { color: #2c3e50; margin-bottom: 15px; font-size: 18px; font-weight: bold; text-align: left; }");
            html.AppendLine("    .cytoscape-diagram { min-height: 500px; height: 600px; width: 100%; margin: 0; display: block; position: relative; }");
            
            // Snap List CSS Styles
            html.AppendLine("    /* Snap List Styles */");
            html.AppendLine("    .snap-list-controls { margin: 15px 0; text-align: center; }");
            html.AppendLine("    .snap-list-toggle { background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 8px 16px; margin: 0 5px; cursor: pointer; border-radius: 4px; font-size: 14px; transition: all 0.2s; }");
            html.AppendLine("    .snap-list-toggle:hover { background-color: #e9ecef; }");
            html.AppendLine("    .snap-list-toggle.active { background-color: #3498db; color: white; border-color: #3498db; }");
            html.AppendLine("    .snap-list-view { margin: 20px 0; }");
            html.AppendLine("    .snap-flow-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 10px; margin: 15px 0; }");
            html.AppendLine("    .snap-flow-item { display: flex; align-items: center; padding: 8px 12px; background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; transition: background-color 0.2s; }");
            html.AppendLine("    .snap-flow-item:hover { background-color: #e9ecef; }");
            html.AppendLine("    .snap-number { background-color: #6c757d; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; margin-right: 10px; flex-shrink: 0; }");
            html.AppendLine("    .snap-link { color: #3498db; text-decoration: none; font-weight: 500; margin-left: 5px; margin-right: 8px; }");
            html.AppendLine("    .snap-link:hover { text-decoration: underline; }");
            html.AppendLine("    .snap-type { color: #6c757d; font-size: 12px; font-style: italic; }");
            html.AppendLine("    .snap-category-group { margin-bottom: 25px; }");
            html.AppendLine("    .snap-category-group h4 { display: flex; align-items: center; margin-bottom: 10px; color: #2c3e50; }");
            html.AppendLine("    .snap-category-items { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 8px; margin-left: 20px; }");
            html.AppendLine("    .snap-category-item { display: flex; align-items: center; justify-content: space-between; padding: 6px 10px; background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 3px; transition: background-color 0.2s; }");
            html.AppendLine("    .snap-category-item:hover { background-color: #e9ecef; }");
            html.AppendLine("    @media (max-width: 768px) { .snap-flow-list, .snap-category-items { grid-template-columns: 1fr; } }");
            
            html.AppendLine("  </style>");

            html.AppendLine("  <script>");
            html.AppendLine("    document.addEventListener('DOMContentLoaded', function() {");
            html.AppendLine("      // Animate confidence meters");
            html.AppendLine("      const confidenceMeters = document.querySelectorAll('.confidence-fill');");
            html.AppendLine("      setTimeout(() => {");
            html.AppendLine("        confidenceMeters.forEach(meter => {");
            html.AppendLine("          const width = meter.getAttribute('style').replace('width: ', '').replace('%;', '');");
            html.AppendLine("          meter.style.width = '0%';");
            html.AppendLine("          setTimeout(() => { meter.style.width = width + '%'; }, 100);");
            html.AppendLine("        });");
            html.AppendLine("      }, 300);");
            html.AppendLine("      ");
            html.AppendLine("      // Add collapsible functionality");
            html.AppendLine("      const collapsibles = document.getElementsByClassName('collapsible');");
            html.AppendLine("      for (let i = 0; i < collapsibles.length; i++) {");
            html.AppendLine("        collapsibles[i].addEventListener('click', function() {");
            html.AppendLine("          this.classList.toggle('active');");
            html.AppendLine("          const content = this.nextElementSibling;");
            html.AppendLine("          if (content.style.maxHeight) {");
            html.AppendLine("            content.style.maxHeight = null;");
            html.AppendLine("          } else {");
            html.AppendLine("            content.style.maxHeight = content.scrollHeight + 'px';");
            html.AppendLine("          }");            html.AppendLine("        });");            html.AppendLine("      }");            html.AppendLine("    });");
            html.AppendLine("  </script>");
            
            html.AppendLine("</head>");
            html.AppendLine("<body>");
            html.AppendLine("  <div class=\"container\">");

            // Header
            html.AppendLine($"    <h1>{pipeline.Name} - SnapLogic Pipeline Documentation</h1>");
            html.AppendLine($"    <p><strong>Author:</strong> {pipeline.Author}</p>");
            html.AppendLine($"    <p><strong>Generated:</strong> {DateTime.Now}</p>");
            // Table of Contents
            html.AppendLine("    <div class=\"toc\">");
            html.AppendLine("      <h2>Table of Contents</h2>");
            html.AppendLine("      <ul>"); html.AppendLine("        <li><a href=\"#overview\">Pipeline Overview</a>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><a href=\"#overview\">Architecture & Purpose</a></li>");
            html.AppendLine("            <li><a href=\"#overview\">Pipeline Functions</a></li>");
            html.AppendLine("            <li><a href=\"#overview\">SnapLogic Concepts</a></li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </li>"); html.AppendLine("        <li><a href=\"#glossary\">Technical Glossary</a></li>");
            html.AppendLine("        <li><a href=\"#flow-diagram\">Flow Diagram</a></li>");
            html.AppendLine("        <li><a href=\"#snap-list\">Snap List</a></li>");
            html.AppendLine("        <li><a href=\"#enhanced-flow-control\">Enhanced Flow Control Diagrams</a></li>");
            html.AppendLine("        <li><a href=\"#parameters\">Pipeline Parameters</a></li>");
            html.AppendLine("        <li><a href=\"#snaps\">Pipeline Snaps</a>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><a href=\"#flow-control\">Flow Control</a></li>");
            html.AppendLine("            <li><a href=\"#transformations\">Transformations</a></li>");
            html.AppendLine("            <li><a href=\"#database\">Database Operations</a></li>");
            html.AppendLine("            <li><a href=\"#external\">External Systems</a></li>");
            html.AppendLine("            <li><a href=\"#file\">File Operations</a></li>");
            html.AppendLine("            <li><a href=\"#error\">Error Handling</a></li>");
            html.AppendLine("            <li><a href=\"#other\">Other Snaps</a></li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </li>"); html.AppendLine("        <li><a href=\"#best-practices\">Pipeline Best Practices</a>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><a href=\"#best-practices\">General Pipeline Design</a></li>");
            html.AppendLine("            <li><a href=\"#best-practices\">Performance Optimization</a></li>");
            html.AppendLine("            <li><a href=\"#best-practices\">Security Considerations</a></li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </li>");
            html.AppendLine("        <li><a href=\"#patterns\">Integration Patterns</a>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><a href=\"#patterns\">ETL Pattern</a></li>");
            html.AppendLine("            <li><a href=\"#patterns\">API Integration</a></li>");
            html.AppendLine("            <li><a href=\"#patterns\">Data Synchronization</a></li>");
            html.AppendLine("            <li><a href=\"#patterns\">Event Processing</a></li>");
            html.AppendLine("            <li><a href=\"#patterns\">Data Validation</a></li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </li>");
            html.AppendLine("        <li><a href=\"#execution-flow\">Execution Flow</a>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><a href=\"#execution-flow\">Pipeline Function Summary</a></li>");
            html.AppendLine("            <li><a href=\"#execution-flow\">Pipeline Entry Points</a></li>");
            html.AppendLine("            <li><a href=\"#execution-flow\">Main Execution Paths</a></li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </li>");
            html.AppendLine("      </ul>");
            html.AppendLine("    </div>");
            // Overview Section
            html.AppendLine("    <div class=\"section\" id=\"overview\">");
            html.AppendLine("      <h2>Pipeline Overview</h2>");

            // Add AI-generated description ONLY if AI was selected (_useAI is true) and we have a description            if (_useAI && !string.IsNullOrEmpty(aiPipelineDescription))
            {
                html.AppendLine("      <div class=\"ai-description\">");
                html.AppendLine("        <h3>AI-Generated Description</h3>");
                html.AppendLine($"        <div>{ConvertMarkdownToHtml(aiPipelineDescription)}</div>");
                html.AppendLine("      </div>");
            }

            html.AppendLine($"      <p>This document provides comprehensive documentation for the SnapLogic pipeline <strong>{pipeline.Name}</strong>, " +
                           $"authored by <strong>{pipeline.Author}</strong>. A SnapLogic pipeline is an integration workflow that processes, transforms, " +
                           "and moves data between different systems using pre-built connectors called \"snaps\".</p>");

            // Add information about enhanced documentation when AI is enabled
            if (_useAI)
            {
                html.AppendLine("      <div class=\"technical-details\">");
                html.AppendLine("        <h3>Enhanced Documentation Features</h3>");
                html.AppendLine("        <p>This documentation includes AI-enhanced content to improve understanding:</p>");
                html.AppendLine("        <ul>");
                html.AppendLine("          <li><strong>Enhanced Snap Descriptions</strong>: Detailed technical explanations of snap functionality</li>");
                html.AppendLine("          <li><strong>Pseudocode Implementation</strong>: Readable representations of snap logic to understand data flow</li>");
                html.AppendLine("          <li><strong>AI-Generated Overview</strong>: Business-focused description of the pipeline's purpose</li>");
                html.AppendLine("        </ul>");
                html.AppendLine("      </div>");
            }

            // Add pipeline architecture insights
            html.AppendLine("      <div class=\"category-description\">");
            html.AppendLine("        <h3>Pipeline Architecture</h3>");

            // Determine if this is an ETL, data synchronization, or API pipeline
            bool hasExternalDataSource = pipeline.Snaps.Any(s => s.Category == SnapCategory.ExternalSystem);
            bool hasDbOperations = pipeline.Snaps.Any(s => s.Category == SnapCategory.Database);
            bool hasTransformations = pipeline.Snaps.Any(s => s.Category == SnapCategory.Transformation);
            bool hasFileOperations = pipeline.Snaps.Any(s => s.Category == SnapCategory.FileOperation);
            bool hasErrorHandlers = pipeline.Snaps.Any(s => s.Category == SnapCategory.ErrorHandling);

            // Pipeline complexity assessment
            int totalSnaps = pipeline.Snaps.Count;
            string complexity = totalSnaps < 5 ? "Simple" : (totalSnaps < 15 ? "Moderate" : "Complex");

            html.AppendLine($"        <p><strong>Pipeline Complexity:</strong> {complexity} ({totalSnaps} snaps)</p>");

            // Architecture pattern identification
            html.AppendLine("        <p><strong>Architecture Pattern:</strong> ");
            if (hasExternalDataSource && hasDbOperations && hasTransformations)
            {
                html.AppendLine("ETL (Extract, Transform, Load) - This pipeline extracts data from external systems, transforms it, and loads it into a database.</p>");
            }
            else if (hasExternalDataSource && hasExternalDataSource && hasTransformations)
            {
                html.AppendLine("System Integration - This pipeline facilitates data exchange between different external systems.</p>");
            }
            else if (hasDbOperations && hasTransformations)
            {
                html.AppendLine("Data Processing - This pipeline performs database operations with transformations on the data.</p>");
            }
            else if (hasFileOperations && hasTransformations)
            {
                html.AppendLine("File Processing - This pipeline processes file content and performs transformations.</p>");
            }
            else
            {
                html.AppendLine("Custom Integration - This pipeline implements a custom integration flow.</p>");
            }

            // Error handling assessment
            if (hasErrorHandlers)
            {
                html.AppendLine("        <p><strong>Error Handling:</strong> This pipeline includes explicit error handling mechanisms for robustness.</p>");
            }
            else
            {
                html.AppendLine("        <p><strong>Error Handling:</strong> This pipeline does not contain explicit error handling snaps.</p>");
            }

            html.AppendLine("      </div>");

            // Add pattern analysis results
            var patternAnalysisResults = _patternAnalyzer.AnalyzePipeline(pipeline);
            if (patternAnalysisResults.Any(p => p.ConfidenceScore >= 0.5f))
            {
                html.AppendLine("      <div class=\"pattern-analysis-overview\">");
                html.AppendLine("        <h3>Pattern Analysis</h3>");
                html.AppendLine("        <p>Based on analysis, this pipeline implements the following integration patterns:</p>");
                html.AppendLine("        <ul class=\"pattern-analysis-list\">");

                foreach (var pattern in patternAnalysisResults.Where(p => p.ConfidenceScore >= 0.5f).Take(3))
                {
                    int confidencePercent = (int)(pattern.ConfidenceScore * 100);
                    html.AppendLine($"          <li>");
                    html.AppendLine($"            <strong>{pattern.PatternName}</strong> ({confidencePercent}% confidence)");
                    html.AppendLine($"            <div class=\"confidence-meter-small\">");
                    html.AppendLine($"              <div class=\"confidence-fill\" style=\"width: {confidencePercent}%\"></div>");
                    html.AppendLine($"            </div>");
                    html.AppendLine($"          </li>");
                }

                html.AppendLine("        </ul>");
                html.AppendLine("        <p><em>See the Integration Patterns section for more details.</em></p>");
                html.AppendLine("      </div>");
            }

            // Pipeline Function section
            html.AppendLine("      <h3>Pipeline Functions</h3>");
            html.AppendLine("      <p>This pipeline performs the following key operations:</p>");
            html.AppendLine("      <div class=\"with-columns\">");
            html.AppendLine("        <ul>");

            // Data Sources
            html.AppendLine("          <li><strong>Data Sources:</strong>");
            html.AppendLine("            <ul>");
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.ExternalSystem && s.Type.Contains("dynamics")))
            {
                html.AppendLine("              <li>Retrieves data from Dynamics 365 CRM system</li>");
            }
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.ExternalSystem && s.Type.Contains("salesforce")))
            {
                html.AppendLine("              <li>Connects to Salesforce for CRM data</li>");
            }
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.ExternalSystem && (s.Type.Contains("rest") || s.Type.Contains("http"))))
            {
                html.AppendLine("              <li>Calls REST API endpoints</li>");
            }
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.Database && s.Type.Contains("select")))
            {
                html.AppendLine("              <li>Queries data from database tables</li>");
            }
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.FileOperation && s.Type.Contains("read")))
            {
                html.AppendLine("              <li>Reads data from files</li>");
            }
            html.AppendLine("            </ul>");
            html.AppendLine("          </li>");

            // Data Transformations
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.Transformation))
            {
                html.AppendLine("          <li><strong>Data Transformations:</strong>");
                html.AppendLine("            <ul>");
                if (pipeline.Snaps.Any(s => s.Type.Contains("datatransform") || s.Type.Contains("map")))
                {
                    html.AppendLine("              <li>Maps and transforms field values</li>");
                }
                if (pipeline.Snaps.Any(s => s.Type.Contains("json")))
                {
                    html.AppendLine("              <li>Parses or formats JSON data</li>");
                }
                if (pipeline.Snaps.Any(s => s.Type.Contains("xml")))
                {
                    html.AppendLine("              <li>Processes XML content</li>");
                }
                if (pipeline.Snaps.Any(s => s.Category == SnapCategory.FlowControl && s.Type.Contains("filter")))
                {
                    html.AppendLine("              <li>Filters data based on conditions</li>");
                }
                if (pipeline.Snaps.Any(s => s.Category == SnapCategory.FlowControl && s.Type.Contains("join")))
                {
                    html.AppendLine("              <li>Joins multiple data streams</li>");
                }
                html.AppendLine("            </ul>");
                html.AppendLine("          </li>");
            }

            html.AppendLine("        </ul>");
            html.AppendLine("        <ul>");

            // Data Destinations
            html.AppendLine("          <li><strong>Data Destinations:</strong>");
            html.AppendLine("            <ul>");
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.Database && s.Type.Contains("insert")))
            {
                html.AppendLine("              <li>Inserts records into database tables</li>");
            }
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.Database && s.Type.Contains("update")))
            {
                html.AppendLine("              <li>Updates existing database records</li>");
            }
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.ExternalSystem))
            {
                html.AppendLine("              <li>Writes data to external systems</li>");
            }
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.FileOperation && s.Type.Contains("write")))
            {
                html.AppendLine("              <li>Writes data to files</li>");
            }
            html.AppendLine("            </ul>");
            html.AppendLine("          </li>");

            // Flow Control
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.FlowControl))
            {
                html.AppendLine("          <li><strong>Flow Control:</strong>");
                html.AppendLine("            <ul>");
                if (pipeline.Snaps.Any(s => s.Type.Contains("router")))
                {
                    html.AppendLine("              <li>Routes data through conditional paths</li>");
                }
                if (pipeline.Snaps.Any(s => s.Type.Contains("union")))
                {
                    html.AppendLine("              <li>Combines multiple data streams</li>");
                }
                if (pipeline.Snaps.Any(s => s.Type.Contains("sort")))
                {
                    html.AppendLine("              <li>Sorts data based on specified criteria</li>");
                }
                html.AppendLine("            </ul>");
                html.AppendLine("          </li>");
            }

            html.AppendLine("        </ul>");
            html.AppendLine("      </div>");

            // SnapLogic Concepts
            html.AppendLine("      <div class=\"category-description\">");
            html.AppendLine("        <h3>SnapLogic Pipeline Concepts</h3>");
            html.AppendLine("        <p>SnapLogic pipelines are composed of the following key components:</p>");
            html.AppendLine("        <ul>");
            html.AppendLine("          <li><strong>Snaps</strong>: Pre-built, configurable components that perform specific functions like reading data, transforming it, or writing it to a destination.</li>");
            html.AppendLine("          <li><strong>Documents</strong>: The data units that flow through the pipeline, similar to JSON objects. Each document contains fields and values.</li>");
            html.AppendLine("          <li><strong>Views</strong>: Input or output ports on snaps that allow them to be connected.</li>");
            html.AppendLine("          <li><strong>Links</strong>: Connections between snaps that define the path for documents to flow.</li>");
            html.AppendLine("          <li><strong>Parameters</strong>: Pipeline-level variables that can be set at runtime to control pipeline behavior.</li>");
            html.AppendLine("        </ul>");
            html.AppendLine("        <p>Pipelines process data by passing documents from one snap to another along the connected links. Each snap performs its specific function on the documents it receives before passing them to the next snap in the pipeline.</p>");
            html.AppendLine("      </div>");

            html.AppendLine("    </div>");
            // Flow Diagram Section            html.AppendLine("    <div class=\"section\" id=\"flow-diagram\">");
            html.AppendLine("      <h2>Flow Diagram</h2>"); html.AppendLine("      <div class=\"diagram-container\">");
            html.AppendLine(diagramSvg);
            html.AppendLine("      </div>");
            html.AppendLine("      <div>");
            html.AppendLine("        <h3>Legend</h3>");
            html.AppendLine("        <p><span class=\"snap-category flow-control\"></span> Flow Control</p>");
            html.AppendLine("        <p><span class=\"snap-category transformation\"></span> Transformation</p>");
            html.AppendLine("        <p><span class=\"snap-category database\"></span> Database Operation</p>");
            html.AppendLine("        <p><span class=\"snap-category external-system\"></span> External System</p>");
            html.AppendLine("        <p><span class=\"snap-category file-operation\"></span> File Operation</p>");
            html.AppendLine("        <p><span class=\"snap-category error-handling\"></span> Error Handling</p>"); html.AppendLine("      </div>");
            html.AppendLine("    </div>");

            // Snap List Section
            html.AppendLine("    <div class=\"section\" id=\"snap-list\">");
            html.AppendLine("      <h2>Pipeline Snap List</h2>");
            html.AppendLine("      <p>This section provides a quick overview of all snaps in the pipeline. Click on any snap name to jump to its detailed description.</p>");
            
            // Add toggle buttons for different views
            html.AppendLine("      <div class=\"snap-list-controls\">");
            html.AppendLine("        <button class=\"snap-list-toggle active\" data-view=\"flow-order\">Flow Order</button>");
            html.AppendLine("        <button class=\"snap-list-toggle\" data-view=\"categories\">By Category</button>");
            html.AppendLine("      </div>");

            // Flow Order View
            html.AppendLine("      <div id=\"snap-list-flow-order\" class=\"snap-list-view\">");
            html.AppendLine("        <h3>Snaps in Flow Order</h3>");
            html.AppendLine("        <p>Snaps listed in the order they appear in the pipeline flow:</p>");
            html.AppendLine("        <div class=\"snap-flow-list\">");
            
            // Create flow-ordered list using helper class
            var flowOrderedSnaps = GetSnapFlowOrder(pipeline);
            int snapIndex = 1;
            
            foreach (var snap in flowOrderedSnaps)
            {
                string categoryClass = GetCategoryClass(snap.Category);
                string snapId = GenerateSnapId(snap);
                
                html.AppendLine($"          <div class=\"snap-flow-item\">");
                html.AppendLine($"            <span class=\"snap-number\">{snapIndex}</span>");
                html.AppendLine($"            <span class=\"snap-category {categoryClass}\"></span>");
                html.AppendLine($"            <a href=\"#{snapId}\" class=\"snap-link\">{snap.Label}</a>");
                html.AppendLine($"            <span class=\"snap-type\">({snap.Type})</span>");
                html.AppendLine($"          </div>");
                snapIndex++;
            }
            
            html.AppendLine("        </div>");
            html.AppendLine("      </div>");

            // Category View
            html.AppendLine("      <div id=\"snap-list-categories\" class=\"snap-list-view\" style=\"display: none;\">");
            html.AppendLine("        <h3>Snaps by Category</h3>");
            html.AppendLine("        <p>Snaps organized by their functional categories:</p>");
            
            // Group snaps by category for the list
            var snapsByCategory = pipeline.Snaps.GroupBy(s => s.Category).OrderBy(g => g.Key.ToString());
            
            foreach (var categoryGroup in snapsByCategory)
            {
                if (!categoryGroup.Any()) continue;
                
                string categoryName = GetCategoryDisplayName(categoryGroup.Key);
                string categoryClass = GetCategoryClass(categoryGroup.Key);
                
                html.AppendLine($"        <div class=\"snap-category-group\">");
                html.AppendLine($"          <h4><span class=\"snap-category {categoryClass}\"></span>{categoryName} ({categoryGroup.Count()})</h4>");
                html.AppendLine($"          <div class=\"snap-category-items\">");
                
                foreach (var snap in categoryGroup.OrderBy(s => s.Label))
                {
                    string snapId = GenerateSnapId(snap);
                    html.AppendLine($"            <div class=\"snap-category-item\">");
                    html.AppendLine($"              <a href=\"#{snapId}\" class=\"snap-link\">{snap.Label}</a>");
                    html.AppendLine($"              <span class=\"snap-type\">({snap.Type})</span>");
                    html.AppendLine($"            </div>");
                }
                
                html.AppendLine($"          </div>");
                html.AppendLine($"        </div>");
            }
            
            html.AppendLine("      </div>");
            html.AppendLine("    </div>");

            // Enhanced Flow Control Diagrams Section
            var enhancedFlowControlSnaps = pipeline.Snaps.Where(s =>
                s.Type.Equals("Router", StringComparison.OrdinalIgnoreCase) ||
                s.Type.Equals("Join", StringComparison.OrdinalIgnoreCase) ||
                s.Type.Equals("Union", StringComparison.OrdinalIgnoreCase))
                .ToList();

            if (enhancedFlowControlSnaps.Any())
            {
                LogMessage($"[FLOW-DIAGRAM] Starting Enhanced Flow Control Diagrams section with {enhancedFlowControlSnaps.Count} flow control snaps");
                LogMessage($"[FLOW-DIAGRAM] Flow control snaps found: {string.Join(", ", enhancedFlowControlSnaps.Select(s => $"{s.Type}:{s.Label}"))}");

                html.AppendLine("    <div class=\"section\" id=\"enhanced-flow-control\">");
                html.AppendLine("      <h2>Enhanced Flow Control Diagrams</h2>");
                html.AppendLine("      <p>These diagrams show the detailed flow paths and conditions for Router, Join, and Union snaps in your pipeline.</p>");

                var flowControlDiagramGenerator = new FlowControlDiagramGenerator();
                foreach (var snap in enhancedFlowControlSnaps)
                {
                    try
                    {
                        string enhancedDiagram = "";

                        if (snap.Type.Equals("Router", StringComparison.OrdinalIgnoreCase))
                        {
                            LogMessage($"[FLOW-DIAGRAM] Generating Router flow diagram for snap '{snap.Label}'");
                            enhancedDiagram = flowControlDiagramGenerator.GenerateRouterFlowDiagram(snap, pipeline.Snaps);
                            LogMessage($"[FLOW-DIAGRAM] Router flow diagram generated for '{snap.Label}', diagram length: {enhancedDiagram?.Length ?? 0} characters");
                        }
                        else if (snap.Type.Equals("Join", StringComparison.OrdinalIgnoreCase))
                        {
                            LogMessage($"[FLOW-DIAGRAM] Generating Join flow diagram for snap '{snap.Label}'");
                            enhancedDiagram = flowControlDiagramGenerator.GenerateJoinFlowDiagram(snap, pipeline.Snaps);
                            LogMessage($"[FLOW-DIAGRAM] Join flow diagram generated for '{snap.Label}', diagram length: {enhancedDiagram?.Length ?? 0} characters");
                        }
                        else if (snap.Type.Equals("Union", StringComparison.OrdinalIgnoreCase))
                        {
                            enhancedDiagram = flowControlDiagramGenerator.GenerateUnionFlowDiagram(snap, pipeline.Snaps);
                        }

                        if (!string.IsNullOrEmpty(enhancedDiagram))
                        {
                            // Add specific logging for Router and Join diagram insertion
                            if (snap.Type.Equals("Router", StringComparison.OrdinalIgnoreCase))
                            {
                                LogMessage($"[FLOW-DIAGRAM] Inserting Router flow diagram into HTML documentation for snap '{snap.Label}'");
                            }
                            else if (snap.Type.Equals("Join", StringComparison.OrdinalIgnoreCase))
                            {
                                LogMessage($"[FLOW-DIAGRAM] Inserting Join flow diagram into HTML documentation for snap '{snap.Label}'");
                            }
                            html.AppendLine($"      <div class=\"flow-control-snap\">");
                            html.AppendLine($"        <h3>{snap.Type}: {snap.Label}</h3>");
                            html.AppendLine("        <div class=\"diagram-container\">");
                            html.AppendLine(enhancedDiagram);
                            html.AppendLine("        </div>");
                            html.AppendLine("      </div>");
                        }
                    }
                    catch (Exception ex)
                    {
                        html.AppendLine($"      <div class=\"flow-control-snap\">");
                        html.AppendLine($"        <h3>{snap.Type}: {snap.Label}</h3>");
                        html.AppendLine($"        <p class=\"error\">Unable to generate enhanced diagram: {ex.Message}</p>");
                        html.AppendLine("      </div>");
                    }
                }

                html.AppendLine("    </div>");
            }

            // Parameters Section
            html.AppendLine("    <div class=\"section\" id=\"parameters\">");
            html.AppendLine("      <h2>Pipeline Parameters</h2>");

            if (pipeline.Parameters.Any())
            {
                html.AppendLine("      <table>");
                html.AppendLine("        <tr><th>Parameter</th><th>Type</th><th>Default Value</th><th>Required</th><th>Description</th></tr>");

                foreach (var param in pipeline.Parameters)
                {
                    html.AppendLine($"        <tr><td>{param.Key}</td><td>{param.DataType}</td><td>{param.Value}</td><td>{(param.Required ? "Yes" : "No")}</td><td>{param.Description}</td></tr>");
                }

                html.AppendLine("      </table>");
            }
            else
            {
                html.AppendLine("      <p>This pipeline does not define any parameters.</p>");
            }

            html.AppendLine("    </div>");

            // Snaps Section
            html.AppendLine("    <div class=\"section\" id=\"snaps\">");
            html.AppendLine("      <h2>Pipeline Snaps</h2>");

            // Group snaps by category
            Console.WriteLine($"[DEBUG-FILTER] Total snaps in pipeline: {pipeline.Snaps.Count}");
            foreach (var snap in pipeline.Snaps)
            {
                Console.WriteLine($"[DEBUG-FILTER] Snap '{snap.Label}' has Category: {snap.Category} (Type: {snap.Type})");
            }
            
            var flowControlSnaps = pipeline.Snaps.Where(s => s.Category == SnapCategory.FlowControl).ToList();
            var transformationSnaps = pipeline.Snaps.Where(s => s.Category == SnapCategory.Transformation).ToList();
            var databaseSnaps = pipeline.Snaps.Where(s => s.Category == SnapCategory.Database).ToList();
            Console.WriteLine($"[DEBUG-FILTER-RESULT] Database snaps filtered: {databaseSnaps.Count}");
            foreach (var dbSnap in databaseSnaps)
            {
                Console.WriteLine($"[DEBUG-FILTER-RESULT] Database snap: '{dbSnap.Label}' (Category: {dbSnap.Category})");
            }
            var externalSystemSnaps = pipeline.Snaps.Where(s => s.Category == SnapCategory.ExternalSystem).ToList();
            var fileOperationSnaps = pipeline.Snaps.Where(s => s.Category == SnapCategory.FileOperation).ToList();
            var errorHandlingSnaps = pipeline.Snaps.Where(s => s.Category == SnapCategory.ErrorHandling).ToList();
            var otherSnaps = pipeline.Snaps.Where(s => s.Category == SnapCategory.Other).ToList();            // The current pipeline has already been set in the _currentPipeline field            // Flow Control Snaps
            LogMessage($"[SNAP-CATEGORY] Starting Flow Control section with {flowControlSnaps.Count} snaps");

            // DEBUG: Show all snaps in this category to help identify condition snaps
            Console.WriteLine($"[DEBUG] ===== ALL SNAPS IN CATEGORY 'Flow Control' =====");
            for (int i = 0; i < flowControlSnaps.Count; i++)
            {
                var snap = flowControlSnaps[i];
                Console.WriteLine($"[DEBUG] Snap {i + 1}: '{snap.Label}' (Type: '{snap.Type}', ID: {snap.Id})");
                
                // Check if this looks like a condition snap
                bool couldBeCondition = snap.Type?.ToLower().Contains("condition") == true ||
                    snap.Type?.ToLower().Contains("router") == true ||
                    snap.Type?.ToLower().Contains("branch") == true ||
                    snap.Type?.ToLower().Contains("switch") == true ||
                    snap.Type?.ToLower().Contains("gate") == true ||
                    snap.Type?.ToLower().Contains("flow") == true ||
                    snap.Type?.ToLower().Contains("decision") == true ||
                    snap.Type?.ToLower().Contains("choice") == true ||
                    snap.Type?.ToLower().Contains("if") == true ||
                    snap.Type?.ToLower().Contains("filter") == true ||
                    snap.Type?.ToLower().Contains("-gate") == true ||
                    snap.Type?.ToLower().Contains("-router") == true ||
                    snap.Type?.ToLower().Contains("-branch") == true ||
                    snap.Type?.ToLower().Contains("-switch") == true ||
                    snap.Type?.ToLower().Contains("-condition") == true ||
                    snap.Type?.ToLower().Contains("snaps-flow-") == true ||
                    snap.Type?.ToLower().Contains("snaps-transform-gate") == true ||
                    snap.Type?.ToLower().Contains("snaps-control-") == true;
                
                if (couldBeCondition)
                {
                    Console.WriteLine($"[DEBUG] *** POTENTIAL CONDITION SNAP FOUND: '{snap.Label}' (Type: '{snap.Type}') ***");
                }
            }
            Console.WriteLine($"[DEBUG] ===== END CATEGORY 'Flow Control' =====");

            if (!flowControlSnaps.Any())
            {
                LogMessage($"[SNAP-CATEGORY] No snaps in Flow Control, skipping section");
            }
            else
            {
                await AddSnapCategorySection(html, "Flow Control", "flow-control", flowControlSnaps, cancellationToken).ConfigureAwait(false);
            }

            // Transformation Snaps
            LogMessage($"[SNAP-CATEGORY] Starting Transformations section with {transformationSnaps.Count} snaps");
            await AddSnapCategorySection(html, "Transformations", "transformations", transformationSnaps, cancellationToken).ConfigureAwait(false);

            // Database Snaps
            LogMessage($"[SNAP-CATEGORY] Starting Database Operations section with {databaseSnaps.Count} snaps");
            await AddSnapCategorySection(html, "Database Operations", "database", databaseSnaps, cancellationToken).ConfigureAwait(false);

            // External System Snaps
            LogMessage($"[SNAP-CATEGORY] Starting External Systems section with {externalSystemSnaps.Count} snaps");
            await AddSnapCategorySection(html, "External Systems", "external", externalSystemSnaps, cancellationToken).ConfigureAwait(false);

            // File Operation Snaps
            LogMessage($"[SNAP-CATEGORY] Starting File Operations section with {fileOperationSnaps.Count} snaps");
            await AddSnapCategorySection(html, "File Operations", "file", fileOperationSnaps, cancellationToken).ConfigureAwait(false);

            // Error Handling Snaps
            LogMessage($"[SNAP-CATEGORY] Starting Error Handling section with {errorHandlingSnaps.Count} snaps");
            await AddSnapCategorySection(html, "Error Handling", "error", errorHandlingSnaps, cancellationToken).ConfigureAwait(false);

            // Other Snaps
            LogMessage($"[SNAP-CATEGORY] Starting Other Snaps section with {otherSnaps.Count} snaps");
            await AddSnapCategorySection(html, "Other Snaps", "other", otherSnaps, cancellationToken).ConfigureAwait(false);

            html.AppendLine("    </div>");            // Best Practices Section
            html.AppendLine("    <div class=\"section\" id=\"best-practices\">");
            html.AppendLine("      <h2>Pipeline Best Practices</h2>");
            html.AppendLine("      <div class=\"category-description\">");
            html.AppendLine("        <p>SnapLogic pipelines follow certain best practices for optimal performance, maintainability, and reliability:</p>");
            html.AppendLine("      </div>");

            html.AppendLine("      <h3>General Pipeline Design</h3>");
            html.AppendLine("      <ul>");
            html.AppendLine("        <li><strong>Modularization</strong>: Break complex workflows into smaller, reusable pipelines for easier maintenance and troubleshooting.</li>");
            html.AppendLine("        <li><strong>Parameterization</strong>: Use pipeline parameters for values that might change between environments or executions.</li>");
            html.AppendLine("        <li><strong>Documentation</strong>: Use descriptive snap labels and pipeline notes to explain complex logic and business requirements.</li>");
            html.AppendLine("        <li><strong>Error Handling</strong>: Include proper error handling snaps to catch and manage exceptions gracefully.</li>");
            html.AppendLine("      </ul>");

            html.AppendLine("      <h3>Performance Optimization</h3>");
            html.AppendLine("      <ul>");
            html.AppendLine("        <li><strong>Filtering Early</strong>: Filter data as early as possible in the pipeline to reduce processing load on downstream snaps.</li>");
            html.AppendLine("        <li><strong>Batch Processing</strong>: Configure database operations to use batch mode when processing large datasets.</li>");
            html.AppendLine("        <li><strong>Connection Pooling</strong>: Reuse database connections instead of creating new connections for each operation.</li>");
            html.AppendLine("        <li><strong>Pipeline Splitting</strong>: Use pipeline executes for parallel processing of large workloads.</li>");
            html.AppendLine("      </ul>");

            html.AppendLine("      <h3>Security Considerations</h3>");
            html.AppendLine("      <ul>");
            html.AppendLine("        <li><strong>Credential Management</strong>: Store credentials in account settings rather than hardcoded in the pipeline.</li>");
            html.AppendLine("        <li><strong>Data Encryption</strong>: Enable encryption for sensitive data at rest and in transit.</li>");
            html.AppendLine("        <li><strong>Access Control</strong>: Apply appropriate access controls to pipelines handling sensitive information.</li>");
            html.AppendLine("      </ul>");

            html.AppendLine("    </div>");

            // Execution Flow Section
            html.AppendLine("    <div class=\"section\" id=\"execution-flow\">");
            html.AppendLine("      <h2>Execution Flow</h2>");

            // Add overall pipeline functional description
            html.AppendLine("      <div class=\"category-description\">");
            html.AppendLine("        <h3>Pipeline Function Summary</h3>");
            html.AppendLine("        <p>This section describes how data flows through the pipeline, from start to finish. Understanding the sequence of operations helps identify " +
                           "the main data processing pathways and decision points in the pipeline.</p>");
            html.AppendLine("      </div>");

            // Add entry points section
            html.AppendLine("      <h3>Pipeline Entry Points</h3>");
            html.AppendLine("      <p>The pipeline execution starts at the following entry points:</p>");
            html.AppendLine("      <ul>");

            foreach (var startNode in pipeline.GetStartPoints())
            {
                html.AppendLine($"        <li><strong>{startNode.Label}</strong> ({GetFriendlySnapType(startNode.Type)}) - {GetSnapDescription(startNode)}</li>");
            }

            html.AppendLine("      </ul>");

            // Generate execution flow paths with enhanced descriptions
            html.AppendLine("      <h3>Main Execution Paths</h3>");
            html.AppendLine("      <p>The following sections outline the main paths that data takes through this pipeline:</p>");

            var startPoints = pipeline.GetStartPoints();
            int pathNumber = 0;

            foreach (var startPoint in startPoints)
            {
                html.AppendLine($"      <h4>Paths from {startPoint.Label}</h4>");
                var paths = FindExecutionPaths(pipeline, startPoint);

                foreach (var path in paths)
                {
                    if (path.Count > 1)
                    {
                        pathNumber++;
                        html.AppendLine("      <div class=\"execution-path\">");
                        html.AppendLine($"        <p><strong>Execution Path {pathNumber}:</strong></p>");
                        html.AppendLine("        <ol>");

                        for (int i = 0; i < path.Count; i++)
                        {
                            var node = path[i];
                            if (i == 0)
                            {
                                // Starting node
                                html.AppendLine($"          <li><strong>{node.Label}</strong> - Starts the pipeline execution</li>");
                            }
                            else if (i == path.Count - 1)
                            {
                                // Ending node
                                html.AppendLine($"          <li><strong>{node.Label}</strong> - Final operation in this path</li>");
                            }
                            else
                            {
                                // Intermediate node - add condensed description
                                html.AppendLine($"          <li><strong>{node.Label}</strong> - {GetSnapDescription(node)}</li>");
                            }
                        }

                        html.AppendLine("        </ol>");
                        // Add functional summary of this path
                        html.AppendLine("        <div class=\"function-details\">");
                        html.AppendLine("          <strong>Path Summary:</strong> ");

                        // Generate summary based on the types of nodes in the path
                        string pathSummary = GeneratePathSummary(path);
                        html.AppendLine($"          {pathSummary}");
                        html.AppendLine("        </div>");
                        // Add detailed path analysis
                        html.AppendLine("        <div class=\"technical-details\">");
                        string pathAnalysis = ProvideAdvancedPathAnalysis(path);
                        html.AppendLine($"          {pathAnalysis}");
                        html.AppendLine("        </div>");

                        html.AppendLine("      </div>");

                        // Limit to a reasonable number of paths
                        if (paths.IndexOf(path) >= 2)
                        {
                            html.AppendLine("      <p>Additional paths exist but are not shown for brevity...</p>");
                            break;
                        }
                    }
                }
            }
            html.AppendLine("    </div>");
            // Integration Patterns Section
            html.AppendLine("    <div class=\"section\" id=\"patterns\">");
            html.AppendLine("      <h2>Integration Patterns</h2>");
            html.AppendLine("      <div class=\"category-description\">");
            html.AppendLine("        <p>SnapLogic pipelines commonly implement the following integration patterns. Understanding these patterns helps in designing efficient and maintainable pipelines.</p>");
            html.AppendLine("      </div>");

            // Add the pattern analysis results
            html.AppendLine(_patternAnalyzer.GeneratePatternAnalysisHtml(pipeline));

            html.AppendLine("      <h3>Common Integration Pattern Reference</h3>");
            html.AppendLine("      <div class=\"pattern-container\">");
            html.AppendLine("        <div class=\"pattern-card\">");
            html.AppendLine("          <h3>ETL (Extract, Transform, Load)</h3>");
            html.AppendLine("          <p><strong>Use case:</strong> Data warehousing, reporting, analytics</p>");
            html.AppendLine("          <p><strong>Pattern:</strong> Extract data from source systems → Transform data to meet target requirements → Load data into destination systems</p>");
            html.AppendLine("          <p><strong>Key snaps:</strong> Database Select/Query, REST Get, File Reader, Mapper, Join, Filter, Database Insert/Update, File Writer</p>");
            html.AppendLine("          <p><strong>Best practices:</strong></p>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><strong>Filter data at the source to reduce processing volume</li>");
            html.AppendLine("            <li>Use batch operations for database writes</li>");
            html.AppendLine("            <li>Consider incremental loading for large datasets</li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </div>");

            html.AppendLine("        <div class=\"pattern-card\">");
            html.AppendLine("          <h3>API Integration</h3>");
            html.AppendLine("          <p><strong>Use case:</strong> SaaS integration, microservices communication</p>");
            html.AppendLine("          <p><strong>Pattern:</strong> API Request → Response Parsing → Data Processing → API Response / Database Update</p>");
            html.AppendLine("          <p><strong>Key snaps:</strong> REST Get/Post/Put, SOAP, JSON Parser/Generator, XML Parser/Generator, Mapper</p>");
            html.AppendLine("          <p><strong>Best practices:</strong></p>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><strong>Handle API authentication properly (OAuth, API Keys)</li>");
            html.AppendLine("            <li>Implement retry logic for transient failures</li>");
            html.AppendLine("            <li>Respect API rate limits with throttling</li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </div>");

            html.AppendLine("        <div class=\"pattern-card\">");
            html.AppendLine("          <h3>Data Synchronization</h3>");
            html.AppendLine("          <p><strong>Use case:</strong> Multi-system consistency, master data management</p>");
            html.AppendLine("          <p><strong>Pattern:</strong> Source Change Detection → Lookup Existing Records → Apply Delta Updates → Log Results</p>");
            html.AppendLine("          <p><strong>Key snaps:</strong> Database Select with timestamps/watermarks, Join, Router, Database Upsert, Error Handler</p>");
            html.AppendLine("          <p><strong>Best practices:</strong></p>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li>Track synchronization timestamps for incremental processing</li>");
            html.AppendLine("            <li>Implement conflict resolution strategies</li>");
            html.AppendLine("            <li>Log synchronization results for auditing</li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </div>");

            html.AppendLine("        <div class=\"pattern-card\">");
            html.AppendLine("          <h3>Event Processing</h3>");
            html.AppendLine("          <p><strong>Use case:</strong> Real-time analytics, notifications, workflow triggers</p>");
            html.AppendLine("          <p><strong>Pattern:</strong> Event Capture → Event Filtering/Enrichment → Parallel Processing → Multiple Destinations</p>");
            html.AppendLine("          <p><strong>Key snaps:</strong> REST Listener, Message Queue Consumer, Router, Copy, Mapper, Database Operations</p>");
            html.AppendLine("          <p><strong>Best practices:</strong></p>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><strong>Process events asynchronously when possible</li>");
            html.AppendLine("            <li>Implement idempotent handling for duplicate events</li>");
            html.AppendLine("            <li>Use error handling for graceful failure recovery</li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </div>");

            html.AppendLine("        <div class=\"pattern-card\">");
            html.AppendLine("          <h3>Data Validation & Cleansing</h3>");
            html.AppendLine("          <p><strong>Use case:</strong> Data quality assurance, compliance, governance</p>");
            html.AppendLine("          <p><strong>Pattern:</strong> Data Source → Schema Validation → Business Rule Validation → Routing (Valid/Invalid) → Processing/Notification</p>");
            html.AppendLine("          <p><strong>Key snaps:</strong> JSON/XML Validator, Script, Router, Error Handler, Email</p>");
            html.AppendLine("          <p><strong>Best practices:</strong></p>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><strong>Define clear validation schemas and rules</li>");
            html.AppendLine("            <li>Separate structural validation from business rule validation</li>");
            html.AppendLine("            <li>Create detailed error logs for invalid data</li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </div>");

            html.AppendLine("        <div class=\"pattern-card\">");
            html.AppendLine("          <h3>Batch File Processing</h3>");
            html.AppendLine("          <p><strong>Use case:</strong> Scheduled data exchange, report generation, bulk operations</p>");
            html.AppendLine("          <p><strong>Pattern:</strong> File Detection → File Reading → Processing → Output Generation → File Archiving</p>");
            html.AppendLine("          <p><strong>Key snaps:</strong> File Reader, CSV/JSON/XML Parser, Mapper, Sort, Aggregate, File Writer</p>");
            html.AppendLine("          <p><strong>Best practices:</strong></p>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><strong>Implement file naming conventions for processing order</li>");
            html.AppendLine("            <li>Use staging areas for in-process files</li>");
            html.AppendLine("            <li>Archive processed files with timestamps</li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </div>");
            html.AppendLine("      </div>");
            html.AppendLine("    </div>");

            // Technical Glossary Section
            html.AppendLine("    <div class=\"section\" id=\"glossary\">");
            html.AppendLine("      <h2>Technical Glossary</h2>");
            html.AppendLine("      <div class=\"category-description\">");
            html.AppendLine("        <p>This glossary explains key technical terms and concepts used in SnapLogic pipelines and this documentation.</p>");
            html.AppendLine("      </div>");
            html.AppendLine("      <table>");
            html.AppendLine("        <tr><th>Term</th><th>Definition</th><th>Example</th></tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Snap</td>");
            html.AppendLine("          <td>Pre-built, configurable components that perform specific functions within a pipeline. Each snap is designed for a particular task such as reading data, transforming it, or writing it to a destination.</td>");
            html.AppendLine("          <td>Database - MySQL Select, File - CSV Parser, Transform - Mapper</td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Pipeline</td>");
            html.AppendLine("          <td>A collection of snaps connected together to implement an integration workflow, data process, or automation task.</td>");
            html.AppendLine("          <td>A pipeline that extracts customer data from CRM, transforms it, and loads it into a data warehouse</td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Document</td>");
            html.AppendLine("          <td>The fundamental data unit in SnapLogic, similar to a JSON object. Documents flow through pipelines and contain the data being processed.</td>");
            html.AppendLine("          <td><pre class=\"code-sample\">{\n  \"customer_id\": \"C-1234\",\n  \"name\": \"John Smith\",\n  \"email\": \"<EMAIL>\",\n  \"orders\": [\n    { \"id\": \"O-789\", \"amount: 99.95 }\n  ]\n}</pre></td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>View</td>");
            html.AppendLine("          <td>Input or output ports on snaps that allow them to be connected.</td>");
            html.AppendLine("          <td>A Database Select snap has an output view, while a Mapper snap has both input and output views</td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Link</td>");
            html.AppendLine("          <td>A connection between the output view of one snap to the input view of another, defining the flow of documents.</td>");
            html.AppendLine("          <td>The line connecting a Database Select snap's output to a Mapper snap's input</td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Parameter</td>");
            html.AppendLine("          <td>Named values defined at the pipeline level that can be referenced by snaps, allowing for configurability without modifying pipeline structure.</td>");
            html.AppendLine("          <td><pre class=\"code-sample\">// Define a parameter\nName: database_table\nDefault value: \"customers\"\n\n// Use in a snap\nSELECT * FROM ${database_table}</pre></td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Expression</td>");
            html.AppendLine("          <td>JavaScript-like code snippets used in snaps for data transformation, field mapping, and conditional logic.</td>");
            html.AppendLine("          <td><pre class=\"code-sample\">// Convert name to uppercase\n$customer.name.toUpperCase()\n\n// Calculate total\n$items.reduce((sum, item) => \n  sum + item.price * item.quantity, 0)</pre></td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Mapper</td>");
            html.AppendLine("          <td>A snap that transforms documents by mapping source fields to target fields, often with transformation expressions.</td>");
            html.AppendLine("          <td><pre class=\"code-sample\">// Mapping fields with transformation\ntarget.full_name = $source.first_name + \" \" + $source.last_name;\ntarget.age = new Date().getFullYear() - $source.birth_year;</pre></td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Router</td>");
            html.AppendLine("          <td>A flow control snap that directs documents to different paths based on conditional expressions.</td>");
            html.AppendLine("          <td><pre class=\"code-sample\">// Router conditions\nPath 1: $order.total > 1000  // High-value orders\nPath 2: $order.items.length > 10  // Large orders\nPath 3: true  // Default path</pre></td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Join</td>");
            html.AppendLine("          <td>A flow control snap that combines documents from multiple streams based on matching key values, similar to SQL joins.</td>");
            html.AppendLine("          <td><pre class=\"code-sample\">// Join configuration\nJoin Type: Inner\nJoin Key: $customer_id\nResult: Merged documents with matching IDs</pre></td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Ultra Task</td>");
            html.AppendLine("          <td>A serverless execution environment for running SnapLogic pipelines, automatically scaling resources as needed.</td>");
            html.AppendLine("          <td>Running a nightly data sync pipeline that scales up resources during heavy processing periods</td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Groundplex</td>");
            html.AppendLine("          <td>An on-premises runtime environment for SnapLogic pipelines, used for accessing systems behind firewalls.</td>");
            html.AppendLine("          <td>Using Groundplex to connect to an on-premises database that is not accessible from the public internet</td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Cloudplex</td>");
            html.AppendLine("          <td>A cloud-based runtime environment for SnapLogic pipelines, hosted and managed by SnapLogic.</td>");
            html.AppendLine("          <td>Running pipelines in Cloudplex to integrate SaaS applications like Salesforce and Workday</td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>ETL</td>");
            html.AppendLine("          <td>Extract, Transform, Load - A common integration pattern where data is extracted from source systems, transformed to meet target requirements, and loaded into destination systems.</td>");
            html.AppendLine("          <td>A pipeline that extracts customer data from CRM, transforms fields to match a data warehouse schema, then loads it into the warehouse</td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Batch Processing</td>");
            html.AppendLine("          <td>Processing multiple documents together in a single operation for improved performance, particularly for database operations.</td>");
            html.AppendLine("          <td><pre class=\"code-sample\">// Database Insert configuration\nBatch Size: 100\nBatch documents together for a single database operation</pre></td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Binary Data</td>");
            html.AppendLine("          <td>Non-textual data such as files or images, typically handled as base64-encoded strings within SnapLogic documents.</td>");
            html.AppendLine("          <td><pre class=\"code-sample\">// Binary data in a document\n{\n  \"filename\": \"report.pdf\",\n  \"content_type\": \"application/pdf\",\n  \"data\": \"JVBERi0xLjUNCiW1tbW1DQoxIDAgb2JqDQo...\"\n}</pre></td>");
            html.AppendLine("        </tr>");
            html.AppendLine("        <tr>");
            html.AppendLine("          <td>Pipeline Execute</td>");
            html.AppendLine("          <td>A snap that triggers execution of another pipeline, allowing for modular design and reuse of common processes.</td>");
            html.AppendLine("          <td>A master pipeline that executes multiple child pipelines for different data sources, then aggregates their results</td>");
            html.AppendLine("        </tr>");
            html.AppendLine("      </table>");
            html.AppendLine("    </div>");
            // HTML footer
            html.AppendLine("  </div>");

            // Add JavaScript for interactive elements
            html.AppendLine("  <script>");
            html.AppendLine("    document.addEventListener('DOMContentLoaded', function() {");
            html.AppendLine("      // Initialize collapsible sections");
            html.AppendLine("      var coll = document.getElementsByClassName('collapsible');");
            html.AppendLine("      for (var i = 0; i < coll.length; i++) {");
            html.AppendLine("        coll[i].addEventListener('click', function() {");
            html.AppendLine("          this.classList.toggle('active');");
            html.AppendLine("          var content = this.nextElementSibling;");
            html.AppendLine("          if (content.style.maxHeight) {");
            html.AppendLine("            content.style.maxHeight = null;");
            html.AppendLine("          } else {");
            html.AppendLine("            content.style.maxHeight = content.scrollHeight + 'px';");
            html.AppendLine("          }");            html.AppendLine("        });");
            html.AppendLine("      }");
            html.AppendLine("    });");
            html.AppendLine("  </script>");
            
            // Add snap list toggle functionality
            html.AppendLine("  <script>");
            html.AppendLine("    document.addEventListener('DOMContentLoaded', function() {");
            html.AppendLine("      // Snap list toggle functionality");
            html.AppendLine("      const snapListToggles = document.querySelectorAll('.snap-list-toggle');");
            html.AppendLine("      snapListToggles.forEach(toggle => {");
            html.AppendLine("        toggle.addEventListener('click', function() {");
            html.AppendLine("          // Remove active class from all toggles");
            html.AppendLine("          snapListToggles.forEach(t => t.classList.remove('active'));");
            html.AppendLine("          // Add active class to clicked toggle");
            html.AppendLine("          this.classList.add('active');");
            html.AppendLine("          ");
            html.AppendLine("          // Hide all views");
            html.AppendLine("          document.getElementById('snap-list-flow-order').style.display = 'none';");
            html.AppendLine("          document.getElementById('snap-list-categories').style.display = 'none';");
            html.AppendLine("          ");
            html.AppendLine("          // Show selected view");
            html.AppendLine("          const viewType = this.getAttribute('data-view');");
            html.AppendLine("          if (viewType === 'flow-order') {");
            html.AppendLine("            document.getElementById('snap-list-flow-order').style.display = 'block';");
            html.AppendLine("          } else if (viewType === 'categories') {");
            html.AppendLine("            document.getElementById('snap-list-categories').style.display = 'block';");
            html.AppendLine("          }");
            html.AppendLine("        });");
            html.AppendLine("      });");
            html.AppendLine("    });");
            html.AppendLine("  </script>");
            
            // Add cytoscape.js library
            html.AppendLine("  <!-- Cytoscape.js dependencies -->");
            html.AppendLine("  <script src=\"https://unpkg.com/cytoscape@3.30.3/dist/cytoscape.min.js\"></script>");
            html.AppendLine("  <script src=\"https://unpkg.com/dagre@0.8.5/dist/dagre.min.js\"></script>");
            html.AppendLine("  <script src=\"https://unpkg.com/cytoscape-dagre@2.5.0/cytoscape-dagre.js\"></script>");
            
            html.AppendLine("</body>");
            html.AppendLine("</html>");

            return html.ToString();
        }          // Add non-async version that calls the async version and waits for the result
        public string GenerateHtmlDocumentation(PipelineData pipeline, string diagramSvg)
        {
            return GenerateHtmlDocumentationAsync(pipeline, diagramSvg, null, CancellationToken.None).GetAwaiter().GetResult();
        }

        public string GenerateHtmlDocumentation(PipelineData pipeline, string diagramSvg, ProjectData project)
        {
            return GenerateHtmlDocumentationAsync(pipeline, diagramSvg, project, CancellationToken.None).GetAwaiter().GetResult();
        }
        private async Task AddSnapCategorySection(StringBuilder html, string categoryTitle, string cssClass, List<SnapNode> snaps, CancellationToken cancellationToken = default)
        {
            Console.WriteLine($"[DEBUG-SECTION-START] AddSnapCategorySection called for '{categoryTitle}' with {snaps.Count} snaps");
            foreach (var snap in snaps)
            {
                Console.WriteLine($"[DEBUG-SECTION-START] Snap in {categoryTitle}: '{snap.Label}' (Category: {snap.Category})");
            }
            
            LogMessage($"[SNAP-CATEGORY] Starting {categoryTitle} section with {snaps.Count} snaps");

            // DEBUG: Show all snaps in this category to help identify condition snaps
            Console.WriteLine($"[DEBUG] ===== ALL SNAPS IN CATEGORY '{categoryTitle}' =====");
            for (int i = 0; i < snaps.Count; i++)
            {
                var snap = snaps[i];
                Console.WriteLine($"[DEBUG] Snap {i + 1}: '{snap.Label}' (Type: '{snap.Type}', ID: {snap.Id})");
                
                // Check if this looks like a condition snap
                bool couldBeCondition = snap.Type?.ToLower().Contains("condition") == true ||
                    snap.Type?.ToLower().Contains("router") == true ||
                    snap.Type?.ToLower().Contains("branch") == true ||
                    snap.Type?.ToLower().Contains("switch") == true ||
                    snap.Type?.ToLower().Contains("gate") == true ||
                    snap.Type?.ToLower().Contains("flow") == true ||
                    snap.Type?.ToLower().Contains("decision") == true ||
                    snap.Type?.ToLower().Contains("choice") == true ||
                    snap.Type?.ToLower().Contains("if") == true ||
                    snap.Type?.ToLower().Contains("filter") == true ||
                    snap.Type?.ToLower().Contains("-gate") == true ||
                    snap.Type?.ToLower().Contains("-router") == true ||
                    snap.Type?.ToLower().Contains("-branch") == true ||
                    snap.Type?.ToLower().Contains("-switch") == true ||
                    snap.Type?.ToLower().Contains("-condition") == true ||
                    snap.Type?.ToLower().Contains("snaps-flow-") == true ||
                    snap.Type?.ToLower().Contains("snaps-transform-gate") == true ||
                    snap.Type?.ToLower().Contains("snaps-control-") == true;
                
                if (couldBeCondition)
                {
                    Console.WriteLine($"[DEBUG] *** POTENTIAL CONDITION SNAP FOUND: '{snap.Label}' (Type: '{snap.Type}') ***");
                }
            }
            Console.WriteLine($"[DEBUG] ===== END CATEGORY '{categoryTitle}' =====");

            if (!snaps.Any())
            {
                LogMessage($"[SNAP-CATEGORY] No snaps in {categoryTitle}, skipping section");
                return;
            }

            html.AppendLine($"      <div class=\"snap-category-section {cssClass}\" id=\"{cssClass}\">");
            html.AppendLine($"        <h3>{categoryTitle} ({snaps.Count})</h3>");
            // Add detailed category description
            AddSnapCategoryDocumentation(html, cssClass);
            LogMessage($"[SNAP-CATEGORY] Added category documentation for {categoryTitle}");

            Console.WriteLine($"[DEBUG-BEFORE-LOOP] About to process {snaps.Count} snaps in {categoryTitle}");
            foreach (var snap in snaps)
            {
                Console.WriteLine($"[DEBUG-IN-LOOP] Processing snap: '{snap.Label}' in {categoryTitle}");
                LogMessage($"[SNAP-PROCESS] Processing snap: {snap.Label} (Type: {snap.Type})");
                Console.WriteLine($"[DEBUG-AFTER-LOG] After LogMessage for snap: '{snap.Label}'");

                html.AppendLine($"        <div class=\"snap-details\" id=\"{GenerateSnapId(snap)}\">");
                html.AppendLine($"          <h4>{snap.Label}</h4>");
                html.AppendLine($"          <p><strong>Type:</strong> {GetFriendlySnapType(snap.Type)}</p>");

                // Add database connection information for database snaps
                Console.WriteLine($"[DEBUG-DB-CHECK] Checking if snap '{snap.Label}' is database category. Category: {snap.Category}");
                if (snap.Category == SnapCategory.Database)
                {
                    Console.WriteLine($"[DEBUG-INSIDE-DB-IF] Inside database if block for snap: '{snap.Label}'");
                    LogMessage($"[DB-CONNECTION] Processing database snap: {snap.Label} (Type: {snap.Type})");
                    Console.WriteLine($"[DEBUG-BEFORE-GET-INFO] About to call GetDatabaseConnectionInfo for: '{snap.Label}'");
                    string connectionInfo = GetDatabaseConnectionInfo(snap);
                    if (!string.IsNullOrEmpty(connectionInfo))
                    {
                        LogMessage($"[DB-CONNECTION] Found connection info: {connectionInfo}");
                        html.AppendLine($"          <p><strong>Database/Connector:</strong> {System.Net.WebUtility.HtmlEncode(connectionInfo)}</p>");
                    }
                    else
                    {
                        LogMessage($"[DB-CONNECTION] No connection info found for: {snap.Label}");
                    }
                }
                else
                {
                    LogMessage($"[DB-CONNECTION] Snap {snap.Label} is not categorized as Database (Category: {snap.Category})");
                }

                // Description
                string description = GetSnapDescription(snap);
                if (!string.IsNullOrEmpty(description))
                {
                    html.AppendLine($"          <p><strong>Function:</strong> {description}</p>");
                }

                // Add AI-enhanced description if available
                if (_useAI)
                {
                    LogMessage($"[AI-PROCESS] Starting AI processing for snap: {snap.Label}");
                    try
                    {                        // Use the current pipeline that was already set
                        if (_currentPipeline != null)
                        {
                            // Track unique snap types to avoid generating descriptions for identical snap types
                            string snapTypeKey = $"{snap.Category}-{snap.Type}";
                            LogMessage($"[AI-PROCESS] Checking if snap type {snapTypeKey} already processed");

                            if (!_processedSnapTypes.Contains(snapTypeKey))
                            {
                                LogMessage($"[AI-PROCESS] Snap type {snapTypeKey} not processed yet, adding to processed list");
                                _processedSnapTypes.Add(snapTypeKey);

                                LogMessage($"[AI-PROCESS] Calling GenerateEnhancedSnapDescription for snap: {snap.Label}");
                                string enhancedDescription = await _aiGenerator.GenerateEnhancedSnapDescription(snap, _currentPipeline, cancellationToken).ConfigureAwait(false);
                                LogMessage($"[AI-PROCESS] GenerateEnhancedSnapDescription completed for snap: {snap.Label}");                                if (!string.IsNullOrEmpty(enhancedDescription))
                                {
                                    LogMessage($"[AI-PROCESS] Adding enhanced description to HTML for snap: {snap.Label}");
                                    
                                    // Use the improved context-aware placeholder processing
                                    string processedDescription = ReplaceAllPlaceholders(enhancedDescription);
                                    
                                    html.AppendLine($"          <div class=\"ai-description\">");
                                    html.AppendLine($"            <h5>Enhanced Description</h5>");
                                    html.AppendLine($"            <div>{ConvertMarkdownToHtml(processedDescription)}</div>");
                                    html.AppendLine($"          </div>");
                                }// Generate pseudocode for the snap logic, but skip for certain snap types
                                string snapTypeLower = snap.Type.ToLower();
                                bool isMapperType = snapTypeLower.Contains("map") || snapTypeLower.Contains("datatransform") || snapTypeLower.Contains("transform");
                                bool isExitOrUnionType = snapTypeLower.Contains("exit") || snapTypeLower.Contains("union");
                                bool isCopyType = snapTypeLower.Contains("copy");
                                // Check for router snaps which should use explicit pseudocode generation
                                bool isRouterType = snap.Category == SnapCategory.FlowControl && snapTypeLower.Contains("router");

                                LogMessage($"[AI-PROCESS] Checking pseudocode eligibility for snap: {snap.Label} (isMapper: {isMapperType}, isRouter: {isRouterType}, isExitOrUnion: {isExitOrUnionType}, isCopy: {isCopyType})");
                                // Skip pseudocode generation for mapper, router, exit, union, and copy snaps
                                if (!isMapperType && !isRouterType && !isExitOrUnionType && !isCopyType)
                                {
                                    LogMessage($"[AI-CALL] Calling GenerateSnapPseudocode for snap: {snap.Label}"); string pseudocode = await _aiGenerator.GenerateSnapPseudocode(snap, _currentPipeline, cancellationToken).ConfigureAwait(false);
                                    LogMessage($"[AI-CALL] GenerateSnapPseudocode completed for snap: {snap.Label}");
                                    LogMessage($"[AI-CALL] Pseudocode result length: {pseudocode?.Length ?? 0}");

                                    if (!string.IsNullOrEmpty(pseudocode))
                                    {
                                        LogMessage($"[AI-PROCESS] Adding pseudocode to HTML for snap: {snap.Label}");
                                        html.AppendLine($"          <div class=\"code-example\">");
                                        html.AppendLine($"            <div class=\"code-title\">Pseudocode Implementation</div>");                                        html.AppendLine($"            <div class=\"code-content\">");
                                        html.AppendLine($"              <div class=\"code-sample\">");
                                        html.AppendLine($"<pre>{ConvertMarkdownToHtml(pseudocode)}</pre>");
                                        html.AppendLine($"              </div>");
                                        html.AppendLine($"            </div>");
                                        html.AppendLine($"          </div>");
                                    }
                                }
                                else
                                {
                                    LogMessage($"[AI-PROCESS] Skipping pseudocode generation for snap type: {snap.Type}");
                                }
                            }
                            else
                            {
                                LogMessage($"[AI-PROCESS] Snap type {snapTypeKey} already processed, skipping AI generation");
                            }
                        }
                        else
                        {
                            LogMessage($"[AI-ERROR] Current pipeline is null, cannot process AI for snap: {snap.Label}");
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log the error but don't disrupt the document generation
                        LogMessage($"[AI-ERROR] Error generating AI content for snap {snap.Label}: {ex.Message}");
                        Console.WriteLine($"Error generating AI content for snap {snap.Label}: {ex.Message}");
                    }
                }
                else
                {
                    LogMessage($"[AI-PROCESS] AI not enabled, skipping AI processing for snap: {snap.Label}");
                }

                LogMessage($"[SNAP-PROCESS] Completed AI processing section for snap: {snap.Label}");                // Add functional details if available - prioritize showing actual mappings
                LogMessage($"[SNAP-PROCESS] Starting function details for snap: {snap.Label}");
                string functionDetails = ProvideSnapFunctionDetails(snap);
                LogMessage($"[SNAP-PROCESS] Completed function details for snap: {snap.Label}, result length: {functionDetails?.Length ?? 0}");
                if (!string.IsNullOrEmpty(functionDetails))
                {
                    html.AppendLine($"          <div class=\"function-details\">");
                    html.AppendLine($"            {functionDetails}");
                    html.AppendLine($"          </div>");
                }

                // Add best practices if available - pass flag to avoid duplicate mapping extraction                
                bool hasMappings = !string.IsNullOrEmpty(functionDetails) &&
                                 (snap.Category == SnapCategory.Transformation &&
                                 (snap.Type?.ToLower().Contains("map") == true ||
                                  snap.Type?.ToLower().Contains("transform") == true ||
                                  snap.Type?.ToLower().Contains("script") == true));

                // Check if this is a flow control snap that should use enhanced configuration
                bool isFlowControlSnap = IsFlowControlSnap(snap);

                LogMessage($"[SNAP-PROCESS] Adding properties for snap: {snap.Label} (hasMappings: {hasMappings}, isFlowControlSnap: {isFlowControlSnap})");

                // For flow control snaps (Router, Join, Union), use enhanced configuration instead of raw properties
                if (isFlowControlSnap)
                {
                    LogMessage($"[FLOW-CONTROL-CONFIG] Generating enhanced configuration for {snap.Type} snap: {snap.Label}");
                    try
                    {
                        var flowControlGenerator = new FlowControlConfigurationGenerator();
                        
                        // Set up the snap with connections for the router configuration generator
                        SetupSnapConnections(snap);
                        
                        // Generate the router configuration which includes routing logic and connected snaps
                        string routerConfig = flowControlGenerator.GenerateRouterConfiguration(snap, _currentPipeline.Snaps);
                        html.AppendLine(routerConfig);
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"[FLOW-CONTROL-CONFIG] Error generating enhanced configuration for {snap.Type}: {ex.Message}");
                    }
                }
                // Properties - Display important configuration settings (skip for mapper snaps and flow control snaps)
                else if (snap.Properties.Any() && !hasMappings)
                {
                }
                
                // Add collapsible section for raw properties (except for router and condition snaps)
                bool skipRawProperties = (snap.Type?.ToLower()?.Contains("router") ?? false) || 
                                        (snap.Type?.ToLower()?.Contains("condition") ?? false);
                if (!skipRawProperties)
                {
                    html.AppendLine($"          <button class=\"collapsible\">View Raw Properties</button>");
                    html.AppendLine($"          <div class=\"collapsible-content\">");
                    DisplayRawProperties(html, snap);
                    html.AppendLine($"          </div>");
                }
                else
                {
                    LogMessage($"[SNAP-PROPERTIES] Skipping collapsible raw properties section for snap: {snap.Label} as it is a router snap");
                }

                LogMessage($"[SNAP-PROCESS] Starting best practices for snap: {snap.Label}");
                string bestPractices = ""; // Temporarily disabled until method signature is fixed
                LogMessage($"[SNAP-PROCESS] Completed best practices for snap: {snap.Label}, result length: {bestPractices?.Length ?? 0}");
                if (!string.IsNullOrEmpty(bestPractices))
                {
                    html.AppendLine($"          {bestPractices}");
                }

                html.AppendLine($"        </div>");
                LogMessage($"[SNAP-PROCESS] Completed processing snap: {snap.Label}");
            }

            html.AppendLine($"      </div>");
            LogMessage($"[SNAP-CATEGORY] Completed {categoryTitle} section");
        }
        private void AddCategoryDescription(StringBuilder html, string anchorId)
        {
            if (anchorId == "flow-control")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>Flow control snaps manage how documents move through the pipeline by " +
                               "routing, filtering, joining, or splitting document streams.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps control the path and flow of documents within the pipeline. They " +
                               "can create conditional branches (Router), merge multiple streams (Union), create copies of documents (Copy), filter documents " +
                               "based on criteria, sort them, or join related documents from different sources.</p>");
                html.AppendLine("          <p><strong>Common Configurations:</strong></p>");
                html.AppendLine("          <ul>");
                html.AppendLine("            <li><strong>Router</strong>: Configured with expressions that evaluate to true/false to determine which output path to follow</li>");
                html.AppendLine("            <li><strong>Join</strong>: Requires keys from both streams to match on, similar to SQL JOIN operations</li>");
                html.AppendLine("            <li><strong>Sort</strong>: Requires field names and sort direction (ascending/descending)</li>");
                html.AppendLine("            <li><strong>Filter</strong>: Uses expressions to determine which documents to pass forward</li>");
                html.AppendLine("          </ul>");
                html.AppendLine("        </div>");
            }
            else if (anchorId == "transformations")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>Transformation snaps manipulate the structure and content of documents by " +
                               "mapping fields, performing calculations, applying business logic, and reformatting data.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps modify document content or structure without changing the " +
                               "flow path. They can map fields from one schema to another, apply formulas and functions to field values, " +
                               "restructure documents, or convert between different data formats (JSON, XML, etc.)</p>");
                html.AppendLine("          <p><strong>Common Configurations:</strong></p>");
                html.AppendLine("          <ul>");
                html.AppendLine("            <li><strong>Mapper</strong>: Contains field-by-field mappings using expressions</li>");
                html.AppendLine("            <li><strong>JSON/XML Generator</strong>: Configured with templates for output format</li>");
                html.AppendLine("            <li><strong>JSON/XML Parser</strong>: Can be configured with paths to extract specific elements</li>");
                html.AppendLine("            <li><strong>Script</strong>: Contains custom JavaScript, Python or other code to transform data</li>");
                html.AppendLine("          </ul>");
                html.AppendLine("        </div>");
            }
            else if (anchorId == "database")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>Database snaps interact with SQL databases to read, write, or modify data. " +
                               "They convert between database tables and document formats using SQL operations.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps communicate with relational database systems using JDBC/ODBC " +
                               "connections. They translate between SnapLogic documents and database records, executing SQL statements for various " +
                               "operations like SELECT, INSERT, UPDATE, DELETE, or custom SQL.</p>");
                html.AppendLine("          <p><strong>Common Configurations:</strong></p>");
                html.AppendLine("          <ul>");
                html.AppendLine("            <li><strong>SELECT</strong>: Configured with table name, columns to retrieve, and WHERE conditions</li>");
                html.AppendLine("            <li><strong>INSERT</strong>: Requires table name and field-to-column mappings</li>");
                html.AppendLine("            <li><strong>UPDATE</strong>: Needs table name, fields to update, and key fields to identify records</li>");
                html.AppendLine("            <li><strong>SQL Execute</strong>: Takes raw SQL statements or stored procedure calls</li>");
                html.AppendLine("          </ul>");
                html.AppendLine("        </div>");
            }
            else if (anchorId == "external")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>External system snaps connect to third-party systems, APIs, or services " +
                               "to exchange data with systems outside the SnapLogic platform.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps manage authentication, connection parameters, " +
                               "and data exchange protocols with external systems. They handle credentials securely, format requests " +
                               "according to the target system's requirements, and parse responses back into SnapLogic documents.</p>");
                html.AppendLine("          <p><strong>Common Configurations:</strong></p>");
                html.AppendLine("          <ul>");
                html.AppendLine("            <li><strong>REST/HTTP</strong>: Configured with endpoint URLs, HTTP methods, headers, and authentication</li>");
                html.AppendLine("            <li><strong>Dynamics 365</strong>: Requires entity names, operation types, and field mappings</li>");
                html.AppendLine("            <li><strong>Salesforce</strong>: Uses object names, SOQL queries, or field mappings depending on operation</li>");
                html.AppendLine("            <li><strong>SOAP</strong>: Configured with WSDL information and XML message formats</li>");
                html.AppendLine("          </ul>");
                html.AppendLine("        </div>");
            }
            else if (anchorId == "file")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>File operation snaps read from or write to files in various formats, " +
                               "handling data import/export needs for the pipeline.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps interact with file systems " +
                               "(local or cloud storage) to read, write, list, or manage files. They handle file format parsing " +
                               "and generation (CSV, Excel, text, binary, etc.), file chunking for large files, and file metadata.</p>");
                html.AppendLine("          <p><strong>Common Configurations:</strong></p>");
                html.AppendLine("          <ul>");
                html.AppendLine("            <li><strong>File Reader</strong>: Configured with file path/pattern, format settings, and parsing options</li>");
                html.AppendLine("            <li><strong>File Writer</strong>: Requires destination path, file naming patterns, and format settings</li>");
                html.AppendLine("            <li><strong>CSV Parser/Generator</strong>: Configured with delimiter settings, header options, and encoding</li>");
                html.AppendLine("            <li><strong>Excel Read/Write</strong>: Requires worksheet names, cell ranges, and format options</li>");
                html.AppendLine("          </ul>");
                html.AppendLine("        </div>");
            }
            else if (anchorId == "error")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>Error handling snaps provide mechanisms to catch, log, and respond to errors " +
                               "that occur during pipeline execution, improving pipeline robustness.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps implement error management strategies " +
                               "including catching exceptions, logging error details, implementing retries, sending notifications, " +
                               "or providing fallback processing paths. They help create resilient pipelines that can recover from failures.</p>");
                html.AppendLine("          <p><strong>Common Configurations:</strong></p>");
                html.AppendLine("          <ul>");
                html.AppendLine("            <li><strong>Error Handler</strong>: Configured with error types to catch and recovery actions</li>");
                html.AppendLine("            <li><strong>Retry</strong>: Contains retry count, delay between attempts, and conditions for retrying</li>");
                html.AppendLine("            <li><strong>Error Log</strong>: Configured with logging level, destination, and format</li>");
                html.AppendLine("            <li><strong>Alert</strong>: Contains notification settings like email recipients or webhook endpoints</li>");
                html.AppendLine("          </ul>");
                html.AppendLine("        </div>");
            }
            else if (anchorId == "other")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>This category includes utility snaps and specialized components that don't fit into the other categories.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps may provide utility functions, custom logic, specialized " +
                               "processing, or integration with unique systems that don't fit cleanly into the standard categories.</p>");
                html.AppendLine("        </div>");
            }
        }

        // Comprehensive placeholder processing wrapper - ensures ALL content is processed
        private string ProcessContentForPlaceholders(string content)
        {
            if (string.IsNullOrEmpty(content))
                return content;

            // Create a mock codeBlocks dictionary for placeholder processing
            var codeBlocks = new Dictionary<string, string>();
            
            // Apply the same placeholder processing logic as in ConvertMarkdownToHtml
            string processedContent = content;

            // 1. Standard format with underscore: ##INLINE_CODE_xxxxxxxx##
            processedContent = Regex.Replace(processedContent, @"##INLINE_CODE_([a-zA-Z0-9]+)##", match =>
            {
                string key = match.Value;
                if (codeBlocks.ContainsKey(key))
                {
                    return $"<code>{codeBlocks[key]}</code>";
                }
                return match.Value; // Keep original if not found
            });

            // 2. Format without underscore: ##INLINECODExxxxxxxx##
            processedContent = Regex.Replace(processedContent, @"##INLINECODE([a-zA-Z0-9]+)##", match =>
            {
                string id = match.Groups[1].Value;
                string alternateFormat = $"##INLINE_CODE_{id}##";
                
                // Try exact match first (in case it was stored this way)
                if (codeBlocks.ContainsKey(alternateFormat))
                {
                    return $"<code>{codeBlocks[alternateFormat]}</code>";
                }

                // Try to find matching key with underscore format
                var keyWithUnderscore = $"##INLINE_CODE_{id}##";
                if (codeBlocks.ContainsKey(keyWithUnderscore))
                {
                    return $"<code>{codeBlocks[keyWithUnderscore]}</code>";
                }

                // Try partial match on any existing key
                var partialMatch = codeBlocks.Keys.FirstOrDefault(k => k.Contains(id));
                if (partialMatch != null)
                {
                    return $"<code>{codeBlocks[partialMatch]}</code>";
                }

                // For unmatched placeholders, try to provide intelligent fallback
                var fallback = GetFallbackForOrphanedPlaceholder(id, processedContent);
                return $"<code class=\"inferred-placeholder\">{fallback}</code>";
            });

            // 3. Catch any remaining malformed patterns
            processedContent = Regex.Replace(processedContent, @"##INLINE_CODE_([^#]+)##", match =>
            {
                string key = match.Value;
                if (codeBlocks.ContainsKey(key))
                {
                    return $"<code>{codeBlocks[key]}</code>";
                }
                
                // Extract just the ID for partial matching
                string id = match.Groups[1].Value;
                var partialMatch = codeBlocks.Keys.FirstOrDefault(k => k.Contains(id));
                if (partialMatch != null)
                {
                    return $"<code>{codeBlocks[partialMatch]}</code>";
                }
                
                // Try to provide intelligent fallback
                var fallback = GetFallbackForOrphanedPlaceholder(id, processedContent);
                return $"<code class=\"inferred-placeholder\">{fallback}</code>";
            });

            return processedContent;
        }

        /// <summary>
        /// Provides a fallback display value for placeholders that couldn't be resolved
        /// </summary>
        /// <param name="placeholderId">The ID of the placeholder</param>
        /// <param name="contextText">The surrounding text to provide context</param>
        /// <returns>A human-friendly placeholder replacement based on context</returns>
        private string GetFallbackForOrphanedPlaceholder(string placeholderId, string contextText)
        {
            var context = contextText.ToLower();
            
            var contextMapping = new Dictionary<string, string[]>
            {
                ["field"] = new[] { "field_name", "input_field", "source_field", "target_field" },
                ["map"] = new[] { "field_mapping", "data_mapping", "field_transformation" },
                ["sql"] = new[] { "SQL_statement", "query", "SELECT_statement" },
                ["error"] = new[] { "error_handling", "retry_count", "error_action" },
                ["mode"] = new[] { "execution_mode", "processing_mode", "operation_mode" },
                ["validate"] = new[] { "validation_setting", "validation_mode" },
                ["execute"] = new[] { "execution_setting", "execute_mode" },
                ["root"] = new[] { "root_path", "document_root", "$" },
                ["document"] = new[] { "document", "input_document", "data_document" },
                ["output"] = new[] { "output_field", "target_field", "result_field" },
                ["input"] = new[] { "input_field", "source_field", "data_field" },
                ["original"] = new[] { "original_field", "passthrough", "preserve_original" },
                ["retain"] = new[] { "retain_fields", "keep_original", "passthrough" },
                ["halt"] = new[] { "halt_on_error", "stop_on_error", "error_handling" },
                ["integrity"] = new[] { "data_integrity", "validation", "quality_check" },
                ["disabled"] = new[] { "passthrough_disabled", "feature_disabled", "setting_off" },
                ["retries"] = new[] { "retry_count", "error_retries", "0" }
            };

            foreach (var mapping in contextMapping)
            {
                if (context.Contains(mapping.Key))
                {
                    return mapping.Value[0];
                }
            }

            if (context.Contains("field") || context.Contains("map"))
            {
                return "field_name";
            }

            if (context.Contains("setting") || context.Contains("config") || context.Contains("parameter"))
            {
                return "configuration_value";
            }

            return $"placeholder_{placeholderId}";
        }

        /// <summary>
        /// Converts a technical snap type to a more user-friendly display name
        /// </summary>
        /// <param name="snapType">The raw snap type string</param>
        /// <returns>User-friendly snap type description</returns>
        private string GetFriendlySnapType(string snapType)
        {
            if (string.IsNullOrEmpty(snapType))
                return "Unknown Snap Type";
                
            // Extract the last component of the snap type for display
            string[] parts = snapType.Split('.');
            string simpleName = parts.Last();
            
            // Handle special cases and formatting
            simpleName = simpleName.Replace("Snap", "");
            
            // Format with spaces between camel case words
            string formatted = System.Text.RegularExpressions.Regex.Replace(
                simpleName, 
                "([a-z])([A-Z])", 
                "$1 $2"
            );
            
            return formatted;
        }        
        
        /// <summary>
        /// Gets a human-readable description for a snap
        /// </summary>
        /// <param name="snap">The snap object to describe</param>
        /// <returns>A description of the snap's purpose and function</returns>
        private string GetSnapDescription(SnapNode snap)
        {
            // First check if there is an enhanced AI-generated description
            if (snap.Properties.ContainsKey("enhancedDescription") && 
                !string.IsNullOrEmpty(snap.Properties["enhancedDescription"]))
            {
                // Process any placeholders in the enhanced description using improved logic
                string enhancedDesc = snap.Properties["enhancedDescription"];
                string processedDesc = ReplaceAllPlaceholders(enhancedDesc);
                
                return ConvertMarkdownToHtml(processedDesc);
            }
            
            // Fall back to the standard description if available
            if (snap.Properties.ContainsKey("description") && 
                !string.IsNullOrEmpty(snap.Properties["description"]))
            {
                // Also process placeholders in standard descriptions
                string standardDesc = snap.Properties["description"];
                string processedDesc = ReplaceAllPlaceholders(standardDesc);
                return ConvertMarkdownToHtml(processedDesc);
            }
            
            // Generate a basic description based on snap type and category
            return $"A {snap.Category} snap that {InferSnapFunction(snap.Type)}.";
        }
        
        /// <summary>
        /// Infers the function of a snap based on its type
        /// </summary>
        private string InferSnapFunction(string snapType)
        {
            if (string.IsNullOrEmpty(snapType))
                return "performs an unknown operation";
                
            string lowerType = snapType.ToLower();
            
            if (lowerType.Contains("mapper") || lowerType.Contains("transform"))
                return "transforms data";
                
            if (lowerType.Contains("join"))
                return "combines multiple data streams";
                
            if (lowerType.Contains("filter"))
                return "filters documents based on criteria";
                
            if (lowerType.Contains("router") || lowerType.Contains("switch"))
                return "routes documents based on conditions";
                
            if (lowerType.Contains("sort"))
                return "sorts documents";
                
            if (lowerType.Contains("aggregate"))
                return "aggregates data";
                
            if (lowerType.Contains("json") || lowerType.Contains("xml"))
                return "processes structured data";
                
            if (lowerType.Contains("file"))
                return "interacts with files";
                
            if (lowerType.Contains("http") || lowerType.Contains("rest"))
                return "communicates with web services";
                
            if (lowerType.Contains("db") || lowerType.Contains("sql"))
                return "interacts with databases";
                
            return "processes data in the pipeline";
        }        
        /// <summary>
        /// Finds all possible execution paths through a pipeline from a given starting point
        /// </summary>
        /// <param name="pipeline">The pipeline data</param>
        /// <param name="startNode">The node to start path finding from</param>
        /// <returns>List of execution paths through the pipeline</returns>
        private List<List<SnapNode>> FindExecutionPaths(PipelineData pipeline, SnapNode startNode)
        {
            var result = new List<List<SnapNode>>();
            var visited = new HashSet<string>();
            var currentPath = new List<SnapNode>();
            
            void DFS(SnapNode node)
            {
                if (node == null)
                    return;
                    
                // Skip if already visited (avoid cycles)
                if (visited.Contains(node.Id))
                    return;
                      visited.Add(node.Id);
                currentPath.Add(node);
                
                // Find outgoing links from this node
                var outgoingLinks = pipeline.Links.Where(l => l.SourceId == node.Id).ToList();
                
                // If no outgoing links, we've reached an endpoint - save this path
                if (outgoingLinks.Count == 0)
                {
                    result.Add(new List<SnapNode>(currentPath));
                }
                else
                {
                    // Continue DFS for each outgoing link
                    foreach (var link in outgoingLinks)
                    {
                        var targetNode = pipeline.Snaps.FirstOrDefault(s => s.Id == link.TargetId);
                        if (targetNode != null)
                        {
                            DFS(targetNode);
                        }
                    }
                }
                
                // Backtrack
                currentPath.RemoveAt(currentPath.Count - 1);
                visited.Remove(node.Id);
            }
            
            // Start DFS from the provided node
            DFS(startNode);
            return result;
        }
          /// <summary>
        /// Generates a summary description of an execution path
        /// </summary>
        /// <param name="path">The execution path to summarize</param>
        /// <returns>A human-readable summary of the path</returns>
        private string GeneratePathSummary(List<SnapNode> path)
        {
            if (path == null || path.Count == 0)
                return "Empty path";
                
            // For very short paths, just list the snaps
            if (path.Count <= 3)
            {
                return string.Join(" → ", path.Select(s => s.Label));
            }
            
            // For longer paths, provide a summary with start, end and count
            var firstSnap = path.First();
            var lastSnap = path.Last();
            var middleCount = path.Count - 2;
            
            return $"Starts with {firstSnap.Label}, passes through {middleCount} snap{(middleCount != 1 ? "s" : "")}, and ends at {lastSnap.Label}";
        }
        
        /// <summary>
        /// Provides detailed information about a snap's functionality
        /// </summary>        /// <param name="snap">The snap to analyze</param>
        /// <returns>HTML formatted details about the snap's function</returns>
        private string ProvideSnapFunctionDetails(SnapNode snap)
        {
            var details = new StringBuilder();
            LogToFile($"=== Processing Snap: {snap.Label} (Type: {snap.Type}, ID: {snap.Id}) ===");

            // Handle Mapper snaps specifically - be more flexible with detection
            string snapTypeLower = snap.Type?.ToLower() ?? "";
            LogToFile($"Analyzing snap type: '{snapTypeLower}'");
            
            bool isMapperSnap = snapTypeLower.Contains("com-snaplogic-snaps-transform-datatransform") ||
                             snapTypeLower.Contains("mapper") ||
                             (snapTypeLower.Contains("transform") && !snapTypeLower.Contains("condition")) ||
                             snapTypeLower.Contains("jsonformatter") ||
                             snapTypeLower.Contains("datatransform");
            
            // Skip mapping table lookup for condition snaps even if they have "transform" in the name
            if (snapTypeLower.Contains("condition"))
            {
                isMapperSnap = false;
                LogToFile("Skipping mapping table lookup for Condition snap");
            }
            
            if (isMapperSnap)
            {
                LogToFile($"\n=== Processing Mapper Snap ===");
                LogToFile($"Label: {snap.Label}");
                LogToFile($"Type: {snap.Type}");
                LogToFile($"ID: {snap.Id}");
                LogToFile($"Properties count: {snap.Properties.Count}");

                // Log ALL properties in full for debugging
                LogToFile("\nDEBUGGING: Full property list:");
                foreach (var prop in snap.Properties)
                {
                    var value = prop.Value ?? "null";
                    var displayValue = value.Length > 200 ? value.Substring(0, 200) + "..." : value;
                    LogToFile($"Property: {prop.Key} = {displayValue}");
                }

                LogToFile("\nLooking for mapping expressions...");
                
                // Check for mapping table in different possible property names
                string[] possibleMappingTableKeys = new[] { 
                    "settings.transformations.mappingTable.value",
                    "transformations.mappingTable.value",
                    "mappingTable.value", "mappingTable", "mappings", "transformersList", 
                    "transformer", "table", "mapTable", "targetList", "mapList",
                    "properties.mappingTable", "properties.mappings" };
                string mappingTableJson = null;
                string foundKey = null;
                
                foreach (var key in possibleMappingTableKeys)
                {
                    // Handle direct property access
                    if (snap.Properties.TryGetValue(key, out mappingTableJson))
                    {
                        foundKey = key;
                        LogToFile($"Found mapping table directly in property: {key}");
                        break;
                    }
                    
                    // Handle nested property paths (e.g. 'settings.transformations.mappingTable.value')
                    if (key.Contains("."))
                    {
                        string[] pathParts = key.Split('.');
                        string currentKey = pathParts[0];
                        
                        if (snap.Properties.TryGetValue(currentKey, out string nestedJson))
                        {
                            try 
                            {
                                // Try to parse the nested structure
                                JObject currentObj = JObject.Parse(nestedJson);
                                JToken currentToken = currentObj;
                                bool foundPath = true;
                                
                                // Navigate through the nested path
                                for (int i = 1; i < pathParts.Length; i++)
                                {
                                    currentToken = currentToken[pathParts[i]];
                                    if (currentToken == null)
                                    {
                                        foundPath = false;
                                        break;
                                    }
                                }
                                
                                if (foundPath && currentToken != null)
                                {
                                    foundKey = key;
                                    mappingTableJson = currentToken.ToString();
                                    LogToFile($"Found mapping table through nested path: {key}");
                                    break;
                                }
                            }
                            catch
                            {
                                // Continue to the next key if this path doesn't work
                            }
                        }
                    }
                }
                
                if (foundKey != null)
                {
                    LogToFile($"Found mapping table in property: {foundKey}");
                    LogToFile($"Mapping table content (first 500 chars): {mappingTableJson?.Substring(0, Math.Min(500, mappingTableJson?.Length ?? 0))}...");
                }
                else
                {
                    var errorMsg = "No mapping table found in any known property";
                    LogToFile($"{errorMsg}. Checked for: {string.Join(", ", possibleMappingTableKeys)}");
                    LogToFile("Available properties:");
                    foreach (var key in snap.Properties.Keys)
                    {
                        LogToFile($"- {key}");
                    }
                    
                    // Special handling: try to parse the whole snap as JSON
                    LogToFile("\nAttempting to find mappings by parsing entire snap properties as JSON...");
                    try
                    {
                        foreach (var propEntry in snap.Properties)
                        {
                            // Skip very small properties - they won't contain mapping tables
                            if (propEntry.Value?.Length < 10) continue;
                            
                            try 
                            {
                                // Try to parse this property as JSON
                                LogToFile($"Trying to parse '{propEntry.Key}' as JSON...");
                                JObject propJson = JObject.Parse(propEntry.Value);
                                
                                // Look for mapping-related fields in this JSON
                                var mappingField = propJson["mappingTable"] ?? propJson["mappings"] ?? 
                                                  propJson["transformer"] ?? propJson["table"];
                                                  
                                if (mappingField != null)
                                {
                                    LogToFile($"Found potential mapping data in '{propEntry.Key}.{mappingField.Path}'");
                                    mappingTableJson = mappingField.ToString();
                                    foundKey = propEntry.Key + "." + mappingField.Path;
                                    break;
                                }
                            }
                            catch
                            {
                                // Just continue to the next property
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogToFile($"Error during advanced mapping search: {ex.Message}");
                    }
                }
                
                if (!string.IsNullOrEmpty(mappingTableJson))
                {
                    try 
                    {
                        LogToFile("Attempting to parse mapping table...");
                        var mappingTable = JArray.Parse(mappingTableJson);
                        LogToFile($"Successfully parsed {mappingTable.Count} mappings from {foundKey}");
                        
                        var expressions = new List<(string TargetPath, string Expression)>();
                        int processedCount = 0;
                        
                        // Extract all mappings
                        foreach (var mapping in mappingTable)
                        {
                            try 
                            {
                                // More robust way to extract target path and expression
                                string targetPath = null;
                                string expression = null;
                                
                                // Log the mapping structure to help diagnose issues
                                LogToFile($"Mapping structure: {mapping.ToString(Newtonsoft.Json.Formatting.None)}");
                                
                                // Try multiple paths to extract target path
                                if (mapping["targetPath"] != null)
                                {
                                    if (mapping["targetPath"]["value"] != null)
                                        targetPath = mapping["targetPath"]["value"].ToString();
                                    else
                                        targetPath = mapping["targetPath"].ToString();
                                }
                                else if (mapping["target"] != null)
                                {
                                    if (mapping["target"]["value"] != null)
                                        targetPath = mapping["target"]["value"].ToString();
                                    else
                                        targetPath = mapping["target"].ToString();
                                }
                                
                                // Try multiple paths to extract expression
                                if (mapping["expression"] != null)
                                {
                                    if (mapping["expression"]["value"] != null)
                                        expression = mapping["expression"]["value"].ToString();
                                    else
                                        expression = mapping["expression"].ToString();
                                }
                                else if (mapping["expr"] != null)
                                {
                                    if (mapping["expr"]["value"] != null)
                                        expression = mapping["expr"]["value"].ToString();
                                    else
                                        expression = mapping["expr"].ToString();
                                }
                                else if (mapping["source"] != null)
                                {
                                    if (mapping["source"]["value"] != null)
                                        expression = mapping["source"]["value"].ToString();
                                    else
                                        expression = mapping["source"].ToString();
                                }
                                
                                if (!string.IsNullOrEmpty(targetPath) && !string.IsNullOrEmpty(expression))
                                {
                                    expressions.Add((targetPath, expression));
                                    LogToFile($"Found mapping [{processedCount + 1}]: {targetPath} -> {expression}");
                                    processedCount++;
                                }
                            }
                            catch (Exception ex)
                            {
                                LogToFile($"Error processing mapping {processedCount + 1}: {ex.Message}");
                            }
                        }

                        if (expressions.Count > 0)
                        {
                            LogToFile($"Successfully extracted {expressions.Count} valid mappings out of {mappingTable.Count}");
                            
                            details.AppendLine("<h4>Mapper Expressions</h4>");
                            details.AppendLine("<div class=\"mapper-expressions\" style=\"max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin-bottom: 15px;\">");
                            details.AppendLine("<table class=\"mapper-table\" style=\"width: 100%; border-collapse: collapse;\">");
                            details.AppendLine("<tr style=\"background-color: #f5f5f5;\"><th style=\"padding: 8px; text-align: left; border-bottom: 1px solid #ddd;\">Expression</th><th style=\"padding: 8px; text-align: left; border-bottom: 1px solid #ddd; width: 30%;\">Output Variable</th></tr>");
                            
                            foreach (var (targetPath, expression) in expressions)
                            {
                                details.AppendLine("<tr>");
                                details.AppendLine($"<td style=\"padding: 8px; border-bottom: 1px solid #eee;\"><pre style=\"margin: 0; white-space: pre-wrap; word-break: break-all;\">{EscapeHtml(expression)}</pre></td>");
                                details.AppendLine($"<td style=\"padding: 8px; border-bottom: 1px solid #eee; word-break: break-word;\">{EscapeHtml(targetPath)}</td>");
                                details.AppendLine("</tr>");
                            }
                            
                            details.AppendLine("</table></div>");
                            LogToFile("Successfully generated mapper expressions HTML");
                            return details.ToString();
                        }
                        else
                        {
                            var errorMsg = "No valid mappings found in the mapping table";
                            LogToFile(errorMsg);
                            details.AppendLine($"<p>Error: {EscapeHtml(errorMsg)}</p>");
                            
                            // Log the first few mappings to help with debugging
                            LogToFile("Sample of first few mappings (raw JSON):");
                            for (int i = 0; i < Math.Min(3, mappingTable.Count); i++)
                            {
                                LogToFile($"Mapping {i}: {mappingTable[i].ToString(Newtonsoft.Json.Formatting.None)}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        var errorMsg = $"Error parsing mapping table: {ex.Message}";
                        LogToFile($"{errorMsg}\n{ex.StackTrace}");
                        LogToFile($"Raw JSON that failed to parse (first 500 chars): {mappingTableJson?.Substring(0, Math.Min(500, mappingTableJson?.Length ?? 0))}...");
                        details.AppendLine($"<p>Error: {EscapeHtml(errorMsg)}</p>");
                    }
                }
                else
                {
                    var errorMsg = "No mapping table found in any known property";
                    LogToFile($"{errorMsg}. Checked for: {string.Join(", ", possibleMappingTableKeys)}");
                    LogToFile("Available properties:");
                    foreach (var key in snap.Properties.Keys)
                    {
                        LogToFile($"- {key}");
                    }
                    
                    // Special handling: try to parse the whole snap as JSON
                    LogToFile("\nAttempting to find mappings by parsing entire snap properties as JSON...");
                    try
                    {
                        foreach (var propEntry in snap.Properties)
                        {
                            // Skip very small properties - they won't contain mapping tables
                            if (propEntry.Value?.Length < 10) continue;
                            
                            try 
                            {
                                // Try to parse this property as JSON
                                LogToFile($"Trying to parse '{propEntry.Key}' as JSON...");
                                JObject propJson = JObject.Parse(propEntry.Value);
                                
                                // Look for mapping-related fields in this JSON
                                var mappingField = propJson["mappingTable"] ?? propJson["mappings"] ?? 
                                                  propJson["transformer"] ?? propJson["table"];
                                                  
                                if (mappingField != null)
                                {
                                    LogToFile($"Found potential mapping data in '{propEntry.Key}.{mappingField.Path}'");
                                    mappingTableJson = mappingField.ToString();
                                    foundKey = propEntry.Key + "." + mappingField.Path;
                                    break;
                                }
                            }
                            catch
                            {
                                // Just continue to the next property
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogToFile($"Error during advanced mapping search: {ex.Message}");
                    }
                }
                
                if (!string.IsNullOrEmpty(mappingTableJson))
                {
                    try 
                    {
                        LogToFile("Attempting to parse mapping table...");
                        var mappingTable = JArray.Parse(mappingTableJson);
                        LogToFile($"Successfully parsed {mappingTable.Count} mappings from {foundKey}");
                        
                        var expressions = new List<(string TargetPath, string Expression)>();
                        int processedCount = 0;
                        
                        // Extract all mappings
                        foreach (var mapping in mappingTable)
                        {
                            try 
                            {
                                // More robust way to extract target path and expression
                                string targetPath = null;
                                string expression = null;
                                
                                // Log the mapping structure to help diagnose issues
                                LogToFile($"Mapping structure: {mapping.ToString(Newtonsoft.Json.Formatting.None)}");
                                
                                // Try multiple paths to extract target path
                                if (mapping["targetPath"] != null)
                                {
                                    if (mapping["targetPath"]["value"] != null)
                                        targetPath = mapping["targetPath"]["value"].ToString();
                                    else
                                        targetPath = mapping["targetPath"].ToString();
                                }
                                else if (mapping["target"] != null)
                                {
                                    if (mapping["target"]["value"] != null)
                                        targetPath = mapping["target"]["value"].ToString();
                                    else
                                        targetPath = mapping["target"].ToString();
                                }
                                
                                // Try multiple paths to extract expression
                                if (mapping["expression"] != null)
                                {
                                    if (mapping["expression"]["value"] != null)
                                        expression = mapping["expression"]["value"].ToString();
                                    else
                                        expression = mapping["expression"].ToString();
                                }
                                else if (mapping["expr"] != null)
                                {
                                    if (mapping["expr"]["value"] != null)
                                        expression = mapping["expr"]["value"].ToString();
                                    else
                                        expression = mapping["expr"].ToString();
                                }
                                else if (mapping["source"] != null)
                                {
                                    if (mapping["source"]["value"] != null)
                                        expression = mapping["source"]["value"].ToString();
                                    else
                                        expression = mapping["source"].ToString();
                                }
                                
                                if (!string.IsNullOrEmpty(targetPath) && !string.IsNullOrEmpty(expression))
                                {
                                    expressions.Add((targetPath, expression));
                                    LogToFile($"Found mapping [{processedCount + 1}]: {targetPath} -> {expression}");
                                    processedCount++;
                                }
                            }
                            catch (Exception ex)
                            {
                                LogToFile($"Error processing mapping {processedCount + 1}: {ex.Message}");
                            }
                        }

                        if (expressions.Count > 0)
                        {
                            LogToFile($"Successfully extracted {expressions.Count} valid mappings out of {mappingTable.Count}");
                            
                            details.AppendLine("<h4>Mapper Expressions</h4>");
                            details.AppendLine("<div class=\"mapper-expressions\" style=\"max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin-bottom: 15px;\">");
                            details.AppendLine("<table class=\"mapper-table\" style=\"width: 100%; border-collapse: collapse;\">");
                            details.AppendLine("<tr style=\"background-color: #f5f5f5;\"><th style=\"padding: 8px; text-align: left; border-bottom: 1px solid #ddd;\">Expression</th><th style=\"padding: 8px; text-align: left; border-bottom: 1px solid #ddd; width: 30%;\">Output Variable</th></tr>");
                            
                            foreach (var (targetPath, expression) in expressions)
                            {
                                details.AppendLine("<tr>");
                                details.AppendLine($"<td style=\"padding: 8px; border-bottom: 1px solid #eee;\"><pre style=\"margin: 0; white-space: pre-wrap; word-break: break-all;\">{EscapeHtml(expression)}</pre></td>");
                                details.AppendLine($"<td style=\"padding: 8px; border-bottom: 1px solid #eee; word-break: break-word;\">{EscapeHtml(targetPath)}</td>");
                                details.AppendLine("</tr>");
                            }
                            
                            details.AppendLine("</table></div>");
                            LogToFile("Successfully generated mapper expressions HTML");
                            return details.ToString();
                        }
                        else
                        {
                            var errorMsg = "No valid mappings found in the mapping table";
                            LogToFile(errorMsg);
                            details.AppendLine($"<p>Error: {EscapeHtml(errorMsg)}</p>");
                            
                            // Log the first few mappings to help with debugging
                            LogToFile("Sample of first few mappings (raw JSON):");
                            for (int i = 0; i < Math.Min(3, mappingTable.Count); i++)
                            {
                                LogToFile($"Mapping {i}: {mappingTable[i].ToString(Newtonsoft.Json.Formatting.None)}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        var errorMsg = $"Error parsing mapping table: {ex.Message}";
                        LogToFile($"{errorMsg}\n{ex.StackTrace}");
                        LogToFile($"Raw JSON that failed to parse (first 500 chars): {mappingTableJson?.Substring(0, Math.Min(500, mappingTableJson?.Length ?? 0))}...");
                        details.AppendLine($"<p>Error: {EscapeHtml(errorMsg)}</p>");
                    }
                }
                else
                {
                    var errorMsg = "No mapping table found in any known property";
                    LogToFile($"{errorMsg}. Checked for: {string.Join(", ", possibleMappingTableKeys)}");
                    LogToFile("Available properties:");
                    foreach (var key in snap.Properties.Keys)
                    {
                        LogToFile($"- {key}");
                    }
                    details.AppendLine($"<p>Error: {EscapeHtml(errorMsg)}</p>");
                }
            }
            // Handle other field mappings (non-mapper snaps)
            else if (snap.Properties.ContainsKey("fieldMappings") && 
                    !string.IsNullOrEmpty(snap.Properties["fieldMappings"]))
            {
                details.AppendLine("<h4>Field Mappings</h4>");
                details.AppendLine("<ul class=\"field-mappings\" style=\"max-height: 200px; overflow-y: auto; padding-left: 20px;\">");
                
                string mappings = ReplaceAllPlaceholders(snap.Properties["fieldMappings"]);
                string[] mappingLines = mappings.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                
                foreach (var line in mappingLines)
                {
                    string cleanLine = line.Trim();
                    if (!string.IsNullOrEmpty(cleanLine))
                    {
                        details.AppendLine($"<li style=\"margin-bottom: 5px;\">{System.Net.WebUtility.HtmlEncode(cleanLine)}</li>");
                    }
                }
                
                details.AppendLine("</ul>");
            }
            
            // Check for SQL queries
            if (snap.Properties.ContainsKey("sqlQuery") && 
                !string.IsNullOrEmpty(snap.Properties["sqlQuery"]))
            {
                details.AppendLine("<h4>SQL Query</h4>");
                details.AppendLine("<pre class=\"sql-query\">");
                details.AppendLine(System.Net.WebUtility.HtmlEncode(ReplaceAllPlaceholders(snap.Properties["sqlQuery"])));
                details.AppendLine("</pre>");
            }
            
            // Check for JSON Schema
            if (snap.Properties.ContainsKey("jsonSchema") && 
                !string.IsNullOrEmpty(snap.Properties["jsonSchema"]))
            {
                details.AppendLine("<h4>JSON Schema</h4>");
                details.AppendLine("<pre class=\"json-schema\">");
                details.AppendLine(System.Net.WebUtility.HtmlEncode(ReplaceAllPlaceholders(snap.Properties["jsonSchema"])));
                details.AppendLine("</pre>");
            }
            
            // Check for specific configurations based on snap type
            if (IsFlowControlSnap(snap))
            {
                // If this is a router snap and we have the pipeline context, use the FlowControlConfigurationGenerator
                // to generate a comprehensive routing table that includes both conditions and connected snaps
                if (_currentPipeline != null && snap.Type.ToLower().Contains("router"))
                {
                    var flowControlGenerator = new FlowControlConfigurationGenerator();
                    
                    // Set up the snap with connections for the router configuration generator
                    SetupSnapConnections(snap);
                    
                    // Generate the router configuration which includes routing logic and connected snaps
                    string routerConfig = flowControlGenerator.GenerateRouterConfiguration(snap, _currentPipeline.Snaps);
                    details.AppendLine(routerConfig);
                }
                else
                {
                    // For non-router flow control snaps, show basic flow settings
                    details.AppendLine("<h4>Flow Control Settings</h4>");
                    details.AppendLine("<ul class=\"flow-settings\">");
                    
                    if (snap.Properties.ContainsKey("routingConditions"))
                    {
                        details.AppendLine($"<li><strong>Routing Conditions:</strong> {System.Net.WebUtility.HtmlEncode(ReplaceAllPlaceholders(snap.Properties["routingConditions"]))}</li>");
                    }
                    
                    if (snap.Properties.ContainsKey("defaultPath"))
                    {
                        details.AppendLine($"<li><strong>Default Path:</strong> {System.Net.WebUtility.HtmlEncode(ReplaceAllPlaceholders(snap.Properties["defaultPath"]))}</li>");
                    }
                    
                    details.AppendLine("</ul>");
                    
                    // Add connected snaps information for flow control snaps
                    // Use pipeline context to find connections
                    if (_currentPipeline != null)
                    {
                        var outgoingLinks = _currentPipeline.Links.Where(l => l.SourceId == snap.Id).ToList();
                        
                        if (outgoingLinks.Any())
                        {
                            details.AppendLine("<h4>Connected Output Snaps</h4>");
                            details.AppendLine("<ul class=\"connected-snaps\">");
                            
                            foreach (var link in outgoingLinks)
                            {
                                var targetSnap = _currentPipeline.Snaps.FirstOrDefault(s => s.Id == link.TargetId);
                                if (targetSnap != null)
                                {
                                    // Show the view name (if available) along with the target snap label
                                    string viewName = link.SourceViewId ?? "Default";
                                    details.AppendLine($"<li><strong>{System.Net.WebUtility.HtmlEncode(viewName)}:</strong> {System.Net.WebUtility.HtmlEncode(targetSnap.Label)}</li>");
                                }
                            }
                            
                            details.AppendLine("</ul>");
                        }
                        else
                        {
                            details.AppendLine("<p><em>No connected output snaps</em></p>");
                        }
                    }
                }
            }
            
            // Check for router snaps which should use explicit pseudocode generation
            if (snap.Type.ToLower().Contains("router"))
            {
                // Skip raw properties for router snaps as requested
                LogMessage($"[SNAP-PROPERTIES] Skipping raw properties display for router snap: {snap.Label}");
            }
            else
            {
              // Raw properties section is already handled in the main snap details generation
              // Removed duplicate "View Raw Properties" section to avoid duplication
            }

            LogMessage($"[SNAP-PROCESS] Starting best practices for snap: {snap.Label}");
            string bestPractices = ""; // Temporarily disabled until method signature is fixed
            LogMessage($"[SNAP-PROCESS] Completed best practices for snap: {snap.Label}, result length: {bestPractices?.Length ?? 0}");
            if (!string.IsNullOrEmpty(bestPractices))
            {
                details.AppendLine($"          {bestPractices}");
            }

            return details.ToString();
        }
        
        /// <summary>
        /// Sets up a snap's InputConnections and OutputConnections from the pipeline context
        /// This is needed for the FlowControlConfigurationGenerator to properly display connected snaps
        /// </summary>
        /// <param name="snap">The snap to set up connections for</param>
        private void SetupSnapConnections(SnapNode snap)
        {
            if (_currentPipeline == null || snap == null)
                return;
                
            LogToFile($"Setting up connections for snap {snap.Label}");
            
            // Initialize collections if needed
            snap.InputConnections = snap.InputConnections ?? new List<SnapLink>();
            snap.OutputConnections = snap.OutputConnections ?? new List<SnapLink>();
            
            // Find all links where this snap is the target (inputs)
            var incomingLinks = _currentPipeline.Links.Where(l => l.TargetId == snap.Id).ToList();
            foreach (var link in incomingLinks)
            {
                // Only add if not already present
                if (!snap.InputConnections.Any(c => c.Id == link.Id))
                {
                    snap.InputConnections.Add(link);
                    LogToFile($"Added input connection from {link.SourceId} to {snap.Label}");
                }
            }
            
            // Find all links where this snap is the source (outputs)
            var outgoingLinks = _currentPipeline.Links.Where(l => l.SourceId == snap.Id).ToList();
            foreach (var link in outgoingLinks)
            {
                // Only add if not already present
                if (!snap.OutputConnections.Any(c => c.Id == link.Id))
                {
                    snap.OutputConnections.Add(link);
                    
                    // Ensure the target snap exists in the pipeline
                    var targetSnap = _currentPipeline.Snaps.FirstOrDefault(s => s.Id == link.TargetId);
                    if (targetSnap != null)
                    {
                        LogToFile($"Added output connection from {snap.Label} to {targetSnap.Label} with view {link.SourceViewId ?? "Default"}");
                    }
                }
            }
        }
        
        /// <summary>
        /// Determines if a snap is a flow control element (router, switch, etc.)
        /// </summary>
        /// <param name="snap">The snap to check</param>        /// <returns>True if the snap controls document flow</returns>
        private bool IsFlowControlSnap(SnapNode snap)
        {
            if (snap == null)
                return false;
                
            string snapType = snap.Type?.ToLower() ?? "";
            
            return snapType.Contains("router") || 
                   snapType.Contains("switch") || 
                   snapType.Contains("branch") || 
                   snapType.Contains("conditional") ||
                   snapType.Contains("if") ||
                   snapType.Contains("case") ||
                   (snap.Category == SnapCategory.FlowControl);
        }
        
        /// <summary>
        /// Escapes HTML special characters to prevent XSS and ensure proper display
        /// </summary>
        private string EscapeHtml(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;
                
            return System.Net.WebUtility.HtmlEncode(input);
        }
        
        /// <summary>
        /// Displays all raw properties of a snap for debugging or detailed views
        /// </summary>
        /// <param name="html">StringBuilder to append the HTML content</param>        /// <param name="snap">The snap whose properties should be displayed</param>
        private void DisplayRawProperties(StringBuilder html, SnapNode snap)
        {
            // Skip displaying raw properties for router and condition snaps
            if ((snap.Type?.ToLower()?.Contains("router") ?? false) || 
                (snap.Type?.ToLower()?.Contains("condition") ?? false) ||
                (snap.Type?.ToLower()?.Contains("branch") ?? false) ||
                (snap.Type?.ToLower()?.Contains("switch") ?? false) ||
                (snap.Type?.ToLower()?.Contains("if") ?? false) ||
                (snap.Type?.ToLower()?.Contains("filter") ?? false))
            {
                LogMessage($"[SNAP-PROPERTIES] Skipping raw properties display for flow control snap: {snap.Label} of type {snap.Type}");
                return;
            }
            
            html.AppendLine("<h4>All Properties</h4>");
            html.AppendLine("<div class=\"raw-properties\">");
            html.AppendLine("<table class=\"property-table\">");
            html.AppendLine("<tr><th>Property</th><th>Value</th></tr>");
            
            foreach (var prop in snap.Properties.OrderBy(p => p.Key))
            {
                if (!string.IsNullOrEmpty(prop.Value))
                {
                    string processedValue = ReplaceAllPlaceholders(prop.Value);
                    html.AppendLine($"<tr><td>{System.Net.WebUtility.HtmlEncode(prop.Key)}</td><td>{System.Net.WebUtility.HtmlEncode(processedValue)}</td></tr>");
                }
            }
            
            html.AppendLine("</table>");
            html.AppendLine("</div>");
        }
        
        /// <summary>
        /// Generates PDF documentation for a pipeline
        /// </summary>
        /// <param name="pipeline">The pipeline data</param>
        /// <param name="diagramSvg">The SVG diagram of the pipeline</param>
        /// <param name="outputPath">The output file path for the PDF</param>
        public void GeneratePdfDocumentation(PipelineData pipeline, string diagramSvg, string outputPath)
        {
            try
            {
                LogMessage($"[PDF-GEN] Starting PDF generation for {pipeline.Name}");
                
                // Generate HTML content first
                string htmlContent = GenerateHtmlDocumentationAsync(pipeline, diagramSvg).GetAwaiter().GetResult();
                
                // Create a temporary HTML file
                string tempHtmlPath = Path.GetTempFileName() + ".html";
                File.WriteAllText(tempHtmlPath, htmlContent);
                
                LogMessage($"[PDF-GEN] Created temporary HTML file at {tempHtmlPath}");
                
                // Use a PDF conversion library or command-line tool to convert HTML to PDF
                // For example, using wkhtmltopdf command-line tool:
                using (var process = new System.Diagnostics.Process())
                {
                    process.StartInfo.FileName = "wkhtmltopdf";
                    process.StartInfo.Arguments = $"\"{tempHtmlPath}\" \"{outputPath}\"";
                    process.StartInfo.UseShellExecute = false;
                    process.StartInfo.CreateNoWindow = true;
                    process.StartInfo.RedirectStandardOutput = true;
                    process.StartInfo.RedirectStandardError = true;
                    
                    LogMessage($"[PDF-GEN] Executing PDF conversion command");
                    
                    process.Start();
                    process.WaitForExit();
                    
                    string output = process.StandardOutput.ReadToEnd();
                    string error = process.StandardError.ReadToEnd();
                    
                    if (!string.IsNullOrEmpty(error))
                    {
                        LogMessage($"[PDF-GEN] Error converting to PDF: {error}");
                    }
                    
                    if (process.ExitCode != 0)
                    {
                        LogMessage($"[PDF-GEN] PDF conversion failed with exit code {process.ExitCode}");
                        throw new Exception($"PDF generation failed with exit code {process.ExitCode}");
                    }
                }
                
                LogMessage($"[PDF-GEN] Successfully generated PDF at {outputPath}");
                
                // Clean up the temporary HTML file
                try
                {
                    File.Delete(tempHtmlPath);
                }
                catch (Exception ex)
                {
                    LogMessage($"[PDF-GEN] Warning: Could not delete temporary HTML file: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"[PDF-GEN] Error generating PDF: {ex.Message}");
                throw new Exception($"Failed to generate PDF documentation: {ex.Message}", ex);
            }
        }

        
        /// <summary>
        /// Gets snaps ordered by their flow sequence in the pipeline
        /// </summary>
        private List<SnapNode> GetSnapFlowOrder(PipelineData pipeline)
        {
            var orderedSnaps = new List<SnapNode>();
            var processedSnaps = new HashSet<string>();
            
            // Find starting snaps (snaps with no input connections)
            var startingSnaps = pipeline.Snaps.Where(snap => 
                !pipeline.Links.Any(link => link.TargetId == snap.Id)).ToList();
            
            // If no clear starting snaps, use all snaps as potential starts
            if (!startingSnaps.Any())
            {
                startingSnaps = pipeline.Snaps.ToList();
            }
            
            // Traverse from each starting snap
            foreach (var startSnap in startingSnaps)
            {
                TraverseSnapFlow(startSnap, pipeline, orderedSnaps, processedSnaps);
            }
            
            // Add any remaining snaps that weren't connected
            foreach (var snap in pipeline.Snaps)
            {
                if (!processedSnaps.Contains(snap.Id))
                {
                    orderedSnaps.Add(snap);
                }
            }
            
            return orderedSnaps;
        }
        
        /// <summary>
        /// Recursively traverses snap connections to build flow order
        /// </summary>
        private void TraverseSnapFlow(SnapNode snap, PipelineData pipeline, List<SnapNode> orderedSnaps, HashSet<string> processedSnaps)
        {
            if (processedSnaps.Contains(snap.Id))
                return;
                
            processedSnaps.Add(snap.Id);
            orderedSnaps.Add(snap);
            
            // Find connected snaps
            var connectedSnaps = pipeline.Links
                .Where(link => link.SourceId == snap.Id)
                .Select(link => pipeline.Snaps.FirstOrDefault(s => s.Id == link.TargetId))
                .Where(s => s != null)
                .ToList();
            
            foreach (var connectedSnap in connectedSnaps)
            {
                TraverseSnapFlow(connectedSnap, pipeline, orderedSnaps, processedSnaps);
            }
        }
        
        /// <summary>
        /// Gets CSS class name for snap category
        /// </summary>
        private string GetCategoryClass(SnapCategory category)
        {
            return category switch
            {
                SnapCategory.FlowControl => "flow-control",
                SnapCategory.Transformation => "transformation",
                SnapCategory.Database => "database",
                SnapCategory.ExternalSystem => "external-system",
                SnapCategory.FileOperation => "file-operation",
                SnapCategory.ErrorHandling => "error-handling",
                _ => "other"
            };
        }
        
        /// <summary>
        /// Gets display name for snap category
        /// </summary>
        private string GetCategoryDisplayName(SnapCategory category)
        {
            return category switch
            {
                SnapCategory.FlowControl => "Flow Control",
                SnapCategory.Transformation => "Transformation",
                SnapCategory.Database => "Database Operations",
                SnapCategory.ExternalSystem => "External Systems",
                SnapCategory.FileOperation => "File Operations",
                SnapCategory.ErrorHandling => "Error Handling",
                _ => "Other"
            };
        }
        
        /// <summary>
        /// Generates a unique HTML ID for a snap
        /// </summary>
        private string GenerateSnapId(SnapNode snap)
        {
            // Create a safe HTML ID from the snap label and ID
            string safeLabel = snap.Label?.Replace(" ", "-").Replace("(", "").Replace(")", "")
                .Replace("[", "").Replace("]", "").Replace(".", "-").Replace(",", "")
                .Replace("'", "").Replace("\"", "").Replace("/", "-").Replace("\\", "-")
                .Replace(":", "-").Replace(";", "-").Replace("?", "").Replace("!", "")
                .Replace("@", "").Replace("#", "").Replace("$", "").Replace("%", "")
                .Replace("^", "").Replace("&", "").Replace("*", "").Replace("+", "")
                .Replace("=", "").Replace("|", "").Replace("<", "").Replace(">", "")
                .ToLower() ?? "snap";
            
            return $"snap-{safeLabel}-{snap.Id}";
        }

        /// <summary>
        /// Extracts database connection information from snap properties
        /// </summary>
        private string GetDatabaseConnectionInfo(SnapNode snap)
        {
            if (snap.Category != SnapCategory.Database)
                return null;

            LogMessage($"[DB-CONNECTION] Checking connection info for snap: {snap.Label}, Properties count: {snap.Properties.Count}");
            
            // Log all properties for debugging
            foreach (var prop in snap.Properties)
            {
                LogMessage($"[DB-CONNECTION] Property: {prop.Key} = {prop.Value}");
            }

            // First, look for the nested account structure
            if (snap.Properties.ContainsKey("account"))
            {
                try
                {
                    string accountJson = snap.Properties["account"];
                    LogMessage($"[DB-CONNECTION] Found account property, parsing JSON: {accountJson}");
                    
                    var accountObj = JObject.Parse(accountJson);
                    
                    // Navigate through the nested structure: account -> account_ref -> value -> label -> value
                    var accountRef = accountObj["account_ref"];
                    if (accountRef != null)
                    {
                        var valueObj = accountRef["value"];
                        if (valueObj != null)
                        {
                            var labelObj = valueObj["label"];
                            if (labelObj != null)
                            {
                                var accountName = labelObj["value"]?.ToString();
                                if (!string.IsNullOrEmpty(accountName))
                                {
                                    LogMessage($"[DB-CONNECTION] Found account name from nested JSON: {accountName}");
                                    return accountName;
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogMessage($"[DB-CONNECTION] Error parsing account JSON: {ex.Message}");
                }
            }

            // Look for account/connection properties, but filter out boolean values
            var connectionProps = snap.Properties.Where(p =>
                (p.Key.ToLower().Contains("account") ||
                p.Key.ToLower().Contains("connect") ||
                p.Key.ToLower().Contains("datasource") ||
                p.Key.ToLower().Contains("database") ||
                p.Key.ToLower().Contains("server") ||
                p.Key.ToLower().Contains("sqlserver") ||
                p.Key.ToLower().Contains("sql_server") ||
                p.Key.ToLower().Contains("dbaccount") ||
                p.Key.ToLower().Contains("db_account") ||
                p.Key.ToLower().Contains("connectionstring") ||
                p.Key.ToLower().Contains("connection_string") ||
                p.Key.ToLower().Contains("host") ||
                p.Key.ToLower().Contains("instance") ||
                p.Key.ToLower().Contains("servername") ||
                p.Key.ToLower().Contains("server_name")) &&
                !string.IsNullOrEmpty(p.Value) &&
                !p.Value.Equals("true", StringComparison.OrdinalIgnoreCase) &&
                !p.Value.Equals("false", StringComparison.OrdinalIgnoreCase) &&
                !bool.TryParse(p.Value, out _))
                .ToList();

            foreach (var prop in connectionProps)
            {
                LogMessage($"[DB-CONNECTION] Found valid connection property: {prop.Key} = {prop.Value}");
                
                // Clean up the account name for display
                string accountName = prop.Value;
                
                // Remove common prefixes/suffixes that might not be user-friendly
                if (accountName.StartsWith("$") && accountName.Contains("."))
                {
                    // Handle parameter references like "$account.database_account"
                    accountName = accountName.Substring(accountName.LastIndexOf('.') + 1);
                }
                
                return accountName;
            }

            // Look for database name in properties (also filter out boolean values)
            var dbNameProps = snap.Properties.Where(p =>
                (p.Key.ToLower().Contains("dbname") ||
                p.Key.ToLower().Contains("database_name") ||
                p.Key.ToLower().Contains("schema") ||
                p.Key.ToLower().Contains("databasename") ||
                p.Key.ToLower().Contains("catalog") ||
                p.Key.ToLower().Contains("initialcatalog") ||
                p.Key.ToLower().Contains("initial_catalog") ||
                p.Key.ToLower().Contains("db") ||
                p.Key.ToLower().Contains("table") ||
                p.Key.ToLower().Contains("tablename") ||
                p.Key.ToLower().Contains("table_name")) &&
                !string.IsNullOrEmpty(p.Value) &&
                !p.Value.Equals("true", StringComparison.OrdinalIgnoreCase) &&
                !p.Value.Equals("false", StringComparison.OrdinalIgnoreCase) &&
                !bool.TryParse(p.Value, out _))
                .ToList();

            foreach (var prop in dbNameProps)
            {
                LogMessage($"[DB-CONNECTION] Found database name property: {prop.Key} = {prop.Value}");
                return prop.Value;
            }

            LogMessage($"[DB-CONNECTION] No connection info found for snap: {snap.Label}");
            return null;
        }
    }
}
