using System;
using System.IO;
using System.Text.Json;
using System.Collections.Generic;

namespace SnapAnalyser
{
    public class ConfigManager
    {        // Add static constructor to ensure _configData is initialized and loaded before any properties are accessed
        static ConfigManager()
        {
            _configData = new ConfigData(); // Initialize with empty data first
            LoadConfig(); // Then try to load from file
            
            // Create a fresh config file if it doesn't exist
            if (string.IsNullOrEmpty(_configData.SnapLogicUrl) ||
                string.IsNullOrEmpty(_configData.SnapLogicUsername) ||
                string.IsNullOrEmpty(_configData.SnapLogicPassword) ||
                string.IsNullOrEmpty(_configData.SnapLogicOrgId))
            {
                SaveConfig();
            }
        }
        
        private static readonly string BaseConfigFilePath = Path.Combine(
            AppDomain.CurrentDomain.BaseDirectory, "config.json");
            
        private static readonly string ProjectConfigFilePath = Path.Combine(
            Directory.GetCurrentDirectory(), "config.json");

        private static ConfigData _configData;        public static string OpenAIApiKey 
        {            get 
            {
                var key = _configData?.OpenAIApiKey ?? string.Empty;
                if (string.IsNullOrWhiteSpace(key))
                {
                    return string.Empty;
                }
                return key;
            }
            set
            {
                if (_configData == null)
                {
                    _configData = new ConfigData();
                }
                
                _configData.OpenAIApiKey = value;
                SaveConfig();
            }
        }
        
        public static string LastSlpFolderLocation
        {
            get => _configData?.LastSlpFolderLocation ?? string.Empty;
            set
            {
                if (_configData == null)
                {
                    _configData = new ConfigData();
                }
                _configData.LastSlpFolderLocation = value;
                SaveConfig();
            }
        }
        
        public static string LastOutputFolderLocation
        {
            get => _configData?.LastOutputFolderLocation ?? string.Empty;
            set
            {
                if (_configData == null)
                {
                    _configData = new ConfigData();
                }
                _configData.LastOutputFolderLocation = value;
                SaveConfig();
            }
        }
        
        // SnapLogic Connection Settings
        public static string SnapLogicUrl
        {
            get => _configData?.SnapLogicUrl ?? string.Empty;
            set
            {
                if (_configData == null)
                {
                    _configData = new ConfigData();
                }
                _configData.SnapLogicUrl = value;
                SaveConfig();
            }
        }
        
        public static string SnapLogicUsername
        {
            get => _configData?.SnapLogicUsername ?? string.Empty;
            set
            {
                if (_configData == null)
                {
                    _configData = new ConfigData();
                }
                _configData.SnapLogicUsername = value;
                SaveConfig();
            }
        }
        
        public static string SnapLogicPassword
        {
            get => _configData?.SnapLogicPassword ?? string.Empty;
            set
            {
                if (_configData == null)
                {
                    _configData = new ConfigData();
                }
                _configData.SnapLogicPassword = value;
                SaveConfig();
            }
        }
        
        public static string SnapLogicOrgId
        {
            get => _configData?.SnapLogicOrgId ?? string.Empty;
            set
            {
                if (_configData == null)
                {
                    _configData = new ConfigData();
                }
                _configData.SnapLogicOrgId = value;
                SaveConfig();
            }
        }

        // Azure OpenAI Endpoint
        public static string AzureOpenAIEndpoint
        {            get
            {
                var endpoint = _configData?.AzureOpenAIEndpoint ?? "https://openai-snaplogic-documenter.openai.azure.com/";
                if (string.IsNullOrWhiteSpace(endpoint))
                {
                    return "https://openai-snaplogic-documenter.openai.azure.com/";
                }
                return endpoint;
            }
            set
            {
                if (_configData == null)
                {
                    _configData = new ConfigData();
                }
                
                if (string.IsNullOrWhiteSpace(value))
                {
                    _configData.AzureOpenAIEndpoint = "https://openai-snaplogic-documenter.openai.azure.com/";
                }
                else
                {
                    _configData.AzureOpenAIEndpoint = value;
                }
                SaveConfig();
            }
        }
        
        // Azure OpenAI Deployment Name
        public static string AzureOpenAIDeploymentName
        {            get
            {
                var deploymentName = _configData?.AzureOpenAIDeploymentName ?? "gpt-35-turbo";
                if (string.IsNullOrWhiteSpace(deploymentName))
                {
                    return "gpt-35-turbo";
                }
                return deploymentName;
            }
            set
            {
                if (_configData == null)
                {
                    _configData = new ConfigData();
                }
                
                if (string.IsNullOrWhiteSpace(value))
                {
                    _configData.AzureOpenAIDeploymentName = "gpt-35-turbo";
                }
                else
                {
                    _configData.AzureOpenAIDeploymentName = value;
                }
                SaveConfig();
            }
        }
        
        // Azure OpenAI Timeout in seconds
        public static int AzureOpenAITimeoutSeconds
        {            get => _configData?.AzureOpenAITimeoutSeconds ?? 30;
            set
            {
                if (_configData == null)
                {
                    _configData = new ConfigData();
                }
                
                if (value < 5)
                {
                    _configData.AzureOpenAITimeoutSeconds = 5;
                }
                else
                {
                    _configData.AzureOpenAITimeoutSeconds = value;
                }
                SaveConfig();
            }
        }

        public static void LoadConfig()
        {
            List<string> configPaths = new List<string>
            {
                // Check multiple locations for the config file, in order of preference
                Path.Combine(Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location), "config.json"), // Assembly location (most reliable)
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.json"),      // App base directory
                Path.Combine(Directory.GetCurrentDirectory(), "config.json"),             // Current directory
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SnapAnalyser", "config.json"), // AppData folder
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "SnapAnalyser", "config.json") // Documents folder
            };

            bool configLoaded = false;
            foreach (string configPath in configPaths)
            {
                Console.WriteLine($"Checking for config file at: {configPath}");
                if (TryLoadConfigFromPath(configPath))
                {
                    configLoaded = true;
                    Console.WriteLine($"Successfully loaded config from: {configPath}");
                    break;
                }
            }

            if (!configLoaded)
            {
                Console.WriteLine("No valid config file found, using default values");
                // Initialize with default values if no config file was found
                if (_configData == null) _configData = new ConfigData();
                
                // Set default values for missing fields
                if (string.IsNullOrEmpty(_configData.SnapLogicUrl))
                    _configData.SnapLogicUrl = "https://cdn.elastic.snaplogic.com";
                    
                if (string.IsNullOrEmpty(_configData.SnapLogicOrgId))
                    _configData.SnapLogicOrgId = "5fd9a86c47060ece477be9b6";
                    
                // Create the config file with these defaults
                SaveConfig();
            }
        }
        
        private static bool TryLoadConfigFromPath(string configPath)
        {
            try
            {
                if (File.Exists(configPath))
                {
                    string json = File.ReadAllText(configPath);
                    Console.WriteLine($"Read config file, content length: {json.Length} characters");

                    if (string.IsNullOrWhiteSpace(json))
                    {
                        Console.WriteLine("Config file is empty");
                        return false;
                    }

                    try
                    {
                        _configData = JsonSerializer.Deserialize<ConfigData>(json);
                        
                        // Log the loaded config details (without sensitive data)
                        Console.WriteLine("Config successfully loaded with values:");
                        Console.WriteLine($"  OpenAIApiKey: {(string.IsNullOrEmpty(_configData.OpenAIApiKey) ? "Not set" : "Set (hidden)")}");
                        Console.WriteLine($"  LastSlpFolderLocation: {_configData.LastSlpFolderLocation}");
                        Console.WriteLine($"  LastOutputFolderLocation: {_configData.LastOutputFolderLocation}");
                        Console.WriteLine($"  SnapLogicUrl: {_configData.SnapLogicUrl}");
                        Console.WriteLine($"  SnapLogicUsername: {_configData.SnapLogicUsername}");
                        Console.WriteLine($"  SnapLogicPassword: {(string.IsNullOrEmpty(_configData.SnapLogicPassword) ? "Not set" : "Set (hidden)")}");
                        Console.WriteLine($"  SnapLogicOrgId: {_configData.SnapLogicOrgId}");
                        
                        return true;
                    }
                    catch (JsonException ex)
                    {
                        Console.WriteLine($"JSON parse error: {ex.Message}");
                        Console.WriteLine($"Config file content (first 100 chars): {(json.Length > 100 ? json.Substring(0, 100) + "..." : json)}");
                        return false;
                    }
                }
                else
                {
                    Console.WriteLine($"Config file not found at: {configPath}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading config from {configPath}: {ex.GetType().Name} - {ex.Message}");
                return false;
            }
        }

        private static void SaveConfig()
        {
            try
            {
                if (_configData == null)
                {
                    Console.WriteLine("Cannot save config: _configData is null");
                    _configData = new ConfigData(); // Create it if it doesn't exist
                }

                string json = JsonSerializer.Serialize(_configData, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                
                // Try locations in order of preference
                List<string> savePaths = new List<string>
                {
                    // Assembly location (most reliable)
                    Path.Combine(Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location), "config.json"),
                    // App base directory
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.json"),
                    // Current directory
                    Path.Combine(Directory.GetCurrentDirectory(), "config.json"),
                    // AppData folder
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SnapAnalyser", "config.json")
                };
                
                Console.WriteLine("Attempting to save config to the following locations:");
                foreach (var path in savePaths) 
                {
                    Console.WriteLine($"  {path}");
                }
                
                bool savedSuccessfully = false;
                Exception lastException = null;
                
                foreach (var path in savePaths)
                {
                    try
                    {
                        // Create the directory if it doesn't exist
                        string directory = Path.GetDirectoryName(path);
                        if (!Directory.Exists(directory) && !string.IsNullOrEmpty(directory))
                        {
                            Directory.CreateDirectory(directory);
                        }
                        
                        // Write the file
                        File.WriteAllText(path, json);
                        Console.WriteLine($"Successfully saved config to: {path}");
                        savedSuccessfully = true;
                        break; // Exit the loop after successful save
                    }
                    catch (Exception ex)
                    {
                        lastException = ex;
                        Console.WriteLine($"Error saving config to {path}: {ex.Message}");
                    }
                }
                
                // If all attempts failed, try saving to the temp directory as a last resort
                if (!savedSuccessfully)
                {
                    try
                    {
                        string tempPath = Path.Combine(Path.GetTempPath(), "SnapAnalyser_config.json");
                        File.WriteAllText(tempPath, json);
                        Console.WriteLine($"Saved config to temp directory as fallback: {tempPath}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Failed to save even to temp directory: {ex.Message}");
                        if (lastException != null)
                            throw lastException; // Rethrow the last exception from the main attempts
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fatal error saving config: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        // Add this public method that users can call to create a default config file
        public static void CreateDefaultConfig()
        {
            Console.WriteLine("Creating default config file with SnapLogic connection settings...");
            if (_configData == null)
            {
                _configData = new ConfigData();
            }
            
            // Set default values if they're not already set
            if (string.IsNullOrEmpty(_configData.SnapLogicUrl))
                _configData.SnapLogicUrl = "https://cdn.elastic.snaplogic.com";
                
            if (string.IsNullOrEmpty(_configData.SnapLogicUsername))
                _configData.SnapLogicUsername = "<EMAIL>";
                
            if (string.IsNullOrEmpty(_configData.SnapLogicPassword))
                _configData.SnapLogicPassword = "your-password-here";
                
            if (string.IsNullOrEmpty(_configData.SnapLogicOrgId))
                _configData.SnapLogicOrgId = "5fd9a86c47060ece477be9b6"; // Example Org ID
            
            // Save the default config
            SaveConfig();
            
            // Show the paths where we tried to save
            List<string> configPaths = new List<string>
            {
                Path.Combine(Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location), "config.json"),
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.json"),
                Path.Combine(Directory.GetCurrentDirectory(), "config.json")
            };
            
            Console.WriteLine("Default config file created. Check the following locations:");
            foreach (var path in configPaths)
            {
                if (File.Exists(path))
                {
                    Console.WriteLine($"  * {path} (EXISTS)");
                }
                else
                {
                    Console.WriteLine($"  - {path} (not found)");
                }
            }
        }        private class ConfigData
        {
            public string OpenAIApiKey { get; set; } = string.Empty;
            public string AzureOpenAIEndpoint { get; set; } = "https://openai-snaplogic-documenter.openai.azure.com/";
            public string AzureOpenAIDeploymentName { get; set; } = "gpt-35-turbo";
            public int AzureOpenAITimeoutSeconds { get; set; } = 30;
            public string LastSlpFolderLocation { get; set; } = string.Empty;
            public string LastOutputFolderLocation { get; set; } = string.Empty;
            public string SnapLogicUrl { get; set; } = string.Empty;
            public string SnapLogicUsername { get; set; } = string.Empty;
            public string SnapLogicPassword { get; set; } = string.Empty;
            public string SnapLogicOrgId { get; set; } = string.Empty;
        }
    }
}