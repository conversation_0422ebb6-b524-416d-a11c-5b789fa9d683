using System;
using System.Collections.Generic;

class JoinPropertyTest
{
    static void Main()
    {
        Console.WriteLine("=== Testing Join Property Extraction Logic ===");
        
        // Simulate the actual property structure from Join snaps
        var snapProperties = new Dictionary<string, object>
        {
            // Real Join snap property structure
            ["settings.joinPaths.value.0.leftPath.value"] = "customer_id",
            ["settings.joinPaths.value.0.rightPath.value"] = "id",
            ["settings.joinType.value"] = "Inner Join",
            ["settings.joinPaths.value.0.leftPath.expression"] = "",
            ["settings.joinPaths.value.0.rightPath.expression"] = "",
            ["settings.nullSafeAccess.value"] = "true",
            ["class_id"] = "com-snaplogic-snaps-flow-join",
            ["class_version"] = "1",
            ["property_map"] = new Dictionary<string, object>(),
            ["datatype"] = "ignored" // This should be filtered out
        };

        Console.WriteLine("Testing property structure:");
        foreach (var prop in snapProperties)
        {
            Console.WriteLine($"  {prop.Key}: {prop.Value}");
        }

        // Test Join Conditions Extraction (mimicking ExtractJoinConditions method)
        Console.WriteLine("\n=== Testing Join Conditions Extraction ===");
        var joinConditions = new List<string>();
        
        foreach (var prop in snapProperties)
        {
            // Updated logic from our fix
            if (prop.Key.Contains("joinPaths.value") && prop.Key.Contains("leftPath") && prop.Key.Contains(".value"))
            {
                Console.WriteLine($"✓ Found leftPath: {prop.Key} = {prop.Value}");
                joinConditions.Add($"Left: {prop.Value}");
            }
            else if (prop.Key.Contains("joinPaths.value") && prop.Key.Contains("rightPath") && prop.Key.Contains(".value"))
            {
                Console.WriteLine($"✓ Found rightPath: {prop.Key} = {prop.Value}");
                joinConditions.Add($"Right: {prop.Value}");
            }
        }

        // Test Join Type Extraction (mimicking ExtractJoinType method)
        Console.WriteLine("\n=== Testing Join Type Extraction ===");
        string joinType = null;
        
        foreach (var prop in snapProperties)
        {
            // Updated logic from our fix
            if (prop.Key.Contains("joinType.value") || 
                prop.Key.Contains("settings.joinType") ||
                (prop.Key.ToLower().Contains("type") && !prop.Key.ToLower().Contains("datatype")))
            {
                Console.WriteLine($"✓ Found joinType: {prop.Key} = {prop.Value}");
                joinType = prop.Value?.ToString();
                break; // Take the first match
            }
        }

        // Results
        Console.WriteLine("\n=== RESULTS ===");
        Console.WriteLine($"Join Type: {joinType ?? "NOT FOUND"}");
        Console.WriteLine($"Join Conditions: [{string.Join(", ", joinConditions)}]");
        
        // Test success criteria
        bool typeFound = !string.IsNullOrEmpty(joinType);
        bool conditionsFound = joinConditions.Count >= 2; // Should have left and right
        bool success = typeFound && conditionsFound;
        
        Console.WriteLine($"\nType Found: {typeFound}");
        Console.WriteLine($"Conditions Found: {conditionsFound} (count: {joinConditions.Count})");
        Console.WriteLine($"\nOVERALL RESULT: {(success ? "✓ SUCCESS" : "✗ FAILED")}");
        
        if (success)
        {
            Console.WriteLine("\n🎉 The Join property extraction logic is working correctly!");
            Console.WriteLine("   Enhanced Join documentation should now be generated properly.");
        }
        else
        {
            Console.WriteLine("\n❌ Property extraction logic needs further adjustment.");
            if (!typeFound) Console.WriteLine("   - Join type extraction failed");
            if (!conditionsFound) Console.WriteLine("   - Join conditions extraction failed");
        }

        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }
}
