using System;
using System.Collections.Generic;
using System.Linq;

namespace SnapAnalyser
{
    class DebugConditionSnap
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== DEBUG CONDITION SNAP PROPERTIES ===");
            Console.WriteLine();
            
            // Create a test condition snap with the properties we expect
            var conditionSnap = new SnapNode
            {
                Id = "debug-condition-001",
                Label = "Debug Condition Snap",
                Type = "com-snaplogic-snaps-transform-conditional",
                Category = SnapCategory.Transform,
                Properties = new Dictionary<string, string>
                {
                    ["expression_0"] = "$data.status == 'active'",
                    ["output_0"] = "Active Records",
                    ["expression_1"] = "$data.status == 'inactive'", 
                    ["output_1"] = "Inactive Records",
                    ["expression_2"] = "$data.priority > 5",
                    ["output_2"] = "High Priority"
                }
            };
            
            Console.WriteLine($"Snap: {conditionSnap.Label}");
            Console.WriteLine($"Type: {conditionSnap.Type}");
            Console.WriteLine($"Properties count: {conditionSnap.Properties.Count}");
            Console.WriteLine();
            
            Console.WriteLine("All Properties:");
            foreach (var prop in conditionSnap.Properties)
            {
                Console.WriteLine($"  {prop.Key} = {prop.Value}");
            }
            Console.WriteLine();
            
            // Test the extraction method
            Console.WriteLine("Testing ExtractConditionExpressions...");
            var conditions = SnapBestPractices.ExtractConditionExpressions(conditionSnap);
            
            Console.WriteLine($"Extracted {conditions.Count} conditions:");
            foreach (var condition in conditions)
            {
                Console.WriteLine($"  - Description: {condition.Description}");
                Console.WriteLine($"    Expression: {condition.Expression}");
                Console.WriteLine($"    Return Value: {condition.ReturnValue ?? "null"}");
                Console.WriteLine($"    Target Path: {condition.TargetPath ?? "null"}");
                Console.WriteLine();
            }
            
            // Now test with a real-world example that might be failing
            Console.WriteLine("=== TESTING WITH REAL-WORLD PROPERTY NAMES ===");
            var realWorldSnap = new SnapNode
            {
                Id = "real-condition-001", 
                Label = "Real Condition Snap",
                Type = "com-snaplogic-snaps-transform-conditional",
                Category = SnapCategory.Transform,
                Properties = new Dictionary<string, string>
                {
                    // Try different property name patterns that might exist in real snaps
                    ["settings.conditionalTable.value"] = "[{\"expression\":\"$status == 'active'\",\"outputViewName\":\"active\"}]",
                    ["settings.expression_0.value"] = "$data.unitCount > 100",
                    ["settings.output_0.value"] = "highValue",
                    ["settings.expression_1.value"] = "$data.unitCount <= 100", 
                    ["settings.output_1.value"] = "lowValue"
                }
            };
            
            Console.WriteLine($"Real-world snap: {realWorldSnap.Label}");
            Console.WriteLine("Properties:");
            foreach (var prop in realWorldSnap.Properties)
            {
                Console.WriteLine($"  {prop.Key} = {prop.Value}");
            }
            Console.WriteLine();
            
            var realConditions = SnapBestPractices.ExtractConditionExpressions(realWorldSnap);
            Console.WriteLine($"Extracted {realConditions.Count} conditions from real-world example:");
            foreach (var condition in realConditions)
            {
                Console.WriteLine($"  - Description: {condition.Description}");
                Console.WriteLine($"    Expression: {condition.Expression}");
                Console.WriteLine($"    Return Value: {condition.ReturnValue ?? "null"}");
                Console.WriteLine($"    Target Path: {condition.TargetPath ?? "null"}");
                Console.WriteLine();
            }
            
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
