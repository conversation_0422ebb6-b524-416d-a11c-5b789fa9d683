using System;
using System.Collections.Generic;
using System.Linq;

// Simple test to verify Join property extraction logic
class SimpleJoinTest
{
    static void Main()
    {
        Console.WriteLine("=== Testing Join Property Extraction Fix ===");
        Console.WriteLine();
        
        // Simulate the real Join snap property structure from SnapLogic
        var testProperties = new Dictionary<string, string>
        {
            // Real Join snap properties from P01 pipeline
            ["settings.joinPaths.value.0.leftPath.value"] = "customer_id",
            ["settings.joinPaths.value.0.rightPath.value"] = "id",
            ["settings.joinType.value"] = "Inner Join",
            ["settings.joinPaths.value.0.leftPath.expression"] = "",
            ["settings.joinPaths.value.0.rightPath.expression"] = "",
            ["settings.nullSafeAccess.value"] = "true",
            
            // Properties that should be ignored
            ["class_id"] = "com-snaplogic-snaps-flow-join",
            ["class_version"] = "1",
            ["datatype"] = "should_be_ignored",
            ["settings.someOtherProperty"] = "ignored"
        };

        Console.WriteLine("Test Data (simulating real Join snap properties):");
        foreach (var prop in testProperties)
        {
            Console.WriteLine($"  {prop.Key}: {prop.Value}");
        }
        Console.WriteLine();

        // Test 1: ExtractJoinConditions logic
        Console.WriteLine("=== Test 1: Join Conditions Extraction ===");
        var joinConditions = new List<string>();
        
        // OLD logic (what was failing)
        Console.WriteLine("OLD Logic Results:");
        var oldLeftPaths = testProperties.Where(p => 
            p.Key.ToLower().Contains("leftpath")).ToList();
        var oldRightPaths = testProperties.Where(p => 
            p.Key.ToLower().Contains("rightpath")).ToList();
        
        Console.WriteLine($"  OLD: Found {oldLeftPaths.Count} left paths, {oldRightPaths.Count} right paths");
        if (oldLeftPaths.Count > 0 && oldRightPaths.Count > 0)
        {
            Console.WriteLine($"  OLD: Would generate: {oldLeftPaths[0].Value} == {oldRightPaths[0].Value}");
        }
        else
        {
            Console.WriteLine("  OLD: ❌ No join conditions found (this was the problem!)");
        }
        
        // NEW logic (our fix)
        Console.WriteLine("\nNEW Logic Results:");
        var newLeftPaths = testProperties.Where(p => 
            p.Key.ToLower().Contains("leftpath") || 
            p.Key.ToLower().Contains("left_path") ||
            (p.Key.Contains("joinPaths.value") && p.Key.Contains("leftPath"))).ToList();
        
        var newRightPaths = testProperties.Where(p => 
            p.Key.ToLower().Contains("rightpath") || 
            p.Key.ToLower().Contains("right_path") ||
            (p.Key.Contains("joinPaths.value") && p.Key.Contains("rightPath"))).ToList();

        Console.WriteLine($"  NEW: Found {newLeftPaths.Count} left paths, {newRightPaths.Count} right paths");
        
        for (int i = 0; i < Math.Min(newLeftPaths.Count, newRightPaths.Count); i++)
        {
            string condition = $"{newLeftPaths[i].Value} == {newRightPaths[i].Value}";
            joinConditions.Add(condition);
            Console.WriteLine($"  NEW: Generated condition: {condition}");
        }

        // Test 2: ExtractJoinType logic  
        Console.WriteLine("\n=== Test 2: Join Type Extraction ===");
        
        // OLD logic
        Console.WriteLine("OLD Logic Results:");
        var oldJoinTypeProps = testProperties.Where(p => 
            p.Key.ToLower().Contains("jointype") || 
            p.Key.ToLower().Contains("join_type")).ToList();
        
        Console.WriteLine($"  OLD: Found {oldJoinTypeProps.Count} join type properties");
        string oldJoinType = oldJoinTypeProps.FirstOrDefault()?.Value ?? "Inner Join";
        Console.WriteLine($"  OLD: Join type: {oldJoinType}");
        
        // NEW logic (our fix)
        Console.WriteLine("\nNEW Logic Results:");
        var newJoinTypeProps = testProperties.Where(p => 
            p.Key.ToLower().Contains("jointype") || 
            p.Key.ToLower().Contains("join_type") ||
            p.Key.Contains("joinType.value") ||
            p.Key.Contains("settings.joinType") ||
            (p.Key.ToLower().Contains("type") && !p.Key.ToLower().Contains("datatype"))).ToList();

        Console.WriteLine($"  NEW: Found {newJoinTypeProps.Count} join type properties");
        foreach (var prop in newJoinTypeProps)
        {
            Console.WriteLine($"    - {prop.Key}: {prop.Value}");
        }
        
        string newJoinType = null;
        foreach (var prop in newJoinTypeProps)
        {
            if (!string.IsNullOrEmpty(prop.Value) && 
                (prop.Value.ToLower().Contains("inner") || 
                 prop.Value.ToLower().Contains("left") || 
                 prop.Value.ToLower().Contains("right") || 
                 prop.Value.ToLower().Contains("outer") || 
                 prop.Value.ToLower().Contains("full")))
            {
                newJoinType = prop.Value;
                break;
            }
        }
        
        Console.WriteLine($"  NEW: Extracted join type: {newJoinType ?? "Inner Join (default)"}");

        // Summary
        Console.WriteLine("\n=== SUMMARY ===");
        Console.WriteLine($"Join Conditions Found: {joinConditions.Count}");
        Console.WriteLine($"Join Type Found: {newJoinType ?? "default"}");
        
        bool conditionsFixed = joinConditions.Count > 0;
        bool typeFixed = !string.IsNullOrEmpty(newJoinType);
        bool overallSuccess = conditionsFixed && typeFixed;
        
        Console.WriteLine($"\nConditions Extraction: {(conditionsFixed ? "✅ FIXED" : "❌ STILL BROKEN")}");
        Console.WriteLine($"Type Extraction: {(typeFixed ? "✅ FIXED" : "❌ STILL BROKEN")}");
        Console.WriteLine($"\nOVERALL RESULT: {(overallSuccess ? "🎉 SUCCESS - Join fix is working!" : "❌ NEEDS MORE WORK")}");
        
        if (overallSuccess)
        {
            Console.WriteLine("\nThe Join snap should now generate enhanced documentation");
            Console.WriteLine("instead of showing raw 'Configuration:' sections!");
        }
        else
        {
            Console.WriteLine("\nThe fix needs further adjustment to handle the property structure.");
        }
        
        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }
}
