// Fixed version of GetDatabaseConnectionInfo method for DocumentationGenerator.cs
// This method correctly handles the nested property structure

/// <summary>
/// Extracts database connection information from snap properties
/// </summary>
private string GetDatabaseConnectionInfo(SnapNode snap)
{
    if (snap.Category != SnapCategory.Database)
        return null;

    Console.WriteLine($"[DB-CONNECTION] Checking connection info for snap: {snap.Label}, Properties count: {snap.Properties.Count}");
    
    // Log all properties for debugging
    foreach (var prop in snap.Properties)
    {
        Console.WriteLine($"[DB-CONNECTION] Property: {prop.Key} = {prop.Value?.ToString().Substring(0, Math.Min(100, prop.Value?.ToString().Length ?? 0))}...");
    }

    // First, look for the nested account structure in the 'account' property
    if (snap.Properties.ContainsKey("account"))
    {
        try
        {
            string accountJson = snap.Properties["account"];
            Console.WriteLine($"[DB-CONNECTION] Found account property, parsing JSON: {accountJson}");
            
            var accountObj = JObject.Parse(accountJson);
            
            // Navigate through the nested structure: account -> account_ref -> value -> label -> value
            var accountRef = accountObj["account_ref"];
            if (accountRef != null)
            {
                var valueObj = accountRef["value"];
                if (valueObj != null)
                {
                    var labelObj = valueObj["label"];
                    if (labelObj != null)
                    {
                        var accountName = labelObj["value"]?.ToString();
                        if (!string.IsNullOrEmpty(accountName))
                        {
                            Console.WriteLine($"[DB-CONNECTION] Found account name from nested JSON: {accountName}");
                            return accountName;
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[DB-CONNECTION] Error parsing account JSON: {ex.Message}");
        }
    }

    Console.WriteLine($"[DB-CONNECTION] No connection info found for snap: {snap.Label}");
    return null;
}

/// <summary>
/// Extracts SQL statement from SQL execute snaps
/// </summary>
private string GetSqlStatement(SnapNode snap)
{
    if (snap.Category != SnapCategory.Database)
        return null;

    // Only get SQL for execute snaps
    if (!snap.SnapType.Contains("execute"))
        return null;

    Console.WriteLine($"[SQL-EXTRACT] Checking SQL for snap: {snap.Label}");

    // Look for SQL statement in settings
    if (snap.Properties.ContainsKey("settings"))
    {
        try
        {
            string settingsJson = snap.Properties["settings"];
            Console.WriteLine($"[SQL-EXTRACT] Found settings property, parsing JSON");
            
            var settingsObj = JObject.Parse(settingsJson);
            var sqlStatement = settingsObj["sqlStatement"];
            
            if (sqlStatement != null)
            {
                var sqlValue = sqlStatement["value"]?.ToString();
                if (!string.IsNullOrEmpty(sqlValue))
                {
                    Console.WriteLine($"[SQL-EXTRACT] Found SQL statement: {sqlValue}");
                    return sqlValue;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[SQL-EXTRACT] Error parsing settings JSON: {ex.Message}");
        }
    }

    Console.WriteLine($"[SQL-EXTRACT] No SQL statement found for snap: {snap.Label}");
    return null;
}
