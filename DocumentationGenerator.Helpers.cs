using Newtonsoft.Json.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;

namespace SnapAnalyser
{
    public partial class DocumentationGenerator
    {
        private void AddCategoryDescription(StringBuilder html, string anchorId)
        {
            if (anchorId == "flow-control")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>Flow control snaps manage how documents move through the pipeline by " +
                               "routing, filtering, joining, or splitting document streams.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps control the path and flow of documents within the pipeline. They " +
                               "can create conditional branches (Router), merge multiple streams (Union), create copies of documents (Copy), filter documents " +
                               "based on criteria, sort them, or join related documents from different sources.</p>");
                html.AppendLine("          <p><strong>Common Configurations:</strong></p>");
                html.AppendLine("          <ul>");
                html.AppendLine("            <li><strong>Router</strong>: Configured with expressions that evaluate to true/false to determine which output path to follow</li>");
                html.AppendLine("            <li><strong>Join</strong>: Requires keys from both streams to match on, similar to SQL JOIN operations</li>");
                html.AppendLine("            <li><strong>Sort</strong>: Requires field names and sort direction (ascending/descending)</li>");
                html.AppendLine("            <li><strong>Filter</strong>: Uses expressions to determine which documents to pass forward</li>");
                html.AppendLine("          </ul>");
                html.AppendLine("        </div>");
            }
            else if (anchorId == "transformations")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>Transformation snaps manipulate the structure and content of documents by " +
                               "mapping fields, performing calculations, applying business logic, and reformatting data.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps modify document content or structure without changing the " +
                               "flow path. They can map fields from one schema to another, apply formulas and functions to field values, " +
                               "restructure documents, or convert between different data formats (JSON, XML, etc.)</p>");
                html.AppendLine("          <p><strong>Common Configurations:</strong></p>");
                html.AppendLine("          <ul>");
                html.AppendLine("            <li><strong>Mapper</strong>: Contains field-by-field mappings using expressions</li>");
                html.AppendLine("            <li><strong>JSON/XML Generator</strong>: Configured with templates for output format</li>");
                html.AppendLine("            <li><strong>JSON/XML Parser</strong>: Can be configured with paths to extract specific elements</li>");
                html.AppendLine("            <li><strong>Script</strong>: Contains custom JavaScript, Python or other code to transform data</li>");
                html.AppendLine("          </ul>");
                html.AppendLine("        </div>");
            }
            else if (anchorId == "database")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>Database snaps interact with SQL databases to read, write, or modify data. " +
                               "They convert between database tables and document formats using SQL operations.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps communicate with relational database systems using JDBC/ODBC " +
                               "connections. They translate between SnapLogic documents and database records, executing SQL statements for various " +
                               "operations like SELECT, INSERT, UPDATE, DELETE, or custom SQL.</p>");
                html.AppendLine("          <p><strong>Common Configurations:</strong></p>");
                html.AppendLine("          <ul>");
                html.AppendLine("            <li><strong>SELECT</strong>: Configured with table name, columns to retrieve, and WHERE conditions</li>");
                html.AppendLine("            <li><strong>INSERT</strong>: Requires table name and field-to-column mappings</li>");
                html.AppendLine("            <li><strong>UPDATE</strong>: Needs table name, fields to update, and key fields to identify records</li>");
                html.AppendLine("            <li><strong>SQL Execute</strong>: Takes raw SQL statements or stored procedure calls</li>");
                html.AppendLine("          </ul>");
                html.AppendLine("        </div>");
            }
            else if (anchorId == "external")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>External system snaps connect to third-party systems, APIs, or services " +
                               "to exchange data with systems outside the SnapLogic platform.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps manage authentication, connection parameters, " +
                               "and data exchange protocols with external systems. They handle credentials securely, format requests " +
                               "according to the target system's requirements, and parse responses back into SnapLogic documents.</p>");
                html.AppendLine("          <p><strong>Common Configurations:</strong></p>");
                html.AppendLine("          <ul>");
                html.AppendLine("            <li><strong>REST/HTTP</strong>: Configured with endpoint URLs, HTTP methods, headers, and authentication</li>");
                html.AppendLine("            <li><strong>Dynamics 365</strong>: Requires entity names, operation types, and field mappings</li>");
                html.AppendLine("            <li><strong>Salesforce</strong>: Uses object names, SOQL queries, or field mappings depending on operation</li>");
                html.AppendLine("            <li><strong>SOAP</strong>: Configured with WSDL information and XML message formats</li>");
                html.AppendLine("          </ul>");
                html.AppendLine("        </div>");
            }
            else if (anchorId == "file")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>File operation snaps read from or write to files in various formats, " +
                               "handling data import/export needs for the pipeline.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps interact with file systems " +
                               "(local or cloud storage) to read, write, list, or manage files. They handle file format parsing " +
                               "and generation (CSV, Excel, text, binary, etc.), file chunking for large files, and file metadata.</p>");
                html.AppendLine("          <p><strong>Common Configurations:</strong></p>");
                html.AppendLine("          <ul>");
                html.AppendLine("            <li><strong>File Reader</strong>: Configured with file path/pattern, format settings, and parsing options</li>");
                html.AppendLine("            <li><strong>File Writer</strong>: Requires destination path, file naming patterns, and format settings</li>");
                html.AppendLine("            <li><strong>CSV Parser/Generator</strong>: Configured with delimiter settings, header options, and encoding</li>");
                html.AppendLine("            <li><strong>Excel Read/Write</strong>: Requires worksheet names, cell ranges, and format options</li>");
                html.AppendLine("          </ul>");
                html.AppendLine("        </div>");
            }
            else if (anchorId == "error")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>Error handling snaps provide mechanisms to catch, log, and respond to errors " +
                               "that occur during pipeline execution, improving pipeline robustness.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps implement error management strategies " +
                               "including catching exceptions, logging error details, implementing retries, sending notifications, " +
                               "or providing fallback processing paths. They help create resilient pipelines that can recover from failures.</p>");
                html.AppendLine("          <p><strong>Common Configurations:</strong></p>");
                html.AppendLine("          <ul>");
                html.AppendLine("            <li><strong>Error Handler</strong>: Configured with error types to catch and recovery actions</li>");
                html.AppendLine("            <li><strong>Retry</strong>: Contains retry count, delay between attempts, and conditions for retrying</li>");
                html.AppendLine("            <li><strong>Error Log</strong>: Configured with logging level, destination, and format</li>");
                html.AppendLine("            <li><strong>Alert</strong>: Contains notification settings like email recipients or webhook endpoints</li>");
                html.AppendLine("          </ul>");
                html.AppendLine("        </div>");
            }
            else if (anchorId == "other")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>This category includes utility snaps and specialized components that don't fit into the other categories.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps may provide utility functions, custom logic, specialized " +
                               "processing, or integration with unique systems that don't fit cleanly into the standard categories.</p>");
                html.AppendLine("        </div>");
            }
        }

        // Comprehensive placeholder processing wrapper - ensures ALL content is processed
        private string ProcessContentForPlaceholders(string content)
        {
            if (string.IsNullOrEmpty(content))
                return content;

            // Create a mock codeBlocks dictionary for placeholder processing
            var codeBlocks = new Dictionary<string, string>();

            // Apply the same placeholder processing logic as in ConvertMarkdownToHtml
            string processedContent = content;

            // 1. Standard format with underscore: ##INLINE_CODE_xxxxxxxx##
            processedContent = Regex.Replace(processedContent, @"##INLINE_CODE_([a-zA-Z0-9]+)##", match =>
            {
                string key = match.Value;
                if (codeBlocks.ContainsKey(key))
                {
                    return $"<code>{codeBlocks[key]}</code>";
                }
                return match.Value; // Keep original if not found
            });

            // 2. Format without underscore: ##INLINECODExxxxxxxx##
            processedContent = Regex.Replace(processedContent, @"##INLINECODE([a-zA-Z0-9]+)##", match =>
            {
                string id = match.Groups[1].Value;
                string alternateFormat = $"##INLINE_CODE_{id}##";

                // Try exact match first (in case it was stored this way)
                if (codeBlocks.ContainsKey(alternateFormat))
                {
                    return $"<code>{codeBlocks[alternateFormat]}</code>";
                }

                // Try to find matching key with underscore format
                var keyWithUnderscore = $"##INLINE_CODE_{id}##";
                if (codeBlocks.ContainsKey(keyWithUnderscore))
                {
                    return $"<code>{codeBlocks[keyWithUnderscore]}</code>";
                }

                // Try partial match on any existing key
                var partialMatch = codeBlocks.Keys.FirstOrDefault(k => k.Contains(id));
                if (partialMatch != null)
                {
                    return $"<code>{codeBlocks[partialMatch]}</code>";
                }

                // For unmatched placeholders, try to provide intelligent fallback
                var fallback = GetFallbackForOrphanedPlaceholder(id, processedContent);
                return $"<code class=\"inferred-placeholder\">{fallback}</code>";
            });

            // 3. Catch any remaining malformed patterns
            processedContent = Regex.Replace(processedContent, @"##INLINE_CODE_([^#]+)##", match =>
            {
                string key = match.Value;
                if (codeBlocks.ContainsKey(key))
                {
                    return $"<code>{codeBlocks[key]}</code>";
                }

                // Extract just the ID for partial matching
                string id = match.Groups[1].Value;
                var partialMatch = codeBlocks.Keys.FirstOrDefault(k => k.Contains(id));
                if (partialMatch != null)
                {
                    return $"<code>{codeBlocks[partialMatch]}</code>";
                }

                // Try to provide intelligent fallback
                var fallback = GetFallbackForOrphanedPlaceholder(id, processedContent);
                return $"<code class=\"inferred-placeholder\">{fallback}</code>";
            });

            return processedContent;
        }

        /// <summary>
        /// Provides a fallback display value for placeholders that couldn't be resolved
        /// </summary>
        /// <param name="placeholderId">The ID of the placeholder</param>
        /// <param name="contextText">The surrounding text to provide context</param>
        /// <returns>A human-friendly placeholder replacement based on context</returns>
        private string GetFallbackForOrphanedPlaceholder(string placeholderId, string contextText)
        {
            var context = contextText.ToLower();

            var contextMapping = new Dictionary<string, string[]>
            {
                ["field"] = new[] { "field_name", "input_field", "source_field", "target_field" },
                ["map"] = new[] { "field_mapping", "data_mapping", "field_transformation" },
                ["sql"] = new[] { "SQL_statement", "query", "SELECT_statement" },
                ["error"] = new[] { "error_handling", "retry_count", "error_action" },
                ["mode"] = new[] { "execution_mode", "processing_mode", "operation_mode" },
                ["validate"] = new[] { "validation_setting", "validation_mode" },
                ["execute"] = new[] { "execution_setting", "execute_mode" },
                ["root"] = new[] { "root_path", "document_root", "$" },
                ["document"] = new[] { "document", "input_document", "data_document" },
                ["output"] = new[] { "output_field", "target_field", "result_field" },
                ["input"] = new[] { "input_field", "source_field", "data_field" },
                ["original"] = new[] { "original_field", "passthrough", "preserve_original" },
                ["retain"] = new[] { "retain_fields", "keep_original", "passthrough" },
                ["halt"] = new[] { "halt_on_error", "stop_on_error", "error_handling" },
                ["integrity"] = new[] { "data_integrity", "validation", "quality_check" },
                ["disabled"] = new[] { "passthrough_disabled", "feature_disabled", "setting_off" },
                ["retries"] = new[] { "retry_count", "error_retries", "0" }
            };

            foreach (var mapping in contextMapping)
            {
                if (context.Contains(mapping.Key))
                {
                    return mapping.Value[0];
                }
            }

            if (context.Contains("field") || context.Contains("map"))
            {
                return "field_name";
            }

            if (context.Contains("setting") || context.Contains("config") || context.Contains("parameter"))
            {
                return "configuration_value";
            }

            return $"placeholder_{placeholderId}";
        }

        /// <summary>
        /// Converts a technical snap type to a more user-friendly display name
        /// </summary>
        /// <param name="snapType">The raw snap type string</param>
        /// <returns>User-friendly snap type description</returns>
        private string GetFriendlySnapType(string snapType)
        {
            if (string.IsNullOrEmpty(snapType))
                return "Unknown Snap Type";

            // Extract the last component of the snap type for display
            string[] parts = snapType.Split('.');
            string simpleName = parts.Last();

            // Handle special cases and formatting
            simpleName = simpleName.Replace("Snap", "");

            // Format with spaces between camel case words
            string formatted = System.Text.RegularExpressions.Regex.Replace(
                simpleName,
                "([a-z])([A-Z])",
                "$1 $2"
            );

            return formatted;
        }

        /// <summary>
        /// Gets a human-readable description for a snap
        /// </summary>
        /// <param name="snap">The snap object to describe</param>
        /// <returns>A description of the snap's purpose and function</returns>
        private string GetSnapDescription(SnapNode snap)
        {
            // First check if there is an enhanced AI-generated description
            if (snap.Properties.ContainsKey("enhancedDescription") &&
                !string.IsNullOrEmpty(snap.Properties["enhancedDescription"]))
            {
                // Process any placeholders in the enhanced description using improved logic
                string enhancedDesc = snap.Properties["enhancedDescription"];
                string processedDesc = ReplaceAllPlaceholders(enhancedDesc);

                return ConvertMarkdownToHtml(processedDesc);
            }

            // Fall back to the standard description if available
            if (snap.Properties.ContainsKey("description") &&
                !string.IsNullOrEmpty(snap.Properties["description"]))
            {
                // Also process placeholders in standard descriptions
                string standardDesc = snap.Properties["description"];
                string processedDesc = ReplaceAllPlaceholders(standardDesc);
                return ConvertMarkdownToHtml(processedDesc);
            }

            // Generate a specific description based on snap configuration and type
            return GenerateSpecificSnapDescription(snap);
        }

        /// <summary>
        /// Generates a specific description based on snap configuration and type
        /// </summary>
        private string GenerateSpecificSnapDescription(SnapNode snap)
        {
            string snapType = snap.Type.ToLower();

            // Database snaps - analyze SQL and connection info
            if (snap.Category == SnapCategory.Database)
            {
                return GenerateDatabaseSnapDescription(snap);
            }

            // Router snaps - analyze routing conditions
            if (snapType.Contains("router"))
            {
                return GenerateRouterSnapDescription(snap);
            }

            // Join snaps - analyze join configuration
            if (snapType.Contains("join"))
            {
                return GenerateJoinSnapDescription(snap);
            }

            // Mapper/Transform snaps - analyze mappings
            if (snapType.Contains("mapper") || snapType.Contains("datatransform"))
            {
                return GenerateMapperSnapDescription(snap);
            }

            // External system snaps - analyze connection and operation
            if (snap.Category == SnapCategory.ExternalSystem)
            {
                return GenerateExternalSystemSnapDescription(snap);
            }

            // File snaps - analyze file operations
            if (snapType.Contains("file") || snapType.Contains("csv") || snapType.Contains("json") || snapType.Contains("xml"))
            {
                return GenerateFileSnapDescription(snap);
            }

            // Flow control snaps
            if (snap.Category == SnapCategory.FlowControl)
            {
                return GenerateFlowControlSnapDescription(snap);
            }

            // Fall back to enhanced generic description
            return $"A {snap.Category} snap that {InferSnapFunction(snap.Type)}.";
        }

        /// <summary>
        /// Infers the function of a snap based on its type
        /// </summary>
        private string InferSnapFunction(string snapType)
        {
            if (string.IsNullOrEmpty(snapType))
                return "performs an unknown operation";

            string lowerType = snapType.ToLower();

            if (lowerType.Contains("mapper") || lowerType.Contains("transform"))
                return "transforms data";

            if (lowerType.Contains("join"))
                return "combines multiple data streams";

            if (lowerType.Contains("filter"))
                return "filters documents based on criteria";

            if (lowerType.Contains("router") || lowerType.Contains("switch"))
                return "routes documents based on conditions";

            if (lowerType.Contains("sort"))
                return "sorts documents";

            if (lowerType.Contains("aggregate"))
                return "aggregates data";

            if (lowerType.Contains("json") || lowerType.Contains("xml"))
                return "processes structured data";

            if (lowerType.Contains("file"))
                return "interacts with files";

            if (lowerType.Contains("http") || lowerType.Contains("rest"))
                return "communicates with web services";

            if (lowerType.Contains("db") || lowerType.Contains("sql"))
                return "interacts with databases";

            return "processes data in the pipeline";
        }
        /// <summary>
        /// Finds all possible execution paths through a pipeline from a given starting point
        /// </summary>
        /// <param name="pipeline">The pipeline data</param>
        /// <param name="startNode">The node to start path finding from</param>
        /// <returns>List of execution paths through the pipeline</returns>
        private List<List<SnapNode>> FindExecutionPaths(PipelineData pipeline, SnapNode startNode)
        {
            var result = new List<List<SnapNode>>();
            var visited = new HashSet<string>();
            var currentPath = new List<SnapNode>();

            void DFS(SnapNode node)
            {
                if (node == null)
                    return;

                // Skip if already visited (avoid cycles)
                if (visited.Contains(node.Id))
                    return;
                visited.Add(node.Id);
                currentPath.Add(node);

                // Find outgoing links from this node
                var outgoingLinks = pipeline.Links.Where(l => l.SourceId == node.Id).ToList();

                // If no outgoing links, we've reached an endpoint - save this path
                if (outgoingLinks.Count == 0)
                {
                    result.Add(new List<SnapNode>(currentPath));
                }
                else
                {
                    // Continue DFS for each outgoing link
                    foreach (var link in outgoingLinks)
                    {
                        var targetNode = pipeline.Snaps.FirstOrDefault(s => s.Id == link.TargetId);
                        if (targetNode != null)
                        {
                            DFS(targetNode);
                        }
                    }
                }

                // Backtrack
                currentPath.RemoveAt(currentPath.Count - 1);
                visited.Remove(node.Id);
            }

            // Start DFS from the provided node
            DFS(startNode);
            return result;
        }
        /// <summary>
        /// Generates a summary description of an execution path
        /// </summary>
        /// <param name="path">The execution path to summarize</param>
        /// <returns>A human-readable summary of the path</returns>
        private string GeneratePathSummary(List<SnapNode> path)
        {
            if (path == null || path.Count == 0)
                return "Empty path";

            // For very short paths, just list the snaps
            if (path.Count <= 3)
            {
                return string.Join(" â†’ ", path.Select(s => s.Label));
            }

            // For longer paths, provide a summary with start, end and count
            var firstSnap = path.First();
            var lastSnap = path.Last();
            var middleCount = path.Count - 2;

            return $"Starts with {firstSnap.Label}, passes through {middleCount} snap{(middleCount != 1 ? "s" : "")}, and ends at {lastSnap.Label}";
        }

        /// <summary>
        /// Provides detailed information about a snap's functionality
        /// </summary>        /// <param name="snap">The snap to analyze</param>
        /// <returns>HTML formatted details about the snap's function</returns>
        private string ProvideSnapFunctionDetails(SnapNode snap)
        {
            var details = new StringBuilder();
            LogToFile($"=== Processing Snap: {snap.Label} (Type: {snap.Type}, ID: {snap.Id}) ===");

            // Handle Mapper snaps specifically - be more flexible with detection
            string snapTypeLower = snap.Type?.ToLower() ?? "";
            LogToFile($"Analyzing snap type: '{snapTypeLower}'");

            // Be more specific about what constitutes a mapper snap
            // Only identify actual data transformation/mapping snaps, not all snaps with "transform" in the name
            bool isMapperSnap = snapTypeLower.Contains("com-snaplogic-snaps-transform-datatransform") ||
                             snapTypeLower.Contains("mapper") ||
                             snapTypeLower.Contains("jsonformatter") ||
                             snapTypeLower.Contains("datatransform") ||
                             // Only include specific transform snaps that are actually mappers
                             (snapTypeLower.Contains("transform") &&
                              (snapTypeLower.Contains("datatransform") ||
                               snapTypeLower.Contains("mapper") ||
                               snapTypeLower.Contains("jsonformatter")));

            // Explicitly exclude non-mapper snaps that contain "transform" in their name
            if (snapTypeLower.Contains("condition") ||
                snapTypeLower.Contains("join") ||
                snapTypeLower.Contains("documenttobinary") ||
                snapTypeLower.Contains("binarytodocument") ||
                snapTypeLower.Contains("xmlformatter") ||
                snapTypeLower.Contains("csvformatter") ||
                snapTypeLower.Contains("sort") ||
                snapTypeLower.Contains("aggregate") ||
                snapTypeLower.Contains("filter") ||
                snapTypeLower.Contains("script"))
            {
                isMapperSnap = false;
                LogToFile($"Skipping mapping table lookup for {snap.Type} - not a mapper snap");
            }

            if (isMapperSnap)
            {
                LogToFile($"\n=== Processing Mapper Snap ===");
                LogToFile($"Label: {snap.Label}");
                LogToFile($"Type: {snap.Type}");
                LogToFile($"ID: {snap.Id}");
                LogToFile($"Properties count: {snap.Properties.Count}");

                // Log ALL properties in full for debugging
                LogToFile("\nDEBUGGING: Full property list:");
                foreach (var prop in snap.Properties)
                {
                    var value = prop.Value ?? "null";
                    var displayValue = value.Length > 200 ? value.Substring(0, 200) + "..." : value;
                    LogToFile($"Property: {prop.Key} = {displayValue}");
                }

                LogToFile("\nLooking for mapping expressions...");

                // Check for mapping table in different possible property names
                string[] possibleMappingTableKeys = new[] {
                    "settings.transformations.mappingTable.value",
                    "transformations.mappingTable.value",
                    "mappingTable.value", "mappingTable", "mappings", "transformersList",
                    "transformer", "table", "mapTable", "targetList", "mapList",
                    "properties.mappingTable", "properties.mappings" };
                string mappingTableJson = null;
                string foundKey = null;

                foreach (var key in possibleMappingTableKeys)
                {
                    // Handle direct property access
                    if (snap.Properties.TryGetValue(key, out mappingTableJson))
                    {
                        foundKey = key;
                        LogToFile($"Found mapping table directly in property: {key}");
                        break;
                    }

                    // Handle nested property paths (e.g. 'settings.transformations.mappingTable.value')
                    if (key.Contains("."))
                    {
                        string[] pathParts = key.Split('.');
                        string currentKey = pathParts[0];

                        if (snap.Properties.TryGetValue(currentKey, out string nestedJson))
                        {
                            try
                            {
                                // Try to parse the nested structure
                                JObject currentObj = JObject.Parse(nestedJson);
                                JToken currentToken = currentObj;
                                bool foundPath = true;

                                // Navigate through the nested path
                                for (int i = 1; i < pathParts.Length; i++)
                                {
                                    currentToken = currentToken[pathParts[i]];
                                    if (currentToken == null)
                                    {
                                        foundPath = false;
                                        break;
                                    }
                                }

                                if (foundPath && currentToken != null)
                                {
                                    foundKey = key;
                                    mappingTableJson = currentToken.ToString();
                                    LogToFile($"Found mapping table through nested path: {key}");
                                    break;
                                }
                            }
                            catch
                            {
                                // Continue to the next key if this path doesn't work
                            }
                        }
                    }
                }

                if (foundKey != null)
                {
                    LogToFile($"Found mapping table in property: {foundKey}");
                    LogToFile($"Mapping table content (first 500 chars): {mappingTableJson?.Substring(0, Math.Min(500, mappingTableJson?.Length ?? 0))}...");
                }
                else
                {
                    var errorMsg = "No mapping table found in any known property";
                    LogToFile($"{errorMsg}. Checked for: {string.Join(", ", possibleMappingTableKeys)}");
                    LogToFile("Available properties:");
                    foreach (var key in snap.Properties.Keys)
                    {
                        LogToFile($"- {key}");
                    }

                    // Special handling: try to parse the whole snap as JSON
                    LogToFile("\nAttempting to find mappings by parsing entire snap properties as JSON...");
                    try
                    {
                        foreach (var propEntry in snap.Properties)
                        {
                            // Skip very small properties - they won't contain mapping tables
                            if (propEntry.Value?.Length < 10) continue;

                            try
                            {
                                // Try to parse this property as JSON
                                LogToFile($"Trying to parse '{propEntry.Key}' as JSON...");
                                JObject propJson = JObject.Parse(propEntry.Value);

                                // Look for mapping-related fields in this JSON
                                var mappingField = propJson["mappingTable"] ?? propJson["mappings"] ??
                                                  propJson["transformer"] ?? propJson["table"];

                                if (mappingField != null)
                                {
                                    LogToFile($"Found potential mapping data in '{propEntry.Key}.{mappingField.Path}'");
                                    mappingTableJson = mappingField.ToString();
                                    foundKey = propEntry.Key + "." + mappingField.Path;
                                    break;
                                }
                            }
                            catch
                            {
                                // Just continue to the next property
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogToFile($"Error during advanced mapping search: {ex.Message}");
                    }
                }

                if (!string.IsNullOrEmpty(mappingTableJson))
                {
                    try
                    {
                        LogToFile("Attempting to parse mapping table...");
                        var mappingTable = JArray.Parse(mappingTableJson);
                        LogToFile($"Successfully parsed {mappingTable.Count} mappings from {foundKey}");

                        var expressions = new List<(string TargetPath, string Expression)>();
                        int processedCount = 0;

                        // Extract all mappings
                        foreach (var mapping in mappingTable)
                        {
                            try
                            {
                                // More robust way to extract target path and expression
                                string targetPath = null;
                                string expression = null;

                                // Log the mapping structure to help diagnose issues
                                LogToFile($"Mapping structure: {mapping.ToString(Newtonsoft.Json.Formatting.None)}");

                                // Try multiple paths to extract target path
                                if (mapping["targetPath"] != null)
                                {
                                    if (mapping["targetPath"]["value"] != null)
                                        targetPath = mapping["targetPath"]["value"].ToString();
                                    else
                                        targetPath = mapping["targetPath"].ToString();
                                }
                                else if (mapping["target"] != null)
                                {
                                    if (mapping["target"]["value"] != null)
                                        targetPath = mapping["target"]["value"].ToString();
                                    else
                                        targetPath = mapping["target"].ToString();
                                }

                                // Try multiple paths to extract expression
                                if (mapping["expression"] != null)
                                {
                                    if (mapping["expression"]["value"] != null)
                                        expression = mapping["expression"]["value"].ToString();
                                    else
                                        expression = mapping["expression"].ToString();
                                }
                                else if (mapping["expr"] != null)
                                {
                                    if (mapping["expr"]["value"] != null)
                                        expression = mapping["expr"]["value"].ToString();
                                    else
                                        expression = mapping["expr"].ToString();
                                }
                                else if (mapping["source"] != null)
                                {
                                    if (mapping["source"]["value"] != null)
                                        expression = mapping["source"]["value"].ToString();
                                    else
                                        expression = mapping["source"].ToString();
                                }

                                if (!string.IsNullOrEmpty(targetPath) && !string.IsNullOrEmpty(expression))
                                {
                                    expressions.Add((targetPath, expression));
                                    LogToFile($"Found mapping [{processedCount + 1}]: {targetPath} -> {expression}");
                                    processedCount++;
                                }
                            }
                            catch (Exception ex)
                            {
                                LogToFile($"Error processing mapping {processedCount + 1}: {ex.Message}");
                            }
                        }

                        if (expressions.Count > 0)
                        {
                            LogToFile($"Successfully extracted {expressions.Count} valid mappings out of {mappingTable.Count}");

                            details.AppendLine("<h4>Mapper Expressions</h4>");
                            details.AppendLine("<div class=\"mapper-expressions\" style=\"max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin-bottom: 15px;\">");
                            details.AppendLine("<table class=\"mapper-table\" style=\"width: 100%; border-collapse: collapse;\">");
                            details.AppendLine("<tr style=\"background-color: #f5f5f5;\"><th style=\"padding: 8px; text-align: left; border-bottom: 1px solid #ddd;\">Expression</th><th style=\"padding: 8px; text-align: left; border-bottom: 1px solid #ddd; width: 30%;\">Output Variable</th></tr>");

                            foreach (var (targetPath, expression) in expressions)
                            {
                                details.AppendLine("<tr>");
                                details.AppendLine($"<td style=\"padding: 8px; border-bottom: 1px solid #eee;\"><pre style=\"margin: 0; white-space: pre-wrap; word-break: break-all;\">{EscapeHtml(expression)}</pre></td>");
                                details.AppendLine($"<td style=\"padding: 8px; border-bottom: 1px solid #eee; word-break: break-word;\">{EscapeHtml(targetPath)}</td>");
                                details.AppendLine("</tr>");
                            }

                            details.AppendLine("</table></div>");
                            LogToFile("Successfully generated mapper expressions HTML");
                            return details.ToString();
                        }
                        else
                        {
                            var errorMsg = "No valid mappings found in the mapping table";
                            LogToFile(errorMsg);
                            details.AppendLine($"<p>Error: {EscapeHtml(errorMsg)}</p>");

                            // Log the first few mappings to help with debugging
                            LogToFile("Sample of first few mappings (raw JSON):");
                            for (int i = 0; i < Math.Min(3, mappingTable.Count); i++)
                            {
                                LogToFile($"Mapping {i}: {mappingTable[i].ToString(Newtonsoft.Json.Formatting.None)}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        var errorMsg = $"Error parsing mapping table: {ex.Message}";
                        LogToFile($"{errorMsg}\n{ex.StackTrace}");
                        LogToFile($"Raw JSON that failed to parse (first 500 chars): {mappingTableJson?.Substring(0, Math.Min(500, mappingTableJson?.Length ?? 0))}...");
                        details.AppendLine($"<p>Error: {EscapeHtml(errorMsg)}</p>");
                    }
                }
                else
                {
                    var errorMsg = "No mapping table found in any known property";
                    LogToFile($"{errorMsg}. Checked for: {string.Join(", ", possibleMappingTableKeys)}");
                    LogToFile("Available properties:");
                    foreach (var key in snap.Properties.Keys)
                    {
                        LogToFile($"- {key}");
                    }

                    // Special handling: try to parse the whole snap as JSON
                    LogToFile("\nAttempting to find mappings by parsing entire snap properties as JSON...");
                    try
                    {
                        foreach (var propEntry in snap.Properties)
                        {
                            // Skip very small properties - they won't contain mapping tables
                            if (propEntry.Value?.Length < 10) continue;

                            try
                            {
                                // Try to parse this property as JSON
                                LogToFile($"Trying to parse '{propEntry.Key}' as JSON...");
                                JObject propJson = JObject.Parse(propEntry.Value);

                                // Look for mapping-related fields in this JSON
                                var mappingField = propJson["mappingTable"] ?? propJson["mappings"] ??
                                                  propJson["transformer"] ?? propJson["table"];

                                if (mappingField != null)
                                {
                                    LogToFile($"Found potential mapping data in '{propEntry.Key}.{mappingField.Path}'");
                                    mappingTableJson = mappingField.ToString();
                                    foundKey = propEntry.Key + "." + mappingField.Path;
                                    break;
                                }
                            }
                            catch
                            {
                                // Just continue to the next property
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogToFile($"Error during advanced mapping search: {ex.Message}");
                    }
                }

                if (!string.IsNullOrEmpty(mappingTableJson))
                {
                    try
                    {
                        LogToFile("Attempting to parse mapping table...");
                        var mappingTable = JArray.Parse(mappingTableJson);
                        LogToFile($"Successfully parsed {mappingTable.Count} mappings from {foundKey}");

                        var expressions = new List<(string TargetPath, string Expression)>();
                        int processedCount = 0;

                        // Extract all mappings
                        foreach (var mapping in mappingTable)
                        {
                            try
                            {
                                // More robust way to extract target path and expression
                                string targetPath = null;
                                string expression = null;

                                // Log the mapping structure to help diagnose issues
                                LogToFile($"Mapping structure: {mapping.ToString(Newtonsoft.Json.Formatting.None)}");

                                // Try multiple paths to extract target path
                                if (mapping["targetPath"] != null)
                                {
                                    if (mapping["targetPath"]["value"] != null)
                                        targetPath = mapping["targetPath"]["value"].ToString();
                                    else
                                        targetPath = mapping["targetPath"].ToString();
                                }
                                else if (mapping["target"] != null)
                                {
                                    if (mapping["target"]["value"] != null)
                                        targetPath = mapping["target"]["value"].ToString();
                                    else
                                        targetPath = mapping["target"].ToString();
                                }

                                // Try multiple paths to extract expression
                                if (mapping["expression"] != null)
                                {
                                    if (mapping["expression"]["value"] != null)
                                        expression = mapping["expression"]["value"].ToString();
                                    else
                                        expression = mapping["expression"].ToString();
                                }
                                else if (mapping["expr"] != null)
                                {
                                    if (mapping["expr"]["value"] != null)
                                        expression = mapping["expr"]["value"].ToString();
                                    else
                                        expression = mapping["expr"].ToString();
                                }
                                else if (mapping["source"] != null)
                                {
                                    if (mapping["source"]["value"] != null)
                                        expression = mapping["source"]["value"].ToString();
                                    else
                                        expression = mapping["source"].ToString();
                                }

                                if (!string.IsNullOrEmpty(targetPath) && !string.IsNullOrEmpty(expression))
                                {
                                    expressions.Add((targetPath, expression));
                                    LogToFile($"Found mapping [{processedCount + 1}]: {targetPath} -> {expression}");
                                    processedCount++;
                                }
                            }
                            catch (Exception ex)
                            {
                                LogToFile($"Error processing mapping {processedCount + 1}: {ex.Message}");
                            }
                        }

                        if (expressions.Count > 0)
                        {
                            LogToFile($"Successfully extracted {expressions.Count} valid mappings out of {mappingTable.Count}");

                            details.AppendLine("<h4>Mapper Expressions</h4>");
                            details.AppendLine("<div class=\"mapper-expressions\" style=\"max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin-bottom: 15px;\">");
                            details.AppendLine("<table class=\"mapper-table\" style=\"width: 100%; border-collapse: collapse;\">");
                            details.AppendLine("<tr style=\"background-color: #f5f5f5;\"><th style=\"padding: 8px; text-align: left; border-bottom: 1px solid #ddd;\">Expression</th><th style=\"padding: 8px; text-align: left; border-bottom: 1px solid #ddd; width: 30%;\">Output Variable</th></tr>");

                            foreach (var (targetPath, expression) in expressions)
                            {
                                details.AppendLine("<tr>");
                                details.AppendLine($"<td style=\"padding: 8px; border-bottom: 1px solid #eee;\"><pre style=\"margin: 0; white-space: pre-wrap; word-break: break-all;\">{EscapeHtml(expression)}</pre></td>");
                                details.AppendLine($"<td style=\"padding: 8px; border-bottom: 1px solid #eee; word-break: break-word;\">{EscapeHtml(targetPath)}</td>");
                                details.AppendLine("</tr>");
                            }

                            details.AppendLine("</table></div>");
                            LogToFile("Successfully generated mapper expressions HTML");
                            return details.ToString();
                        }
                        else
                        {
                            var errorMsg = "No valid mappings found in the mapping table";
                            LogToFile(errorMsg);
                            details.AppendLine($"<p>Error: {EscapeHtml(errorMsg)}</p>");

                            // Log the first few mappings to help with debugging
                            LogToFile("Sample of first few mappings (raw JSON):");
                            for (int i = 0; i < Math.Min(3, mappingTable.Count); i++)
                            {
                                LogToFile($"Mapping {i}: {mappingTable[i].ToString(Newtonsoft.Json.Formatting.None)}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        var errorMsg = $"Error parsing mapping table: {ex.Message}";
                        LogToFile($"{errorMsg}\n{ex.StackTrace}");
                        LogToFile($"Raw JSON that failed to parse (first 500 chars): {mappingTableJson?.Substring(0, Math.Min(500, mappingTableJson?.Length ?? 0))}...");
                        details.AppendLine($"<p>Error: {EscapeHtml(errorMsg)}</p>");
                    }
                }
                else
                {
                    var errorMsg = "No mapping table found in any known property";
                    LogToFile($"{errorMsg}. Checked for: {string.Join(", ", possibleMappingTableKeys)}");
                    LogToFile("Available properties:");
                    foreach (var key in snap.Properties.Keys)
                    {
                        LogToFile($"- {key}");
                    }

                    // Special handling: try to parse the whole snap as JSON
                    LogToFile("\nAttempting to find mappings by parsing entire snap properties as JSON...");
                    try
                    {
                        foreach (var propEntry in snap.Properties)
                        {
                            // Skip very small properties - they won't contain mapping tables
                            if (propEntry.Value?.Length < 10) continue;

                            try
                            {
                                // Try to parse this property as JSON
                                LogToFile($"Trying to parse '{propEntry.Key}' as JSON...");
                                JObject propJson = JObject.Parse(propEntry.Value);

                                // Look for mapping-related fields in this JSON
                                var mappingField = propJson["mappingTable"] ?? propJson["mappings"] ??
                                                  propJson["transformer"] ?? propJson["table"];

                                if (mappingField != null)
                                {
                                    LogToFile($"Found potential mapping data in '{propEntry.Key}.{mappingField.Path}'");
                                    mappingTableJson = mappingField.ToString();
                                    foundKey = propEntry.Key + "." + mappingField.Path;
                                    break;
                                }
                            }
                            catch
                            {
                                // Just continue to the next property
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogToFile($"Error during advanced mapping search: {ex.Message}");
                    }
                }

                if (!string.IsNullOrEmpty(mappingTableJson))
                {
                    try
                    {
                        LogToFile("Attempting to parse mapping table...");
                        var mappingTable = JArray.Parse(mappingTableJson);
                        LogToFile($"Successfully parsed {mappingTable.Count} mappings from {foundKey}");

                        var expressions = new List<(string TargetPath, string Expression)>();
                        int processedCount = 0;

                        // Extract all mappings
                        foreach (var mapping in mappingTable)
                        {
                            try
                            {
                                // More robust way to extract target path and expression
                                string targetPath = null;
                                string expression = null;

                                // Log the mapping structure to help diagnose issues
                                LogToFile($"Mapping structure: {mapping.ToString(Newtonsoft.Json.Formatting.None)}");

                                // Try multiple paths to extract target path
                                if (mapping["targetPath"] != null)
                                {
                                    if (mapping["targetPath"]["value"] != null)
                                        targetPath = mapping["targetPath"]["value"].ToString();
                                    else
                                        targetPath = mapping["targetPath"].ToString();
                                }
                                else if (mapping["target"] != null)
                                {
                                    if (mapping["target"]["value"] != null)
                                        targetPath = mapping["target"]["value"].ToString();
                                    else
                                        targetPath = mapping["target"].ToString();
                                }

                                // Try multiple paths to extract expression
                                if (mapping["expression"] != null)
                                {
                                    if (mapping["expression"]["value"] != null)
                                        expression = mapping["expression"]["value"].ToString();
                                    else
                                        expression = mapping["expression"].ToString();
                                }
                                else if (mapping["expr"] != null)
                                {
                                    if (mapping["expr"]["value"] != null)
                                        expression = mapping["expr"]["value"].ToString();
                                    else
                                        expression = mapping["expr"].ToString();
                                }
                                else if (mapping["source"] != null)
                                {
                                    if (mapping["source"]["value"] != null)
                                        expression = mapping["source"]["value"].ToString();
                                    else
                                        expression = mapping["source"].ToString();
                                }

                                if (!string.IsNullOrEmpty(targetPath) && !string.IsNullOrEmpty(expression))
                                {
                                    expressions.Add((targetPath, expression));
                                    LogToFile($"Found mapping [{processedCount + 1}]: {targetPath} -> {expression}");
                                    processedCount++;
                                }
                            }
                            catch (Exception ex)
                            {
                                LogToFile($"Error processing mapping {processedCount + 1}: {ex.Message}");
                            }
                        }

                        if (expressions.Count > 0)
                        {
                            LogToFile($"Successfully extracted {expressions.Count} valid mappings out of {mappingTable.Count}");

                            details.AppendLine("<h4>Mapper Expressions</h4>");
                            details.AppendLine("<div class=\"mapper-expressions\" style=\"max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin-bottom: 15px;\">");
                            details.AppendLine("<table class=\"mapper-table\" style=\"width: 100%; border-collapse: collapse;\">");
                            details.AppendLine("<tr style=\"background-color: #f5f5f5;\"><th style=\"padding: 8px; text-align: left; border-bottom: 1px solid #ddd;\">Expression</th><th style=\"padding: 8px; text-align: left; border-bottom: 1px solid #ddd; width: 30%;\">Output Variable</th></tr>");

                            foreach (var (targetPath, expression) in expressions)
                            {
                                details.AppendLine("<tr>");
                                details.AppendLine($"<td style=\"padding: 8px; border-bottom: 1px solid #eee;\"><pre style=\"margin: 0; white-space: pre-wrap; word-break: break-all;\">{EscapeHtml(expression)}</pre></td>");
                                details.AppendLine($"<td style=\"padding: 8px; border-bottom: 1px solid #eee; word-break: break-word;\">{EscapeHtml(targetPath)}</td>");
                                details.AppendLine("</tr>");
                            }

                            details.AppendLine("</table></div>");
                            LogToFile("Successfully generated mapper expressions HTML");
                            return details.ToString();
                        }
                        else
                        {
                            var errorMsg = "No valid mappings found in the mapping table";
                            LogToFile(errorMsg);
                            details.AppendLine($"<p>Error: {EscapeHtml(errorMsg)}</p>");

                            // Log the first few mappings to help with debugging
                            LogToFile("Sample of first few mappings (raw JSON):");
                            for (int i = 0; i < Math.Min(3, mappingTable.Count); i++)
                            {
                                LogToFile($"Mapping {i}: {mappingTable[i].ToString(Newtonsoft.Json.Formatting.None)}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        var errorMsg = $"Error parsing mapping table: {ex.Message}";
                        LogToFile($"{errorMsg}\n{ex.StackTrace}");
                        LogToFile($"Raw JSON that failed to parse (first 500 chars): {mappingTableJson?.Substring(0, Math.Min(500, mappingTableJson?.Length ?? 0))}...");
                        details.AppendLine($"<p>Error: {EscapeHtml(errorMsg)}</p>");
                    }
                }
                else
                {
                    var errorMsg = "No mapping table found in any known property";
                    LogToFile($"{errorMsg}. Checked for: {string.Join(", ", possibleMappingTableKeys)}");
                    LogToFile("Available properties:");
                    foreach (var key in snap.Properties.Keys)
                    {
                        LogToFile($"- {key}");
                    }
                    details.AppendLine($"<p>Error: {EscapeHtml(errorMsg)}</p>");
                }
            }
            // Handle other field mappings (non-mapper snaps)
            else if (snap.Properties.ContainsKey("fieldMappings") &&
                    !string.IsNullOrEmpty(snap.Properties["fieldMappings"]))
            {
                details.AppendLine("<h4>Field Mappings</h4>");
                details.AppendLine("<ul class=\"field-mappings\" style=\"max-height: 200px; overflow-y: auto; padding-left: 20px;\">");

                string mappings = ReplaceAllPlaceholders(snap.Properties["fieldMappings"]);
                string[] mappingLines = mappings.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);

                foreach (var line in mappingLines)
                {
                    string cleanLine = line.Trim();
                    if (!string.IsNullOrEmpty(cleanLine))
                    {
                        details.AppendLine($"<li style=\"margin-bottom: 5px;\">{System.Net.WebUtility.HtmlEncode(cleanLine)}</li>");
                    }
                }

                details.AppendLine("</ul>");
            }

            // Check for SQL queries
            if (snap.Properties.ContainsKey("sqlQuery") &&
                !string.IsNullOrEmpty(snap.Properties["sqlQuery"]))
            {
                details.AppendLine("<h4>SQL Query</h4>");
                details.AppendLine("<pre class=\"sql-query\">");
                details.AppendLine(System.Net.WebUtility.HtmlEncode(ReplaceAllPlaceholders(snap.Properties["sqlQuery"])));
                details.AppendLine("</pre>");
            }

            // Check for JSON Schema
            if (snap.Properties.ContainsKey("jsonSchema") &&
                !string.IsNullOrEmpty(snap.Properties["jsonSchema"]))
            {
                details.AppendLine("<h4>JSON Schema</h4>");
                details.AppendLine("<pre class=\"json-schema\">");
                details.AppendLine(System.Net.WebUtility.HtmlEncode(ReplaceAllPlaceholders(snap.Properties["jsonSchema"])));
                details.AppendLine("</pre>");
            }

            // Check for specific configurations based on snap type
            if (IsFlowControlSnap(snap))
            {
                // NOTE: Router configuration is already handled in the main snap processing loop in DocumentationGenerator.cs
                // This duplicate section was causing duplicate "Routing Logic" sections to appear.
                // The main processing already calls FlowControlConfigurationGenerator.GenerateRouterConfiguration()
                // so we don't need to do it again here in ProvideSnapFunctionDetails.

                // For non-router flow control snaps, show basic flow settings
                if (!snap.Type.ToLower().Contains("router"))
                {
                    details.AppendLine("<h4>Flow Control Settings</h4>");
                    details.AppendLine("<ul class=\"flow-settings\">");

                    if (snap.Properties.ContainsKey("routingConditions"))
                    {
                        details.AppendLine($"<li><strong>Routing Conditions:</strong> {System.Net.WebUtility.HtmlEncode(ReplaceAllPlaceholders(snap.Properties["routingConditions"]))}</li>");
                    }

                    if (snap.Properties.ContainsKey("defaultPath"))
                    {
                        details.AppendLine($"<li><strong>Default Path:</strong> {System.Net.WebUtility.HtmlEncode(ReplaceAllPlaceholders(snap.Properties["defaultPath"]))}</li>");
                    }

                    details.AppendLine("</ul>");

                    // Add connected snaps information for flow control snaps
                    // Use pipeline context to find connections
                    if (_currentPipeline != null)
                    {
                        var outgoingLinks = _currentPipeline.Links.Where(l => l.SourceId == snap.Id).ToList();

                        if (outgoingLinks.Any())
                        {
                            details.AppendLine("<h4>Connected Output Snaps</h4>");
                            details.AppendLine("<ul class=\"connected-snaps\">");

                            foreach (var link in outgoingLinks)
                            {
                                var targetSnap = _currentPipeline.Snaps.FirstOrDefault(s => s.Id == link.TargetId);
                                if (targetSnap != null)
                                {
                                    // Show the view name (if available) along with the target snap label
                                    string viewName = link.SourceViewId ?? "Default";
                                    details.AppendLine($"<li><strong>{System.Net.WebUtility.HtmlEncode(viewName)}:</strong> {System.Net.WebUtility.HtmlEncode(targetSnap.Label)}</li>");
                                }
                            }

                            details.AppendLine("</ul>");
                        }
                        else
                        {
                            details.AppendLine("<p><em>No connected output snaps</em></p>");
                        }
                    }
                }
            }

            // Check for router snaps which should use explicit pseudocode generation
            if (snap.Type.ToLower().Contains("router"))
            {
                // Skip raw properties for router snaps as requested
                LogMessage($"[SNAP-PROPERTIES] Skipping raw properties display for router snap: {snap.Label}");
            }
            else
            {
                // Raw properties section is already handled in the main snap details generation
                // Removed duplicate "View Raw Properties" section to avoid duplication
            }

            LogMessage($"[SNAP-PROCESS] Starting best practices for snap: {snap.Label}");
            string bestPractices = ""; // Temporarily disabled until method signature is fixed
            LogMessage($"[SNAP-PROCESS] Completed best practices for snap: {snap.Label}, result length: {bestPractices?.Length ?? 0}");
            if (!string.IsNullOrEmpty(bestPractices))
            {
                details.AppendLine($"          {bestPractices}");
            }

            return details.ToString();
        }

        /// <summary>
        /// Sets up a snap's InputConnections and OutputConnections from the pipeline context
        /// This is needed for the FlowControlConfigurationGenerator to properly display connected snaps
        /// </summary>
        /// <param name="snap">The snap to set up connections for</param>
        private void SetupSnapConnections(SnapNode snap)
        {
            if (_currentPipeline == null || snap == null)
                return;

            LogToFile($"Setting up connections for snap {snap.Label}");

            // Initialize collections if needed
            snap.InputConnections = snap.InputConnections ?? new List<SnapLink>();
            snap.OutputConnections = snap.OutputConnections ?? new List<SnapLink>();

            // Find all links where this snap is the target (inputs)
            var incomingLinks = _currentPipeline.Links.Where(l => l.TargetId == snap.Id).ToList();
            foreach (var link in incomingLinks)
            {
                // Only add if not already present
                if (!snap.InputConnections.Any(c => c.Id == link.Id))
                {
                    snap.InputConnections.Add(link);
                    LogToFile($"Added input connection from {link.SourceId} to {snap.Label}");
                }
            }

            // Find all links where this snap is the source (outputs)
            var outgoingLinks = _currentPipeline.Links.Where(l => l.SourceId == snap.Id).ToList();
            foreach (var link in outgoingLinks)
            {
                // Only add if not already present
                if (!snap.OutputConnections.Any(c => c.Id == link.Id))
                {
                    snap.OutputConnections.Add(link);

                    // Ensure the target snap exists in the pipeline
                    var targetSnap = _currentPipeline.Snaps.FirstOrDefault(s => s.Id == link.TargetId);
                    if (targetSnap != null)
                    {
                        LogToFile($"Added output connection from {snap.Label} to {targetSnap.Label} with view {link.SourceViewId ?? "Default"}");
                    }
                }
            }
        }

        /// <summary>
        /// Determines if a snap is a flow control element (router, join, union, switch, etc.)
        /// </summary>
        /// <param name="snap">The snap to check</param>        /// <returns>True if the snap controls document flow</returns>
        private bool IsFlowControlSnap(SnapNode snap)
        {
            if (snap == null)
                return false;

            string snapType = snap.Type?.ToLower() ?? "";

            return snapType.Contains("router") ||
                   snapType.Contains("join") ||
                   snapType.Contains("union") ||
                   snapType.Contains("switch") ||
                   snapType.Contains("branch") ||
                   snapType.Contains("conditional") ||
                   snapType.Contains("if") ||
                   snapType.Contains("case") ||
                   (snap.Category == SnapCategory.FlowControl);
        }

        /// <summary>
        /// Escapes HTML special characters to prevent XSS and ensure proper display
        /// </summary>
        private string EscapeHtml(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            return System.Net.WebUtility.HtmlEncode(input);
        }

        /// <summary>
        /// Displays all raw properties of a snap for debugging or detailed views
        /// </summary>
        /// <param name="html">StringBuilder to append the HTML content</param>        /// <param name="snap">The snap whose properties should be displayed</param>
        private void DisplayRawProperties(StringBuilder html, SnapNode snap)
        {
            html.AppendLine("<h4>All Properties</h4>");
            html.AppendLine("<div class=\"raw-properties\">");

            if (snap.Properties.Any())
            {
                html.AppendLine("<table class=\"property-table\">");
                html.AppendLine("<tr><th>Property</th><th>Value</th></tr>");

                foreach (var prop in snap.Properties.OrderBy(p => p.Key))
                {
                    if (!string.IsNullOrEmpty(prop.Value))
                    {
                        string processedValue = ReplaceAllPlaceholders(prop.Value);

                        // Format long JSON values for better readability
                        if (processedValue.Length > 100 && (processedValue.StartsWith("{") || processedValue.StartsWith("[")))
                        {
                            try
                            {
                                var parsed = Newtonsoft.Json.JsonConvert.DeserializeObject(processedValue);
                                processedValue = Newtonsoft.Json.JsonConvert.SerializeObject(parsed, Newtonsoft.Json.Formatting.Indented);
                            }
                            catch
                            {
                                // Keep original value if JSON parsing fails
                            }
                        }

                        html.AppendLine($"<tr><td style=\"vertical-align: top; font-weight: bold;\">{System.Net.WebUtility.HtmlEncode(prop.Key)}</td><td style=\"vertical-align: top;\"><pre style=\"margin: 0; white-space: pre-wrap; word-break: break-word; font-family: 'Courier New', monospace; font-size: 12px;\">{System.Net.WebUtility.HtmlEncode(processedValue)}</pre></td></tr>");
                    }
                }

                html.AppendLine("</table>");
            }
            else
            {
                html.AppendLine("<p><em>No properties found for this snap.</em></p>");
            }

            html.AppendLine("</div>");
        }

        /// <summary>
        /// Generates PDF documentation for a pipeline
        /// </summary>
        /// <param name="pipeline">The pipeline data</param>
        /// <param name="diagramSvg">The SVG diagram of the pipeline</param>
        /// <param name="outputPath">The output file path for the PDF</param>
        public void GeneratePdfDocumentation(PipelineData pipeline, string diagramSvg, string outputPath)
        {
            try
            {
                LogMessage($"[PDF-GEN] Starting PDF generation for {pipeline.Name}");

                // Generate HTML content first
                string htmlContent = GenerateHtmlDocumentationAsync(pipeline, diagramSvg).GetAwaiter().GetResult();

                // Create a temporary HTML file
                string tempHtmlPath = Path.GetTempFileName() + ".html";
                File.WriteAllText(tempHtmlPath, htmlContent);

                LogMessage($"[PDF-GEN] Created temporary HTML file at {tempHtmlPath}");

                // Use a PDF conversion library or command-line tool to convert HTML to PDF
                // For example, using wkhtmltopdf command-line tool:
                using (var process = new System.Diagnostics.Process())
                {
                    process.StartInfo.FileName = "wkhtmltopdf";
                    process.StartInfo.Arguments = $"\"{tempHtmlPath}\" \"{outputPath}\"";
                    process.StartInfo.UseShellExecute = false;
                    process.StartInfo.CreateNoWindow = true;
                    process.StartInfo.RedirectStandardOutput = true;
                    process.StartInfo.RedirectStandardError = true;

                    LogMessage($"[PDF-GEN] Executing PDF conversion command");

                    process.Start();
                    process.WaitForExit();

                    string output = process.StandardOutput.ReadToEnd();
                    string error = process.StandardError.ReadToEnd();

                    if (!string.IsNullOrEmpty(error))
                    {
                        LogMessage($"[PDF-GEN] Error converting to PDF: {error}");
                    }

                    if (process.ExitCode != 0)
                    {
                        LogMessage($"[PDF-GEN] PDF conversion failed with exit code {process.ExitCode}");
                        throw new Exception($"PDF generation failed with exit code {process.ExitCode}");
                    }
                }

                LogMessage($"[PDF-GEN] Successfully generated PDF at {outputPath}");

                // Clean up the temporary HTML file
                try
                {
                    File.Delete(tempHtmlPath);
                }
                catch (Exception ex)
                {
                    LogMessage($"[PDF-GEN] Warning: Could not delete temporary HTML file: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"[PDF-GEN] Error generating PDF: {ex.Message}");
                throw new Exception($"Failed to generate PDF documentation: {ex.Message}", ex);
            }
        }


        /// <summary>
        /// Gets snaps ordered by their flow sequence in the pipeline
        /// </summary>
        private List<SnapNode> GetSnapFlowOrder(PipelineData pipeline)
        {
            var orderedSnaps = new List<SnapNode>();
            var processedSnaps = new HashSet<string>();

            // Find starting snaps (snaps with no input connections)
            var startingSnaps = pipeline.Snaps.Where(snap =>
                !pipeline.Links.Any(link => link.TargetId == snap.Id)).ToList();

            // If no clear starting snaps, use all snaps as potential starts
            if (!startingSnaps.Any())
            {
                startingSnaps = pipeline.Snaps.ToList();
            }

            // Traverse from each starting snap
            foreach (var startSnap in startingSnaps)
            {
                TraverseSnapFlow(startSnap, pipeline, orderedSnaps, processedSnaps);
            }

            // Add any remaining snaps that weren't connected
            foreach (var snap in pipeline.Snaps)
            {
                if (!processedSnaps.Contains(snap.Id))
                {
                    orderedSnaps.Add(snap);
                }
            }

            return orderedSnaps;
        }

        /// <summary>
        /// Recursively traverses snap connections to build flow order
        /// </summary>
        private void TraverseSnapFlow(SnapNode snap, PipelineData pipeline, List<SnapNode> orderedSnaps, HashSet<string> processedSnaps)
        {
            if (processedSnaps.Contains(snap.Id))
                return;

            processedSnaps.Add(snap.Id);
            orderedSnaps.Add(snap);

            // Find connected snaps
            var connectedSnaps = pipeline.Links
                .Where(link => link.SourceId == snap.Id)
                .Select(link => pipeline.Snaps.FirstOrDefault(s => s.Id == link.TargetId))
                .Where(s => s != null)
                .ToList();

            foreach (var connectedSnap in connectedSnaps)
            {
                TraverseSnapFlow(connectedSnap, pipeline, orderedSnaps, processedSnaps);
            }
        }

        /// <summary>
        /// Gets CSS class name for snap category
        /// </summary>
        private string GetCategoryClass(SnapCategory category)
        {
            return category switch
            {
                SnapCategory.FlowControl => "flow-control",
                SnapCategory.Transformation => "transformation",
                SnapCategory.Database => "database",
                SnapCategory.ExternalSystem => "external-system",
                SnapCategory.FileOperation => "file-operation",
                SnapCategory.ErrorHandling => "error-handling",
                _ => "other"
            };
        }

        /// <summary>
        /// Gets display name for snap category
        /// </summary>
        private string GetCategoryDisplayName(SnapCategory category)
        {
            return category switch
            {
                SnapCategory.FlowControl => "Flow Control",
                SnapCategory.Transformation => "Transformation",
                SnapCategory.Database => "Database Operations",
                SnapCategory.ExternalSystem => "External Systems",
                SnapCategory.FileOperation => "File Operations",
                SnapCategory.ErrorHandling => "Error Handling",
                _ => "Other"
            };
        }

        /// <summary>
        /// Generates a unique HTML ID for a snap
        /// </summary>
        private string GenerateSnapId(SnapNode snap)
        {
            // Create a safe HTML ID from the snap label and ID
            string safeLabel = snap.Label?.Replace(" ", "-").Replace("(", "").Replace(")", "")
                .Replace("[", "").Replace("]", "").Replace(".", "-").Replace(",", "")
                .Replace("'", "").Replace("\"", "").Replace("/", "-").Replace("\\", "-")
                .Replace(":", "-").Replace(";", "-").Replace("?", "").Replace("!", "")
                .Replace("@", "").Replace("#", "").Replace("$", "").Replace("%", "")
                .Replace("^", "").Replace("&", "").Replace("*", "").Replace("+", "")
                .Replace("=", "").Replace("|", "").Replace("<", "").Replace(">", "")
                .ToLower() ?? "snap";

            return $"snap-{safeLabel}-{snap.Id}";
        }

        /// <summary>
        /// Extracts connection information from snap properties and raw JSON
        /// </summary>
        private string GetConnectionInfo(SnapNode snap, JObject rawPipelineJson = null)
        {
            // Show connection info for any snap that has an account
            LogMessage($"[CONNECTION] Checking connection info for snap: {snap.Label}, Properties count: {snap.Properties.Count}");
            Console.WriteLine($"[DEBUG-CONNECTION-START] GetConnectionInfo called for: {snap.Label}");
            Console.WriteLine($"[DEBUG-CONNECTION-PROPS] Snap has {snap.Properties.Count} properties");
            
            foreach (var prop in snap.Properties.Take(5)) // Show first 5 properties for debugging
            {
                Console.WriteLine($"[DEBUG-CONNECTION-PROP] {prop.Key} = {(string.IsNullOrEmpty(prop.Value) ? "NULL/EMPTY" : prop.Value.Substring(0, Math.Min(50, prop.Value.Length)) + "...")}");
            }
            
            // Log all properties for debugging
            foreach (var prop in snap.Properties)
            {
                LogMessage($"[CONNECTION] Property: {prop.Key} = {prop.Value}");
            }

            // First priority: Extract from raw JSON pipeline data
            if (rawPipelineJson != null)
            {
                try
                {
                    LogMessage($"[CONNECTION] Attempting to extract account from raw pipeline JSON for snap: {snap.Label}");
                    Console.WriteLine($"[DEBUG-JSON-KEYS] Raw JSON top-level keys: {string.Join(", ", rawPipelineJson.Properties().Select(p => p.Name))}");
                    
                    // Extract the specific snap JSON from the pipeline's "snap_map" collection using snap ID
                    var snapsCollection = rawPipelineJson["snap_map"];
                    Console.WriteLine($"[DEBUG-SNAPS] snap_map collection exists: {snapsCollection != null}");
                    
                    if (snapsCollection != null && !string.IsNullOrEmpty(snap.Id))
                    {
                        // Find the specific snap in the collection using the snap ID
                        var snapJson = snapsCollection[snap.Id];
                        Console.WriteLine($"[DEBUG-SNAP-JSON] Found snap JSON for ID {snap.Id}: {snapJson != null}");
                        
                        if (snapJson != null)
                        {
                            // Look for property_map which contains the snap configuration
                            var propertyMap = snapJson["property_map"];
                            Console.WriteLine($"[DEBUG-PROPERTY-MAP] property_map exists: {propertyMap != null}");
                            
                            if (propertyMap != null)
                            {
                                // Try to extract account information from nested structure
                                var account = propertyMap["account"];
                                Console.WriteLine($"[DEBUG-ACCOUNT] account property exists: {account != null}");
                                
                                if (account != null)
                                {
                                    // Try to extract account name from nested structure
                                    var accountRef = account["account_ref"];
                                    if (accountRef != null)
                                    {
                                        var accountValue = accountRef["value"];
                                        if (accountValue != null)
                                        {
                                            var accountLabel = accountValue["label"];
                                            if (accountLabel != null)
                                            {
                                                var accountName = accountLabel["value"]?.ToString();
                                                if (!string.IsNullOrEmpty(accountName))
                                                {
                                                    LogMessage($"[CONNECTION] Found account name from raw JSON property_map: {accountName}");
                                                    return accountName;
                                                }
                                            }
                                        }
                                    }
                                    
                                    // Also try direct account value
                                    var directAccountName = account["value"]?.ToString();
                                    if (!string.IsNullOrEmpty(directAccountName))
                                    {
                                        LogMessage($"[CONNECTION] Found direct account name from raw JSON: {directAccountName}");
                                        return directAccountName;
                                    }
                                }
                                
                                LogMessage($"[CONNECTION] No account found in raw JSON property_map for snap: {snap.Label}");
                            }
                            else
                            {
                                LogMessage($"[CONNECTION] No property_map found in raw JSON");
                            }
                        }
                    }
                    else
                    {
                        LogMessage($"[CONNECTION] No snaps collection or snap ID not found in raw JSON");
                    }
                }
                catch (Exception ex)
                {
                    LogMessage($"[CONNECTION] Error extracting account from raw JSON: {ex.Message}");
                }
            }
            else
            {
                LogMessage($"[CONNECTION] No raw pipeline JSON available for account extraction");
            }

            // Since no account information was found in the expected location,
            // return NULL rather than showing table names or other properties
            // that are not actual account names
            LogMessage($"[CONNECTION] No account information found for snap: {snap.Label}");
            Console.WriteLine($"[DEBUG-CONNECTION-NO-ACCOUNT] No account info found - returning NULL");
            return "NULL";
        }

        /// <summary>
        /// Extracts and formats Dynamics365ForSales-specific details
        /// </summary>
        private string GetDynamics365ForSalesDetails(SnapNode snap)
        {
            var html = new StringBuilder();
            LogMessage($"[DYNAMICS365] Extracting details for snap: {snap.Label}");

            try
            {
                var details = new Dictionary<string, string>();

                // Look for specific Dynamics365ForSales properties
                foreach (var prop in snap.Properties)
                {
                    string key = prop.Key.ToLower();
                    string value = prop.Value;

                    // Skip empty or boolean values
                    if (string.IsNullOrEmpty(value) ||
                        value.Equals("true", StringComparison.OrdinalIgnoreCase) ||
                        value.Equals("false", StringComparison.OrdinalIgnoreCase))
                        continue;

                    // Filter Condition
                    if (key.Contains("filter") && (key.Contains("condition") || key.Contains("criteria") || key.Contains("where")))
                    {
                        details["Filter Condition"] = value;
                        LogMessage($"[DYNAMICS365] Found Filter Condition: {value}");
                    }
                    // Query Parameters
                    else if (key.Contains("query") && (key.Contains("parameter") || key.Contains("param") || key.Contains("variable")))
                    {
                        details["Query Parameters"] = value;
                        LogMessage($"[DYNAMICS365] Found Query Parameters: {value}");
                    }
                    // Output Attributes
                    else if (key.Contains("output") && (key.Contains("attribute") || key.Contains("field") || key.Contains("column")))
                    {
                        details["Output Attributes"] = value;
                        LogMessage($"[DYNAMICS365] Found Output Attributes: {value}");
                    }
                    // Order By
                    else if (key.Contains("order") && (key.Contains("by") || key.Contains("sort")))
                    {
                        details["Order By"] = value;
                        LogMessage($"[DYNAMICS365] Found Order By: {value}");
                    }
                    // Generic property matching for these specific terms
                    else if (key.Contains("filtercondition") || key == "filter_condition")
                    {
                        details["Filter Condition"] = value;
                        LogMessage($"[DYNAMICS365] Found Filter Condition (exact match): {value}");
                    }
                    else if (key.Contains("queryparameters") || key == "query_parameters")
                    {
                        details["Query Parameters"] = value;
                        LogMessage($"[DYNAMICS365] Found Query Parameters (exact match): {value}");
                    }
                    else if (key.Contains("outputattributes") || key == "output_attributes")
                    {
                        details["Output Attributes"] = value;
                        LogMessage($"[DYNAMICS365] Found Output Attributes (exact match): {value}");
                    }
                    else if (key.Contains("orderby") || key == "order_by")
                    {
                        details["Order By"] = value;
                        LogMessage($"[DYNAMICS365] Found Order By (exact match): {value}");
                    }
                }

                // Format the details for HTML output
                if (details.Any())
                {
                    html.AppendLine($"          <div style=\"margin: 10px 0; padding: 10px; background-color: #f0f8ff; border-left: 4px solid #0066cc;\">");
                    html.AppendLine($"            <h5 style=\"color: #0066cc; margin: 0 0 8px 0;\">Dynamics365 Configuration</h5>");

                    foreach (var detail in details)
                    {
                        string displayValue = detail.Value;

                        // Try to parse JSON values for better display
                        if (displayValue.StartsWith("{") || displayValue.StartsWith("["))
                        {
                            try
                            {
                                var parsed = JToken.Parse(displayValue);
                                if (parsed is JArray array)
                                {
                                    displayValue = string.Join(", ", array.Select(item => item.ToString()));
                                }
                                else if (parsed is JObject obj)
                                {
                                    displayValue = string.Join(", ", obj.Properties().Select(p => $"{p.Name}: {p.Value}"));
                                }
                            }
                            catch
                            {
                                // Keep original value if JSON parsing fails
                            }
                        }

                        html.AppendLine($"            <p style=\"margin: 4px 0;\"><strong>{detail.Key}:</strong> {System.Net.WebUtility.HtmlEncode(displayValue)}</p>");
                        LogMessage($"[DYNAMICS365] Added to output: {detail.Key} = {displayValue}");
                    }

                    html.AppendLine($"          </div>");
                    LogMessage($"[DYNAMICS365] Generated HTML details block for: {snap.Label}");
                }
                else
                {
                    LogMessage($"[DYNAMICS365] No specific details found for: {snap.Label}");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"[DYNAMICS365] Error processing details for {snap.Label}: {ex.Message}");
            }

            return html.ToString();
        }

        /// <summary>
        /// Extracts SQL statement from SQL execute snaps
        /// </summary>
        private string GetSqlStatement(SnapNode snap)
        {
            LogMessage($"[SQL-EXTRACT] Checking SQL statement for snap: {snap.Label}");

            // Only process database snaps that contain "execute" in their type
            if (snap.Category != SnapCategory.Database || !snap.Type?.ToLower().Contains("execute") == true)
            {
                LogMessage($"[SQL-EXTRACT] Skipping non-execute or non-database snap: {snap.Label}");
                return null;
            }

            // Look for SQL statement in properties
            foreach (var prop in snap.Properties)
            {
                string key = prop.Key.ToLower();
                string value = prop.Value;

                LogMessage($"[SQL-EXTRACT] Checking property: {prop.Key} = {(string.IsNullOrEmpty(value) ? "NULL/EMPTY" : $"'{value.Substring(0, Math.Min(100, value.Length))}...'")}");

                // Check for common SQL property keys
                if (key.Contains("sqlstatement") || key.Contains("sql_statement") || 
                    key.Contains("query") || key.Contains("statement") || key == "sql")
                {
                    if (!string.IsNullOrEmpty(value))
                    {
                        LogMessage($"[SQL-EXTRACT] Found SQL in property '{prop.Key}': {value.Substring(0, Math.Min(200, value.Length))}...");
                        return value;
                    }
                }

                // Check if the property value is JSON containing SQL
                if (!string.IsNullOrEmpty(value) && (value.Trim().StartsWith("{") || value.Trim().StartsWith("[")))
                {
                    try
                    {
                        var jsonObj = JObject.Parse(value);
                        foreach (var jsonProp in jsonObj.Properties())
                        {
                            string jsonKey = jsonProp.Name.ToLower();
                            if (jsonKey.Contains("sql") || jsonKey.Contains("statement") || jsonKey.Contains("query"))
                            {
                                string sqlValue = jsonProp.Value?.ToString();
                                if (!string.IsNullOrEmpty(sqlValue))
                                {
                                    LogMessage($"[SQL-EXTRACT] Found SQL in JSON property '{jsonProp.Name}': {sqlValue.Substring(0, Math.Min(200, sqlValue.Length))}...");
                                    return sqlValue;
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"[SQL-EXTRACT] Error parsing JSON for SQL: {ex.Message}");
                    }
                }
            }

            LogMessage($"[SQL-EXTRACT] No SQL statement found for snap: {snap.Label}");
            return null;
        }

        /// <summary>
        /// Generates specific description for database snaps
        /// </summary>
        private string GenerateDatabaseSnapDescription(SnapNode snap)
        {
            string snapType = snap.Type.ToLower();

            // Try to extract SQL statement for more specific description
            string sqlStatement = GetSqlStatement(snap);
            if (!string.IsNullOrEmpty(sqlStatement))
            {
                string operation = "executes";
                if (sqlStatement.TrimStart().StartsWith("SELECT", StringComparison.OrdinalIgnoreCase))
                    operation = "retrieves data using";
                else if (sqlStatement.TrimStart().StartsWith("INSERT", StringComparison.OrdinalIgnoreCase))
                    operation = "inserts data using";
                else if (sqlStatement.TrimStart().StartsWith("UPDATE", StringComparison.OrdinalIgnoreCase))
                    operation = "updates data using";
                else if (sqlStatement.TrimStart().StartsWith("DELETE", StringComparison.OrdinalIgnoreCase))
                    operation = "deletes data using";
                else if (sqlStatement.TrimStart().StartsWith("MERGE", StringComparison.OrdinalIgnoreCase))
                    operation = "merges data using";

                return $"Database snap that {operation} a SQL statement to interact with the connected database.";
            }

            // Fall back to connection-based description
            if (snapType.Contains("select"))
                return "Database snap that retrieves data from the connected database using a SELECT query.";
            if (snapType.Contains("insert"))
                return "Database snap that inserts new records into the connected database.";
            if (snapType.Contains("update"))
                return "Database snap that updates existing records in the connected database.";
            if (snapType.Contains("delete"))
                return "Database snap that removes records from the connected database.";
            if (snapType.Contains("execute"))
                return "Database snap that executes SQL statements against the connected database.";

            return "Database snap that performs operations on the connected database.";
        }

        /// <summary>
        /// Generates specific description for router snaps
        /// </summary>
        private string GenerateRouterSnapDescription(SnapNode snap)
        {
            // Use the existing router route extraction logic from FlowControlConfigurationGenerator
            var routes = ExtractRouterRoutesForDescription(snap);

            if (routes.Any())
            {
                return $"Router snap that directs documents to different output paths based on {routes.Count} conditional expression{(routes.Count > 1 ? "s" : "")}.";
            }

            return "Router snap that directs documents to different output paths based on conditional expressions.";
        }

        /// <summary>
        /// Extracts router routes for description purposes (simplified version of FlowControlConfigurationGenerator logic)
        /// </summary>
        private List<(string condition, string outputPath)> ExtractRouterRoutesForDescription(SnapNode snap)
        {
            var routes = new List<(string condition, string outputPath)>();

            try
            {
                // Check for routes in the settings.routes.value format (from SlpAnalyzer extraction)
                var routeProperties = snap.Properties.Where(p =>
                    p.Key.StartsWith("settings.routes.value[") &&
                    p.Key.EndsWith(".expression.value")).ToList();

                foreach (var routeProp in routeProperties)
                {
                    if (!string.IsNullOrEmpty(routeProp.Value))
                    {
                        // Extract the index from the key (e.g., "settings.routes.value[0].expression.value")
                        var indexMatch = System.Text.RegularExpressions.Regex.Match(routeProp.Key, @"\[(\d+)\]");
                        if (indexMatch.Success)
                        {
                            string index = indexMatch.Groups[1].Value;
                            string outputViewKey = $"settings.routes.value[{index}].outputViewName.value";

                            if (snap.Properties.TryGetValue(outputViewKey, out string outputView))
                            {
                                routes.Add((routeProp.Value, outputView));
                            }
                            else
                            {
                                // If no specific output view, use generic output name
                                routes.Add((routeProp.Value, $"output{index}"));
                            }
                        }
                    }
                }

                // If no routes found in the structured format, try legacy format
                if (!routes.Any())
                {
                    var legacyRouteProps = snap.Properties
                        .Where(p => System.Text.RegularExpressions.Regex.IsMatch(p.Key, @"output\d+") &&
                                  (p.Key.EndsWith(".condition") || p.Key.EndsWith(".expression")))
                        .ToList();

                    foreach (var prop in legacyRouteProps)
                    {
                        if (!string.IsNullOrEmpty(prop.Value))
                        {
                            var match = System.Text.RegularExpressions.Regex.Match(prop.Key, @"(output\d+)");
                            if (match.Success)
                            {
                                string outputName = match.Groups[1].Value;
                                routes.Add((prop.Value, outputName));
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
                // If extraction fails, return empty list
            }

            return routes;
        }

        /// <summary>
        /// Generates specific description for join snaps
        /// </summary>
        private string GenerateJoinSnapDescription(SnapNode snap)
        {
            string snapType = snap.Type.ToLower();

            // Determine join type
            string joinType = "Inner";
            if (snap.Properties.TryGetValue("settings.joinType.value", out string joinTypeValue))
            {
                joinType = joinTypeValue;
            }

            // Count join conditions
            int joinConditions = snap.Properties.Count(p =>
                p.Key.Contains("joinPaths.value") && p.Key.Contains("leftPath.value"));

            string conditionText = joinConditions > 0 ?
                $" using {joinConditions} join condition{(joinConditions > 1 ? "s" : "")}" : "";

            return $"Join snap that performs an {joinType} join to combine data from multiple input streams{conditionText}.";
        }

        /// <summary>
        /// Generates specific description for mapper/transform snaps
        /// </summary>
        private string GenerateMapperSnapDescription(SnapNode snap)
        {
            // Count mappings by looking for the actual mapping table entries
            int mappingCount = 0;

            // Look for properties like "settings.transformations.mappingTable.0.targetPath.value",
            // "settings.transformations.mappingTable.1.targetPath.value", etc.
            var mappingProperties = snap.Properties.Where(p =>
                p.Key.Contains("settings.transformations.mappingTable.") &&
                p.Key.EndsWith(".targetPath.value")).ToList();

            mappingCount = mappingProperties.Count;

            // If no structured mappings found, try legacy format
            if (mappingCount == 0)
            {
                // Look for other mapping patterns
                var legacyMappings = snap.Properties.Where(p =>
                    p.Key.Contains("mappingTable") &&
                    (p.Key.Contains("targetPath") || p.Key.Contains("expression"))).ToList();

                // Count unique mapping indices
                var indices = new HashSet<string>();
                foreach (var prop in legacyMappings)
                {
                    var match = System.Text.RegularExpressions.Regex.Match(prop.Key, @"mappingTable\.(\d+)\.");
                    if (match.Success)
                    {
                        indices.Add(match.Groups[1].Value);
                    }
                }
                mappingCount = indices.Count;
            }

            if (mappingCount > 0)
            {
                return $"Mapper snap that transforms document structure and values using {mappingCount} field mapping{(mappingCount > 1 ? "s" : "")}.";
            }

            return "Mapper snap that transforms document structure and field values according to configured mapping rules.";
        }

        /// <summary>
        /// Generates specific description for external system snaps
        /// </summary>
        private string GenerateExternalSystemSnapDescription(SnapNode snap)
        {
            string snapType = snap.Type.ToLower();

            if (snapType.Contains("dynamics"))
            {
                if (snapType.Contains("search"))
                    return GenerateDynamics365SearchDescription(snap);
                if (snapType.Contains("create"))
                    return "External system snap that creates new records in Dynamics 365 CRM.";
                if (snapType.Contains("update"))
                    return "External system snap that updates existing records in Dynamics 365 CRM.";
                if (snapType.Contains("delete"))
                    return "External system snap that deletes records from Dynamics 365 CRM.";
                return "External system snap that interacts with Dynamics 365 CRM.";
            }

            if (snapType.Contains("rest"))
            {
                if (snapType.Contains("get"))
                    return "External system snap that makes HTTP GET requests to a REST API.";
                if (snapType.Contains("post"))
                    return "External system snap that makes HTTP POST requests to a REST API.";
                if (snapType.Contains("put"))
                    return "External system snap that makes HTTP PUT requests to a REST API.";
                if (snapType.Contains("delete"))
                    return "External system snap that makes HTTP DELETE requests to a REST API.";
                return "External system snap that makes HTTP requests to a REST API.";
            }

            if (snapType.Contains("soap"))
                return "External system snap that makes SOAP web service calls.";

            return "External system snap that connects to and interacts with an external service or API.";
        }

        /// <summary>
        /// Generates specific description for file operation snaps
        /// </summary>
        private string GenerateFileSnapDescription(SnapNode snap)
        {
            string snapType = snap.Type.ToLower();

            if (snapType.Contains("reader") || snapType.Contains("read"))
                return "File snap that reads data from files and converts them into document streams.";
            if (snapType.Contains("writer") || snapType.Contains("write"))
                return "File snap that writes document streams to files in the specified format.";
            if (snapType.Contains("parser"))
                return "File snap that parses file content and converts it into structured documents.";
            if (snapType.Contains("formatter"))
                return "File snap that formats documents and converts them into file content.";

            if (snapType.Contains("csv"))
                return "File snap that processes CSV (comma-separated values) files.";
            if (snapType.Contains("json"))
                return "File snap that processes JSON files and data structures.";
            if (snapType.Contains("xml"))
                return "File snap that processes XML files and data structures.";

            return "File snap that performs file operations and data format conversions.";
        }

        /// <summary>
        /// Generates specific description for flow control snaps
        /// </summary>
        private string GenerateFlowControlSnapDescription(SnapNode snap)
        {
            string snapType = snap.Type.ToLower();

            if (snapType.Contains("union"))
                return "Flow control snap that merges multiple input streams into a single output stream.";
            if (snapType.Contains("copy"))
                return "Flow control snap that duplicates documents and sends copies to multiple output paths.";
            if (snapType.Contains("filter"))
                return "Flow control snap that filters documents based on specified criteria.";
            if (snapType.Contains("sort"))
                return "Flow control snap that sorts documents based on specified field values.";
            if (snapType.Contains("gate"))
                return "Flow control snap that controls document flow based on conditions or timing.";
            if (snapType.Contains("exit"))
                return "Flow control snap that terminates pipeline execution based on specified conditions.";

            return "Flow control snap that manages document flow and processing logic.";
        }

        /// <summary>
        /// Generates specific description for Dynamics 365 search snaps with search criteria details
        /// </summary>
        private string GenerateDynamics365SearchDescription(SnapNode snap)
        {
            // Extract object type
            string objectType = "records";
            if (snap.Properties.TryGetValue("settings.objectType.value", out string objType))
            {
                objectType = objType;
            }

            // Extract search criteria
            var searchCriteria = ExtractDynamics365SearchCriteria(snap);

            // Extract output attributes
            var outputAttributes = ExtractDynamics365OutputAttributes(snap);

            // Extract order by criteria
            var orderByCriteria = ExtractDynamics365OrderBy(snap);

            // Build description
            var description = $"Dynamics 365 search snap that retrieves {objectType} records";

            if (searchCriteria.Any())
            {
                description += $" with {searchCriteria.Count} filter condition{(searchCriteria.Count > 1 ? "s" : "")}";

                // Add specific criteria details
                var criteriaDetails = new List<string>();
                foreach (var criteria in searchCriteria.Take(3)) // Show up to 3 criteria
                {
                    criteriaDetails.Add($"{criteria.attribute} {criteria.operatorType} {criteria.value}");
                }

                if (criteriaDetails.Any())
                {
                    description += $" ({string.Join(", ", criteriaDetails)}";
                    if (searchCriteria.Count > 3)
                    {
                        description += $", +{searchCriteria.Count - 3} more";
                    }
                    description += ")";
                }
            }

            if (outputAttributes.Any())
            {
                description += $" returning {outputAttributes.Count} specific attribute{(outputAttributes.Count > 1 ? "s" : "")}";

                // Add the actual attribute names
                if (outputAttributes.Count <= 5) // Show all attributes if 5 or fewer
                {
                    description += $" ({string.Join(", ", outputAttributes)})";
                }
                else // Show first 3 and indicate there are more
                {
                    description += $" ({string.Join(", ", outputAttributes.Take(3))}, +{outputAttributes.Count - 3} more)";
                }
            }

            if (orderByCriteria.Any())
            {
                description += $" ordered by {string.Join(", ", orderByCriteria.Take(2))}";
                if (orderByCriteria.Count > 2)
                {
                    description += $" (+{orderByCriteria.Count - 2} more)";
                }
            }

            description += ".";
            return description;
        }

        /// <summary>
        /// Extracts search criteria from Dynamics 365 search snap properties
        /// </summary>
        private List<(string attribute, string operatorType, string value)> ExtractDynamics365SearchCriteria(SnapNode snap)
        {
            var criteria = new List<(string attribute, string operatorType, string value)>();

            // Look for the specific Dynamics 365 filter_prop structure
            foreach (var prop in snap.Properties)
            {
                // Look for extracted filter properties from the new parsing logic
                if (prop.Key.Contains("settings.filter_prop.") && prop.Key.EndsWith(".attribute"))
                {
                    string baseKey = prop.Key.Replace(".attribute", "");
                    string attribute = prop.Value;
                    string operatorType = snap.Properties.GetValueOrDefault(baseKey + ".operator", "equal");
                    string value = snap.Properties.GetValueOrDefault(baseKey + ".value", "");

                    if (!string.IsNullOrEmpty(attribute))
                    {
                        criteria.Add((attribute, operatorType, value));
                    }
                }

                // Look for filter conditions in various property formats (legacy support)
                else if (prop.Key.ToLower().Contains("filter") || prop.Key.ToLower().Contains("criteria") ||
                    prop.Key.ToLower().Contains("condition") || prop.Key.ToLower().Contains("query"))
                {
                    // Try to parse JSON filter structures
                    try
                    {
                        if (prop.Value.StartsWith("[") || prop.Value.StartsWith("{"))
                        {
                            // Parse JSON to extract filter conditions
                            var filterText = prop.Value.ToLower();

                            // Look for the specific D365 structure with filterAttribute_prop
                            if (filterText.Contains("filterattribute_prop") && filterText.Contains("filteroperator_prop") && filterText.Contains("filtervalue_prop"))
                            {
                                // Extract using regex for the _prop structure
                                var attributeMatches = System.Text.RegularExpressions.Regex.Matches(prop.Value, @"""filterAttribute_prop"":\s*{\s*""value"":\s*""([^""]+)""", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                                var operatorMatches = System.Text.RegularExpressions.Regex.Matches(prop.Value, @"""filterOperator_prop"":\s*{\s*""value"":\s*""([^""]+)""", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                                var valueMatches = System.Text.RegularExpressions.Regex.Matches(prop.Value, @"""filterValue_prop"":\s*{\s*""value"":\s*""([^""]+)""", System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                                for (int i = 0; i < Math.Min(Math.Min(attributeMatches.Count, operatorMatches.Count), valueMatches.Count); i++)
                                {
                                    criteria.Add((attributeMatches[i].Groups[1].Value, operatorMatches[i].Groups[1].Value, valueMatches[i].Groups[1].Value));
                                }
                            }
                            // Look for common patterns in filter JSON (legacy)
                            else if (filterText.Contains("attribute") && filterText.Contains("operator") && filterText.Contains("value"))
                            {
                                // Extract attribute, operator, value patterns
                                var attributeMatch = System.Text.RegularExpressions.Regex.Match(prop.Value, @"""attribute"":\s*""([^""]+)""", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                                var operatorMatch = System.Text.RegularExpressions.Regex.Match(prop.Value, @"""operator"":\s*""([^""]+)""", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                                var valueMatch = System.Text.RegularExpressions.Regex.Match(prop.Value, @"""value"":\s*""([^""]+)""", System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                                if (attributeMatch.Success && operatorMatch.Success && valueMatch.Success)
                                {
                                    criteria.Add((attributeMatch.Groups[1].Value, operatorMatch.Groups[1].Value, valueMatch.Groups[1].Value));
                                }
                            }
                        }
                    }
                    catch { }
                }

                // Look for individual filter properties (legacy format)
                else if (prop.Key.Contains("filterAttribute") || prop.Key.Contains("filterField"))
                {
                    string attribute = prop.Value;
                    string operatorType = "equals"; // default
                    string value = "";

                    // Look for corresponding operator and value properties
                    string baseKey = prop.Key.Replace("filterAttribute", "").Replace("filterField", "");
                    if (snap.Properties.TryGetValue(baseKey + "filterOperator", out string op))
                        operatorType = op;
                    if (snap.Properties.TryGetValue(baseKey + "filterValue", out string val))
                        value = val;

                    criteria.Add((attribute, operatorType, value));
                }
            }

            return criteria;
        }

        /// <summary>
        /// Extracts output attributes from Dynamics 365 search snap properties
        /// </summary>
        private List<string> ExtractDynamics365OutputAttributes(SnapNode snap)
        {
            var attributes = new List<string>();

            // Look for the specific Dynamics 365 select_condition_prop structure
            foreach (var prop in snap.Properties)
            {
                // Look for extracted select properties from the new parsing logic
                if (prop.Key.Contains("settings.select_condition_prop.") && prop.Key.EndsWith(".attribute"))
                {
                    if (!string.IsNullOrEmpty(prop.Value))
                    {
                        // Only add clean attribute names, not JSON structures
                        string cleanValue = prop.Value.Trim();
                        if (!cleanValue.StartsWith("{") && !cleanValue.StartsWith("["))
                        {
                            attributes.Add(cleanValue);
                        }
                    }
                }
                // Look for legacy output attribute formats
                else if (prop.Key.ToLower().Contains("output") && prop.Key.ToLower().Contains("attribute"))
                {
                    // Parse output attributes
                    if (prop.Value.StartsWith("["))
                    {
                        // Parse JSON array of attributes
                        var matches = System.Text.RegularExpressions.Regex.Matches(prop.Value, @"""([^""]+)""");
                        foreach (System.Text.RegularExpressions.Match match in matches)
                        {
                            attributes.Add(match.Groups[1].Value);
                        }
                    }
                    else if (!string.IsNullOrEmpty(prop.Value))
                    {
                        attributes.Add(prop.Value);
                    }
                }
                // Look for select conditions in JSON format
                else if (prop.Key.ToLower().Contains("select") && (prop.Key.ToLower().Contains("condition") || prop.Key.ToLower().Contains("attribute")))
                {
                    try
                    {
                        if (prop.Value.StartsWith("[") || prop.Value.StartsWith("{"))
                        {
                            // Look for the specific D365 structure with select_prop
                            if (prop.Value.ToLower().Contains("select_prop"))
                            {
                                // Extract using regex for the select_prop structure - only get the value, not the whole JSON
                                var selectMatches = System.Text.RegularExpressions.Regex.Matches(prop.Value, @"""select_prop"":\s*{\s*""value"":\s*""([^""]+)""", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                                foreach (System.Text.RegularExpressions.Match match in selectMatches)
                                {
                                    string attributeName = match.Groups[1].Value;
                                    if (!string.IsNullOrEmpty(attributeName))
                                    {
                                        attributes.Add(attributeName);
                                    }
                                }
                            }
                        }
                    }
                    catch { }
                }
            }

            return attributes.Distinct().ToList();
        }

        /// <summary>
        /// Extracts order by criteria from Dynamics 365 search snap properties
        /// </summary>
        private List<string> ExtractDynamics365OrderBy(SnapNode snap)
        {
            var orderBy = new List<string>();

            foreach (var prop in snap.Properties)
            {
                if (prop.Key.ToLower().Contains("order") && prop.Key.ToLower().Contains("by"))
                {
                    if (!string.IsNullOrEmpty(prop.Value))
                    {
                        // Parse order by fields
                        if (prop.Value.Contains(","))
                        {
                            orderBy.AddRange(prop.Value.Split(',').Select(s => s.Trim()));
                        }
                        else
                        {
                            orderBy.Add(prop.Value);
                        }
                    }
                }
            }

            return orderBy;
        }
    }
}
