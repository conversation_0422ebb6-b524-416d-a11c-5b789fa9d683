using System;
using System.Collections.Generic;
using SnapAnalyser;

class TestConditionFix
{
    static void Main()
    {
        Console.WriteLine("======= TESTING CONDITION EXTRACTION FIX =======");
        
        // Create a test condition snap with expression_N pattern
        var conditionSnap = new SnapNode
        {
            Id = "test-condition",
            Label = "Test Condition Snap",
            Type = "com-snaplogic-snaps-transform-conditional",
            Properties = new Dictionary<string, string>
            {
                { "expression_0", "$blocktypes.blockId == null || $blocktypes.blockId == ''" },
                { "expression_1", "$blocktypes.parentBlock == null || $blocktypes.parentBlock == ''" },
                { "expression_2", "$blocktypes.blocktype == 'District'" },
                { "output_0", "Remove District" },
                { "output_1", "Collapse Parent" },
                { "output_2", "Keep District" },
                { "settings.evaluateAll.value", "False" }
            }
        };

        Console.WriteLine($"Testing snap: {conditionSnap.Label}");
        Console.WriteLine($"Type: {conditionSnap.Type}");
        Console.WriteLine($"Properties count: {conditionSnap.Properties.Count}");
        
        // Test the ExtractConditionExpressions method
        var conditions = SnapBestPractices.ExtractConditionExpressions(conditionSnap);
        
        Console.WriteLine($"\nExtracted {conditions.Count} condition expressions:");
        
        foreach (var condition in conditions)
        {
            Console.WriteLine($"- Description: {condition.Description}");
            Console.WriteLine($"  Expression: {condition.Expression}");
            Console.WriteLine($"  Return Value: {condition.ReturnValue ?? "null"}");
            Console.WriteLine($"  Target Path: {condition.TargetPath ?? "null"}");
            Console.WriteLine();
        }
        
        // Test the GetSnapBestPractices method
        Console.WriteLine("======= TESTING HTML GENERATION =======");
        
        var html = SnapBestPractices.GetSnapBestPractices(conditionSnap, new List<SnapNode>(), false);
        
        Console.WriteLine($"Generated HTML length: {html.Length}");
        if (html.Length > 0)
        {
            Console.WriteLine("Generated HTML:");
            Console.WriteLine(html);
        }
        else
        {
            Console.WriteLine("WARNING: No HTML generated!");
        }
        
        Console.WriteLine("\n======= TEST COMPLETED =======");
    }
}
