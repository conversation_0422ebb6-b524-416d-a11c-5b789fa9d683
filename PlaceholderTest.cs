using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Linq;

namespace SnapAnalyser.Test
{
    class PlaceholderTest
    {
        static void Main()
        {
            Console.WriteLine("=== Placeholder Handling Test ===\n");

            string testText = @"This SnapLogic snap performs data mapping, transforming input document fields. It maps the input field ##INLINECODEb20cdc1a## to ##INLINECODE85af0109## and ##INLINECODEe127afa3## to ##INLINECODE820e6bf0## in the output. Key settings include no SQL statement, no error retries (##INLINECODE68df6922##), and execution in ""Validate & Execute"" mode. ##INLINECODE01d14ea2## is disabled, so only mapped fields are output. The mapping root is set to ##INLINECODE5572589d##, processing each input document at the root level. Input is expected as a document stream; output is a transformed document containing only the mapped fields. Best practice: ensure input fields exist and consider enabling ##INLINECODEc0547d29## if original fields should be retained. Set ##INLINECODE4f9e7cd2## to ##INLINECODE30f92c1e## to halt on errors for data integrity.";

            Console.WriteLine("Original text with placeholders:");
            Console.WriteLine(testText);
            Console.WriteLine("\n" + new string('=', 80) + "\n");

            // Simulate the improved placeholder handling
            var processedText = ProcessPlaceholders(testText);
            
            Console.WriteLine("Processed text with intelligent fallbacks:");
            Console.WriteLine(processedText);
            Console.WriteLine("\n" + new string('=', 80) + "\n");

            // Show which placeholders were found
            var placeholders = ExtractPlaceholders(testText);
            Console.WriteLine($"Found {placeholders.Count} placeholders:");
            foreach (var placeholder in placeholders)
            {
                Console.WriteLine($"  - {placeholder}");
            }
        }

        static string ProcessPlaceholders(string text)
        {
            var codeBlocks = new Dictionary<string, string>(); // Empty - simulating no matches

            // Process ##INLINECODExxxxxxxx## format
            text = Regex.Replace(text, @"##INLINECODE([a-zA-Z0-9]+)##", match =>
            {
                string id = match.Groups[1].Value;
                string currentPlaceholder = match.Value;

                // Try exact match first
                if (codeBlocks.ContainsKey(currentPlaceholder))
                {
                    return $"<code>{codeBlocks[currentPlaceholder]}</code>";
                }

                // Try with underscore format
                var keyWithUnderscore = $"##INLINE_CODE_{id}##";
                if (codeBlocks.ContainsKey(keyWithUnderscore))
                {
                    return $"<code>{codeBlocks[keyWithUnderscore]}</code>";
                }

                // Try partial match
                var partialMatch = codeBlocks.Keys.FirstOrDefault(k => k.Contains(id));
                if (partialMatch != null)
                {
                    return $"<code>{codeBlocks[partialMatch]}</code>";
                }

                // Provide intelligent fallback
                var fallback = GetFallbackForOrphanedPlaceholder(id, text);
                return $"<code class=\"inferred-placeholder\">{fallback}</code>";
            });

            return text;
        }

        static string GetFallbackForOrphanedPlaceholder(string placeholderid, string contextText)
        {
            var context = contextText.ToLower();
            
            var contextMapping = new Dictionary<string, string[]>
            {
                ["field"] = new[] { "field_name", "input_field", "source_field", "target_field" },
                ["map"] = new[] { "field_mapping", "data_mapping", "field_transformation" },
                ["sql"] = new[] { "SQL_statement", "query", "SELECT_statement" },
                ["error"] = new[] { "error_handling", "retry_count", "error_action" },
                ["mode"] = new[] { "execution_mode", "processing_mode", "operation_mode" },
                ["validate"] = new[] { "validation_setting", "validation_mode" },
                ["execute"] = new[] { "execution_setting", "execute_mode" },
                ["root"] = new[] { "root_path", "document_root", "$" },
                ["document"] = new[] { "document", "input_document", "data_document" },
                ["output"] = new[] { "output_field", "target_field", "result_field" },
                ["input"] = new[] { "input_field", "source_field", "data_field" },
                ["original"] = new[] { "original_field", "passthrough", "preserve_original" },
                ["retain"] = new[] { "retain_fields", "keep_original", "passthrough" },
                ["halt"] = new[] { "halt_on_error", "stop_on_error", "error_handling" },
                ["integrity"] = new[] { "data_integrity", "validation", "quality_check" },
                ["disabled"] = new[] { "passthrough_disabled", "feature_disabled", "setting_off" },
                ["retries"] = new[] { "retry_count", "error_retries", "0" }
            };

            foreach (var mapping in contextMapping)
            {
                if (context.Contains(mapping.Key))
                {
                    return mapping.Value[0];
                }
            }

            if (context.Contains("field") || context.Contains("map"))
            {
                return "field_name";
            }

            if (context.Contains("setting") || context.Contains("config") || context.Contains("parameter"))
            {
                return "configuration_value";
            }

            return $"placeholder_{placeholderid}";
        }

        static List<string> ExtractPlaceholders(string text)
        {
            var placeholders = new List<string>();
            var matches = Regex.Matches(text, @"##INLINECODE([a-zA-Z0-9]+)##");
            
            foreach (Match match in matches)
            {
                placeholders.Add(match.Value);
            }
            
            return placeholders.Distinct().ToList();
        }
    }
}
