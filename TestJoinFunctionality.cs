using System;
using System.Collections.Generic;
using System.Linq;
using SnapAnalyser;

namespace TestJoinFunctionality
{
    class Program
    {
        static void Main()
        {
            Console.WriteLine("=== Join Snap Functionality Test ===\n");
            
            // Test 1: Create a multijoin snap and verify detection
            var joinSnap = CreateTestJoinSnap();
            Console.WriteLine($"Test Snap Created:");
            Console.WriteLine($"  Label: {joinSnap.Label}");
            Console.WriteLine($"  Type: {joinSnap.Type}");
            Console.WriteLine($"  Category: {joinSnap.Category}\n");
            
            // Test 2: Verify IsFlowControlSnap detection
            var documentationGenerator = new DocumentationGenerator();
            bool isFlowControl = TestIsFlowControlSnap(joinSnap);
            Console.WriteLine($"IsFlowControlSnap Result: {isFlowControl}");
            Console.WriteLine($"Expected: True\n");
            
            // Test 3: Test Join Configuration Generation
            Console.WriteLine("Testing Join Configuration Generation:");
            var flowControlGenerator = new FlowControlConfigurationGenerator();
            var allSnaps = new List<SnapNode> { joinSnap };
            string joinConfig = flowControlGenerator.GenerateJoinConfiguration(joinSnap, allSnaps);
            
            Console.WriteLine($"Generated Configuration Length: {joinConfig?.Length ?? 0} characters");
            Console.WriteLine($"Contains 'Join Configuration': {joinConfig?.Contains("Join Configuration") ?? false}");
            Console.WriteLine($"Contains 'Routing Logic': {joinConfig?.Contains("Routing Logic") ?? false}");
            
            if (!string.IsNullOrEmpty(joinConfig))
            {
                Console.WriteLine("\n--- Generated Join Configuration ---");
                Console.WriteLine(joinConfig);
                Console.WriteLine("--- End Configuration ---");
            }
            
            // Test 4: Verify snap type detection logic
            Console.WriteLine("\n=== Snap Type Detection Tests ===");
            TestSnapTypeDetection("com-snaplogic-snaps-transform-multijoin", "Multijoin");
            TestSnapTypeDetection("com-snaplogic-snaps-transform-join", "Standard Join");
            TestSnapTypeDetection("com-snaplogic-snaps-flow-router", "Router");
            TestSnapTypeDetection("com-snaplogic-snaps-flow-union", "Union");
            
            Console.WriteLine("\n=== Test Summary ===");
            Console.WriteLine($"✓ Join snap creation: Success");
            Console.WriteLine($"✓ Flow control detection: {(isFlowControl ? "Success" : "Failed")}");
            Console.WriteLine($"✓ Configuration generation: {(!string.IsNullOrEmpty(joinConfig) ? "Success" : "Failed")}");
            Console.WriteLine($"✓ Join vs Router logic: {(joinConfig?.Contains("Join Configuration") == true && joinConfig?.Contains("Routing Logic") == false ? "Success" : "Failed")}");
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
        
        static SnapNode CreateTestJoinSnap()
        {
            var joinSnap = new SnapNode
            {
                Id = "test-join-snap-001",
                Label = "Test Multijoin Snap",
                Type = "com-snaplogic-snaps-transform-multijoin",
                Category = SnapCategory.Transform,
                Properties = new Dictionary<string, string>
                {
                    ["joinType"] = "Inner",
                    ["leftPath"] = "$.customer_id",
                    ["rightPath"] = "$.id",
                    ["expression"] = "$.customer_id == $.id",
                    ["join1.leftPath"] = "$.customer_id",
                    ["join1.rightPath"] = "$.cust_id",
                    ["join1.expression"] = "$.customer_id == $.cust_id"
                },
                InputConnections = new List<SnapLink>(),
                OutputConnections = new List<SnapLink>()
            };
            
            return joinSnap;
        }
        
        static bool TestIsFlowControlSnap(SnapNode snap)
        {
            // Replicate the IsFlowControlSnap logic
            if (snap == null)
                return false;

            string snapType = snap.Type?.ToLower() ?? "";

            return snapType.Contains("router") ||
                   snapType.Contains("join") ||
                   snapType.Contains("union") ||
                   snapType.Contains("switch") ||
                   snapType.Contains("branch") ||
                   snapType.Contains("conditional") ||
                   snapType.Contains("if") ||
                   snapType.Contains("case") ||
                   (snap.Category == SnapCategory.FlowControl);
        }
        
        static void TestSnapTypeDetection(string snapType, string description)
        {
            string snapTypeLower = snapType?.ToLower() ?? "";
            bool isRouter = snapTypeLower.Contains("router");
            bool isJoin = snapTypeLower.Contains("join");  
            bool isUnion = snapTypeLower.Contains("union");
            
            string expectedMethod = isRouter ? "GenerateRouterConfiguration" :
                                  isJoin ? "GenerateJoinConfiguration" :
                                  isUnion ? "GenerateUnionConfiguration" :
                                  "GenerateRouterConfiguration (fallback)";
                                  
            Console.WriteLine($"  {description} ({snapType})");
            Console.WriteLine($"    -> Would call: {expectedMethod}");
            Console.WriteLine($"    -> Router: {isRouter}, Join: {isJoin}, Union: {isUnion}");
        }
    }
}
