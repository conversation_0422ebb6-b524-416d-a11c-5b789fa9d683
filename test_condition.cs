using System;
using System.Collections.Generic;

namespace SnapAnalyser
{
    public class TestCondition
    {
        public static void Main()
        {
            // Create a test snap node that simulates a condition snap
            var conditionSnap = new SnapNode
            {
                Id = "test-condition-snap",
                Label = "Test Condition Snap",
                Type = "com-snaplogic-snaps-transform-conditional",
                Properties = new Dictionary<string, string>
                {
                    { "expression_0", "$value > 10" },
                    { "condition", "$field == 'test'" },
                    { "filterConfig", "{\"expression\": \"$data.field != null\", \"type\": \"complex\"}" },
                    { "output_0", "true" },
                    { "output_1", "false" }
                }
            };
            
            // Create an instance of SnapBestPractices and test the condition handling
            var bestPractices = new SnapBestPractices();
            var details = bestPractices.GenerateSnapDetails(conditionSnap);
            
            // Print the result to verify if condition details are extracted correctly
            Console.WriteLine("====== TEST CONDITION SNAP DETAILS ======");
            Console.WriteLine(details);
            Console.WriteLine("========================================");
        }
    }
}
