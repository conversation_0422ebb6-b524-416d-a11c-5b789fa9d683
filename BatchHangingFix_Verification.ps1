# BatchHangingFix_Verification.ps1
# Comprehensive verification script for the batch processing hanging fix

Write-Host "=== BATCH PROCESSING HANGING FIX VERIFICATION ===" -ForegroundColor Green
Write-Host "Testing all critical components for hanging issues..." -ForegroundColor Yellow
Write-Host "Started at: $(Get-Date)" -ForegroundColor Cyan
Write-Host ""

$testResults = @()

# Test 1: Verify compilation
Write-Host "🔧 Test 1: Verifying compilation..." -ForegroundColor Cyan
try {
    $buildResult = dotnet build SnapAnalyzer.csproj --no-restore 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✅ Compilation successful" -ForegroundColor Green
        $testResults += "✅ Compilation: PASSED"
    } else {
        Write-Host "  ❌ Compilation failed" -ForegroundColor Red
        Write-Host "  Output: $buildResult" -ForegroundColor Red
        $testResults += "❌ Compilation: FAILED"
    }
} catch {
    Write-Host "  ❌ Compilation test error: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "❌ Compilation: ERROR"
}

# Test 2: Verify DescriptionCache fix
Write-Host "`n🔒 Test 2: Verifying DescriptionCache deadlock fix..." -ForegroundColor Cyan
try {
    if (Test-Path "DescriptionCache.cs") {
        $cacheContent = Get-Content "DescriptionCache.cs" -Raw
        
        # Check for problematic patterns
        $hasConfigureAwaitFalse = $cacheContent -match "ConfigureAwait\(false\)"
        $hasBlockingPattern = $cacheContent -match "\.GetAwaiter\(\)\.GetResult\(\)" -and $cacheContent -notmatch "Task\.Run"
        
        if ($hasConfigureAwaitFalse -and -not $hasBlockingPattern) {
            Write-Host "  ✅ DescriptionCache uses proper async patterns" -ForegroundColor Green
            $testResults += "✅ DescriptionCache: FIXED"
        } else {
            Write-Host "  ⚠️  DescriptionCache may still have deadlock risks" -ForegroundColor Yellow
            $testResults += "⚠️  DescriptionCache: NEEDS REVIEW"
        }
    } else {
        Write-Host "  ❌ DescriptionCache.cs not found" -ForegroundColor Red
        $testResults += "❌ DescriptionCache: NOT FOUND"
    }
} catch {
    Write-Host "  ❌ DescriptionCache test error: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "❌ DescriptionCache: ERROR"
}

# Test 3: Verify AIDescriptionGenerator fixes
Write-Host "`n🤖 Test 3: Verifying AIDescriptionGenerator ConfigureAwait fixes..." -ForegroundColor Cyan
try {
    if (Test-Path "AIDescriptionGenerator.cs") {
        $aiContent = Get-Content "AIDescriptionGenerator.cs" -Raw
        
        # Count ConfigureAwait(false) instances
        $configureAwaitCount = ($aiContent | Select-String "ConfigureAwait\(false\)" -AllMatches).Matches.Count
        $sendPromptMatches = ($aiContent | Select-String "await SendPromptToAzureAI.*ConfigureAwait\(false\)" -AllMatches).Matches.Count
        $httpClientMatches = ($aiContent | Select-String "await _httpClient\.SendAsync.*ConfigureAwait\(false\)" -AllMatches).Matches.Count
        
        Write-Host "  📊 ConfigureAwait(false) instances: $configureAwaitCount" -ForegroundColor White
        Write-Host "  📊 Fixed SendPromptToAzureAI calls: $sendPromptMatches" -ForegroundColor White
        Write-Host "  📊 Fixed HttpClient calls: $httpClientMatches" -ForegroundColor White
        
        if ($configureAwaitCount -ge 5) {
            Write-Host "  ✅ AIDescriptionGenerator has ConfigureAwait fixes" -ForegroundColor Green
            $testResults += "✅ AIDescriptionGenerator: FIXED"
        } else {
            Write-Host "  ⚠️  AIDescriptionGenerator may need more ConfigureAwait fixes" -ForegroundColor Yellow
            $testResults += "⚠️  AIDescriptionGenerator: PARTIAL"
        }
    } else {
        Write-Host "  ❌ AIDescriptionGenerator.cs not found" -ForegroundColor Red
        $testResults += "❌ AIDescriptionGenerator: NOT FOUND"
    }
} catch {
    Write-Host "  ❌ AIDescriptionGenerator test error: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "❌ AIDescriptionGenerator: ERROR"
}

# Test 4: Verify DocumentationGenerator fixes
Write-Host "`n📄 Test 4: Verifying DocumentationGenerator ConfigureAwait fixes..." -ForegroundColor Cyan
try {
    if (Test-Path "DocumentationGenerator.cs") {
        $docContent = Get-Content "DocumentationGenerator.cs" -Raw
        
        $configureAwaitCount = ($docContent | Select-String "ConfigureAwait\(false\)" -AllMatches).Matches.Count
        $addSnapCategoryMatches = ($docContent | Select-String "await AddSnapCategorySection.*ConfigureAwait\(false\)" -AllMatches).Matches.Count
        
        Write-Host "  📊 ConfigureAwait(false) instances: $configureAwaitCount" -ForegroundColor White
        Write-Host "  📊 Fixed AddSnapCategorySection calls: $addSnapCategoryMatches" -ForegroundColor White
        
        if ($configureAwaitCount -ge 8) {
            Write-Host "  ✅ DocumentationGenerator has ConfigureAwait fixes" -ForegroundColor Green
            $testResults += "✅ DocumentationGenerator: FIXED"
        } else {
            Write-Host "  ⚠️  DocumentationGenerator may need more ConfigureAwait fixes" -ForegroundColor Yellow
            $testResults += "⚠️  DocumentationGenerator: PARTIAL"
        }
    } else {
        Write-Host "  ❌ DocumentationGenerator.cs not found" -ForegroundColor Red
        $testResults += "❌ DocumentationGenerator: NOT FOUND"
    }
} catch {
    Write-Host "  ❌ DocumentationGenerator test error: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "❌ DocumentationGenerator: ERROR"
}

# Test 5: Verify configuration optimization
Write-Host "`n⚙️  Test 5: Verifying configuration optimization..." -ForegroundColor Cyan
try {
    if (Test-Path "config.json") {
        $config = Get-Content "config.json" | ConvertFrom-Json
        $timeout = $config.AzureOpenAITimeoutSeconds
        
        Write-Host "  📊 Current Azure OpenAI timeout: $timeout seconds" -ForegroundColor White
        
        if ($timeout -ge 90) {
            Write-Host "  ✅ Timeout is optimized for batch processing" -ForegroundColor Green
            $testResults += "✅ Configuration: OPTIMIZED"
        } elseif ($timeout -ge 60) {
            Write-Host "  ⚠️  Timeout is adequate but could be higher for complex operations" -ForegroundColor Yellow
            $testResults += "⚠️  Configuration: ADEQUATE"
        } else {
            Write-Host "  ❌ Timeout may be too short for reliable batch processing" -ForegroundColor Red
            $testResults += "❌ Configuration: TOO SHORT"
        }
    } else {
        Write-Host "  ❌ config.json not found" -ForegroundColor Red
        $testResults += "❌ Configuration: NOT FOUND"
    }
} catch {
    Write-Host "  ❌ Configuration test error: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "❌ Configuration: ERROR"
}

# Test 6: Check for backup files
Write-Host "`n💾 Test 6: Verifying backup files exist..." -ForegroundColor Cyan
$backupFiles = @("DescriptionCache.cs.backup", "BATCH_HANGING_FIX_SUMMARY.md")
$backupsExist = 0

foreach ($backup in $backupFiles) {
    if (Test-Path $backup) {
        Write-Host "  ✅ Found backup: $backup" -ForegroundColor Green
        $backupsExist++
    } else {
        Write-Host "  ⚠️  Missing backup: $backup" -ForegroundColor Yellow
    }
}

if ($backupsExist -gt 0) {
    $testResults += "✅ Backups: AVAILABLE"
} else {
    $testResults += "⚠️  Backups: NONE"
}

# Summary
Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "VERIFICATION SUMMARY" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green

foreach ($result in $testResults) {
    Write-Host $result
}

$passedTests = ($testResults | Where-Object { $_ -match "✅" }).Count
$totalTests = $testResults.Count

Write-Host "`n🎯 OVERALL RESULT: $passedTests/$totalTests tests passed" -ForegroundColor Cyan

if ($passedTests -eq $totalTests) {
    Write-Host "🎉 ALL TESTS PASSED - Batch hanging fix is ready!" -ForegroundColor Green
} elseif ($passedTests -ge ($totalTests * 0.8)) {
    Write-Host "✅ MOSTLY READY - Minor issues may exist" -ForegroundColor Yellow
} else {
    Write-Host "⚠️  NEEDS ATTENTION - Several issues found" -ForegroundColor Red
}

Write-Host "`nCompleted at: $(Get-Date)" -ForegroundColor Cyan

# Recommendations
Write-Host "`n📋 NEXT STEPS:" -ForegroundColor Cyan
Write-Host "  1. Test batch processing with multiple .slp files" -ForegroundColor White
Write-Host "  2. Monitor for hanging during AI-enabled processing" -ForegroundColor White
Write-Host "  3. Check ai_processing_log files for performance metrics" -ForegroundColor White
Write-Host "  4. Verify cancellation works properly with Ctrl+C or timeout" -ForegroundColor White
