using System;
using System.Collections.Generic;

namespace SnapAnalyser
{
    class TestCytoscapeJs
    {
        public static void RunCytoscapeTest()
        {
            Console.WriteLine("Testing Cytoscape.js Implementation");
            Console.WriteLine("===================================");
            Console.WriteLine();

            try
            {
                // Test 1: Basic CytoscapeJsGenerator functionality
                Console.WriteLine("1. Testing CytoscapeJsGenerator:");
                TestCytoscapeJsGenerator();

                Console.WriteLine();

                // Test 2: Integration with DiagramGenerator
                Console.WriteLine("2. Testing DiagramGenerator integration:");
                TestDiagramGenerator();

                Console.WriteLine();

                // Test 3: FlowControlDiagramGenerator
                Console.WriteLine("3. Testing FlowControlDiagramGenerator:");
                TestFlowControlDiagramGenerator();

                Console.WriteLine();
                Console.WriteLine("✅ All Cytoscape.js tests PASSED");
            }
            catch (Exception ex)
            {
                Console.WriteLine("❌ ERROR: " + ex.Message);
                Console.WriteLine(ex.StackTrace);
            }
        }

        static void TestCytoscapeJsGenerator()
        {
            var generator = new CytoscapeJsGenerator();
            
            // Create a simple test pipeline
            var pipeline = CreateTestPipeline();

            // Test elements generation
            string elements = generator.GenerateCytoscapeElements(pipeline);
            Console.WriteLine($"   Elements generated: {!string.IsNullOrEmpty(elements)}");
            
            if (!string.IsNullOrEmpty(elements))
            {
                Console.WriteLine($"   Elements length: {elements.Length} characters");
                Console.WriteLine($"   Contains JSON structure: {elements.Contains("[") && elements.Contains("]")}");
                Console.WriteLine($"   Contains node data: {elements.Contains("\"data\"") && elements.Contains("\"id\"")}");
            }

            // Test HTML generation
            string html = generator.GenerateCytoscapeHtml(elements, "test-diagram", "Test Diagram");
            Console.WriteLine($"   HTML generated: {!string.IsNullOrEmpty(html)}");
            
            if (!string.IsNullOrEmpty(html))
            {
                Console.WriteLine($"   HTML length: {html.Length} characters");
                Console.WriteLine($"   Contains cytoscape.js references: {html.Contains("cytoscape")}");
                Console.WriteLine($"   Contains container div: {html.Contains("<div id=\"test-diagram\">")}");
                Console.WriteLine($"   Contains initialization script: {html.Contains("cytoscape({")}");
            }

            Console.WriteLine("   ✅ CytoscapeJsGenerator basic functionality: PASSED");
        }

        static void TestDiagramGenerator()
        {
            var diagramGenerator = new DiagramGenerator();
            var pipeline = CreateTestPipeline();

            string diagram = diagramGenerator.GenerateDiagram(pipeline);
            
            Console.WriteLine($"   Diagram generated: {!string.IsNullOrEmpty(diagram)}");
            
            if (!string.IsNullOrEmpty(diagram))
            {
                Console.WriteLine($"   Diagram length: {diagram.Length} characters");
                Console.WriteLine($"   Contains cytoscape.js syntax: {diagram.Contains("cytoscape")}");
                Console.WriteLine($"   Contains HTML structure: {diagram.Contains("<div") && diagram.Contains("</div>")}");
                Console.WriteLine($"   Contains JSON elements: {diagram.Contains("\"data\"") && diagram.Contains("\"id\"")}");
            }

            Console.WriteLine("   ✅ DiagramGenerator integration: PASSED");
        }

        static void TestFlowControlDiagramGenerator()
        {
            var flowControlGen = new FlowControlDiagramGenerator();
            
            // Create test router snap
            var routerSnap = new SnapNode
            {
                Id = "router-test",
                Label = "Test Router",
                Type = "com-snaplogic-snaps-flow-router",
                Category = SnapCategory.FlowControl,
                Properties = new Dictionary<string, string>
                {
                    ["routes"] = "[{\"condition\":\"$count > 10\",\"outputPath\":\"high\"},{\"condition\":\"$count <= 10\",\"outputPath\":\"low\"}]"
                }
            };

            // Create connected snaps
            var allSnaps = new List<SnapNode>
            {
                routerSnap,
                new SnapNode { Id = "input-snap", Label = "Input", Type = "input", Category = SnapCategory.ExternalSystem, IsStartPoint = true },
                new SnapNode { Id = "output-high", Label = "High Output", Type = "output", Category = SnapCategory.ExternalSystem, IsEndPoint = true },
                new SnapNode { Id = "output-low", Label = "Low Output", Type = "output", Category = SnapCategory.ExternalSystem, IsEndPoint = true }
            };

            string routerDiagram = flowControlGen.GenerateRouterFlowDiagram(routerSnap, allSnaps);
            
            Console.WriteLine($"   Router diagram generated: {!string.IsNullOrEmpty(routerDiagram)}");
            
            if (!string.IsNullOrEmpty(routerDiagram))
            {
                Console.WriteLine($"   Router diagram length: {routerDiagram.Length} characters");
                Console.WriteLine($"   Contains cytoscape.js syntax: {routerDiagram.Contains("cytoscape")}");
                Console.WriteLine($"   Contains HTML structure: {routerDiagram.Contains("<div") && routerDiagram.Contains("</div>")}");
                Console.WriteLine($"   Contains JSON elements: {routerDiagram.Contains("\"data\"") && routerDiagram.Contains("\"id\"")}");
            }

            Console.WriteLine("   ✅ FlowControlDiagramGenerator: PASSED");
        }

        static PipelineData CreateTestPipeline()
        {
            var snaps = new List<SnapNode>
            {
                new SnapNode
                {
                    Id = "snap1",
                    Label = "Input Reader",
                    Type = "com-snaplogic-snaps-read",
                    Category = SnapCategory.ExternalSystem,
                    Position = new Position { X = 0, Y = 0 }
                },
                new SnapNode
                {
                    Id = "snap2",
                    Label = "Data Mapper",
                    Type = "com-snaplogic-snaps-transform-datatransform",
                    Category = SnapCategory.Transformation,
                    Position = new Position { X = 1, Y = 0 }
                },
                new SnapNode
                {
                    Id = "snap3",
                    Label = "Output Writer",
                    Type = "com-snaplogic-snaps-write",
                    Category = SnapCategory.ExternalSystem,
                    Position = new Position { X = 2, Y = 0 }
                }
            };

            var links = new List<SnapLink>
            {
                new SnapLink { SourceId = "snap1", TargetId = "snap2", SourceViewId = "output0", TargetViewId = "input0" },
                new SnapLink { SourceId = "snap2", TargetId = "snap3", SourceViewId = "output0", TargetViewId = "input0" }
            };

            return new PipelineData
            {
                Name = "Test Pipeline",
                Snaps = snaps,
                Links = links
            };
        }
    }
}
