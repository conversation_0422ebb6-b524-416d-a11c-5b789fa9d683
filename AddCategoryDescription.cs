using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SnapAnalyser
{
    public partial class DocumentationGenerator
    {
        // Add advanced path analysis for execution paths
        private string AnalyzeExecutionPath(List<SnapNode> path)
        {
            if (path == null || path.Count < 2)
                return "Path is too short for meaningful analysis.";
                
            StringBuilder analysis = new StringBuilder();
            
            // Identify key characteristics of this path
            var snapsByCategory = path.GroupBy(n => n.Category)
                .ToDictionary(g => g.Key, g => g.Count());
                
            // Identify the pattern of operations
            analysis.AppendLine("<strong>Path Architecture Analysis:</strong>");
            
            // Pattern detection
            if (path.Any(n => n.Category == SnapCategory.Database && n.Type.Contains("select")) &&
                path.Any(n => n.Category == SnapCategory.ExternalSystem) &&
                path.Any(n => n.Category == SnapCategory.Transformation))
            {
                analysis.AppendLine("<br/>This path implements a <strong>System Integration Pattern</strong> that extracts data " +
                                   "from a database, transforms it, and sends it to an external system.");
            }
            else if (path.Any(n => n.Category == SnapCategory.ExternalSystem && path.IndexOf(path.First(n => n.Category == SnapCategory.ExternalSystem)) < path.Count / 2) &&
                     path.Any(n => n.Category == SnapCategory.Database && n.Type.Contains("insert") && path.IndexOf(path.First(n => n.Category == SnapCategory.Database)) > path.Count / 2))
            {
                analysis.AppendLine("<br/>This path implements an <strong>API-to-Database Integration Pattern</strong> that " +
                                   "consumes data from an external API and stores it in a database.");
            }
            else if (path.Any(n => n.Category == SnapCategory.FileOperation && n.Type.Contains("read")) &&
                     path.Any(n => n.Category == SnapCategory.Database && 
                              (n.Type.Contains("insert") || n.Type.Contains("update"))))
            {
                analysis.AppendLine("<br/>This path implements a <strong>File Import Pattern</strong> that reads " +
                                   "data from files and loads it into database tables.");
            }
            else if (snapsByCategory.ContainsKey(SnapCategory.Transformation) && 
                     snapsByCategory[SnapCategory.Transformation] >= Math.Max(1, path.Count / 3))
            {
                analysis.AppendLine("<br/>This path implements a <strong>Complex Transformation Pattern</strong> with " +
                                   "multiple transformation steps to reshape and enrich data.");
            }
            else if (path.Any(n => n.Category == SnapCategory.FlowControl && n.Type.Contains("router")))
            {
                analysis.AppendLine("<br/>This path implements a <strong>Conditional Processing Pattern</strong> that " +
                                   "routes documents through different processing branches based on content.");
            }
            
            // Data complexity analysis
            var transformationSnaps = path.Where(n => n.Category == SnapCategory.Transformation).ToList();
            int transformationComplexity = transformationSnaps.Count;
            
            if (transformationComplexity > 3)
            {
                analysis.AppendLine("<br/><strong>Data Transformation Complexity:</strong> High - This path contains multiple " +
                                   "transformation steps suggesting complex data mapping or business logic.");
            }
            else if (transformationComplexity > 0)
            {
                analysis.AppendLine("<br/><strong>Data Transformation Complexity:</strong> Medium - This path contains standard " +
                                   "data transformations for field mapping and formatting.");
            }
            else
            {
                analysis.AppendLine("<br/><strong>Data Transformation Complexity:</strong> Low - This path moves data with " +
                                   "minimal transformation, focusing on data movement rather than restructuring.");
            }
            
            // Performance considerations
            if (path.Any(n => n.Type.Contains("batch")))
            {
                analysis.AppendLine("<br/><strong>Performance Optimization:</strong> Uses batch processing for improved throughput.");
            }
            
            if (path.Any(n => n.Category == SnapCategory.FlowControl && n.Type.Contains("join")))
            {
                analysis.AppendLine("<br/><strong>Performance Consideration:</strong> Contains join operations that may require " +
                                   "memory for caching data and could be a performance bottleneck with large datasets.");
            }
            
            // Error handling assessment
            if (path.Any(n => n.Category == SnapCategory.ErrorHandling))
            {
                analysis.AppendLine("<br/><strong>Error Handling:</strong> This path includes explicit error handling mechanisms " +
                                   "for improved robustness and fault tolerance.");
            }
            else
            {
                analysis.AppendLine("<br/><strong>Error Handling:</strong> No explicit error handling detected in this path. " +
                                   "Consider adding error handling for improved robustness.");
            }
            
            return analysis.ToString();
        }
        private void AddSnapCategoryDocumentation(StringBuilder html, string anchorId)
        {
            if (anchorId == "flow-control")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>Flow control snaps manage how documents move through the pipeline by " +
                               "routing, filtering, joining, or splitting document streams.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps control the path and flow of documents within the pipeline. They " +
                               "can create conditional branches (Router), merge multiple streams (Union), create copies of documents (Copy), filter documents " +
                               "based on criteria, sort them, or join related documents from different sources.</p>");
                html.AppendLine("          <p><strong>Common Configurations:</strong></p>");
                html.AppendLine("          <ul>");
                html.AppendLine("            <li><strong>Router</strong>: Configured with expressions that evaluate to true/false to determine which output path to follow</li>");
                html.AppendLine("            <li><strong>Join</strong>: Requires keys from both streams to match on, similar to SQL JOIN operations</li>");
                html.AppendLine("            <li><strong>Sort</strong>: Requires field names and sort direction (ascending/descending)</li>");
                html.AppendLine("            <li><strong>Filter</strong>: Uses expressions to determine which documents to pass forward</li>");
                html.AppendLine("          </ul>");
                html.AppendLine("        </div>");
            }
            else if (anchorId == "transformations")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>Transformation snaps manipulate the structure and content of documents by " +
                               "mapping fields, performing calculations, applying business logic, and reformatting data.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps modify document content or structure without changing the " +
                               "flow path. They can map fields from one schema to another, apply formulas and functions to field values, " +
                               "restructure documents, or convert between different data formats (JSON, XML, etc.)</p>");
                html.AppendLine("          <p><strong>Common Configurations:</strong></p>");
                html.AppendLine("          <ul>");
                html.AppendLine("            <li><strong>Mapper</strong>: Contains field-by-field mappings using expressions</li>");
                html.AppendLine("            <li><strong>JSON/XML Generator</strong>: Configured with templates for output format</li>");
                html.AppendLine("            <li><strong>JSON/XML Parser</strong>: Can be configured with paths to extract specific elements</li>");
                html.AppendLine("            <li><strong>Script</strong>: Contains custom JavaScript, Python or other code to transform data</li>");
                html.AppendLine("          </ul>");
                html.AppendLine("        </div>");
            }
            else if (anchorId == "database")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>Database snaps interact with SQL databases to read, write, or modify data. " +
                               "They handle SQL generation, transaction management, and result set processing.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps communicate with relational database systems using JDBC/ODBC " +
                               "connections. They translate between SnapLogic documents and database records, executing SQL statements for various " +
                               "operations like SELECT, INSERT, UPDATE, DELETE, or custom SQL.</p>");
                html.AppendLine("          <p><strong>Common Configurations:</strong></p>");
                html.AppendLine("          <ul>");
                html.AppendLine("            <li><strong>SELECT</strong>: Configured with table name, columns to retrieve, and WHERE conditions</li>");
                html.AppendLine("            <li><strong>INSERT</strong>: Requires table name and field-to-column mappings</li>");
                html.AppendLine("            <li><strong>UPDATE</strong>: Needs table name, fields to update, and key fields to identify records</li>");
                html.AppendLine("            <li><strong>SQL Execute</strong>: Takes raw SQL statements or stored procedure calls</li>");
                html.AppendLine("          </ul>");
                html.AppendLine("        </div>");
            }
            else if (anchorId == "external")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>External system snaps connect to third-party systems, APIs, or services " +
                               "to exchange data with systems outside the SnapLogic platform.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps manage authentication, connection parameters, " +
                               "and data exchange protocols with external systems. They handle credentials securely, format requests " +
                               "according to the target system's requirements, and parse responses back into SnapLogic documents.</p>");
                html.AppendLine("          <p><strong>Common Configurations:</strong></p>");
                html.AppendLine("          <ul>");
                html.AppendLine("            <li><strong>REST/HTTP</strong>: Configured with endpoint URLs, HTTP methods, headers, and authentication</li>");
                html.AppendLine("            <li><strong>Dynamics 365</strong>: Requires entity names, operation types, and field mappings</li>");
                html.AppendLine("            <li><strong>Salesforce</strong>: Uses object names, SOQL queries, or field mappings depending on operation</li>");
                html.AppendLine("            <li><strong>SOAP</strong>: Configured with WSDL information and XML message formats</li>");
                html.AppendLine("          </ul>");
                html.AppendLine("        </div>");
            }
            else if (anchorId == "file")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>File operation snaps read from or write to files in various formats, " +
                               "handling data import/export needs for the pipeline.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps interact with file systems " +
                               "(local or cloud storage) to read, write, list, or manage files. They handle file format parsing " +
                               "and generation (CSV, Excel, text, binary, etc.), file chunking for large files, and file metadata.</p>");
                html.AppendLine("          <p><strong>Common Configurations:</strong></p>");
                html.AppendLine("          <ul>");
                html.AppendLine("            <li><strong>File Reader</strong>: Configured with file path/pattern, format settings, and parsing options</li>");
                html.AppendLine("            <li><strong>File Writer</strong>: Requires destination path, file naming patterns, and format settings</li>");
                html.AppendLine("            <li><strong>CSV Parser/Generator</strong>: Configured with delimiter settings, header options, and encoding</li>");
                html.AppendLine("            <li><strong>Excel Read/Write</strong>: Requires worksheet names, cell ranges, and format options</li>");
                html.AppendLine("          </ul>");
                html.AppendLine("        </div>");
            }
            else if (anchorId == "error")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>Error handling snaps provide mechanisms to catch, log, and respond to errors " +
                               "that occur during pipeline execution, improving pipeline robustness.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps implement error management strategies " +
                               "including catching exceptions, logging error details, implementing retries, sending notifications, " +
                               "or providing fallback processing paths. They help create resilient pipelines that can recover from failures.</p>");
                html.AppendLine("          <p><strong>Common Configurations:</strong></p>");
                html.AppendLine("          <ul>");
                html.AppendLine("            <li><strong>Error Handler</strong>: Configured with error types to catch and recovery actions</li>");
                html.AppendLine("            <li><strong>Retry</strong>: Contains retry count, delay between attempts, and conditions for retrying</li>");
                html.AppendLine("            <li><strong>Error Log</strong>: Configured with logging level, destination, and format</li>");
                html.AppendLine("            <li><strong>Alert</strong>: Contains notification settings like email recipients or webhook endpoints</li>");
                html.AppendLine("          </ul>");
                html.AppendLine("        </div>");
            }
            else if (anchorId == "other")
            {
                html.AppendLine("        <div class=\"category-description\">");
                html.AppendLine("          <p>This category includes utility snaps and specialized components that don't fit into the other categories.</p>");
                html.AppendLine("          <p><strong>Technical Function:</strong> These snaps may provide utility functions, custom logic, specialized " +
                               "processing, or integration with unique systems that don't fit cleanly into the standard categories.</p>");
                html.AppendLine("        </div>");
            }
        }
    }
}
