using System;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using SnapAnalyser;

class CheckDeployments
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== Azure OpenAI Deployment Checker ===");
        Console.WriteLine($"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        Console.WriteLine();
        
        // Load configuration
        string apiKey = ConfigManager.OpenAIApiKey;
        string endpoint = ConfigManager.AzureOpenAIEndpoint;
        string currentDeployment = ConfigManager.AzureOpenAIDeploymentName;
        
        Console.WriteLine("Current Configuration:");
        Console.WriteLine($"  Endpoint: {endpoint}");
        Console.WriteLine($"  Current Deployment Name: {currentDeployment}");
        Console.WriteLine($"  API Key: {(string.IsNullOrEmpty(apiKey) ? "MISSING" : "Present")}");
        Console.WriteLine();
        
        if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(endpoint))
        {
            Console.WriteLine("❌ Cannot check deployments - missing API key or endpoint");
            return;
        }
        
        await ListAvailableDeployments(apiKey, endpoint);
    }
    
    static async Task ListAvailableDeployments(string apiKey, string endpoint)
    {
        try
        {
            Console.WriteLine("🔍 Checking available deployments...");
            
            // Ensure endpoint is properly formatted
            if (!endpoint.EndsWith("/"))
                endpoint += "/";
                
            var listUrl = $"{endpoint}openai/deployments?api-version=2023-05-15";
            Console.WriteLine($"Requesting: {listUrl}");
            Console.WriteLine();
            
            using var client = new HttpClient();
            client.DefaultRequestHeaders.Add("api-key", apiKey);
            client.Timeout = TimeSpan.FromSeconds(30);
            
            var response = await client.GetAsync(listUrl);
            var responseBody = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                Console.WriteLine("✅ Successfully retrieved deployment list!");
                Console.WriteLine();
                
                try
                {
                    using var document = JsonDocument.Parse(responseBody);
                    
                    if (document.RootElement.TryGetProperty("data", out JsonElement data))
                    {
                        Console.WriteLine("Available Deployments:");
                        Console.WriteLine("─────────────────────");
                        
                        bool hasDeployments = false;
                        foreach (var deployment in data.EnumerateArray())
                        {
                            hasDeployments = true;
                            
                            string id = deployment.TryGetProperty("id", out JsonElement idElement) ? 
                                idElement.GetString() : "Unknown";
                            string model = deployment.TryGetProperty("model", out JsonElement modelElement) ? 
                                modelElement.GetString() : "Unknown";
                            string status = deployment.TryGetProperty("status", out JsonElement statusElement) ? 
                                statusElement.GetString() : "Unknown";
                                
                            Console.WriteLine($"• Deployment Name: {id}");
                            Console.WriteLine($"  Model: {model}");
                            Console.WriteLine($"  Status: {status}");
                            Console.WriteLine();
                        }
                        
                        if (!hasDeployments)
                        {
                            Console.WriteLine("❌ No deployments found in your Azure OpenAI resource!");
                            Console.WriteLine("You need to create a deployment first in the Azure portal.");
                        }
                    }
                    else
                    {
                        Console.WriteLine("❌ No 'data' property found in response");
                        Console.WriteLine($"Raw response: {responseBody}");
                    }
                }
                catch (JsonException ex)
                {
                    Console.WriteLine($"❌ Error parsing JSON response: {ex.Message}");
                    Console.WriteLine($"Raw response: {responseBody}");
                }
            }
            else
            {
                Console.WriteLine($"❌ Failed to get deployments: {response.StatusCode}");
                Console.WriteLine($"Response: {responseBody}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
        }
        
        Console.WriteLine();
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}
