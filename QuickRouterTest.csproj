<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0-windows7.0</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Azure.AI.OpenAI" Version="1.0.0-beta.14" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="SlpAnalyzer.cs" />
    <Compile Include="FlowControlConfigurationGenerator.cs" />
    <Compile Include="ProjectData.cs" />
    <Compile Include="ConfigManager.cs" />
    <Compile Include="QuickRouterTest.cs" />
  </ItemGroup>

</Project>
