using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;

namespace SnapAnalyser
{
    public class ProjectData
    {
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string Purpose { get; set; } = "";
        public List<string> SlpFiles { get; set; } = new List<string>();
        public string OutputFolder { get; set; } = "";
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;
        
        // Project settings
        public bool GenerateHtml { get; set; } = true;
        public bool GeneratePdf { get; set; } = false;
        public bool UseAI { get; set; } = false;
        public bool UseCachedDescriptions { get; set; } = true;

        public ProjectData()
        {
        }

        public ProjectData(string name, string description, string purpose)
        {
            Name = name;
            Description = description;
            Purpose = purpose;
        }

        /// <summary>
        /// Gets the parent pipelines (those starting with 'P')
        /// </summary>
        public List<string> GetParentPipelines()
        {
            var parentPipelines = new List<string>();
            foreach (var file in SlpFiles)
            {
                var fileName = Path.GetFileNameWithoutExtension(file);
                if (fileName.StartsWith("P", StringComparison.OrdinalIgnoreCase))
                {
                    parentPipelines.Add(file);
                }
            }
            return parentPipelines;
        }

        /// <summary>
        /// Gets the child pipelines (those starting with 'CH')
        /// </summary>
        public List<string> GetChildPipelines()
        {
            var childPipelines = new List<string>();
            foreach (var file in SlpFiles)
            {
                var fileName = Path.GetFileNameWithoutExtension(file);
                if (fileName.StartsWith("CH", StringComparison.OrdinalIgnoreCase))
                {
                    childPipelines.Add(file);
                }
            }
            return childPipelines;
        }

        /// <summary>
        /// Gets all other pipelines that don't match parent or child conventions
        /// </summary>
        public List<string> GetOtherPipelines()
        {
            var otherPipelines = new List<string>();
            foreach (var file in SlpFiles)
            {
                var fileName = Path.GetFileNameWithoutExtension(file);
                if (!fileName.StartsWith("P", StringComparison.OrdinalIgnoreCase) && 
                    !fileName.StartsWith("CH", StringComparison.OrdinalIgnoreCase))
                {
                    otherPipelines.Add(file);
                }
            }
            return otherPipelines;
        }

        /// <summary>
        /// Determines if a pipeline is a parent pipeline
        /// </summary>
        public bool IsParentPipeline(string filePath)
        {
            var fileName = Path.GetFileNameWithoutExtension(filePath);
            return fileName.StartsWith("P", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Determines if a pipeline is a child pipeline
        /// </summary>
        public bool IsChildPipeline(string filePath)
        {
            var fileName = Path.GetFileNameWithoutExtension(filePath);
            return fileName.StartsWith("CH", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Gets context information for AI generation
        /// </summary>
        public string GetProjectContext()
        {
            var context = $"Project: {Name}\n";
            context += $"Description: {Description}\n";
            context += $"Purpose: {Purpose}\n\n";
            
            var parentPipelines = GetParentPipelines();
            var childPipelines = GetChildPipelines();
            var otherPipelines = GetOtherPipelines();

            if (parentPipelines.Count > 0)
            {
                context += $"Parent Pipelines ({parentPipelines.Count}): ";
                context += string.Join(", ", parentPipelines.ConvertAll(f => Path.GetFileNameWithoutExtension(f)));
                context += "\n";
            }

            if (childPipelines.Count > 0)
            {
                context += $"Child Pipelines ({childPipelines.Count}): ";
                context += string.Join(", ", childPipelines.ConvertAll(f => Path.GetFileNameWithoutExtension(f)));
                context += "\n";
            }

            if (otherPipelines.Count > 0)
            {
                context += $"Other Pipelines ({otherPipelines.Count}): ";
                context += string.Join(", ", otherPipelines.ConvertAll(f => Path.GetFileNameWithoutExtension(f)));
                context += "\n";
            }

            return context;
        }

        /// <summary>
        /// Save project to JSON file
        /// </summary>
        public void SaveToFile(string filePath)
        {
            LastModified = DateTime.Now;
            var json = JsonSerializer.Serialize(this, new JsonSerializerOptions 
            { 
                WriteIndented = true 
            });
            File.WriteAllText(filePath, json);
        }

        /// <summary>
        /// Load project from JSON file
        /// </summary>
        public static ProjectData LoadFromFile(string filePath)
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"Project file not found: {filePath}");

            var json = File.ReadAllText(filePath);
            var project = JsonSerializer.Deserialize<ProjectData>(json);
            return project ?? new ProjectData();
        }
    }
}
