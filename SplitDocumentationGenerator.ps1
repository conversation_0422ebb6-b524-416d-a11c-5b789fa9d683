# PowerShell script to split DocumentationGenerator.cs into two partial class files
# Split around line 2000 - first part contains core functionality, second part contains helper methods

$originalFile = "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\DocumentationGenerator.cs"
$part1File = "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\DocumentationGenerator.Core.cs"
$part2File = "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\DocumentationGenerator.Helpers.cs"
$backupFile = "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\DocumentationGenerator.cs.backup"

Write-Host "Reading the original DocumentationGenerator.cs file..."
$content = Get-Content -Path $originalFile

Write-Host "Creating backup of original file..."
Copy-Item -Path $originalFile -Destination $backupFile
Write-Host "✅ Backup created: DocumentationGenerator.cs.backup"

# Find logical split point around line 2000 - look for a method boundary
$splitLine = 2000
$actualSplitLine = $splitLine

# Adjust split line to avoid breaking methods - find next method start after line 2000
for ($i = $splitLine; $i -lt $content.Length - 100; $i++) {
    if ($content[$i] -match '^\s*(public|private|protected|internal).*\w+\s*\(' -and 
        $content[$i-1] -match '^\s*}?\s*$' -and
        $content[$i] -notmatch '^\s*//') {
        $actualSplitLine = $i
        Write-Host "Found clean split point at line: $($actualSplitLine + 1)"
        break
    }
}

Write-Host "Splitting file at line: $($actualSplitLine + 1)"

# Extract header (using statements and class declaration start)
$headerLines = @()
$foundClassStart = $false
for ($i = 0; $i -lt $content.Length; $i++) {
    $headerLines += $content[$i]
    if ($content[$i] -match '^\s*public partial class DocumentationGenerator\s*$') {
        $headerLines += "    {"
        $foundClassStart = $true
        break
    }
}

if (-not $foundClassStart) {
    Write-Error "Could not find class declaration start"
    exit 1
}

# Create Part 1 - Core functionality (beginning to split point)
Write-Host "Creating Part 1: DocumentationGenerator.Core.cs..."
$part1Content = @()
$part1Content += $headerLines

# Add content from after class declaration to split point
$classStartIndex = $headerLines.Length - 1
for ($i = $classStartIndex + 1; $i -lt $actualSplitLine; $i++) {
    if ($i -lt $content.Length) {
        $part1Content += $content[$i]
    }
}

# Close the class and namespace for part 1
$part1Content += "    }"
$part1Content += "}"

# Create Part 2 - Helper methods (split point to end)
Write-Host "Creating Part 2: DocumentationGenerator.Helpers.cs..."
$part2Content = @()
$part2Content += $headerLines

# Add content from split point to end (excluding final closing braces)
for ($i = $actualSplitLine; $i -lt $content.Length - 2; $i++) {
    $part2Content += $content[$i]
}

# Close the class and namespace for part 2
$part2Content += "    }"
$part2Content += "}"

# Write Part 1
Write-Host "Writing DocumentationGenerator.Core.cs..."
$part1Content | Set-Content -Path $part1File -Encoding UTF8

# Write Part 2  
Write-Host "Writing DocumentationGenerator.Helpers.cs..."
$part2Content | Set-Content -Path $part2File -Encoding UTF8

# Remove original file
Write-Host "Removing original large file..."
Remove-Item -Path $originalFile

Write-Host ""
Write-Host "🎉 Successfully split DocumentationGenerator.cs into two partial class files:"
Write-Host "   📄 DocumentationGenerator.Core.cs    - Core functionality and main methods"
Write-Host "   📄 DocumentationGenerator.Helpers.cs - Helper methods and utilities"
Write-Host "   💾 DocumentationGenerator.cs.backup  - Original file backup"
Write-Host ""
Write-Host "✅ Original file removed - now you can edit the smaller partial files!"
Write-Host ""
Write-Host "File sizes:"
$part1Size = (Get-Item $part1File).Length
$part2Size = (Get-Item $part2File).Length
Write-Host "   Part 1 (Core):    $([math]::Round($part1Size/1024, 1)) KB"
Write-Host "   Part 2 (Helpers): $([math]::Round($part2Size/1024, 1)) KB"
Write-Host "   Original:         $([math]::Round(((Get-Item $backupFile).Length)/1024, 1)) KB"
