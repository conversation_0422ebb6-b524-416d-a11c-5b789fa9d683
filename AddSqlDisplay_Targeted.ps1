# Targeted PowerShell script to add SQL statement display only

$filePath = "DocumentationGenerator.cs"

Write-Host "Adding SQL statement display in a targeted way..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Find the exact location after the database connection info section and add SQL display
$insertLocation = 'Console.WriteLine($"[DB-CONNECTION] Snap {snap.Label} is not categorized as Database (Category: {snap.Category})");'
$sqlCode = @'
Console.WriteLine($"[DB-CONNECTION] Snap {snap.Label} is not categorized as Database (Category: {snap.Category})");
                }

                // Add SQL statement for execute snaps (all categories)
                if (snap.Type.Contains("execute"))
                {
                    Console.WriteLine($"[SQL-EXTRACT] Checking SQL for execute snap: {snap.Label}");
                    
                    // Look for SQL statement in properties
                    var sqlProp = snap.Properties.FirstOrDefault(p => 
                        p.Key.ToLower().Contains("sqlstatement") && 
                        !string.IsNullOrEmpty(p.Value));

                    if (sqlProp.Key != null)
                    {
                        Console.WriteLine($"[SQL-EXTRACT] Found SQL statement for: {snap.Label}");
                        html.AppendLine($"          <p><strong>SQL Statement:</strong></p>");
                        html.AppendLine($"          <pre style='background-color: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; white-space: pre-wrap;'><code>{System.Net.WebUtility.HtmlEncode(sqlProp.Value)}</code></pre>");
                    }
                    else
                    {
                        Console.WriteLine($"[SQL-EXTRACT] No SQL statement found for: {snap.Label}");
                    }
'@

$content = $content.Replace($insertLocation, $sqlCode)

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "SQL statement display added successfully!" -ForegroundColor Green
