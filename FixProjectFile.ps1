# PowerShell script to fix the project file to reference the new partial class files

$projectFile = "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapAnalyzer.csproj"

Write-Host "Reading project file..."
$content = Get-Content -Path $projectFile -Raw

Write-Host "Updating project file to reference new partial class files..."

# Replace the old DocumentationGenerator.cs reference with the two new partial files
$oldReference = '<Compile Include="DocumentationGenerator.cs" />'
$newReference = '<Compile Include="DocumentationGenerator.Core.cs" />
    <Compile Include="DocumentationGenerator.Helpers.cs" />'

if ($content.Contains($oldReference)) {
    $content = $content.Replace($oldReference, $newReference)
    Write-Host "✅ Successfully replaced DocumentationGenerator.cs reference with partial files"
} else {
    Write-Host "⚠️  Could not find exact reference, trying alternative approaches..."
    
    # Try different variations
    $patterns = @(
        'DocumentationGenerator\.cs',
        'Include="DocumentationGenerator\.cs"'
    )
    
    foreach ($pattern in $patterns) {
        if ($content -match $pattern) {
            $content = $content -replace $pattern, 'DocumentationGenerator.Core.cs" />
    <Compile Include="DocumentationGenerator.Helpers.cs'
            Write-Host "✅ Successfully updated using pattern: $pattern"
            break
        }
    }
}

Write-Host "Writing updated project file..."
$content | Set-Content -Path $projectFile -Encoding UTF8

Write-Host "✅ Project file updated successfully!"
