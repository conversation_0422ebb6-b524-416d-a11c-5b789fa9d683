using System;
using System.Collections.Generic;
using System.Linq;

namespace SnapAnalyser
{
    public class TestRouterConnectionFix
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("=== Testing Router Connection Fix ===");
            Console.WriteLine();

            // Create a mock router snap with routes
            var routerSnap = new SnapNode
            {
                Id = "router-test-001",
                Label = "Route Unit Count",
                Type = "com-snaplogic-snaps-flow-router",
                Category = SnapCategory.FlowControl,
                Properties = new Dictionary<string, string>
                {
                    ["settings.routes.value[0].expression.value"] = "$unitCount > 100",
                    ["settings.routes.value[0].outputViewName.value"] = "highValue",
                    ["settings.routes.value[1].expression.value"] = "$unitCount <= 100",
                    ["settings.routes.value[1].outputViewName.value"] = "lowValue"
                },
                OutputConnections = new List<Connection>
                {
                    new Connection { SourceId = "router-test-001", TargetId = "snap-high-001", SourceViewId = null }, // Simulate empty SourceViewId
                    new Connection { SourceId = "router-test-001", TargetId = "snap-low-001", SourceViewId = null }   // Simulate empty SourceViewId
                }
            };

            // Create connected snaps
            var allSnaps = new List<SnapNode>
            {
                routerSnap,
                new SnapNode { Id = "snap-high-001", Label = "Process High Value Orders", Type = "com-snaplogic-snaps-transform-mapper" },
                new SnapNode { Id = "snap-low-001", Label = "Process Low Value Orders", Type = "com-snaplogic-snaps-transform-mapper" }
            };

            Console.WriteLine($"Router Snap: {routerSnap.Label}");
            Console.WriteLine($"Output Connections: {routerSnap.OutputConnections.Count}");
            Console.WriteLine();

            // Test the FlowControlConfigurationGenerator
            var generator = new FlowControlConfigurationGenerator();
            
            try
            {
                Console.WriteLine("Testing router configuration generation...");
                string config = generator.GenerateRouterConfiguration(routerSnap, allSnaps);
                
                Console.WriteLine("✅ Configuration generated successfully!");
                Console.WriteLine();
                Console.WriteLine("Generated Configuration:");
                Console.WriteLine("========================");
                Console.WriteLine(config);
                
                // Check if the configuration contains proper snap connections
                if (config.Contains("Process High Value Orders") && config.Contains("Process Low Value Orders"))
                {
                    Console.WriteLine();
                    Console.WriteLine("✅ SUCCESS: Connected snaps are properly displayed in router configuration!");
                    Console.WriteLine("✅ FIX VERIFIED: Router snap connections no longer show empty quotes");
                }
                else if (config.Contains("No connected snaps"))
                {
                    Console.WriteLine();
                    Console.WriteLine("❌ ISSUE: Still showing 'No connected snaps' - fix may need refinement");
                }
                else
                {
                    Console.WriteLine();
                    Console.WriteLine("⚠️  PARTIAL: Configuration generated but connection status unclear");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
    
    // Helper classes for testing
    public class Connection
    {
        public string SourceId { get; set; }
        public string TargetId { get; set; }
        public string SourceViewId { get; set; }
    }
}
