using System.Text;
using Microsoft.Extensions.Logging;

namespace SnapLogic.Documentation.Shared
{
    public class DocumentationGenerator
    {
        private readonly ILogger<DocumentationGenerator>? _logger;
        private readonly IAIDescriptionService _aiService;

        public DocumentationGenerator(ILogger<DocumentationGenerator>? logger = null, IAIDescriptionService? aiService = null)
        {
            _logger = logger;
            _aiService = aiService ?? new BasicAIDescriptionService();
        }

        public async Task<string> GenerateHtmlDocumentationAsync(PipelineData pipeline, string diagramSvg, ProjectData? project = null, CancellationToken cancellationToken = default)
        {
            await Task.CompletedTask; // Placeholder for actual async operations
            var html = new StringBuilder();
            
            // HTML header
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html lang=\"en\">");
            html.AppendLine("<head>");
            html.AppendLine("  <meta charset=\"UTF-8\">");
            html.AppendLine("  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">");
            html.AppendLine($"  <title>{pipeline.Name} - SnapLogic Pipeline Documentation</title>");
            html.AppendLine("  <style>");
            html.AppendLine(GetOriginalCssStyles());
            html.AppendLine("  </style>");
            html.AppendLine(GetJavaScript());
            html.AppendLine("</head>");
            html.AppendLine("<body>");
            html.AppendLine("  <div class=\"container\">");
            
            // Header
            html.AppendLine($"    <h1>{pipeline.Name} - SnapLogic Pipeline Documentation</h1>");
            html.AppendLine($"    <p><strong>Author:</strong> {pipeline.Author}</p>");
            html.AppendLine($"    <p><strong>Generated:</strong> {DateTime.Now}</p>");
            
            // Table of Contents
            html.AppendLine("    <div class=\"toc\">");
            html.AppendLine("      <h2>Table of Contents</h2>");
            html.AppendLine("      <ul>");
            html.AppendLine("        <li><a href=\"#overview\">Pipeline Overview</a>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><a href=\"#overview\">Architecture & Purpose</a></li>");
            html.AppendLine("            <li><a href=\"#overview\">Pipeline Functions</a></li>");
            html.AppendLine("            <li><a href=\"#overview\">SnapLogic Concepts</a></li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </li>");
            html.AppendLine("        <li><a href=\"#diagram\">Pipeline Flow Diagram</a></li>");
            html.AppendLine("        <li><a href=\"#snap-list\">Snap List</a>");
            html.AppendLine("          <ul>");
            html.AppendLine("            <li><a href=\"#snap-flow\">Flow Order</a></li>");
            html.AppendLine("            <li><a href=\"#snap-categories\">By Category</a></li>");
            html.AppendLine("          </ul>");
            html.AppendLine("        </li>");
            if (pipeline.Parameters.Any())
            {
                html.AppendLine("        <li><a href=\"#parameters\">Pipeline Parameters</a></li>");
            }
            html.AppendLine("        <li><a href=\"#snap-details\">Snap Details</a>");
            html.AppendLine("          <ul>");
            var snapsByCategory = pipeline.Snaps.GroupBy(s => s.Category).ToList();
            foreach (var categoryGroup in snapsByCategory)
            {
                html.AppendLine($"            <li><a href=\"#{GetCategoryAnchor(categoryGroup.Key)}\">{GetCategoryDisplayName(categoryGroup.Key)} ({categoryGroup.Count()})</a></li>");
            }
            html.AppendLine("          </ul>");
            html.AppendLine("        </li>");
            html.AppendLine("      </ul>");
            html.AppendLine("    </div>");
            
            // Overview Section
            html.AppendLine("    <div class=\"section\" id=\"overview\">");
            html.AppendLine("      <h2>Pipeline Overview</h2>");
            
            // Project context if available
            if (project != null)
            {
                html.AppendLine("      <div class=\"ai-description\">");
                html.AppendLine("        <h3>Project Context</h3>");
                html.AppendLine($"        <p><strong>Project:</strong> {project.Name}</p>");
                html.AppendLine($"        <p><strong>Description:</strong> {project.Description}</p>");
                html.AppendLine($"        <p><strong>Purpose:</strong> {project.Purpose}</p>");
                html.AppendLine("      </div>");
            }
            
            // Pipeline statistics
            html.AppendLine("      <h3>Pipeline Statistics</h3>");
            html.AppendLine("      <ul>");
            html.AppendLine($"        <li><strong>Total Snaps:</strong> {pipeline.Snaps.Count}</li>");
            html.AppendLine($"        <li><strong>Total Links:</strong> {pipeline.Links.Count}</li>");
            html.AppendLine($"        <li><strong>Entry Points:</strong> {pipeline.GetStartPoints().Count}</li>");
            html.AppendLine($"        <li><strong>End Points:</strong> {pipeline.GetEndPoints().Count}</li>");
            html.AppendLine("      </ul>");
            
            // Add entry points section
            html.AppendLine("      <h3>Pipeline Entry Points</h3>");
            html.AppendLine("      <p>The pipeline execution starts at the following entry points:</p>");
            html.AppendLine("      <ul>");
            
            foreach (var startNode in pipeline.GetStartPoints())
            {
                html.AppendLine($"        <li><strong>{startNode.Label}</strong> ({GetFriendlySnapType(startNode.Type)}) - {GetSnapDescription(startNode)}</li>");
            }
            
            html.AppendLine("      </ul>");
            html.AppendLine("    </div>");
            
            // Pipeline diagram
            html.AppendLine("    <div class=\"section\" id=\"diagram\">");
            html.AppendLine("      <h2>Pipeline Flow Diagram</h2>");
            html.AppendLine("      <div class=\"diagram-container\">");
            html.AppendLine(diagramSvg);
            html.AppendLine("      </div>");
            html.AppendLine("    </div>");
            
            // Snap List Section
            html.AppendLine("    <div class=\"section\" id=\"snap-list\">");
            html.AppendLine("      <h2>Snap List</h2>");
            html.AppendLine("      <div class=\"snap-list-controls\">");
            html.AppendLine("        <button class=\"snap-list-toggle active\" onclick=\"showSnapView('flow')\">Flow Order</button>");
            html.AppendLine("        <button class=\"snap-list-toggle\" onclick=\"showSnapView('category')\">By Category</button>");
            html.AppendLine("      </div>");
            
            // Flow order view
            html.AppendLine("      <div id=\"snap-flow\" class=\"snap-list-view\">");
            html.AppendLine("        <h3>Snap Flow Order</h3>");
            html.AppendLine("        <div class=\"snap-flow-list\">");
            
            var flowOrderedSnaps = GetSnapFlowOrder(pipeline);
            int snapIndex = 1;
            
            foreach (var snap in flowOrderedSnaps)
            {
                string categoryClass = GetCategoryClass(snap.Category);
                string snapId = GenerateSnapId(snap);
                
                html.AppendLine($"          <div class=\"snap-flow-item\">");
                html.AppendLine($"            <span class=\"snap-number\">{snapIndex}</span>");
                html.AppendLine($"            <span class=\"snap-category {categoryClass}\"></span>");
                html.AppendLine($"            <a href=\"#{snapId}\" class=\"snap-link\">{snap.Label}</a>");
                html.AppendLine($"            <span class=\"snap-type\">({snap.Type})</span>");
                html.AppendLine($"          </div>");
                snapIndex++;
            }
            
            html.AppendLine("        </div>");
            html.AppendLine("      </div>");
            
            // Category view
            html.AppendLine("      <div id=\"snap-categories\" class=\"snap-list-view\" style=\"display: none;\">");
            html.AppendLine("        <h3>Snaps by Category</h3>");
            
            var snapsByCategoryForList = pipeline.Snaps.GroupBy(s => s.Category).ToList();
            foreach (var categoryGroup in snapsByCategoryForList)
            {
                string categoryClass = GetCategoryClass(categoryGroup.Key);
                html.AppendLine($"        <div class=\"snap-category-group\">");
                html.AppendLine($"          <h4><span class=\"snap-category {categoryClass}\"></span>{GetCategoryDisplayName(categoryGroup.Key)} <span class=\"snap-count\">({categoryGroup.Count()})</span></h4>");
                html.AppendLine($"          <div class=\"snap-category-items\">");
                
                foreach (var snap in categoryGroup)
                {
                    string snapId = GenerateSnapId(snap);
                    html.AppendLine($"            <div class=\"snap-category-item\">");
                    html.AppendLine($"              <a href=\"#{snapId}\" class=\"snap-link\">{snap.Label}</a>");
                    html.AppendLine($"              <span class=\"snap-type\">({snap.Type})</span>");
                    html.AppendLine($"            </div>");
                }
                
                html.AppendLine($"          </div>");
                html.AppendLine($"        </div>");
            }
            
            html.AppendLine("      </div>");
            html.AppendLine("    </div>");
            
            // Parameters
            if (pipeline.Parameters.Any())
            {
                html.AppendLine("    <div class=\"section\" id=\"parameters\">");
                html.AppendLine("      <h2>Pipeline Parameters</h2>");
                html.AppendLine("      <table>");
                html.AppendLine("        <thead>");
                html.AppendLine("          <tr>");
                html.AppendLine("            <th>Parameter</th>");
                html.AppendLine("            <th>Type</th>");
                html.AppendLine("            <th>Required</th>");
                html.AppendLine("            <th>Default Value</th>");
                html.AppendLine("            <th>Description</th>");
                html.AppendLine("          </tr>");
                html.AppendLine("        </thead>");
                html.AppendLine("        <tbody>");
                
                foreach (var param in pipeline.Parameters)
                {
                    html.AppendLine("          <tr>");
                    html.AppendLine($"            <td>{param.Key}</td>");
                    html.AppendLine($"            <td>{param.DataType}</td>");
                    html.AppendLine($"            <td>{(param.Required ? "Yes" : "No")}</td>");
                    html.AppendLine($"            <td>{param.Value}</td>");
                    html.AppendLine($"            <td>{param.Description}</td>");
                    html.AppendLine("          </tr>");
                }
                
                html.AppendLine("        </tbody>");
                html.AppendLine("      </table>");
                html.AppendLine("    </div>");
            }
            
            // Snap Details Section
            html.AppendLine("    <div class=\"section\" id=\"snap-details\">");
            html.AppendLine("      <h2>Snap Details</h2>");
            
            foreach (var categoryGroup in snapsByCategory)
            {
                html.AppendLine($"      <div class=\"section\" id=\"{GetCategoryAnchor(categoryGroup.Key)}\">");
                html.AppendLine($"        <h3>{GetCategoryDisplayName(categoryGroup.Key)}</h3>");
                html.AppendLine($"        <div class=\"category-description\">");
                html.AppendLine($"          <p>{GetCategoryDescription(categoryGroup.Key)}</p>");
                html.AppendLine($"        </div>");
                
                foreach (var snap in categoryGroup)
                {
                    html.AppendLine($"        <div class=\"snap-details\" id=\"{GenerateSnapId(snap)}\">");
                    html.AppendLine($"          <h4>{snap.Label}</h4>");
                    html.AppendLine($"          <p><strong>Type:</strong> {GetFriendlySnapType(snap.Type)}</p>");
                    html.AppendLine($"          <p><strong>Description:</strong> {await GetEnhancedSnapDescriptionAsync(snap, pipeline)}</p>");

                    // Connection information
                    html.AppendLine($"          <p><strong>Connections:</strong> {snap.InputConnections.Count} input(s), {snap.OutputConnections.Count} output(s)</p>");

                    // Log basic snap info
                    _logger?.LogDebug("Processing snap '{SnapLabel}' with {PropertyCount} properties", snap.Label, snap.Properties.Count);

                    // Add detailed connection/account information
                    var connectionInfo = GetConnectionInfo(snap, pipeline.RawPipelineJson);
                    if (!string.IsNullOrEmpty(connectionInfo) && connectionInfo != "NULL")
                    {
                        string displayLabel = snap.Category == SnapCategory.Database ? "Database/Connector" : "Account/Connection";
                        html.AppendLine($"          <p><strong>{displayLabel}:</strong> {System.Net.WebUtility.HtmlEncode(connectionInfo)}</p>");
                    }

                    // Add Dynamics365 specific details
                    if (snap.Type?.ToLower().Contains("dynamics365forsales") == true)
                    {
                        string dynamics365Details = GetDynamics365ForSalesDetails(snap);
                        if (!string.IsNullOrEmpty(dynamics365Details))
                        {
                            html.AppendLine(dynamics365Details);
                        }
                    }

                    // Add SQL query information for database snaps
                    if (snap.Category == SnapCategory.Database)
                    {
                        var sqlInfo = GetSqlQueryInfo(snap);
                        if (!string.IsNullOrEmpty(sqlInfo))
                        {
                            html.AppendLine($"          <div class=\"sql-query\">");
                            html.AppendLine($"            <h5>SQL Query</h5>");
                            html.AppendLine($"            <pre><code>{System.Net.WebUtility.HtmlEncode(sqlInfo)}</code></pre>");
                            html.AppendLine($"          </div>");
                        }
                    }

                    // Add mapping information for transform snaps
                    if (snap.Category == SnapCategory.Transformation || snap.Type?.ToLower().Contains("transform") == true)
                    {
                        var mappings = GetSnapMappings(snap);
                        if (mappings.Any())
                        {
                            html.AppendLine($"          <div class=\"mapping-details\">");
                            html.AppendLine($"            <h5>Field Mappings ({mappings.Count} total)</h5>");
                            html.AppendLine($"            <div class=\"mapping-table\">");

                            var displayMappings = mappings.Take(10); // Show first 10 mappings
                            foreach (var mapping in displayMappings)
                            {
                                html.AppendLine($"              <div class=\"mapping-row\">");
                                html.AppendLine($"                <span class=\"target-field\">{System.Net.WebUtility.HtmlEncode(mapping.Key)}</span>");
                                html.AppendLine($"                <span class=\"mapping-arrow\">←</span>");
                                html.AppendLine($"                <span class=\"source-expression\">{System.Net.WebUtility.HtmlEncode(mapping.Value)}</span>");
                                html.AppendLine($"              </div>");
                            }

                            if (mappings.Count > 10)
                            {
                                html.AppendLine($"              <div class=\"mapping-more\">... and {mappings.Count - 10} more mappings</div>");
                            }

                            html.AppendLine($"            </div>");
                            html.AppendLine($"          </div>");
                        }
                    }

                    // Enhanced Properties with better categorization
                    if (snap.Properties.Any())
                    {
                        html.AppendLine($"          <h5>Configuration Properties</h5>");
                        html.AppendLine($"          <div class=\"property-details\">");

                        var importantProps = GetImportantProperties(snap);
                        var otherProps = snap.Properties.Where(p => !importantProps.ContainsKey(p.Key)).Take(10);

                        if (importantProps.Any())
                        {
                            html.AppendLine($"            <h6>Key Configuration</h6>");
                            html.AppendLine($"            <ul class=\"property-list\">");
                            foreach (var prop in importantProps)
                            {
                                html.AppendLine($"              <li><strong>{prop.Key}:</strong> {prop.Value}</li>");
                            }
                            html.AppendLine($"            </ul>");
                        }

                        if (otherProps.Any())
                        {
                            html.AppendLine($"            <h6>Other Properties</h6>");
                            html.AppendLine($"            <ul class=\"property-list\">");
                            foreach (var prop in otherProps)
                            {
                                html.AppendLine($"              <li><strong>{prop.Key}:</strong> {prop.Value}</li>");
                            }
                            if (snap.Properties.Count > importantProps.Count + 10)
                            {
                                html.AppendLine($"              <li><em>... and {snap.Properties.Count - importantProps.Count - 10} more properties</em></li>");
                            }
                            html.AppendLine($"            </ul>");
                        }

                        html.AppendLine($"          </div>");
                    }

                    html.AppendLine($"        </div>");
                }
                
                html.AppendLine($"      </div>");
            }
            
            html.AppendLine("    </div>");
            
            // Close HTML
            html.AppendLine("  </div>");
            html.AppendLine("</body>");
            html.AppendLine("</html>");
            
            return html.ToString();
        }
        
        public string GenerateHtmlDocumentation(PipelineData pipeline, string diagramSvg, ProjectData? project = null)
        {
            return GenerateHtmlDocumentationAsync(pipeline, diagramSvg, project, CancellationToken.None).GetAwaiter().GetResult();
        }

        // Helper methods
        private List<SnapNode> GetSnapFlowOrder(PipelineData pipeline)
        {
            // Simple flow order - start with entry points and follow connections
            var ordered = new List<SnapNode>();
            var visited = new HashSet<string>();

            foreach (var startPoint in pipeline.GetStartPoints())
            {
                TraverseFlow(startPoint, pipeline, ordered, visited);
            }

            // Add any remaining snaps that weren't reached
            foreach (var snap in pipeline.Snaps)
            {
                if (!visited.Contains(snap.Id))
                {
                    ordered.Add(snap);
                }
            }

            return ordered;
        }

        private void TraverseFlow(SnapNode snap, PipelineData pipeline, List<SnapNode> ordered, HashSet<string> visited)
        {
            if (visited.Contains(snap.Id))
                return;

            visited.Add(snap.Id);
            ordered.Add(snap);

            // Follow output connections
            foreach (var link in snap.OutputConnections)
            {
                var targetSnap = pipeline.Snaps.FirstOrDefault(s => s.Id == link.TargetId);
                if (targetSnap != null)
                {
                    TraverseFlow(targetSnap, pipeline, ordered, visited);
                }
            }
        }

        private string GetCategoryClass(SnapCategory category)
        {
            return category.ToString().ToLower().Replace("_", "-");
        }

        private string GetCategoryDisplayName(SnapCategory category)
        {
            return category switch
            {
                SnapCategory.FlowControl => "Flow Control",
                SnapCategory.Transformation => "Transformation",
                SnapCategory.Database => "Database",
                SnapCategory.ExternalSystem => "External System",
                SnapCategory.FileOperation => "File Operation",
                SnapCategory.ErrorHandling => "Error Handling",
                _ => "Other"
            };
        }

        private string GetCategoryAnchor(SnapCategory category)
        {
            return $"category-{category.ToString().ToLower()}";
        }

        private string GetCategoryDescription(SnapCategory category)
        {
            return category switch
            {
                SnapCategory.FlowControl => "Snaps that control the flow of data through the pipeline, including routers, filters, and flow control logic.",
                SnapCategory.Transformation => "Snaps that transform, map, or manipulate data as it flows through the pipeline.",
                SnapCategory.Database => "Snaps that interact with databases for reading, writing, or executing database operations.",
                SnapCategory.ExternalSystem => "Snaps that integrate with external systems and services.",
                SnapCategory.FileOperation => "Snaps that handle file operations such as reading, writing, or processing files.",
                SnapCategory.ErrorHandling => "Snaps that handle errors and exceptions in the pipeline.",
                _ => "Other snaps that don't fit into the standard categories."
            };
        }

        private string GenerateSnapId(SnapNode snap)
        {
            return $"snap-{snap.Id.Replace(" ", "-").Replace("_", "-").ToLower()}";
        }

        private string GetFriendlySnapType(string snapType)
        {
            if (string.IsNullOrEmpty(snapType))
                return "Unknown";

            _logger?.LogDebug("Parsing snap type: {SnapType}", snapType);
            Console.WriteLine($"[SNAP-TYPE] Parsing: {snapType}");

            // Handle common SnapLogic snap type patterns
            var type = snapType.ToLower().Replace(" ", "").Replace(".", "");

            // Map common snap types to friendly names - check for specific patterns
            var typeMap = new Dictionary<string, string>
            {
                { "datatransform", "Data Transform" },
                { "mapper", "Mapper" },
                { "sqlserverlookup", "SQL Server Lookup" },
                { "sqlserverselect", "SQL Server Select" },
                { "sqlserverinsert", "SQL Server Insert" },
                { "sqlserverupdate", "SQL Server Update" },
                { "sqlserverdelete", "SQL Server Delete" },
                { "sqlserverexecute", "SQL Server Execute" },
                { "sqlserverbulkload", "SQL Server Bulk Load" },
                { "dynamics365forsalesread", "Dynamics365 Read" },
                { "dynamics365forsaleswrite", "Dynamics365 Write" },
                { "dynamics365forsalesupsert", "Dynamics365 Upsert" },
                { "csvformatter", "CSV Formatter" },
                { "csvparser", "CSV Parser" },
                { "jsonformatter", "JSON Formatter" },
                { "jsonparser", "JSON Parser" },
                { "excelformatter", "Excel Formatter" },
                { "excelparser", "Excel Parser" },
                { "filereader", "File Reader" },
                { "filewriter", "File Writer" },
                { "router", "Router" },
                { "filter", "Filter" },
                { "join", "Join" },
                { "aggregate", "Aggregate" },
                { "sort", "Sort" },
                { "union", "Union" },
                { "copy", "Copy" },
                { "head", "Head" },
                { "tail", "Tail" },
                { "sequence", "Sequence" },
                { "gate", "Gate" }
            };

            // Try to find a direct match first
            foreach (var mapping in typeMap)
            {
                if (type.Contains(mapping.Key))
                {
                    _logger?.LogDebug("Found type mapping: {OriginalType} -> {FriendlyType}", snapType, mapping.Value);
                    return mapping.Value;
                }
            }

            // Handle space-separated types like "com snaplogic snaps sqlserver lookup"
            var words = snapType.ToLower().Split(new[] { ' ', '.', '_', '-' }, StringSplitOptions.RemoveEmptyEntries);

            // Look for database types
            if (words.Contains("sqlserver") || words.Contains("sql"))
            {
                var operation = words.LastOrDefault();
                if (!string.IsNullOrEmpty(operation))
                {
                    var friendlyOp = char.ToUpper(operation[0]) + operation.Substring(1);
                    var result = $"🔧 SQL Server {friendlyOp}"; // Added emoji to make it obvious this code is running
                    _logger?.LogDebug("Parsed SQL Server type: {OriginalType} -> {FriendlyType}", snapType, result);
                    return result;
                }
            }

            // Look for other database types
            if (words.Contains("oracle"))
            {
                var operation = words.LastOrDefault();
                return $"Oracle {char.ToUpper(operation[0]) + operation.Substring(1)}";
            }

            if (words.Contains("mysql"))
            {
                var operation = words.LastOrDefault();
                return $"MySQL {char.ToUpper(operation[0]) + operation.Substring(1)}";
            }

            // Extract the last meaningful part of the snap type
            var lastPart = words.LastOrDefault() ?? snapType;

            // Convert to readable format
            var readable = char.ToUpper(lastPart[0]) + lastPart.Substring(1);

            _logger?.LogDebug("Fallback parsing: {OriginalType} -> {FriendlyType}", snapType, readable);
            return readable;
        }

        private string GetSnapDescription(SnapNode snap)
        {
            // Generate basic description based on snap type and properties
            var type = snap.Type.ToLower();

            if (type.Contains("read") || type.Contains("select"))
                return "Reads data from a source";
            else if (type.Contains("write") || type.Contains("insert") || type.Contains("update"))
                return "Writes data to a destination";
            else if (type.Contains("transform") || type.Contains("map"))
                return "Transforms data structure or content";
            else if (type.Contains("filter"))
                return "Filters data based on conditions";
            else if (type.Contains("router"))
                return "Routes data to different paths";
            else if (type.Contains("join"))
                return "Joins data from multiple sources";
            else
                return "Processes data according to its configuration";
        }

        /// <summary>
        /// Extracts connection information from snap properties and raw JSON - EXACT COPY FROM WINDOWS APP
        /// </summary>
        private string GetConnectionInfo(SnapNode snap, Newtonsoft.Json.Linq.JObject? rawPipelineJson = null)
        {
            _logger?.LogInformation("[CONNECTION] Checking connection info for snap: {SnapLabel}, Properties count: {PropertyCount}", snap.Label, snap.Properties.Count);

            // Try to extract account information from raw JSON first (from Windows app logic)
            if (rawPipelineJson != null && !string.IsNullOrEmpty(snap.Id))
            {
                try
                {
                    var snapsCollection = rawPipelineJson["snaps"];
                    if (snapsCollection != null && !string.IsNullOrEmpty(snap.Id))
                    {
                        // Find the specific snap in the collection using the snap ID
                        var snapJson = snapsCollection[snap.Id];
                        _logger?.LogInformation("[CONNECTION] Found snap JSON for ID {SnapId}: {Found}", snap.Id, snapJson != null);

                        if (snapJson != null)
                        {
                            // Look for property_map which contains the snap configuration
                            var propertyMap = snapJson["property_map"];
                            _logger?.LogInformation("[CONNECTION] property_map exists: {Exists}", propertyMap != null);

                            if (propertyMap != null)
                            {
                                // Try to extract account information from nested structure
                                var account = propertyMap["account"];
                                _logger?.LogInformation("[CONNECTION] account property exists: {Exists}", account != null);

                                if (account != null && account.HasValues)
                                {
                                    var accountValue = account["value"];
                                    if (accountValue != null)
                                    {
                                        var result = accountValue.ToString();
                                        _logger?.LogInformation("[CONNECTION] Found account from raw JSON: {Account}", result);
                                        return result;
                                    }
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "[CONNECTION] Error accessing raw pipeline JSON");
                }
            }

            // Log all properties for debugging (from Windows app)
            foreach (var prop in snap.Properties)
            {
                var value = prop.Value.Length > 100 ? prop.Value.Substring(0, 100) + "..." : prop.Value;
                _logger?.LogInformation("[CONNECTION] Property: {Key} = {Value}", prop.Key, value);
            }

            // First, look for the nested account structure (from Windows app)
            if (snap.Properties.ContainsKey("account"))
            {
                try
                {
                    string accountJson = snap.Properties["account"];
                    _logger?.LogInformation("[CONNECTION] Found account property, parsing JSON (length: {Length})", accountJson.Length);

                    var accountObj = Newtonsoft.Json.Linq.JObject.Parse(accountJson);
                    var accountValue = accountObj["value"];
                    if (accountValue != null)
                    {
                        var result = accountValue.ToString();
                        _logger?.LogInformation("[CONNECTION] Found account from properties: {Account}", result);
                        return result;
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "[CONNECTION] Error parsing account JSON");
                    // If JSON parsing fails, return the raw value
                    return snap.Properties["account"];
                }
            }

            // Since no account information was found in the expected location,
            // return NULL rather than showing table names or other properties
            // that are not actual account names (from Windows app logic)
            _logger?.LogInformation("[CONNECTION] No account information found for snap: {SnapLabel}", snap.Label);
            return "NULL";
        }

        /// <summary>
        /// Extracts and formats Dynamics365ForSales-specific details
        /// </summary>
        private string GetDynamics365ForSalesDetails(SnapNode snap)
        {
            var html = new StringBuilder();

            try
            {
                var details = new Dictionary<string, string>();

                // Extract key Dynamics365 properties
                var dynamicsProps = new[] { "entity", "operation", "fields", "filter", "orderby", "top" };
                foreach (var prop in dynamicsProps)
                {
                    if (snap.Properties.ContainsKey(prop))
                    {
                        details[prop] = snap.Properties[prop];
                    }
                }

                if (details.Any())
                {
                    html.AppendLine($"          <div class=\"dynamics365-details\">");
                    html.AppendLine($"            <h5>Dynamics365 Configuration</h5>");
                    html.AppendLine($"            <ul class=\"property-list\">");

                    foreach (var detail in details)
                    {
                        html.AppendLine($"              <li><strong>{detail.Key}:</strong> {System.Net.WebUtility.HtmlEncode(detail.Value)}</li>");
                    }

                    html.AppendLine($"            </ul>");
                    html.AppendLine($"          </div>");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Error processing Dynamics365 details for snap {SnapLabel}", snap.Label);
            }

            return html.ToString();
        }

        /// <summary>
        /// Extracts SQL query information from database snaps - EXACT COPY FROM WINDOWS APP
        /// </summary>
        private string GetSqlQueryInfo(SnapNode snap)
        {
            _logger?.LogInformation("[SQL-EXTRACT] Checking SQL for snap: {SnapLabel}", snap.Label);

            // Look for SQL statement in settings (from Windows app)
            if (snap.Properties.ContainsKey("settings"))
            {
                try
                {
                    string settingsJson = snap.Properties["settings"];
                    _logger?.LogInformation("[SQL-EXTRACT] Found settings property, parsing JSON");

                    var settingsObj = Newtonsoft.Json.Linq.JObject.Parse(settingsJson);
                    var sqlStatement = settingsObj["sqlStatement"];

                    if (sqlStatement != null)
                    {
                        var sqlValue = sqlStatement["value"]?.ToString();
                        if (!string.IsNullOrEmpty(sqlValue))
                        {
                            _logger?.LogInformation("[SQL-EXTRACT] Found SQL statement: {SqlValue}", sqlValue);
                            return sqlValue;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "[SQL-EXTRACT] Error parsing settings JSON");
                }
            }

            // Look for SQL statement in properties (from Windows app)
            foreach (var prop in snap.Properties)
            {
                string key = prop.Key.ToLower();
                string value = prop.Value;

                _logger?.LogInformation("[SQL-EXTRACT] Checking property: {Key} = {Value}", prop.Key,
                    string.IsNullOrEmpty(value) ? "NULL/EMPTY" : $"'{value.Substring(0, Math.Min(100, value.Length))}...'");

                // Check for common SQL property keys (from Windows app)
                if (key.Contains("sqlstatement") || key.Contains("sql_statement") ||
                    key.Contains("query") || key.Contains("statement") || key == "sql")
                {
                    if (!string.IsNullOrEmpty(value))
                    {
                        _logger?.LogInformation("[SQL-EXTRACT] Found SQL in property '{Key}': {Value}", prop.Key, value.Substring(0, Math.Min(200, value.Length)) + "...");
                        return value;
                    }
                }
            }

            _logger?.LogInformation("[SQL-EXTRACT] No SQL statement found for snap: {SnapLabel}", snap.Label);
            return "";
        }

        /// <summary>
        /// Gets important properties for a snap based on its type and category
        /// </summary>
        private Dictionary<string, string> GetImportantProperties(SnapNode snap)
        {
            var important = new Dictionary<string, string>();

            try
            {
                // Define important properties based on snap category
                var importantProps = snap.Category switch
                {
                    SnapCategory.Database => new[] { "table", "schema", "database", "operation", "batchSize", "timeout" },
                    SnapCategory.FileOperation => new[] { "filename", "path", "encoding", "delimiter", "hasHeader" },
                    SnapCategory.Transformation => new[] { "expression", "mapping", "passThrough", "nullSafeAccess" },
                    SnapCategory.FlowControl => new[] { "condition", "expression", "path", "default" },
                    SnapCategory.ExternalSystem => new[] { "endpoint", "method", "headers", "timeout", "retries" },
                    _ => new[] { "operation", "expression", "condition", "path", "method" }
                };

                foreach (var prop in importantProps)
                {
                    if (snap.Properties.ContainsKey(prop))
                    {
                        important[prop] = snap.Properties[prop];
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Error getting important properties for snap {SnapLabel}", snap.Label);
            }

            return important;
        }

        /// <summary>
        /// Gets enhanced snap description using AI service or fallback logic
        /// </summary>
        private async Task<string> GetEnhancedSnapDescriptionAsync(SnapNode snap, PipelineData pipeline)
        {
            try
            {
                _logger?.LogInformation("Getting enhanced description for snap: {SnapLabel} (Type: {SnapType})", snap.Label, snap.Type);
                Console.WriteLine($"[ENHANCED-DESC] Processing snap: {snap.Label} (Type: {snap.Type})");

                var result = await _aiService.GenerateSnapDescriptionAsync(snap, pipeline);

                Console.WriteLine($"[ENHANCED-DESC] Generated description: {result}");
                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Error generating AI description for snap {SnapLabel}, using fallback", snap.Label);
                Console.WriteLine($"[ENHANCED-DESC] Error for {snap.Label}: {ex.Message}, using fallback");
                return GetSnapDescription(snap);
            }
        }

        /// <summary>
        /// Extracts field mappings from transform/mapper snaps - EXACT COPY FROM WINDOWS APP
        /// </summary>
        private Dictionary<string, string> GetSnapMappings(SnapNode snap)
        {
            var mappings = new Dictionary<string, string>();

            // Log the fact that we're attempting to extract mappings for this snap (from Windows app)
            _logger?.LogInformation("Extracting mappings for snap: {SnapLabel} ({SnapType})", snap.Label, snap.Type);

            // First check for mappingTable in different potential locations (from Windows app)
            List<string> mappingTableKeys = new List<string> {
                "settings.transformations.value.mappingTable",
                "settings.transformations.mappingTable.value",
                "settings.mappingTable.value",
                "settings.mappingTable",
                "mappingTable.value",
                "mappingTable",
                "transformations.value.mappingTable.value"  // Added this format
            };

            foreach (string key in mappingTableKeys)
            {
                if (snap.Properties.ContainsKey(key))
                {
                    try
                    {
                        string mappingData = snap.Properties[key];
                        _logger?.LogInformation("Found mappingTable at key: {Key}, data length: {Length}", key, mappingData.Length);

                        // Parse the mapping data as JSON array (from Windows app)
                        using var doc = System.Text.Json.JsonDocument.Parse(mappingData);
                        if (doc.RootElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                        {
                            foreach (var mapping in doc.RootElement.EnumerateArray())
                            {
                                if (mapping.TryGetProperty("targetPath", out var targetProp) &&
                                    mapping.TryGetProperty("expression", out var exprProp))
                                {
                                    var targetPath = targetProp.TryGetProperty("value", out var tVal) ? tVal.GetString() : targetProp.GetString();
                                    var expression = exprProp.TryGetProperty("value", out var eVal) ? eVal.GetString() : exprProp.GetString();

                                    if (!string.IsNullOrEmpty(targetPath) && !string.IsNullOrEmpty(expression))
                                    {
                                        mappings[targetPath] = expression;
                                        _logger?.LogInformation("Found mapping: {Target} = {Expression}", targetPath, expression);
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning(ex, "Error parsing mappingTable at key {Key}", key);
                    }
                }
            }

            // Extract mappings from SnapLogic mapper specific format (from Windows app)
            try
            {
                // Look for patterns like these in SnapLogic's datatransform snaps:
                // "transformations" : { "value" : { "mappingRoot" : { "value" : "$" }, "mappingTable" : { "value" : [ { ... } ] } } }
                var potentialMappingRoots = snap.Properties.Where(p =>
                    p.Key.Contains("mappingRoot") && p.Key.EndsWith(".value")).ToList();

                foreach (var rootProp in potentialMappingRoots)
                {
                    string rootPath = rootProp.Value; // Usually "$"
                    string rootPrefix = rootProp.Key.Substring(0, rootProp.Key.LastIndexOf(".mappingRoot"));
                    string mappingKeyBase = $"{rootPrefix}.mappingTable.value";

                    _logger?.LogInformation("Found potential mapping root at {RootPrefix} with value {RootPath}", rootPrefix, rootPath);

                    // Now that we found the root, try to extract a mappingTable array
                    if (snap.Properties.TryGetValue(mappingKeyBase, out string mappingData) &&
                        !string.IsNullOrEmpty(mappingData))
                    {
                        try
                        {
                            _logger?.LogInformation("Found mappingTable data at {MappingKeyBase}, parsing...", mappingKeyBase);
                            using var doc = System.Text.Json.JsonDocument.Parse(mappingData);
                            if (doc.RootElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                            {
                                foreach (var mapping in doc.RootElement.EnumerateArray())
                                {
                                    if (mapping.TryGetProperty("targetPath", out var targetProp) &&
                                        mapping.TryGetProperty("expression", out var exprProp))
                                    {
                                        string targetPath = targetProp.GetProperty("value").GetString();
                                        string expression = exprProp.GetProperty("value").GetString();
                                        _logger?.LogInformation("Found SnapLogic mapping: {Target} = {Expression}", targetPath, expression);
                                        mappings[targetPath] = expression;
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogWarning(ex, "Error parsing SnapLogic mapping format");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Error in SnapLogic specific mapping extraction");
            }

            _logger?.LogInformation("Total mappings extracted: {Count}", mappings.Count);
            return mappings;
        }

        private string GetOriginalCssStyles()
        {
            return @"
    body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; color: #333; }
    .container { max-width: none; width: 100%; margin: 0; padding: 0 20px; }
    h1 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
    h2 { color: #2c3e50; margin-top: 30px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
    h3 { color: #3498db; }
    h4 { color: #3498db; margin-top: 20px; margin-bottom: 10px; }
    .section { margin-bottom: 30px; }
    table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    tr:nth-child(even) { background-color: #f9f9f9; }
    .diagram-container { overflow: auto; border: 1px solid #ddd; margin: 20px 0; padding: 10px; text-align: left; width: 100%; display: block; }
    .diagram-container svg { display: block; margin: 0; position: relative; }
    .snap-category { display: inline-block; width: 16px; height: 16px; margin-right: 5px; vertical-align: middle; border: 1px solid #333; }
    .flowcontrol { background-color: #ffffcc; }
    .transformation { background-color: #ccffcc; }
    .database { background-color: #ccccff; }
    .externalsystem { background-color: #ffcccc; }
    .fileoperation { background-color: #ffccff; }
    .errorhandling { background-color: #ffdddd; }
    .other { background-color: #f0f0f0; }
    .toc { background-color: #f5f5f5; padding: 15px; margin-bottom: 20px; border-radius: 5px; }
    .toc ul { list-style-type: none; padding-left: 20px; }
    .toc a { text-decoration: none; color: #3498db; }
    .toc a:hover { text-decoration: underline; }
    .ai-description { background-color: #f8f9fa; border-left: 4px solid #3498db; padding: 15px; margin: 15px 0; }
    .function-details { background-color: #f0f7fb; border-left: 5px solid #72b7e5; padding: 10px 15px; margin: 5px 0; line-height: 1.6; }
    .snap-details { background-color: #f9f9f9; padding: 15px; margin: 10px 0; border-radius: 5px; }
    .property-list { margin: 0; padding: 0; list-style-type: none; }
    .property-list li { margin-bottom: 5px; }
    .category-description { background-color: #f9f9f9; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px; }
    .snap-count { font-weight: bold; }
    .snap-list-controls { margin: 15px 0; text-align: center; }
    .snap-list-toggle { background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 8px 16px; margin: 0 5px; cursor: pointer; border-radius: 4px; font-size: 14px; transition: all 0.2s; }
    .snap-list-toggle:hover { background-color: #e9ecef; }
    .snap-list-toggle.active { background-color: #3498db; color: white; border-color: #3498db; }
    .snap-list-view { margin: 20px 0; }
    .snap-flow-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 10px; margin: 15px 0; }
    .snap-flow-item { display: flex; align-items: center; padding: 8px 12px; background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; transition: background-color 0.2s; }
    .snap-flow-item:hover { background-color: #e9ecef; }
    .snap-number { background-color: #6c757d; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; margin-right: 10px; flex-shrink: 0; }
    .snap-link { color: #3498db; text-decoration: none; font-weight: 500; margin-left: 5px; margin-right: 8px; }
    .snap-link:hover { text-decoration: underline; }
    .snap-type { color: #6c757d; font-size: 12px; font-style: italic; }
    .snap-category-group { margin-bottom: 25px; }
    .snap-category-group h4 { display: flex; align-items: center; margin-bottom: 10px; color: #2c3e50; }
    .snap-category-items { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 8px; margin-left: 20px; }
    .snap-category-item { display: flex; align-items: center; justify-content: space-between; padding: 6px 10px; background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 3px; transition: background-color 0.2s; }
    .snap-category-item:hover { background-color: #e9ecef; }
    .sql-query { background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 10px; margin: 10px 0; }
    .sql-query pre { background-color: #ffffff; border: 1px solid #e9ecef; border-radius: 3px; padding: 8px; margin: 5px 0; font-size: 12px; overflow-x: auto; }
    .dynamics365-details { background-color: #e7f3ff; border-left: 4px solid #0066cc; padding: 10px; margin: 10px 0; }
    .property-details h6 { color: #495057; font-size: 14px; margin-top: 15px; margin-bottom: 8px; }
    .mapping-details { background-color: #f0f8f0; border-left: 4px solid #28a745; padding: 10px; margin: 10px 0; }
    .mapping-table { font-family: 'Courier New', monospace; font-size: 12px; }
    .mapping-row { display: flex; align-items: center; margin: 5px 0; padding: 3px 0; }
    .target-field { font-weight: bold; color: #0066cc; min-width: 150px; }
    .mapping-arrow { margin: 0 10px; color: #666; font-weight: bold; }
    .source-expression { color: #333; flex: 1; }
    .mapping-more { font-style: italic; color: #666; margin-top: 10px; }
    @media (max-width: 768px) { .snap-flow-list, .snap-category-items { grid-template-columns: 1fr; } }
            ";
        }

        private string GetJavaScript()
        {
            return @"
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Snap list view toggle functionality
      window.showSnapView = function(viewType) {
        const flowView = document.getElementById('snap-flow');
        const categoryView = document.getElementById('snap-categories');
        const toggles = document.querySelectorAll('.snap-list-toggle');

        // Hide all views
        flowView.style.display = 'none';
        categoryView.style.display = 'none';

        // Remove active class from all toggles
        toggles.forEach(toggle => toggle.classList.remove('active'));

        // Show selected view and activate toggle
        if (viewType === 'flow') {
          flowView.style.display = 'block';
          toggles[0].classList.add('active');
        } else if (viewType === 'category') {
          categoryView.style.display = 'block';
          toggles[1].classList.add('active');
        }
      };
    });
  </script>";
        }
    }
}
