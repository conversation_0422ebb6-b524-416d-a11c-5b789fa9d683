# PowerShell script to add Dynamics365ForSales snap details display
# Will show Filter Condition, Query Parameters, Output Attributes, and Order By

$filePath = "DocumentationGenerator.cs"

Write-Host "Adding Dynamics365ForSales snap details display..." -ForegroundColor Green

$content = Get-Content $filePath -Raw

# Find the insertion point - after connection info but before description
$insertionPoint = 'LogMessage\(\$"\[CONNECTION\] Snap \{snap\.Label\} does not qualify for account display \(Category: \{snap\.Category\}, Type: \{snap\.Type\}\)"\);'
$afterInsertionPoint = @'
LogMessage($"[CONNECTION] Snap {snap.Label} does not qualify for account display (Category: {snap.Category}, Type: {snap.Type})");
                }

                // Add Dynamics365ForSales-specific details
                if (snap.Type?.ToLower().Contains("dynamics365forsales") == true)
                {
                    LogMessage($"[DYNAMICS365] Processing Dynamics365ForSales snap details for: {snap.Label}");
                    string dynamics365Details = GetDynamics365ForSalesDetails(snap);
                    if (!string.IsNullOrEmpty(dynamics365Details))
                    {
                        html.AppendLine(dynamics365Details);
                    }
                }
'@

if ($content.Contains($insertionPoint)) {
    $content = $content.Replace($insertionPoint, $afterInsertionPoint)
    Write-Host "✅ Added Dynamics365ForSales details integration point" -ForegroundColor Yellow
}

# Add the new method at the end of the class, before the closing braces
$methodInsertionPoint = 'LogMessage\(\$"\[CONNECTION\] No connection info found for snap: \{snap\.Label\}"\);\s*return null;\s*\}'
$newMethodDefinition = @'
LogMessage($"[CONNECTION] No connection info found for snap: {snap.Label}");
            return null;
        }

        /// <summary>
        /// Extracts and formats Dynamics365ForSales-specific details
        /// </summary>
        private string GetDynamics365ForSalesDetails(SnapNode snap)
        {
            var html = new StringBuilder();
            LogMessage($"[DYNAMICS365] Extracting details for snap: {snap.Label}");

            try
            {
                var details = new Dictionary<string, string>();

                // Look for specific Dynamics365ForSales properties
                foreach (var prop in snap.Properties)
                {
                    string key = prop.Key.ToLower();
                    string value = prop.Value;

                    // Skip empty or boolean values
                    if (string.IsNullOrEmpty(value) || 
                        value.Equals("true", StringComparison.OrdinalIgnoreCase) || 
                        value.Equals("false", StringComparison.OrdinalIgnoreCase))
                        continue;

                    // Filter Condition
                    if (key.Contains("filter") && (key.Contains("condition") || key.Contains("criteria") || key.Contains("where")))
                    {
                        details["Filter Condition"] = value;
                        LogMessage($"[DYNAMICS365] Found Filter Condition: {value}");
                    }
                    // Query Parameters
                    else if (key.Contains("query") && (key.Contains("parameter") || key.Contains("param") || key.Contains("variable")))
                    {
                        details["Query Parameters"] = value;
                        LogMessage($"[DYNAMICS365] Found Query Parameters: {value}");
                    }
                    // Output Attributes
                    else if (key.Contains("output") && (key.Contains("attribute") || key.Contains("field") || key.Contains("column")))
                    {
                        details["Output Attributes"] = value;
                        LogMessage($"[DYNAMICS365] Found Output Attributes: {value}");
                    }
                    // Order By
                    else if (key.Contains("order") && (key.Contains("by") || key.Contains("sort")))
                    {
                        details["Order By"] = value;
                        LogMessage($"[DYNAMICS365] Found Order By: {value}");
                    }
                    // Generic property matching for these specific terms
                    else if (key.Contains("filtercondition") || key == "filter_condition")
                    {
                        details["Filter Condition"] = value;
                        LogMessage($"[DYNAMICS365] Found Filter Condition (exact match): {value}");
                    }
                    else if (key.Contains("queryparameters") || key == "query_parameters")
                    {
                        details["Query Parameters"] = value;
                        LogMessage($"[DYNAMICS365] Found Query Parameters (exact match): {value}");
                    }
                    else if (key.Contains("outputattributes") || key == "output_attributes")
                    {
                        details["Output Attributes"] = value;
                        LogMessage($"[DYNAMICS365] Found Output Attributes (exact match): {value}");
                    }
                    else if (key.Contains("orderby") || key == "order_by")
                    {
                        details["Order By"] = value;
                        LogMessage($"[DYNAMICS365] Found Order By (exact match): {value}");
                    }
                }

                // Format the details for HTML output
                if (details.Any())
                {
                    html.AppendLine($"          <div style=\"margin: 10px 0; padding: 10px; background-color: #f0f8ff; border-left: 4px solid #0066cc;\">");
                    html.AppendLine($"            <h5 style=\"color: #0066cc; margin: 0 0 8px 0;\">Dynamics365 Configuration</h5>");

                    foreach (var detail in details)
                    {
                        string displayValue = detail.Value;
                        
                        // Try to parse JSON values for better display
                        if (displayValue.StartsWith("{") || displayValue.StartsWith("["))
                        {
                            try
                            {
                                var parsed = JToken.Parse(displayValue);
                                if (parsed is JArray array)
                                {
                                    displayValue = string.Join(", ", array.Select(item => item.ToString()));
                                }
                                else if (parsed is JObject obj)
                                {
                                    displayValue = string.Join(", ", obj.Properties().Select(p => $"{p.Name}: {p.Value}"));
                                }
                            }
                            catch
                            {
                                // Keep original value if JSON parsing fails
                            }
                        }

                        html.AppendLine($"            <p style=\"margin: 4px 0;\"><strong>{detail.Key}:</strong> {System.Net.WebUtility.HtmlEncode(displayValue)}</p>");
                        LogMessage($"[DYNAMICS365] Added to output: {detail.Key} = {displayValue}");
                    }

                    html.AppendLine($"          </div>");
                    LogMessage($"[DYNAMICS365] Generated HTML details block for: {snap.Label}");
                }
                else
                {
                    LogMessage($"[DYNAMICS365] No specific details found for: {snap.Label}");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"[DYNAMICS365] Error processing details for {snap.Label}: {ex.Message}");
            }

            return html.ToString();
        }
'@

if ($content -match $methodInsertionPoint) {
    $content = $content -replace $methodInsertionPoint, [regex]::Escape($newMethodDefinition)
    Write-Host "✅ Added GetDynamics365ForSalesDetails method" -ForegroundColor Yellow
} else {
    Write-Host "⚠️  Could not find insertion point for new method" -ForegroundColor Red
}

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "✅ Dynamics365ForSales details display added!" -ForegroundColor Green
Write-Host ""
Write-Host "Features added:" -ForegroundColor Cyan
Write-Host "  ✓ Automatic detection of Dynamics365ForSales snaps" -ForegroundColor White
Write-Host "  ✓ Extraction of Filter Condition details" -ForegroundColor White
Write-Host "  ✓ Extraction of Query Parameters details" -ForegroundColor White
Write-Host "  ✓ Extraction of Output Attributes details" -ForegroundColor White
Write-Host "  ✓ Extraction of Order By details" -ForegroundColor White
Write-Host "  ✓ JSON parsing for complex property values" -ForegroundColor White
Write-Host "  ✓ Styled HTML display with blue highlight box" -ForegroundColor White
Write-Host "  ✓ Detailed logging for debugging" -ForegroundColor White
Write-Host ""
Write-Host "Dynamics365ForSales snaps will now show their configuration details" -ForegroundColor Green
Write-Host "in a special highlighted section in the generated documentation." -ForegroundColor Green
