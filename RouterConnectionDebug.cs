using System;
using System.Collections.Generic;
using System.Linq;

namespace SnapAnalyser
{
    class RouterConnectionDebug
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Router Connection Debug ===");
            
            // Let's create a test that matches your exact scenario
            Console.WriteLine("Creating test router with conditions...");
            
            // Create router snap similar to your "Route Rent Officer"
            var routerSnap = new SnapNode
            {
                Id = "router-rent-officer",
                Label = "Route Rent Officer", 
                Type = "Flow Router",
                Category = SnapCategory.FlowControl,
                Properties = new Dictionary<string, string>
                {
                    {"routingConditions", "[$RentOfficer != null, $RentOfficer == null]"}
                },
                // Let's check if OutputConnections is the issue
                OutputConnections = new List<SnapConnection>
                {
                    new SnapConnection 
                    { 
                        SourceId = "router-rent-officer", 
                        TargetId = "exit-target-1", 
                        SourceViewId = null // This is often the case in real data
                    },
                    new SnapConnection 
                    { 
                        SourceId = "router-rent-officer", 
                        TargetId = "exit-target-2", 
                        SourceViewId = "" // Or empty string
                    }
                },
                InputConnections = new List<SnapConnection>()
            };
            
            // Create target snaps
            var allSnaps = new List<SnapNode>
            {
                routerSnap,
                new SnapNode
                {
                    Id = "exit-target-1",
                    Label = "Exit Snap A",
                    Type = "Exit",
                    Category = SnapCategory.FlowControl,
                    InputConnections = new List<SnapConnection>(),
                    OutputConnections = new List<SnapConnection>()
                },
                new SnapNode
                {
                    Id = "exit-target-2",
                    Label = "Exit Snap B", 
                    Type = "Exit",
                    Category = SnapCategory.FlowControl,
                    InputConnections = new List<SnapConnection>(),
                    OutputConnections = new List<SnapConnection>()
                }
            };
            
            Console.WriteLine($"Router has {routerSnap.OutputConnections.Count} output connections");
            for (int i = 0; i < routerSnap.OutputConnections.Count; i++)
            {
                var conn = routerSnap.OutputConnections[i];
                Console.WriteLine($"  Connection {i}: SourceId={conn.SourceId}, TargetId={conn.TargetId}, SourceViewId='{conn.SourceViewId}'");
            }
            
            Console.WriteLine($"Available target snaps: {string.Join(", ", allSnaps.Where(s => s.Id != routerSnap.Id).Select(s => s.Label))}");
            
            // Test the router configuration generator
            var generator = new FlowControlConfigurationGenerator();
            
            Console.WriteLine("\nGenerating router configuration...");
            string result = generator.GenerateRouterConfiguration(routerSnap, allSnaps);
            
            Console.WriteLine("\n=== RESULT ===");
            Console.WriteLine(result);
            
            // Let's also manually test the GetConnectedSnapsForOutput method using reflection
            Console.WriteLine("\n=== MANUAL DEBUG ===");
            var method = typeof(FlowControlConfigurationGenerator).GetMethod("GetConnectedSnapsForOutput", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (method != null)
            {
                for (int outputIndex = 0; outputIndex < 2; outputIndex++)
                {
                    var connectedSnaps = (List<object>)method.Invoke(generator, 
                        new object[] { routerSnap, allSnaps, outputIndex, $"output{outputIndex}" });
                    
                    Console.WriteLine($"Output {outputIndex}: Found {connectedSnaps.Count} connected snaps");
                    foreach (var snap in connectedSnaps)
                    {
                        var labelProp = snap.GetType().GetProperty("Label");
                        var label = labelProp?.GetValue(snap);
                        Console.WriteLine($"  - {label}");
                    }
                }
            }
            else
            {
                Console.WriteLine("Could not access GetConnectedSnapsForOutput method via reflection");
            }
            
            // Create HTML debug output
            var htmlContent = $@"<!DOCTYPE html>
<html>
<head>
    <title>Router Connection Debug Results</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .debug-section {{ border: 1px solid #ddd; margin: 10px 0; padding: 15px; }}
        pre {{ background: #f5f5f5; padding: 10px; border-radius: 3px; }}
        .error {{ color: red; font-weight: bold; }}
        .success {{ color: green; font-weight: bold; }}
    </style>
</head>
<body>
    <h1>Router Connection Debug Results</h1>
    <p>Generated: {DateTime.Now}</p>
    
    <div class='debug-section'>
        <h2>Router Configuration</h2>
        <p><strong>Router:</strong> {routerSnap.Label}</p>
        <p><strong>Output Connections:</strong> {routerSnap.OutputConnections.Count}</p>
        <ul>
            {string.Join("", routerSnap.OutputConnections.Select((conn, i) => 
                $"<li>Connection {i}: {conn.SourceId} → {conn.TargetId} (SourceViewId: '{conn.SourceViewId}')</li>"))}
        </ul>
    </div>
    
    <div class='debug-section'>
        <h2>Target Snaps</h2>
        <ul>
            {string.Join("", allSnaps.Where(s => s.Id != routerSnap.Id).Select(s => 
                $"<li>{s.Label} (ID: {s.Id})</li>"))}
        </ul>
    </div>
    
    <div class='debug-section'>
        <h2>Generated Router Configuration</h2>
        {result}
    </div>
    
    <div class='debug-section'>
        <h2>Issue Analysis</h2>
        <p>If ""No connected snaps"" appears above, the issue is likely:</p>
        <ul>
            <li>Router OutputConnections collection is empty or null</li>
            <li>Target snap IDs don't match the connection TargetIds</li>
            <li>The GetConnectedSnapsForOutput method logic needs adjustment</li>
        </ul>
    </div>
    
</body>
</html>";
            
            System.IO.File.WriteAllText("router_debug_results.html", htmlContent);
            Console.WriteLine("\nDebug results saved to router_debug_results.html");
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
