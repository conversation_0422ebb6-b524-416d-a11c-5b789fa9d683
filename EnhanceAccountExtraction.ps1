# Enhanced PowerShell script to improve account name extraction from nested JSON

$filePath = "DocumentationGenerator.cs"

Write-Host "Enhancing account name extraction to parse nested JSON structure..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Find the existing GetDatabaseConnectionInfo method and enhance the account parsing section
$oldAccountParsing = @'
            // First, look for the nested account structure
            if (snap.Properties.ContainsKey("account"))
            {
                try
                {
                    string accountJson = snap.Properties["account"];
                    Console.WriteLine($"[DB-CONNECTION] Found account property, parsing JSON: {accountJson}");
                    
                    var accountObj = JObject.Parse(accountJson);
                    
                    // Navigate through the nested structure: account -> account_ref -> value -> label -> value
                    var accountRef = accountObj["account_ref"];
                    if (accountRef != null)
                    {
                        var valueObj = accountRef["value"];
                        if (valueObj != null)
                        {
                            var labelObj = valueObj["label"];
                            if (labelObj != null)
                            {
                                var accountName = labelObj["value"]?.ToString();
                                if (!string.IsNullOrEmpty(accountName))
                                {
                                    Console.WriteLine($"[DB-CONNECTION] Found account name from nested JSON: {accountName}");
                                    return accountName;
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[DB-CONNECTION] Error parsing account JSON: {ex.Message}");
                }
            }'@

$newAccountParsing = @'
            // First, look for the nested account structure
            if (snap.Properties.ContainsKey("account"))
            {
                try
                {
                    string accountJson = snap.Properties["account"];
                    Console.WriteLine($"[DB-CONNECTION] Found account property, parsing JSON (length: {accountJson.Length}): {accountJson.Substring(0, Math.Min(200, accountJson.Length))}...");
                    
                    var accountObj = JObject.Parse(accountJson);
                    Console.WriteLine($"[DB-CONNECTION] Parsed account JSON object successfully");
                    
                    // Navigate through the nested structure: account -> account_ref -> value -> label -> value
                    var accountRef = accountObj["account_ref"];
                    Console.WriteLine($"[DB-CONNECTION] account_ref found: {accountRef != null}");
                    if (accountRef != null)
                    {
                        var valueObj = accountRef["value"];
                        Console.WriteLine($"[DB-CONNECTION] account_ref.value found: {valueObj != null}");
                        if (valueObj != null)
                        {
                            var labelObj = valueObj["label"];
                            Console.WriteLine($"[DB-CONNECTION] account_ref.value.label found: {labelObj != null}");
                            if (labelObj != null)
                            {
                                var accountName = labelObj["value"]?.ToString();
                                Console.WriteLine($"[DB-CONNECTION] account_ref.value.label.value: '{accountName}'");
                                if (!string.IsNullOrEmpty(accountName))
                                {
                                    Console.WriteLine($"[DB-CONNECTION] SUCCESS: Found account name from nested JSON: {accountName}");
                                    return accountName;
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[DB-CONNECTION] Error parsing account JSON: {ex.Message}");
                    Console.WriteLine($"[DB-CONNECTION] Exception details: {ex.ToString()}");
                }
            }
            
            // Also check for account in other possible property names
            foreach (var prop in snap.Properties)
            {
                if (prop.Key.ToLower().Contains("account") && prop.Value.Contains("account_ref"))
                {
                    try
                    {
                        Console.WriteLine($"[DB-CONNECTION] Found potential account JSON in property '{prop.Key}': {prop.Value.Substring(0, Math.Min(200, prop.Value.Length))}...");
                        var accountObj = JObject.Parse(prop.Value);
                        var accountRef = accountObj["account_ref"];
                        if (accountRef != null)
                        {
                            var valueObj = accountRef["value"];
                            if (valueObj != null)
                            {
                                var labelObj = valueObj["label"];
                                if (labelObj != null)
                                {
                                    var accountName = labelObj["value"]?.ToString();
                                    if (!string.IsNullOrEmpty(accountName))
                                    {
                                        Console.WriteLine($"[DB-CONNECTION] SUCCESS: Found account name from property '{prop.Key}': {accountName}");
                                        return accountName;
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"[DB-CONNECTION] Error parsing account JSON from property '{prop.Key}': {ex.Message}");
                    }
                }
            }'@

# Replace the old account parsing with the enhanced version
$content = $content.Replace($oldAccountParsing, $newAccountParsing)

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Account extraction enhanced with better JSON parsing and debugging!" -ForegroundColor Green
Write-Host "Now includes comprehensive debug logging to trace the JSON parsing process." -ForegroundColor Yellow
