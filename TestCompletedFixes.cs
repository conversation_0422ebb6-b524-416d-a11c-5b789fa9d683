using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SnapAnalyser;

namespace TestCompletedFixes
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("Testing Completed Fixes");
            Console.WriteLine("=======================");

            // Test 1: Cache Expiry Functionality
            Console.WriteLine("\n1. Testing Cache Expiry Functionality:");
            await TestCacheExpiry();

            // Test 2: Mapping Fix (AI Content Leakage Prevention)
            Console.WriteLine("\n2. Testing Mapping Fix:");
            TestMappingFix();

            Console.WriteLine("\n✅ All tests completed successfully!");
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        static async Task TestCacheExpiry()
        {
            try
            {
                // Create a cache with very short expiry (2 seconds for testing)
                var cache = new DescriptionCache(null, TimeSpan.FromSeconds(2));
                await cache.InitializeAsync();

                // Create a test snap
                var testSnap = new SnapNode
                {
                    Type = "TestSnap",
                    Label = "Test Label",
                    Properties = new Dictionary<string, string> { { "test", "value" } }
                };

                // Store a description
                string testDescription = "This is a test description for expiry";
                cache.StoreDescription(testSnap, testDescription);
                
                // Verify it can be retrieved immediately
                if (cache.TryGetDescription(testSnap, out string retrievedDescription))
                {
                    Console.WriteLine($"  ✅ Successfully stored and retrieved: {retrievedDescription}");
                }
                else
                {
                    Console.WriteLine("  ❌ Failed to retrieve just-stored description");
                    return;
                }

                Console.WriteLine("  ⏳ Waiting 3 seconds for cache to expire...");
                await Task.Delay(3000);

                // Try to retrieve after expiry
                if (cache.TryGetDescription(testSnap, out string expiredDescription))
                {
                    Console.WriteLine($"  ❌ Retrieved expired description: {expiredDescription}");
                }
                else
                {
                    Console.WriteLine("  ✅ Expired description was correctly removed from cache");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ Cache expiry test failed: {ex.Message}");
            }
        }

        static void TestMappingFix()
        {
            try
            {
                // Create a test snap with properties that might trigger the old bug
                var testSnap = new SnapNode
                {
                    Type = "datatransform",
                    Label = "Test Mapper",
                    Properties = new Dictionary<string, string>
                    {
                        { "mappingDefinition", "Valid mapping content" },
                        { "transformations", "Valid transformation content" },
                        { "expression.field1", "$input.field1" },
                        { "ai_generated_description", "This is AI generated content that should be filtered out" },
                        { "other.expression", "Some expression that might have been picked up before" }
                    }
                };

                // Simulate the extraction logic (simplified version of what's in DocumentationGenerator)
                var legitimateMappingKeys = new string[] {
                    "transformations", "transformersList", "mapDefinition", 
                    "mappingDefinition", "fieldMapping", "transformationRules"
                };

                var mappingProps = new List<KeyValuePair<string, string>>();

                foreach (var prop in testSnap.Properties)
                {
                    // Skip empty values
                    if (string.IsNullOrEmpty(prop.Value))
                        continue;

                    // Check for legitimate mapping properties
                    if (legitimateMappingKeys.Any(key => prop.Key.Equals(key, StringComparison.OrdinalIgnoreCase)))
                    {
                        mappingProps.Add(prop);
                        continue;
                    }

                    // Check for .expression properties but exclude AI-generated content
                    if (prop.Key.Contains(".expression") && 
                        !prop.Value.Contains("AI") && 
                        !prop.Value.Contains("generated") &&
                        !prop.Key.Contains("ai_"))
                    {
                        mappingProps.Add(prop);
                    }
                }

                Console.WriteLine($"  ✅ Found {mappingProps.Count} legitimate mapping properties:");
                foreach (var prop in mappingProps)
                {
                    Console.WriteLine($"    - {prop.Key}: {prop.Value}");
                }

                // Verify AI content was filtered out
                bool hasAIContent = mappingProps.Any(p => 
                    p.Value.Contains("AI") || 
                    p.Value.Contains("generated") || 
                    p.Key.Contains("ai_"));

                if (!hasAIContent)
                {
                    Console.WriteLine("  ✅ AI-generated content successfully filtered out");
                }
                else
                {
                    Console.WriteLine("  ❌ AI-generated content was not properly filtered");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ Mapping fix test failed: {ex.Message}");
            }
        }
    }
}
