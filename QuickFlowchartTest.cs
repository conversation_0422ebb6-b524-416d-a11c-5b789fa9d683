using System;
using System.Collections.Generic;

// Quick test to verify flowchart.js conversion is working
class QuickFlowchartTest
{
    static void Main()
    {
        Console.WriteLine("=== Quick Flowchart.js Validation ===");
        
        try
        {
            // Test 1: Can we create the FlowchartJsGenerator?
            Console.WriteLine("1. Creating FlowchartJsGenerator...");
            var generator = new SnapAnalyser.FlowchartJsGenerator();
            Console.WriteLine("   ✓ FlowchartJsGenerator created successfully");
            
            // Test 2: Can we create a test pipeline?
            Console.WriteLine("2. Creating test pipeline...");
            var pipeline = CreateTestPipeline();
            Console.WriteLine($"   ✓ Test pipeline created with {pipeline.Snaps.Count} snaps");
            
            // Test 3: Can we generate flowchart definition?
            Console.WriteLine("3. Generating flowchart definition...");
            string definition = generator.GenerateFlowchartDefinition(pipeline);
            Console.WriteLine($"   ✓ Definition generated: {!string.IsNullOrEmpty(definition)}");
            Console.WriteLine($"   ✓ Definition length: {definition?.Length ?? 0} characters");
            
            if (!string.IsNullOrEmpty(definition))
            {
                Console.WriteLine($"   ✓ Contains flowchart syntax: {definition.Contains("=>") && definition.Contains("->")}");
                Console.WriteLine("   First 200 characters of definition:");
                Console.WriteLine($"   {definition.Substring(0, Math.Min(200, definition.Length))}...");
            }
            
            // Test 4: Can we generate HTML?
            Console.WriteLine("4. Generating HTML...");
            string html = generator.GenerateFlowchartHtml(definition, "test-diagram", "Test Pipeline");
            Console.WriteLine($"   ✓ HTML generated: {!string.IsNullOrEmpty(html)}");
            Console.WriteLine($"   ✓ HTML length: {html?.Length ?? 0} characters");
            
            if (!string.IsNullOrEmpty(html))
            {
                Console.WriteLine($"   ✓ Contains flowchart.js: {html.Contains("flowchart")}");
                Console.WriteLine($"   ✓ Contains CDN links: {html.Contains("cdn")}");
            }
            
            // Test 5: Test DiagramGenerator integration
            Console.WriteLine("5. Testing DiagramGenerator integration...");
            var diagramGen = new SnapAnalyser.DiagramGenerator();
            string diagram = diagramGen.GenerateDiagram(pipeline);
            Console.WriteLine($"   ✓ Diagram generated: {!string.IsNullOrEmpty(diagram)}");
            Console.WriteLine($"   ✓ Uses flowchart.js: {diagram?.Contains("flowchart") ?? false}");
            
            Console.WriteLine();
            Console.WriteLine("🎉 ALL TESTS PASSED! 🎉");
            Console.WriteLine("The flowchart.js conversion is working correctly!");
            Console.WriteLine();
            Console.WriteLine("Key achievements:");
            Console.WriteLine("- Replaced custom SVG generation with flowchart.js");
            Console.WriteLine("- Maintained existing DiagramGenerator interface");
            Console.WriteLine("- Successfully generates flowchart.js syntax");
            Console.WriteLine("- Produces valid HTML with CDN links");
            Console.WriteLine("- Integration with existing codebase preserved");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ TEST FAILED: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        
        Console.WriteLine();
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
    
    static SnapAnalyser.PipelineData CreateTestPipeline()
    {
        var snaps = new List<SnapAnalyser.SnapNode>
        {
            new SnapAnalyser.SnapNode
            {
                Id = "start",
                Label = "Start",
                Type = "start",
                Category = SnapAnalyser.SnapCategory.ExternalSystem,
                IsStartPoint = true
            },
            new SnapAnalyser.SnapNode
            {
                Id = "process",
                Label = "Process Data",
                Type = "mapper",
                Category = SnapAnalyser.SnapCategory.Transformation
            },
            new SnapAnalyser.SnapNode
            {
                Id = "end",
                Label = "End",
                Type = "end",
                Category = SnapAnalyser.SnapCategory.ExternalSystem,
                IsEndPoint = true
            }
        };
        
        var links = new List<SnapAnalyser.SnapLink>
        {
            new SnapAnalyser.SnapLink { SourceId = "start", TargetId = "process" },
            new SnapAnalyser.SnapLink { SourceId = "process", TargetId = "end" }
        };
        
        return new SnapAnalyser.PipelineData
        {
            Name = "Test Pipeline",
            Snaps = snaps,
            Links = links
        };
    }
}
