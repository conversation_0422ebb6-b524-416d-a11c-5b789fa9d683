# Simple script to add more detailed logging to account extraction

$filePath = "DocumentationGenerator.cs"

Write-Host "Adding detailed account extraction logging..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Add detailed logging right after the account JSON parsing line
$oldLine = 'Console.WriteLine($"[DB-CONNECTION] Found account property, parsing JSON: {accountJson}");'
$newLine = @"
Console.WriteLine(`$"[DB-CONNECTION] Found account property, parsing JSON (length: {accountJson.Length})");
                    Console.WriteLine(`$"[DB-CONNECTION] Account JSON content: {accountJson}");
"@

$content = $content.Replace($oldLine, $newLine)

# Also add logging for when account is not found
$oldNotFound = 'Console.WriteLine($"[DB-CONNECTION] Error parsing account JSON: {ex.Message}");'
$newNotFound = @"
Console.WriteLine(`$"[DB-CONNECTION] Error parsing account JSON: {ex.Message}");
                    Console.WriteLine(`$"[DB-CONNECTION] JSON that failed: {accountJson}");
"@

$content = $content.Replace($oldNotFound, $newNotFound)

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Added detailed account extraction logging!" -ForegroundColor Green
