using System;
using System.Collections.Generic;
using SnapAnalyser;

class TestRedundantSection
{
    static void Main()
    {
        Console.WriteLine("Testing Redundant Section Removal");
        Console.WriteLine("================================");
        
        // Create a test mapper snap
        var mapperSnap = new SnapNode
        {
            Id = "test-mapper",
            Label = "Test DataMapper",
            Type = "com.snaplogic.snaps.transform.datatransform.DataMapper",
            Category = SnapCategory.Transformation,
            Properties = new Dictionary<string, string>
            {
                {"customer.name.expression", "$input.customer_name"},
                {"order.total.expression", "$input.amount * 1.2"}
            }
        };
        
        // Test with hasMappings = false to see if we get the redundant section
        Console.WriteLine("Testing mapper with hasMappings = false:");
        string result = SnapBestPractices.GetSnapBestPractices(mapperSnap, new List<SnapNode> { mapperSnap }, hasMappings: false);
        
        // Check if the redundant section appears
        bool hasRedundantSection = result.Contains("Best Practices for this Transformation Snap:");
        
        Console.WriteLine($"Contains redundant section: {hasRedundantSection}");
        
        if (hasRedundantSection)
        {
            Console.WriteLine("❌ FAIL: Redundant section still appears for mapper snaps");
            
            // Show where it appears in the output
            int index = result.IndexOf("Best Practices for this Transformation Snap:");
            if (index >= 0)
            {
                int startContext = Math.Max(0, index - 100);
                int endContext = Math.Min(result.Length, index + 200);
                Console.WriteLine("\nContext around redundant section:");
                Console.WriteLine("---");
                Console.WriteLine(result.Substring(startContext, endContext - startContext));
                Console.WriteLine("---");
            }
        }
        else
        {
            Console.WriteLine("✅ PASS: No redundant section for mapper snaps");
        }
        
        // Also test with a non-mapper transformation snap to ensure it still works
        var genericTransformSnap = new SnapNode
        {
            Id = "test-generic",
            Label = "Test Generic Transform",
            Type = "com.snaplogic.snaps.transform.generictransform",
            Category = SnapCategory.Transformation,
            Properties = new Dictionary<string, string>
            {
                {"config.setting", "some_value"}
            }
        };
        
        Console.WriteLine("\nTesting non-mapper transformation:");
        string genericResult = SnapBestPractices.GetSnapBestPractices(genericTransformSnap, new List<SnapNode> { genericTransformSnap }, hasMappings: false);
        
        bool genericHasSection = genericResult.Contains("Best Practices for this Transformation Snap:");
        Console.WriteLine($"Generic transform has section: {genericHasSection}");
        
        if (genericHasSection)
        {
            Console.WriteLine("✅ PASS: Generic transformation snaps still get the section");
        }
        else
        {
            Console.WriteLine("⚠️  NOTE: Generic transformation snaps no longer get the section");
        }
        
        Console.WriteLine("\nTest completed!");
    }
}
