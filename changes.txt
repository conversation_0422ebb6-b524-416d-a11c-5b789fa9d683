// This is a temporary file to prepare our changes.
// For JSON Formatter snap:
details.AppendLine("<p><strong>Best Practices for JSON Formatter Snaps:</strong></p>");
details.AppendLine("<ul>");
details.AppendLine("<li>Specify whether to format as a JSON string or binary data.</li>");
details.AppendLine("<li>Use the 'Pretty print' option for human-readable output during development or for logging.</li>");
details.AppendLine("</ul>");
// No Example Usage section for JSON Formatter snap types as per requirements

// For Script snap:
details.AppendLine("<p><strong>Best Practices for Script Snaps:</strong></p>");
details.AppendLine("<ul>");
details.AppendLine("<li>Use Script snaps for logic that is too complex or not possible with standard snaps.</li>");
details.AppendLine("<li>Write efficient and readable code (typically JavaScript). Add comments for complex sections.</li>");
details.AppendLine("<li>Handle errors within the script using try-catch blocks and write to the error view if necessary.</li>");
details.AppendLine("<li>Avoid very long scripts; consider custom snap development or breaking logic into multiple snaps.</li>");
details.AppendLine("<li>Be mindful of script execution time, as it can impact pipeline performance.</li>");
details.AppendLine("</ul>");
// No Example Usage section for Script snap types as per requirements

// For Filter snap:
details.AppendLine("<p><strong>Best Practices for Filter Snaps:</strong></p>");
details.AppendLine("<ul>");
details.AppendLine("<li>Write clear and concise filter expressions.</li>");
details.AppendLine("<li>Test expressions with various input data to ensure correctness.</li>");
details.AppendLine("<li>Avoid overly complex expressions in a single Filter; consider multiple Filters or a Script snap.</li>");
details.AppendLine("</ul>");
// No Example Usage section for Filter snap types as per requirements

// For Copy snap:
details.AppendLine("<p><strong>Best Practices for Copy Snaps:</strong></p>");
details.AppendLine("<ul><li>Use Copy snaps to duplicate document streams for parallel processing paths.</li><li>Be aware that this creates full duplicates of documents, which can increase memory usage.</li></ul>");
// No Example Usage section for Copy snap types as per requirements

// For Pipeline Execute snap:
details.AppendLine("<p><strong>Best Practices for Pipeline Execute Snaps:</strong></p>");
details.AppendLine("<ul><li>Pass only necessary parameters to the child pipeline.</li><li>Ensure the child pipeline is designed to handle the inputs and produce expected outputs.</li><li>Manage error handling for child pipeline failures.</li></ul>");
// No Example Usage section for Pipeline Execute snap types as per requirements

// For Generic Transformation:
details.AppendLine("<p><strong>Best Practices for this Transformation Snap:</strong></p>");
details.AppendLine("<ul><li>Understand the specific transformation logic this snap applies.</li><li>Validate output against expected schema and values.</li></ul>");
// No Example Usage section for generic transformation snap types as per requirements
