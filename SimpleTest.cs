using System;
using System.Collections.Generic;
using System.Linq;

// Simple test to verify redundant section removal
class SimpleTest
{
    static void Main()
    {
        Console.WriteLine("=== Testing Redundant Section Removal ===");
        
        // Since we can't easily build the full project due to file locks,
        // let's manually test the logic by recreating the conditions
        
        // Test mapper snap types
        string[] mapperTypes = {
            "com.snaplogic.snaps.transform.datatransform.DataMapper",
            "com.snaplogic.snaps.mapper.JSONMapper",
            "com.snaplogic.snaps.map.XMLMapper"
        };
        
        string[] nonMapperTypes = {
            "com.snaplogic.snaps.transform.generictransform",
            "com.snaplogic.snaps.transform.aggregator",
            "com.snaplogic.snaps.transform.sorter"
        };
        
        Console.WriteLine("Testing mapper snap type detection:");
        foreach (string snapType in mapperTypes)
        {
            bool isMapper = snapType.ToLower().Contains("map") || snapType.ToLower().Contains("datatransform");
            bool shouldShowSection = !isMapper; // Our fix logic
            
            Console.WriteLine($"  {snapType}");
            Console.WriteLine($"    Is detected as mapper: {isMapper}");
            Console.WriteLine($"    Would show redundant section: {shouldShowSection}");
            
            if (shouldShowSection)
            {
                Console.WriteLine("    ❌ PROBLEM: Mapper would still show redundant section!");
            }
            else
            {
                Console.WriteLine("    ✅ GOOD: Mapper would NOT show redundant section");
            }
            Console.WriteLine();
        }
        
        Console.WriteLine("Testing non-mapper transformation snap types:");
        foreach (string snapType in nonMapperTypes)
        {
            bool isMapper = snapType.ToLower().Contains("map") || snapType.ToLower().Contains("datatransform");
            bool shouldShowSection = !isMapper; // Our fix logic
            
            Console.WriteLine($"  {snapType}");
            Console.WriteLine($"    Is detected as mapper: {isMapper}");
            Console.WriteLine($"    Would show transformation section: {shouldShowSection}");
            
            if (shouldShowSection)
            {
                Console.WriteLine("    ✅ GOOD: Non-mapper would show transformation section");
            }
            else
            {
                Console.WriteLine("    ⚠️  NOTE: Non-mapper would NOT show transformation section");
            }
            Console.WriteLine();
        }
        
        Console.WriteLine("=== Test Complete ===");
        Console.WriteLine("\nPress any key to continue...");
        Console.ReadKey();
    }
}
