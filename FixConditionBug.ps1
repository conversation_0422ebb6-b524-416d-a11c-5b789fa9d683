# PowerShell script to fix the critical bug where connection info is never shown
# The condition was changed to "if (false)" which means NO snaps ever show connection info

$filePath = "DocumentationGenerator.cs"

Write-Host "Fixing critical condition bug..." -ForegroundColor Red

$content = Get-Content $filePath -Raw

# Fix the critical bug - change "if (false)" to proper condition
$buggyCondition = 'if \(false\) // Show accounts for any snap that has an account'
$fixedCondition = @'
// Check if this snap might have account/connection information
                bool mightHaveAccountInfo = 
                    snap.Category == SnapCategory.Database ||  // Database snaps
                    (snap.Type?.ToLower().Contains("dynamics365forsales") == true) ||  // Dynamics365 snaps
                    snap.Properties.ContainsKey("account") ||  // Has account property
                    snap.Properties.Any(p => p.Key.ToLower().Contains("connect") ||   // Connection properties
                                           p.Key.ToLower().Contains("database") || 
                                           p.Key.ToLower().Contains("server") ||
                                           p.Key.ToLower().Contains("instance") ||
                                           p.Key.ToLower().Contains("organization"));
                
                if (mightHaveAccountInfo)
'@

if ($content -match $buggyCondition) {
    $content = $content -replace $buggyCondition, $fixedCondition
    Write-Host "✅ FIXED CRITICAL BUG: Changed 'if (false)' to proper account detection condition" -ForegroundColor Green
} else {
    Write-Host "❌ Could not find 'if (false)' condition" -ForegroundColor Red
}

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "✅ Critical bug fix applied!" -ForegroundColor Green
Write-Host "Connection info will now be shown for:" -ForegroundColor Cyan
Write-Host "  • Database snaps" -ForegroundColor White
Write-Host "  • Dynamics365ForSales snaps" -ForegroundColor White  
Write-Host "  • Any snap with account property" -ForegroundColor White
Write-Host "  • Any snap with connection/server/database/instance/organization properties" -ForegroundColor White
