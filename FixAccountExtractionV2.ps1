# Enhanced PowerShell script to fix account extraction by accessing raw snap JSON

$filePath = "DocumentationGenerator.cs"

Write-Host "Fixing account extraction to access raw snap JSON structure..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# First, find the GetDatabaseConnectionInfo method signature and add a rawSnap parameter
$oldSignature = 'private string GetDatabaseConnectionInfo(SnapNode snap)'
$newSignature = 'private string GetDatabaseConnectionInfo(SnapNode snap, JObject rawSnap = null)'

$content = $content.Replace($oldSignature, $newSignature)

# Now replace the account extraction logic
$oldAccountSection = @'
            // First, look for the nested account structure
            if (snap.Properties.ContainsKey("account"))
            {
                try
                {
                    string accountJson = snap.Properties["account"];
                    Console.WriteLine($"[DB-CONNECTION] Found account property, parsing JSON (length: {accountJson.Length})");
                    Console.WriteLine($"[DB-CONNECTION] Account JSON content: {accountJson}");
                    
                    var accountObj = JObject.Parse(accountJson);
                    
                    // Navigate through the nested structure: account -> account_ref -> value -> label -> value
                    var accountRef = accountObj["account_ref"];
                    if (accountRef != null)
                    {
                        var valueObj = accountRef["value"];
                        if (valueObj != null)
                        {
                            var labelObj = valueObj["label"];
                            if (labelObj != null)
                            {
                                var accountName = labelObj["value"]?.ToString();
                                if (!string.IsNullOrEmpty(accountName))
                                {
                                    Console.WriteLine($"[DB-CONNECTION] Found account name from nested JSON: {accountName}");
                                    return accountName;
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[DB-CONNECTION] Error parsing account JSON: {ex.Message}");
                    Console.WriteLine($"[DB-CONNECTION] JSON parsing failed - see details above");
                }
            }'@

$newAccountSection = @'
            // First, try to get account from raw snap JSON structure
            if (rawSnap != null)
            {
                try
                {
                    var accountNode = rawSnap["account"];
                    if (accountNode != null)
                    {
                        Console.WriteLine($"[DB-CONNECTION] Found account node in raw snap JSON");
                        var accountRef = accountNode["account_ref"];
                        if (accountRef != null)
                        {
                            var valueObj = accountRef["value"];
                            if (valueObj != null)
                            {
                                var labelObj = valueObj["label"];
                                if (labelObj != null)
                                {
                                    var accountName = labelObj["value"]?.ToString();
                                    if (!string.IsNullOrEmpty(accountName))
                                    {
                                        Console.WriteLine($"[DB-CONNECTION] SUCCESS: Found account name from raw JSON: {accountName}");
                                        return accountName;
                                    }
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[DB-CONNECTION] Error parsing account from raw JSON: {ex.Message}");
                }
            }

            // Fallback: look for the nested account structure in flattened properties
            if (snap.Properties.ContainsKey("account"))
            {
                try
                {
                    string accountJson = snap.Properties["account"];
                    Console.WriteLine($"[DB-CONNECTION] Found account property, parsing JSON (length: {accountJson.Length})");
                    Console.WriteLine($"[DB-CONNECTION] Account JSON content: {accountJson}");
                    
                    var accountObj = JObject.Parse(accountJson);
                    
                    // Navigate through the nested structure: account -> account_ref -> value -> label -> value
                    var accountRef = accountObj["account_ref"];
                    if (accountRef != null)
                    {
                        var valueObj = accountRef["value"];
                        if (valueObj != null)
                        {
                            var labelObj = valueObj["label"];
                            if (labelObj != null)
                            {
                                var accountName = labelObj["value"]?.ToString();
                                if (!string.IsNullOrEmpty(accountName))
                                {
                                    Console.WriteLine($"[DB-CONNECTION] Found account name from nested JSON: {accountName}");
                                    return accountName;
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[DB-CONNECTION] Error parsing account JSON: {ex.Message}");
                    Console.WriteLine($"[DB-CONNECTION] JSON parsing failed - see details above");
                }
            }'@

$content = $content.Replace($oldAccountSection, $newAccountSection)

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Enhanced account extraction to use raw snap JSON!" -ForegroundColor Green
Write-Host "Now need to update calls to pass rawSnap parameter..." -ForegroundColor Yellow
