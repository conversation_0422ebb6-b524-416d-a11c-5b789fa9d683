@echo off
echo Making changes to fix syntax in SnapBestPractices.cs...

REM First, make a backup
copy "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs" "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs.orig"

REM Fix the JSON Formatter section
powershell -Command "(Get-Content -Path 'c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs') -replace 'details.AppendLine\(\"</ul>\"\);\r?\n}', 'details.AppendLine(\"</ul>\");// No Example Usage section for JSON Formatter snap types as per requirements\r\n}'" > "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs.new"

REM Fix the Script snap section 
powershell -Command "(Get-Content -Path 'c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs.new') -replace 'details.AppendLine\(\"</ul>\"\);\r?\ndetails.AppendLine\(\"<p><strong>Example Usage:</strong></p>\"\);', 'details.AppendLine(\"</ul>\");\r\n// No Example Usage section for Script snap types as per requirements'" > "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs.new2"

REM Fix the Filter snap section 
powershell -Command "(Get-Content -Path 'c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs.new2') -replace 'details.AppendLine\(\"<li>Avoid overly complex expressions in a single Filter; consider multiple Filters or a Script snap.</li>\"\);\r?\ndetails.AppendLine\(\"</ul>\"\);', 'details.AppendLine(\"<li>Avoid overly complex expressions in a single Filter; consider multiple Filters or a Script snap.</li>\");\r\ndetails.AppendLine(\"</ul>\");\r\n// No Example Usage section for Filter snap types as per requirements'" > "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs.new3"

REM Fix the Copy snap section 
powershell -Command "(Get-Content -Path 'c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs.new3') -replace 'details.AppendLine\(\"<ul><li>Use Copy snaps to duplicate document streams for parallel processing paths.</li><li>Be aware that this creates full duplicates of documents, which can increase memory usage.</li></ul>\"\);\r?\ndetails.AppendLine\(\"<p><strong>Example Usage:</strong></p>\"\);\r?\ndetails.AppendLine\(\"<p>This Copy snap duplicates each incoming document to its multiple output views, allowing identical data to flow through different processing branches simultaneously.</p>\"\);', 'details.AppendLine(\"<ul><li>Use Copy snaps to duplicate document streams for parallel processing paths.</li><li>Be aware that this creates full duplicates of documents, which can increase memory usage.</li></ul>\");\r\n// No Example Usage section for Copy snap types as per requirements'" > "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs.new4"

REM Fix the Pipeline Execute snap section 
powershell -Command "(Get-Content -Path 'c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs.new4') -replace 'details.AppendLine\(\"<ul><li>Pass only necessary parameters to the child pipeline.</li><li>Ensure the child pipeline is designed to handle the inputs and produce expected outputs.</li><li>Manage error handling for child pipeline failures.</li></ul>\"\);\r?\ndetails.AppendLine\(\"<p><strong>Example Usage:</strong></p>\"\);', 'details.AppendLine(\"<ul><li>Pass only necessary parameters to the child pipeline.</li><li>Ensure the child pipeline is designed to handle the inputs and produce expected outputs.</li><li>Manage error handling for child pipeline failures.</li></ul>\");\r\n// No Example Usage section for Pipeline Execute snap types as per requirements'" > "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs.new5"

REM Fix the generic transformation section 
powershell -Command "(Get-Content -Path 'c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs.new5') -replace 'details.AppendLine\(\"<ul><li>Understand the specific transformation logic this snap applies.</li><li>Validate output against expected schema and values.</li></ul>\"\);\r?\ndetails.AppendLine\(\"<p><strong>Example Usage:</strong></p>\"\);', 'details.AppendLine(\"<ul><li>Understand the specific transformation logic this snap applies.</li><li>Validate output against expected schema and values.</li></ul>\");\r\n// No Example Usage section for generic transformation snap types as per requirements'" > "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs.new6"

REM Move the final version to replace the original
copy "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs.new6" "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs"

REM Clean up temporary files
del "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs.new"
del "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs.new2"
del "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs.new3"
del "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs.new4"
del "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs.new5"
del "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\SnapBestPractices.cs.new6"

echo Changes completed.
