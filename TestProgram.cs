using System;
using System.Windows.Forms;

namespace SnapAnalyser
{
    static class TestProgram
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Console.WriteLine("=== Application Startup Test ===");
                
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                Console.WriteLine("Loading configuration...");
                ConfigManager.LoadConfig();
                
                Console.WriteLine("Creating MainForm...");
                var mainForm = new MainForm();
                
                Console.WriteLine("Starting application...");
                Application.Run(mainForm);
                
                Console.WriteLine("Application closed normally.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
                
                MessageBox.Show($"Application failed to start:\n\n{ex.Message}\n\nStack Trace:\n{ex.StackTrace}", 
                               "Startup Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
