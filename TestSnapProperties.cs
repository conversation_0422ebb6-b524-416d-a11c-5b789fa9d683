using System;
using System.IO;
using System.Linq;
using Newtonsoft.Json.Linq;

// Create a minimal test to check actual snap properties from the SLP file
class TestSnapProperties
{
    static void Main()
    {
        string slpPath = @"c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Snap-Pipelines\CH03 - Update Kypera Property Characteristics_2025_05_16.slp";
        
        if (!File.Exists(slpPath))
        {
            Console.WriteLine($"SLP file not found: {slpPath}");
            return;
        }
        
        string jsonContent = File.ReadAllText(slpPath);
        var pipeline = JObject.Parse(jsonContent);
        
        // Find database snaps
        var snaps = pipeline["snap_map"];
        if (snaps != null)
        {
            foreach (var snapProp in snaps.Properties())
            {
                var snap = snapProp.Value;
                var snapType = snap["class_id"]?.ToString();
                var snapLabel = snap["instance_id"]?.ToString();
                
                // Check if it's a database snap
                if (snapType != null && (snapType.Contains("sqlserver") || snapType.Contains("database")))
                {
                    Console.WriteLine($"\n=== DATABASE SNAP: {snapLabel} ===");
                    Console.WriteLine($"Type: {snapType}");
                    
                    // Look at settings
                    var settings = snap["settings"];
                    if (settings != null)
                    {
                        Console.WriteLine("Settings:");
                        foreach (var setting in settings.Properties())
                        {
                            Console.WriteLine($"  {setting.Name}: {setting.Value}");
                        }
                    }
                    
                    // Look at property_map
                    var propertyMap = snap["property_map"];
                    if (propertyMap != null)
                    {
                        Console.WriteLine("Property Map:");
                        foreach (var prop in propertyMap.Properties())
                        {
                            Console.WriteLine($"  {prop.Name}: {prop.Value}");
                        }
                    }
                    
                    Console.WriteLine("=====================================");
                }
            }
        }
    }
}
