using System;
using System.Windows.Forms;

namespace SnapAnalyser
{
    static class GUITest
    {
        [STAThread]
        static void Main()
        {
            try
            {
                MessageBox.Show("Starting application test...", "Debug", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                MessageBox.Show("About to load config...", "Debug", MessageBoxButtons.OK, MessageBoxIcon.Information);
                ConfigManager.LoadConfig();
                
                MessageBox.Show("Config loaded, creating MainForm...", "Debug", MessageBoxButtons.OK, MessageBoxIcon.Information);
                var mainForm = new MainForm();
                
                MessageBox.Show("MainForm created, starting application...", "Debug", MessageBoxButtons.OK, MessageBoxIcon.Information);
                Application.Run(mainForm);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"ERROR: {ex.Message}\n\nStack Trace:\n{ex.StackTrace}", 
                               "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
