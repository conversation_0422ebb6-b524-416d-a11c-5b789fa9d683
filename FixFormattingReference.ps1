# Fix the Formatting reference issue

$filePath = "DocumentationGenerator.cs"

Write-Host "Fixing Formatting reference..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Fix the Formatting reference
$oldFormatting = 'Console.WriteLine($"[DB-CONNECTION-DEBUG]         {accountObj.ToString(Formatting.Indented)}");'
$newFormatting = 'Console.WriteLine($"[DB-CONNECTION-DEBUG]         {accountObj.ToString(Newtonsoft.Json.Formatting.Indented)}");'

if ($content.Contains($oldFormatting)) {
    $content = $content.Replace($oldFormatting, $newFormatting)
    Write-Host "Fixed Formatting reference" -ForegroundColor Yellow
} else {
    Write-Host "Formatting reference not found" -ForegroundColor Red
}

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Fixed Formatting reference!" -ForegroundColor Green
