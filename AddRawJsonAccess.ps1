# Simple script to add raw JSON access to GetDatabaseConnectionInfo method

$filePath = "DocumentationGenerator.cs"

Write-Host "Adding raw JSON access for account extraction..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Step 1: Change the method signature to accept raw snap data
$oldSignature = 'private string GetDatabaseConnectionInfo(SnapNode snap)'
$newSignature = 'private string GetDatabaseConnectionInfo(SnapNode snap, JObject rawSnapJson = null)'

if ($content.Contains($oldSignature)) {
    $content = $content.Replace($oldSignature, $newSignature)
    Write-Host "Updated method signature" -ForegroundColor Yellow
} else {
    Write-Host "Method signature not found or already updated" -ForegroundColor Yellow
}

# Step 2: Add raw JSON account extraction at the beginning of the method
$insertPoint = 'Console.WriteLine($"[DB-CONNECTION] Checking connection info for snap: {snap.Label}, Properties count: {snap.Properties.Count}");'
$newCode = @'
Console.WriteLine($"[DB-CONNECTION] Checking connection info for snap: {snap.Label}, Properties count: {snap.Properties.Count}");
    
    // First try to extract account from raw JSON structure
    if (rawSnapJson != null)
    {
        try
        {
            var accountNode = rawSnapJson["account"];
            if (accountNode != null)
            {
                Console.WriteLine($"[DB-CONNECTION] Found account node in raw JSON");
                var accountRef = accountNode["account_ref"];
                if (accountRef != null)
                {
                    var valueObj = accountRef["value"];
                    if (valueObj != null)
                    {
                        var labelObj = valueObj["label"];
                        if (labelObj != null)
                        {
                            var accountName = labelObj["value"]?.ToString();
                            if (!string.IsNullOrEmpty(accountName))
                            {
                                Console.WriteLine($"[DB-CONNECTION] SUCCESS: Found account name from raw JSON: {accountName}");
                                return accountName;
                            }
                        }
                    }
                }
            }
            else
            {
                Console.WriteLine($"[DB-CONNECTION] No account node found in raw JSON");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[DB-CONNECTION] Error parsing account from raw JSON: {ex.Message}");
        }
    }
    else
    {
        Console.WriteLine($"[DB-CONNECTION] No raw JSON provided for account extraction");
    }
'@

if ($content.Contains($insertPoint)) {
    $content = $content.Replace($insertPoint, $newCode)
    Write-Host "Added raw JSON account extraction" -ForegroundColor Yellow
} else {
    Write-Host "Insert point not found" -ForegroundColor Red
}

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Enhanced GetDatabaseConnectionInfo method with raw JSON access!" -ForegroundColor Green
