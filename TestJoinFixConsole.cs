using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json;

namespace SnapDocumenter
{
    class TestJoinFixConsole
    {
        static void Main(string[] args)
        {
            Console.WriteLine("Testing Join snap enhanced configuration fix...");
            
            try
            {
                // Read an actual pipeline file with Join snaps
                var pipelineFile = @"c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Snap-Pipelines\P01 - Smoke Alarm Attribute CRM_2025_05_21.json";
                
                if (!File.Exists(pipelineFile))
                {
                    Console.WriteLine($"Pipeline file not found: {pipelineFile}");
                    return;
                }
                
                var jsonContent = File.ReadAllText(pipelineFile);
                var pipelineData = JsonConvert.DeserializeObject<dynamic>(jsonContent);
                
                var analyzer = new SlpAnalyzer();
                var snapNodes = analyzer.ExtractSnapNodes(jsonContent);
                
                Console.WriteLine($"Found {snapNodes.Count} snap nodes in pipeline");
                
                var joinSnaps = snapNodes.Where(s => s.SnapType?.ToLower() == "join").ToList();
                Console.WriteLine($"Found {joinSnaps.Count} Join snaps");
                
                if (joinSnaps.Any())
                {
                    var generator = new FlowControlConfigurationGenerator();
                    
                    foreach (var joinSnap in joinSnaps)
                    {
                        Console.WriteLine($"\n--- Testing Join snap: {joinSnap.InstanceId} ---");
                        Console.WriteLine($"Properties count: {joinSnap.Properties.Count}");
                        
                        // Show relevant properties
                        var relevantProps = joinSnap.Properties.Where(p => 
                            p.Key.Contains("joinPaths") || 
                            p.Key.Contains("joinType") || 
                            p.Key.Contains("leftPath") || 
                            p.Key.Contains("rightPath")).ToList();
                        
                        Console.WriteLine("Relevant properties:");
                        foreach (var prop in relevantProps)
                        {
                            Console.WriteLine($"  {prop.Key} = {prop.Value}");
                        }
                        
                        var enhancedConfig = generator.GenerateJoinConfiguration(joinSnap);
                        
                        Console.WriteLine("\nGenerated Enhanced Configuration:");
                        Console.WriteLine("=================================");
                        Console.WriteLine(enhancedConfig);
                        
                        // Check if we're getting meaningful content
                        if (enhancedConfig.Length > 500 && !enhancedConfig.Contains("No join conditions"))
                        {
                            Console.WriteLine("✅ Enhanced configuration appears to be working!");
                        }
                        else
                        {
                            Console.WriteLine("❌ Enhanced configuration may not be extracting data properly");
                        }
                    }
                }
                else
                {
                    Console.WriteLine("No Join snaps found in the pipeline");
                }
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
