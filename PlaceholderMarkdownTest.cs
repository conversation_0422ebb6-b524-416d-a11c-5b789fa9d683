using System;
using System.Collections.Generic;
using System.IO;

namespace SnapAnalyser
{
    class PlaceholderMarkdownTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Testing Placeholder Handling in Markdown Conversion ===");
            
            // Create DocumentationGenerator instance
            var docGen = new DocumentationGenerator();
            
            // Test cases with different markdown formatting that could break placeholders
            var testCases = new List<(string name, string input, string expected)>
            {
                ("Basic placeholder", 
                 "This is a test with ##INLINECODE12345## placeholder.", 
                 "##INLINECODE12345##"),
                
                ("Placeholder with underscore format", 
                 "Router uses ##INLINE_CODE_67890## for mapping.", 
                 "##INLINE_CODE_67890##"),
                
                ("Markdown emphasis around placeholder", 
                 "The _field_ uses ##INLINECODE_TEST## for **validation**.", 
                 "##INLINECODE_TEST##"),
                
                ("Placeholder in italic context", 
                 "_This contains ##INLINECODE_FIELD## within emphasis_", 
                 "##INLINECODE_FIELD##"),
                
                ("Multiple placeholders with mixed formatting", 
                 "**Router** uses _##INLINECODE_INPUT##_ and __##INLINE_CODE_OUTPUT##__ fields.", 
                 "##INLINECODE_INPUT##|##INLINE_CODE_OUTPUT##"),
                
                ("Placeholder in list item", 
                 "- Field: ##INLINECODE_LIST##\n- Another: normal text", 
                 "##INLINECODE_LIST##"),
                
                ("Placeholder in code context", 
                 "Use `##INLINECODE_CODE##` for processing.", 
                 "##INLINECODE_CODE##"),
                
                ("Complex router description", 
                 "The router processes _##INLINECODE_FIELD_NAME##_ using **##INLINE_CODE_ROUTING_PROPERTY##** for routing.", 
                 "##INLINECODE_FIELD_NAME##|##INLINE_CODE_ROUTING_PROPERTY##")
            };
            
            Console.WriteLine($"Running {testCases.Count} test cases...\n");
            
            int passed = 0;
            int failed = 0;
            
            foreach (var (name, input, expectedPlaceholders) in testCases)
            {
                Console.WriteLine($"Test: {name}");
                Console.WriteLine($"Input: {input}");
                
                try
                {
                    // Use reflection to access the private ConvertMarkdownToHtml method
                    var method = typeof(DocumentationGenerator).GetMethod("ConvertMarkdownToHtml", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    
                    if (method == null)
                    {
                        Console.WriteLine("ERROR: ConvertMarkdownToHtml method not found!");
                        failed++;
                        continue;
                    }
                    
                    string result = (string)method.Invoke(docGen, new object[] { input });
                    Console.WriteLine($"Output: {result}");
                    
                    // Check if all expected placeholders are preserved
                    var expectedPlaceholderList = expectedPlaceholders.Split('|');
                    bool allPlaceholdersPreserved = true;
                    
                    foreach (var placeholder in expectedPlaceholderList)
                    {
                        if (!result.Contains(placeholder))
                        {
                            Console.WriteLine($"❌ FAILED: Placeholder '{placeholder}' not found in output!");
                            allPlaceholdersPreserved = false;
                        }
                        else
                        {
                            Console.WriteLine($"✅ Placeholder '{placeholder}' preserved correctly");
                        }
                    }
                    
                    // Check if any placeholders were broken by HTML formatting
                    if (result.Contains("##INLINE<") || result.Contains(">CODE<") || result.Contains(">CODE"))
                    {
                        Console.WriteLine("❌ FAILED: Placeholder was broken by HTML formatting!");
                        allPlaceholdersPreserved = false;
                    }
                    
                    if (allPlaceholdersPreserved)
                    {
                        Console.WriteLine("✅ PASSED");
                        passed++;
                    }
                    else
                    {
                        Console.WriteLine("❌ FAILED");
                        failed++;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ ERROR: {ex.Message}");
                    failed++;
                }
                
                Console.WriteLine(new string('-', 50));
            }
            
            Console.WriteLine($"\n=== Test Summary ===");
            Console.WriteLine($"Passed: {passed}");
            Console.WriteLine($"Failed: {failed}");
            Console.WriteLine($"Total: {passed + failed}");
            
            if (failed == 0)
            {
                Console.WriteLine("🎉 All tests passed! Placeholder handling is working correctly.");
            }
            else
            {
                Console.WriteLine("⚠️  Some tests failed. Placeholder handling needs attention.");
            }
            
            // Create a detailed HTML test report
            CreateHtmlTestReport(testCases, docGen);
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
        
        static void CreateHtmlTestReport(List<(string name, string input, string expected)> testCases, DocumentationGenerator docGen)
        {
            var htmlContent = @"<!DOCTYPE html>
<html>
<head>
    <title>Placeholder Markdown Conversion Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { border: 1px solid #ddd; margin: 10px 0; padding: 15px; }
        .test-input { background: #f0f0f0; padding: 10px; margin: 5px 0; }
        .test-output { background: #e8f5e8; padding: 10px; margin: 5px 0; }
        .placeholder { background: yellow; font-weight: bold; }
        .broken-placeholder { background: red; color: white; font-weight: bold; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Placeholder Markdown Conversion Test Results</h1>
    <p>Generated on: " + DateTime.Now + @"</p>";
            
            try
            {
                var method = typeof(DocumentationGenerator).GetMethod("ConvertMarkdownToHtml", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                foreach (var (name, input, expected) in testCases)
                {
                    htmlContent += $@"
    <div class='test-case'>
        <h3>{name}</h3>
        <div class='test-input'>
            <strong>Input:</strong><br>
            {System.Web.HttpUtility.HtmlEncode(input)}
        </div>";
                    
                    if (method != null)
                    {
                        string result = (string)method.Invoke(docGen, new object[] { input });
                        
                        // Highlight placeholders
                        string highlightedResult = result;
                        highlightedResult = System.Text.RegularExpressions.Regex.Replace(highlightedResult, 
                            @"##INLINE_?CODE_?[a-zA-Z0-9]+##", 
                            "<span class='placeholder'>$0</span>");
                        
                        // Highlight broken placeholders
                        highlightedResult = System.Text.RegularExpressions.Regex.Replace(highlightedResult, 
                            @"##INLINE<[^>]*>CODE<[^>]*>[a-zA-Z0-9]*##", 
                            "<span class='broken-placeholder'>$0</span>");
                        
                        htmlContent += $@"
        <div class='test-output'>
            <strong>Output:</strong><br>
            {highlightedResult}
        </div>";
                        
                        // Check for issues
                        var expectedPlaceholders = expected.Split('|');
                        bool hasIssues = false;
                        
                        foreach (var placeholder in expectedPlaceholders)
                        {
                            if (!result.Contains(placeholder))
                            {
                                htmlContent += $"<div class='error'>❌ Missing placeholder: {placeholder}</div>";
                                hasIssues = true;
                            }
                        }
                        
                        if (result.Contains("##INLINE<") || result.Contains(">CODE<"))
                        {
                            htmlContent += "<div class='error'>❌ Placeholder broken by HTML formatting!</div>";
                            hasIssues = true;
                        }
                        
                        if (!hasIssues)
                        {
                            htmlContent += "<div class='success'>✅ All placeholders preserved correctly</div>";
                        }
                    }
                    else
                    {
                        htmlContent += "<div class='error'>ERROR: ConvertMarkdownToHtml method not accessible</div>";
                    }
                    
                    htmlContent += "</div>";
                }
            }
            catch (Exception ex)
            {
                htmlContent += $"<div class='error'>Error during testing: {ex.Message}</div>";
            }
            
            htmlContent += @"
</body>
</html>";
            
            string filePath = Path.Combine(Directory.GetCurrentDirectory(), "placeholder_markdown_test.html");
            File.WriteAllText(filePath, htmlContent);
            Console.WriteLine($"\nDetailed test report saved to: {filePath}");
        }
    }
}
