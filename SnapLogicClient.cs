using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.IO;

namespace SnapAnalyser
{
    public class SnapLogicClient
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly string _username;
        private readonly string _password;
        private readonly string _orgId;
        private string _accessToken;
        private DateTime _tokenExpiration = DateTime.MinValue;
        
        public SnapLogicClient(string baseUrl, string username, string password, string orgId)
        {
            _baseUrl = baseUrl.TrimEnd('/');
            _username = username;
            _password = password;
            _orgId = orgId;
            _httpClient = new HttpClient();
        }
        
        public async Task<bool> AuthenticateAsync()
        {
            try
            {
                if (!string.IsNullOrEmpty(_accessToken) && DateTime.Now < _tokenExpiration)
                {
                    // Token still valid
                    return true;
                }
                
                var authUrl = $"{_baseUrl}";
                
                var authPayload = new
                {
                    username = _username,
                    password = _password,
                    orgId = _orgId
                };
                
                var jsonContent = JsonSerializer.Serialize(authPayload);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync(authUrl, content);
                
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Authentication failed: {response.StatusCode} - {errorContent}");
                    return false;
                }
                
                var responseBody = await response.Content.ReadAsStringAsync();
                using JsonDocument doc = JsonDocument.Parse(responseBody);
                
                if (doc.RootElement.TryGetProperty("JSessionId", out JsonElement sessionId))
                {
                    _accessToken = sessionId.GetString();
                    _tokenExpiration = DateTime.Now.AddHours(1); // Most sessions expire in 1 hour
                    _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _accessToken);
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Authentication error: {ex.Message}");
                return false;
            }
        }
        
        public async Task<List<ProjectInfo>> GetProjectsAsync()
        {
            if (!await AuthenticateAsync())
            {
                throw new Exception("Authentication failed. Cannot retrieve projects.");
            }
            
            var projectsUrl = $"{_baseUrl}/api/1/rest/public/projects";
            var response = await _httpClient.GetAsync(projectsUrl);
            
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new Exception($"Failed to get projects: {response.StatusCode} - {errorContent}");
            }
            
            var responseBody = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<List<ProjectInfo>>(responseBody, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }
        
        public async Task<List<PipelineInfo>> GetPipelinesInProjectAsync(string projectId)
        {
            if (!await AuthenticateAsync())
            {
                throw new Exception("Authentication failed. Cannot retrieve pipelines.");
            }
            
            var pipelinesUrl = $"{_baseUrl}/api/1/rest/public/pipelines?project_id={projectId}";
            var response = await _httpClient.GetAsync(pipelinesUrl);
            
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new Exception($"Failed to get pipelines: {response.StatusCode} - {errorContent}");
            }
            
            var responseBody = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<List<PipelineInfo>>(responseBody, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }
        
        public async Task<string> GetPipelineContentAsync(string pipelineId)
        {
            if (!await AuthenticateAsync())
            {
                throw new Exception("Authentication failed. Cannot retrieve pipeline content.");
            }
            
            var pipelineUrl = $"{_baseUrl}/api/1/rest/public/pipeline/{pipelineId}";
            var response = await _httpClient.GetAsync(pipelineUrl);
            
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new Exception($"Failed to get pipeline content: {response.StatusCode} - {errorContent}");
            }
            
            return await response.Content.ReadAsStringAsync();
        }
        
        public async Task DownloadPipelineAsync(string pipelineId, string outputPath)
        {
            string pipelineContent = await GetPipelineContentAsync(pipelineId);
            File.WriteAllText(outputPath, pipelineContent);
        }
        
        public async Task<List<PipelineInfo>> GetAllPipelinesAsync()
        {
            var allPipelines = new List<PipelineInfo>();
            var projects = await GetProjectsAsync();
            
            foreach (var project in projects)
            {
                var projectPipelines = await GetPipelinesInProjectAsync(project.Id);
                
                // Add project information to each pipeline
                foreach (var pipeline in projectPipelines)
                {
                    pipeline.ProjectName = project.Name;
                }
                
                allPipelines.AddRange(projectPipelines);
            }
            
            return allPipelines;
        }
    }
    
    public class ProjectInfo
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public DateTime Created { get; set; }
        public string CreatedBy { get; set; }
        public DateTime Modified { get; set; }
        public string ModifiedBy { get; set; }
    }
    
    public class PipelineInfo
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string ProjectId { get; set; }
        public string ProjectName { get; set; } // Added for convenience
        public string Description { get; set; }
        public DateTime Created { get; set; }
        public string CreatedBy { get; set; }
        public DateTime Modified { get; set; }
        public string ModifiedBy { get; set; }
    }
} 