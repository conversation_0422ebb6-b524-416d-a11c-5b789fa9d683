<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0-windows7.0</TargetFramework>
    <StartupObject>TestAzureConfig.Program</StartupObject>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>
  
  <ItemGroup>
    <Compile Include="ConfigManager.cs" />
    <Compile Include="AzureOpenAITester.cs" />
    <Compile Include="TestAzureConfig.cs" />
  </ItemGroup>
</Project>
