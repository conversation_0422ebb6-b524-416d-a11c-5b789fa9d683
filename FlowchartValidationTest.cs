using System;
using System.Collections.Generic;

namespace SnapAnalyser
{
    class FlowchartValidationTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Flowchart.js Conversion Validation Test ===");
            Console.WriteLine();

            try
            {
                // Test 1: Basic FlowchartJsGenerator
                Console.WriteLine("1. Testing FlowchartJsGenerator...");
                TestFlowchartJsGenerator();

                // Test 2: DiagramGenerator with flowchart.js
                Console.WriteLine("\n2. Testing DiagramGenerator with flowchart.js...");
                TestDiagramGenerator();

                // Test 3: Create a sample HTML file to test rendering
                Console.WriteLine("\n3. Creating sample HTML output...");
                CreateSampleHtmlFile();

                Console.WriteLine("\n✅ ALL VALIDATION TESTS PASSED!");
                Console.WriteLine("The flowchart.js conversion is working correctly.");
                Console.WriteLine("\nNext steps:");
                Console.WriteLine("- Check the generated 'sample_flowchart_test.html' file");
                Console.WriteLine("- Open it in a web browser to verify visual rendering");
                Console.WriteLine("- Test with actual pipeline data");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ VALIDATION TEST FAILED: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }

        static void TestFlowchartJsGenerator()
        {
            var generator = new FlowchartJsGenerator();
            var pipeline = CreateTestPipeline();

            // Test definition generation
            string definition = generator.GenerateFlowchartDefinition(pipeline);
            Console.WriteLine($"   ✓ Definition generated: {!string.IsNullOrEmpty(definition)}");
            
            if (!string.IsNullOrEmpty(definition))
            {
                Console.WriteLine($"   ✓ Definition contains flowchart syntax: {definition.Contains("=>") && definition.Contains("->")}");
                Console.WriteLine($"   ✓ Definition length: {definition.Length} characters");
            }

            // Test HTML generation
            string html = generator.GenerateFlowchartHtml(definition, "test-container", "Test Pipeline Diagram");
            Console.WriteLine($"   ✓ HTML generated: {!string.IsNullOrEmpty(html)}");
            
            if (!string.IsNullOrEmpty(html))
            {
                Console.WriteLine($"   ✓ HTML contains flowchart.js CDN: {html.Contains("//cdn.jsdelivr.net/npm/flowchart.js")}");
                Console.WriteLine($"   ✓ HTML contains Raphael.js CDN: {html.Contains("//cdnjs.cloudflare.com/ajax/libs/raphael")}");
                Console.WriteLine($"   ✓ HTML contains container div: {html.Contains("<div id=\"test-container\">")}");
            }
        }

        static void TestDiagramGenerator()
        {
            var diagramGenerator = new DiagramGenerator();
            var pipeline = CreateTestPipeline();

            string diagram = diagramGenerator.GenerateDiagram(pipeline);
            
            Console.WriteLine($"   ✓ Diagram generated: {!string.IsNullOrEmpty(diagram)}");
            
            if (!string.IsNullOrEmpty(diagram))
            {
                Console.WriteLine($"   ✓ Uses flowchart.js: {diagram.Contains("flowchart")}");
                Console.WriteLine($"   ✓ Contains HTML structure: {diagram.Contains("<div") && diagram.Contains("</div>")}");
                Console.WriteLine($"   ✓ Diagram length: {diagram.Length} characters");
            }
        }

        static void CreateSampleHtmlFile()
        {
            var generator = new FlowchartJsGenerator();
            var pipeline = CreateTestPipeline();
            
            string definition = generator.GenerateFlowchartDefinition(pipeline);
            string html = generator.GenerateFlowchartHtml(definition, "sample-diagram", "Sample Pipeline Flow");

            // Create a complete HTML file
            string fullHtml = $@"<!DOCTYPE html>
<html lang=""en"">
<head>
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <title>Flowchart.js Test - Sample Pipeline</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        h1 {{ color: #333; }}
        .diagram-container {{ border: 1px solid #ddd; padding: 20px; margin: 20px 0; }}
        .info {{ background-color: #f0f8ff; padding: 10px; border-radius: 5px; margin: 10px 0; }}
    </style>
</head>
<body>
    <h1>Flowchart.js Conversion Test</h1>
    <div class=""info"">
        <strong>Status:</strong> Successfully converted from custom SVG to flowchart.js<br>
        <strong>Date:</strong> {DateTime.Now:yyyy-MM-dd HH:mm:ss}<br>
        <strong>Pipeline:</strong> Sample Test Pipeline
    </div>
    
    <div class=""diagram-container"">
        <h2>Pipeline Flow Diagram</h2>
        {html}
    </div>
    
    <div class=""info"">
        <h3>Flowchart Definition:</h3>
        <pre>{definition}</pre>
    </div>
</body>
</html>";

            string outputPath = "sample_flowchart_test.html";
            System.IO.File.WriteAllText(outputPath, fullHtml);
            Console.WriteLine($"   ✓ Sample HTML file created: {outputPath}");
            Console.WriteLine($"   ✓ File size: {new System.IO.FileInfo(outputPath).Length} bytes");
        }

        static PipelineData CreateTestPipeline()
        {
            var snaps = new List<SnapNode>
            {
                new SnapNode
                {
                    Id = "input_reader",
                    Label = "Input Reader",
                    Type = "com-snaplogic-snaps-file-read",
                    Category = SnapCategory.ExternalSystem,
                    Position = new Position { X = 0, Y = 0 },
                    IsStartPoint = true
                },
                new SnapNode
                {
                    Id = "data_mapper",
                    Label = "Data Mapper",
                    Type = "com-snaplogic-snaps-transform-datatransform",
                    Category = SnapCategory.Transformation,
                    Position = new Position { X = 1, Y = 0 }
                },
                new SnapNode
                {
                    Id = "router_snap",
                    Label = "Router",
                    Type = "com-snaplogic-snaps-flow-router",
                    Category = SnapCategory.FlowControl,
                    Position = new Position { X = 2, Y = 0 }
                },
                new SnapNode
                {
                    Id = "db_writer",
                    Label = "Database Writer",
                    Type = "com-snaplogic-snaps-database-write",
                    Category = SnapCategory.Database,
                    Position = new Position { X = 3, Y = 0 },
                    IsEndPoint = true
                },
                new SnapNode
                {
                    Id = "file_writer",
                    Label = "File Writer",
                    Type = "com-snaplogic-snaps-file-write",
                    Category = SnapCategory.ExternalSystem,
                    Position = new Position { X = 3, Y = 1 },
                    IsEndPoint = true
                }
            };

            var links = new List<SnapLink>
            {
                new SnapLink { SourceId = "input_reader", TargetId = "data_mapper" },
                new SnapLink { SourceId = "data_mapper", TargetId = "router_snap" },
                new SnapLink { SourceId = "router_snap", TargetId = "db_writer" },
                new SnapLink { SourceId = "router_snap", TargetId = "file_writer" }
            };

            return new PipelineData
            {
                Name = "Sample Test Pipeline",
                Author = "Flowchart.js Conversion Test",
                Snaps = snaps,
                Links = links
            };
        }
    }
}
