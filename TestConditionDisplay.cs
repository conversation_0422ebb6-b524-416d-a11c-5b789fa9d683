using System;
using System.Collections.Generic;
using System.Linq;
using SnapAnalyser;

class TestConditionDisplay
{
    static void Main()
    {
        Console.WriteLine("Testing Condition Snap Display");
        
        // Create a mock condition snap with indexed expressions
        var conditionSnap = new SnapNode
        {
            Id = "condition-snap",
            Label = "BlockTypes Condition",
            Type = "com-snaplogic-snaps-transform-conditional",
            Properties = new Dictionary<string, string>
            {
                { "expression_0", "$blocktypes.blockId == null || $blocktypes.blockId == ''" },
                { "expression_1", "$blocktypes.parentBlock == null || $blocktypes.parentBlock == ''" },
                { "expression_2", "$blocktypes.blocktype == 'District'" },
                { "output_0", "Remove District" },
                { "output_1", "Collapse Parent" },
                { "output_2", "Keep District" },
                { "settings.evaluateAll.value", "False" },
                { "settings.execution_mode.value", "Validate & Execute" },
                { "settings.nullSafeAccess.value", "False" }
            }
        };
        
        // Get the best practices HTML for this snap
        string bestPracticesHtml = SnapBestPractices.GetSnapBestPractices(conditionSnap, new List<SnapNode>());
        
        // Display the result
        Console.WriteLine("Generated HTML for Condition Snap:");
        Console.WriteLine(bestPracticesHtml);
    }
}
