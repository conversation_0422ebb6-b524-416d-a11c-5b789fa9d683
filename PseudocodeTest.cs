using System;
using System.Collections.Generic;
using SnapAnalyser;

namespace PseudocodeTest
{
    class Program
    {
        static void Main()
        {
            Console.WriteLine("=== Pseudocode Generation Test ===");
            Console.WriteLine($"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine();

            try
            {
                Console.WriteLine("Creating test AI generator...");
                var generator = new AIDescriptionGenerator(null);
                
                Console.WriteLine("Creating test snap node...");
                var testSnap = new SnapNode
                {
                    Id = "test-mapper-1",
                    Label = "Test Mapper",
                    Type = "Mapper",
                    Category = SnapCategory.Transformation,
                    Properties = new Dictionary<string, string>
                    {
                        { "settings.transformations.firstName.value", "$firstName" },
                        { "settings.transformations.lastName.value", "$lastName" },
                        { "settings.transformations.fullName.value", "$firstName + ' ' + $lastName" }
                    }
                };

                // Test the explicit pseudocode generation
                Console.WriteLine("\n=== Testing Mapper Pseudocode Generation ===");
                var mapperPseudocode = typeof(AIDescriptionGenerator)
                    .GetMethod("GenerateMapperPseudocode", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                    ?.Invoke(generator, new object[] { testSnap }) as string;

                if (!string.IsNullOrEmpty(mapperPseudocode))
                {
                    Console.WriteLine("✅ Mapper pseudocode generated successfully!");
                    Console.WriteLine("Generated pseudocode:");
                    Console.WriteLine("---");
                    Console.WriteLine(mapperPseudocode);
                    Console.WriteLine("---");

                    // Check if it follows proper pseudocode format
                    bool hasProperFormat = mapperPseudocode.Contains("BEGIN") && 
                                         mapperPseudocode.Contains("END") && 
                                         mapperPseudocode.Contains("WHILE") &&
                                         !mapperPseudocode.Contains("class ") &&
                                         !mapperPseudocode.Contains("function") &&
                                         !mapperPseudocode.Contains("{") &&
                                         !mapperPseudocode.Contains("}");

                    if (hasProperFormat)
                    {
                        Console.WriteLine("✅ Pseudocode follows proper abstract format!");
                    }
                    else
                    {
                        Console.WriteLine("❌ Pseudocode still contains programming language constructs");
                    }
                }
                else
                {
                    Console.WriteLine("❌ Failed to generate mapper pseudocode");
                }

                // Test router pseudocode
                Console.WriteLine("\n=== Testing Router Pseudocode Generation ===");
                var routerSnap = new SnapNode
                {
                    Id = "test-router-1",
                    Label = "Test Router",
                    Type = "Router",
                    Category = SnapCategory.FlowControl,
                    Properties = new Dictionary<string, string>()
                };

                var routerPseudocode = typeof(AIDescriptionGenerator)
                    .GetMethod("GenerateRouterPseudocode", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                    ?.Invoke(generator, new object[] { routerSnap }) as string;

                if (!string.IsNullOrEmpty(routerPseudocode))
                {
                    Console.WriteLine("✅ Router pseudocode generated successfully!");
                    Console.WriteLine("Generated pseudocode:");
                    Console.WriteLine("---");
                    Console.WriteLine(routerPseudocode);
                    Console.WriteLine("---");
                }

                // Test condition pseudocode
                Console.WriteLine("\n=== Testing Condition Pseudocode Generation ===");
                var conditionSnap = new SnapNode
                {
                    Id = "test-condition-1",
                    Label = "Test Filter",
                    Type = "Filter",
                    Category = SnapCategory.FlowControl,
                    Properties = new Dictionary<string, string>
                    {
                        { "condition", "$age > 18" }
                    }
                };

                var conditionPseudocode = typeof(AIDescriptionGenerator)
                    .GetMethod("GenerateConditionPseudocode", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                    ?.Invoke(generator, new object[] { conditionSnap }) as string;

                if (!string.IsNullOrEmpty(conditionPseudocode))
                {
                    Console.WriteLine("✅ Condition pseudocode generated successfully!");
                    Console.WriteLine("Generated pseudocode:");
                    Console.WriteLine("---"); 
                    Console.WriteLine(conditionPseudocode);
                    Console.WriteLine("---");
                }

                Console.WriteLine("\n✅ All pseudocode tests completed successfully!");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: {ex.GetType().Name}: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.GetType().Name}: {ex.InnerException.Message}");
                }
            }
            
            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
