using System;
using System.Collections.Generic;
using System.Linq;

namespace SnapAnalyser
{
    class JoinFixTestProgram
    {
        public static void RunJoinFixTest()
        {
            Console.WriteLine("=== Join Fix Verification Test ===");
            Console.WriteLine($"Test Date: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine();

            // Create test Join snap with realistic properties
            var joinSnap = new SnapNode
            {
                Id = "test-join-001",
                Label = "Test Join Snap", 
                Type = "com-snaplogic-snaps-transform-multijoin",
                Properties = new List<KeyValuePair<string, string>>
                {
                    new KeyValuePair<string, string>("settings.joinPaths.value.0.leftPath.value", "customer_id"),
                    new KeyValuePair<string, string>("settings.joinPaths.value.0.rightPath.value", "id"),
                    new KeyValuePair<string, string>("settings.joinType.value", "Inner Join"),
                    new KeyValuePair<string, string>("settings.joinPaths.value.0.leftPath.expression", ""),
                    new KeyValuePair<string, string>("settings.joinPaths.value.0.rightPath.expression", ""),
                    new KeyValuePair<string, string>("settings.nullSafeAccess.value", "true"),
                    new KeyValuePair<string, string>("class_id", "com-snaplogic-snaps-flow-join"),
                    new KeyValuePair<string, string>("datatype", "binary")
                }
            };

            var allSnaps = new List<SnapNode> { joinSnap };

            bool configSuccess = false;
            bool bestPracticesSuccess = false;
            bool diagramSuccess = false;

            Console.WriteLine("1. Testing FlowControlConfigurationGenerator");
            Console.WriteLine("=============================================");

            try
            {
                var flowConfigGenerator = new FlowControlConfigurationGenerator();
                string joinConfig = flowConfigGenerator.GenerateJoinConfiguration(joinSnap, allSnaps);

                if (!string.IsNullOrEmpty(joinConfig))
                {
                    Console.WriteLine("✅ SUCCESS: Join configuration generated");
                    Console.WriteLine($"   Length: {joinConfig.Length} characters");
                    
                    // Check for expected content
                    if (joinConfig.Contains("customer_id") && joinConfig.Contains("Inner Join"))
                    {
                        Console.WriteLine("✅ SUCCESS: Contains expected join details");
                        configSuccess = true;
                    }
                    else
                    {
                        Console.WriteLine("⚠️  WARNING: May not contain expected join details");
                        Console.WriteLine($"   Config preview: {joinConfig.Substring(0, Math.Min(200, joinConfig.Length))}...");
                    }
                }
                else
                {
                    Console.WriteLine("❌ FAILED: No configuration generated");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: {ex.Message}");
                Console.WriteLine($"   Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine();
            Console.WriteLine("2. Testing SnapBestPractices Integration");
            Console.WriteLine("=========================================");

            try
            {
                string bestPractices = SnapBestPractices.GetSnapBestPractices(joinSnap, allSnaps, false);

                if (!string.IsNullOrEmpty(bestPractices))
                {
                    Console.WriteLine("✅ SUCCESS: Best practices generated");
                    Console.WriteLine($"   Length: {bestPractices.Length} characters");

                    // Check if it contains enhanced configuration instead of raw config
                    if (bestPractices.Contains("Join Configuration:") || bestPractices.Contains("Join Type:"))
                    {
                        Console.WriteLine("✅ SUCCESS: Contains enhanced join configuration");
                        bestPracticesSuccess = true;
                    }
                    else if (bestPractices.Contains("Configuration:") && bestPractices.Contains("property_map"))
                    {
                        Console.WriteLine("⚠️  WARNING: Still showing raw configuration sections");
                        bestPracticesSuccess = false;
                    }
                    else
                    {
                        Console.WriteLine("ℹ️  INFO: Contains basic join documentation");
                        bestPracticesSuccess = true; // Basic documentation is still success
                    }

                    // Show preview
                    Console.WriteLine("\nPreview (first 300 characters):");
                    var preview = bestPractices.Length > 300 ? bestPractices.Substring(0, 300) + "..." : bestPractices;
                    Console.WriteLine(preview);
                }
                else
                {
                    Console.WriteLine("❌ FAILED: No best practices generated");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: {ex.Message}");
                Console.WriteLine($"   Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine();
            Console.WriteLine("3. Testing FlowControlDiagramGenerator");
            Console.WriteLine("======================================");

            try
            {
                var diagramGenerator = new FlowControlDiagramGenerator();
                string diagram = diagramGenerator.GenerateJoinFlowDiagram(joinSnap, allSnaps);

                if (!string.IsNullOrEmpty(diagram))
                {
                    Console.WriteLine("✅ SUCCESS: Flow diagram generated");
                    Console.WriteLine($"   Length: {diagram.Length} characters");
                    
                    if (diagram.Contains("<svg") && diagram.Contains("customer_id"))
                    {
                        Console.WriteLine("✅ SUCCESS: Contains SVG diagram with join details");
                        diagramSuccess = true;
                    }
                    else
                    {
                        Console.WriteLine("⚠️  INFO: Basic diagram generated");
                        diagramSuccess = true;
                    }
                }
                else
                {
                    Console.WriteLine("⚠️  WARNING: No flow diagram generated");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: {ex.Message}");
                Console.WriteLine($"   Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine();
            Console.WriteLine("=== SUMMARY ===");
            Console.WriteLine($"Configuration Generator: {(configSuccess ? "✅ PASS" : "❌ FAIL")}");
            Console.WriteLine($"Best Practices Integration: {(bestPracticesSuccess ? "✅ PASS" : "❌ FAIL")}");
            Console.WriteLine($"Diagram Generator: {(diagramSuccess ? "✅ PASS" : "⚠️  PARTIAL")}");
            
            bool overallSuccess = configSuccess && bestPracticesSuccess;
            Console.WriteLine($"\nOVERALL RESULT: {(overallSuccess ? "🎉 JOIN FIX IS WORKING!" : "❌ NEEDS ATTENTION")}");
            
            if (overallSuccess)
            {
                Console.WriteLine("\nThe Join snap documentation fix is working correctly.");
                Console.WriteLine("Join snaps should now show enhanced flow control documentation");
                Console.WriteLine("instead of raw 'Configuration:' sections.");
            }
            else
            {
                Console.WriteLine("\nSome issues detected. Review the test results above.");
            }
        }
    }
}
