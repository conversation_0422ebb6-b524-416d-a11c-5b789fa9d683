using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace ConditionTest
{
    // Mock classes to simulate your actual classes
    public class ConditionInfo
    {
        public string Expression { get; set; }
        public string Description { get; set; }

        public ConditionInfo(string expression, string description)
        {
            Expression = expression;
            Description = description;
        }
    }

    public class SnapNode
    {
        public string Id { get; set; }
        public string Label { get; set; }
        public string Type { get; set; }
        public Dictionary<string, string> Properties { get; set; }
    }

    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("====== CONDITION SNAP EXTRACTION TEST ======");

            // Create test condition snap with indexed expression properties
            var snap = new SnapNode
            {
                Id = "condition-1",
                Label = "BlockTypes Condition",
                Type = "com-snaplogic-snaps-transform-conditional",
                Properties = new Dictionary<string, string>
                {
                    { "expression_0", "$blocktypes.blockId == null || $blocktypes.blockId == ''" },
                    { "expression_1", "$blocktypes.parentBlock == null || $blocktypes.parentBlock == ''" },
                    { "expression_2", "$blocktypes.blocktype == 'District'" },
                    { "output_0", "Remove District" },
                    { "output_1", "Collapse Parent" },
                    { "output_2", "Keep District" },
                    { "settings.evaluateAll.value", "False" },
                    { "settings.execution_mode.value", "Validate & Execute" },
                    { "settings.nullSafeAccess.value", "False" }
                }
            };

            // Extract condition expressions
            var conditions = ExtractConditionExpressions(snap);
            
            // Display results
            Console.WriteLine($"Found {conditions.Count} condition expressions:");
            foreach (var condition in conditions)
            {
                Console.WriteLine($"- {condition.Description}: {condition.Expression}");
            }
            
            Console.WriteLine("\n====== TEST COMPLETE ======");
        }

        private static List<ConditionInfo> ExtractConditionExpressions(SnapNode snap)
        {
            var conditions = new List<ConditionInfo>();

            // Check for direct condition property
            if (snap.Properties.TryGetValue("condition", out string directCondition))
            {
                conditions.Add(new ConditionInfo(directCondition, "Main Condition"));
            }

            // Check for expression property
            if (snap.Properties.TryGetValue("expression", out string expression))
            {
                conditions.Add(new ConditionInfo(expression, "Filter Expression"));
            }
            
            // Check for indexed expression properties (expression_0, expression_1, etc.)
            var indexedExpressions = new List<KeyValuePair<string, string>>();
            foreach (var prop in snap.Properties)
            {
                if (Regex.IsMatch(prop.Key, @"expression_\d+") && !string.IsNullOrWhiteSpace(prop.Value))
                {
                    indexedExpressions.Add(prop);
                }
            }
            
            // Sort by index number
            indexedExpressions.Sort((a, b) => {
                var matchA = Regex.Match(a.Key, @"expression_(\d+)");
                var matchB = Regex.Match(b.Key, @"expression_(\d+)");
                int indexA = int.Parse(matchA.Groups[1].Value);
                int indexB = int.Parse(matchB.Groups[1].Value);
                return indexA.CompareTo(indexB);
            });
            
            // Add sorted expressions to conditions list
            foreach (var expr in indexedExpressions)
            {
                var indexMatch = Regex.Match(expr.Key, @"expression_(\d+)");
                var index = indexMatch.Groups[1].Value;
                conditions.Add(new ConditionInfo(expr.Value, $"Condition {index}"));
            }
            
            return conditions;
        }
    }
}
