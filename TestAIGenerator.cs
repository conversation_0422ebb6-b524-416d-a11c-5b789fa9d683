using System;
using SnapAnalyser;

namespace TestAIGenerator
{
    class Program
    {
        static void Main()
        {
            Console.WriteLine("=== AIDescriptionGenerator Constructor Test ===");
            Console.WriteLine($"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine();

            try
            {
                Console.WriteLine("Testing ConfigManager properties...");
                string apiKey = ConfigManager.OpenAIApiKey;
                string endpoint = ConfigManager.AzureOpenAIEndpoint;
                string deployment = ConfigManager.AzureOpenAIDeploymentName;
                int timeout = ConfigManager.AzureOpenAITimeoutSeconds;
                
                Console.WriteLine($"API Key: {(string.IsNullOrEmpty(apiKey) ? "NULL/EMPTY" : $"Present (length: {apiKey.Length})")}");
                Console.WriteLine($"Endpoint: {endpoint ?? "NULL"}");
                Console.WriteLine($"Deployment: {deployment ?? "NULL"}");
                Console.WriteLine($"Timeout: {timeout} seconds");
                Console.WriteLine();

                Console.WriteLine("Creating AIDescriptionGenerator with null parameter...");
                var generator = new AIDescriptionGenerator(null);
                Console.WriteLine("✅ SUCCESS: AIDescriptionGenerator created successfully!");
                
                Console.WriteLine("Testing basic properties...");
                Console.WriteLine($"UseCachedDescriptions: {generator.UseCachedDescriptions}");
                
                Console.WriteLine();
                Console.WriteLine("✅ All tests passed!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: {ex.GetType().Name}: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.GetType().Name}: {ex.InnerException.Message}");
                }
            }
            
            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
