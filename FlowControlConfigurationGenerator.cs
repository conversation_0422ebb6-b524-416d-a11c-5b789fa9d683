using System.Text;
using System.Text.Json;
using System.Web;

namespace SnapAnalyser
{
    /// <summary>
    /// Generates enhanced configuration sections for Router, Join, and Union snaps
    /// that display meaningful flow logic instead of raw properties
    /// </summary>
    public class FlowControlConfigurationGenerator
    {

        /// <summary>
        /// Generates an enhanced configuration section for Router snaps showing routing logic clearly
        /// </summary>
        public string GenerateRouterConfiguration(SnapNode routerSnap, List<SnapNode> allSnaps)
        {
            var config = new StringBuilder();
            var routes = ExtractRouterRoutes(routerSnap);
            var connectedSnaps = GetConnectedSnaps(routerSnap, allSnaps);

            config.AppendLine("<div class=\"flow-control-config\">");
            config.AppendLine("<h5>Routing Logic</h5>");

            if (routes.Any())
            {
                config.AppendLine("<div class=\"routing-table\">");
                config.AppendLine("<table style=\"width: 100%; border-collapse: collapse; margin: 10px 0;\">");
                config.AppendLine("<thead>");
                config.AppendLine("<tr style=\"background-color: #f8f9fa;\">");
                config.AppendLine("<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">Expression</th>");
                config.AppendLine("<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">Return Value</th>");
                config.AppendLine("<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">Target Path</th>");
                config.AppendLine("<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">Connected Snaps</th>");
                config.AppendLine("</tr>");
                config.AppendLine("</thead>");
                config.AppendLine("<tbody>");

                // Try to get enhanced condition information using the SnapBestPractices.ExtractConditionExpressions method
                Console.WriteLine($"[DEBUG] Getting enhanced condition info for router snap {routerSnap.Label} (ID: {routerSnap.Id})");
                var enhancedConditions = SnapBestPractices.ExtractConditionExpressions(routerSnap);
                Console.WriteLine($"[DEBUG] Found {enhancedConditions.Count} enhanced conditions for router snap {routerSnap.Label}");

                // Try to match enhanced conditions with routes based on expression matching
                var routesWithEnhancedInfo = new List<(string condition, string returnValue, string targetPath, string outputPath, int index)>();

                // First, try to directly match the enhanced conditions with our routes
                for (int i = 0; i < routes.Count; i++)
                {
                    var route = routes[i];
                    bool foundMatch = false;

                    // Try to find a matching condition in the enhanced list
                    foreach (var enhancedCondition in enhancedConditions)
                    {
                        if (string.Equals(enhancedCondition.Expression, route.condition, StringComparison.OrdinalIgnoreCase))
                        {
                            // We found a match, use the enhanced data
                            routesWithEnhancedInfo.Add((enhancedCondition.Expression,
                                                     enhancedCondition.ReturnValue ?? "",
                                                     enhancedCondition.TargetPath ?? route.outputPath,
                                                     route.outputPath,
                                                     i));
                            foundMatch = true;
                            Console.WriteLine($"[DEBUG] Found matching enhanced condition for route {i}: {enhancedCondition.Expression}");
                            break;
                        }
                    }

                    // If we didn't find a match in enhanced conditions, use the basic route data
                    if (!foundMatch)
                    {
                        routesWithEnhancedInfo.Add((route.condition, "", route.outputPath, route.outputPath, i));
                        Console.WriteLine($"[DEBUG] No enhanced condition match found for route {i}: {route.condition}");
                    }
                }

                // Now, look for any enhanced conditions that weren't matched
                foreach (var enhancedCondition in enhancedConditions)
                {
                    bool alreadyAdded = routesWithEnhancedInfo.Any(r => string.Equals(r.condition, enhancedCondition.Expression, StringComparison.OrdinalIgnoreCase));

                    if (!alreadyAdded)
                    {
                        // This is an enhanced condition we haven't processed yet, add it
                        int newIndex = routesWithEnhancedInfo.Count;
                        string outputPath = enhancedCondition.TargetPath ?? "Unknown";
                        routesWithEnhancedInfo.Add((enhancedCondition.Expression,
                                                 enhancedCondition.ReturnValue ?? "",
                                                 enhancedCondition.TargetPath ?? "",
                                                 outputPath,
                                                 newIndex));
                        Console.WriteLine($"[DEBUG] Added additional enhanced condition: {enhancedCondition.Expression}");
                    }
                }

                // Render all the conditions in the table
                foreach (var enhancedRoute in routesWithEnhancedInfo)
                {
                    // Find connected snaps for this specific route/output
                    var connectedOutput = GetConnectedSnapsForOutput(routerSnap, allSnaps, enhancedRoute.index, enhancedRoute.outputPath);

                    // Only show the row if there are connected snaps
                    if (connectedOutput.Any())
                    {
                        // Format the expression for better readability
                        string formattedExpression = enhancedRoute.condition;
                        
                        // Replace common operators with highlighted versions for better readability
                        formattedExpression = HttpUtility.HtmlEncode(formattedExpression)
                            .Replace("&amp;&amp;", "<span style='color:#0066cc; font-weight:bold;'>&amp;&amp;</span>")
                            .Replace("|||", "<span style='color:#0066cc; font-weight:bold;'>||</span>")
                            .Replace("==", "<span style='color:#cc6600; font-weight:bold;'>==</span>")
                            .Replace("!=", "<span style='color:#cc6600; font-weight:bold;'>!=</span>")
                            .Replace("&gt;", "<span style='color:#cc6600; font-weight:bold;'>&gt;</span>")
                            .Replace("&lt;", "<span style='color:#cc6600; font-weight:bold;'>&lt;</span>");
                            
                        config.AppendLine("<tr>");
                        config.AppendLine($"<td style=\"border: 1px solid #ddd; padding: 8px; font-family: monospace;\"><pre style='margin: 0; white-space: pre-wrap; word-break: break-word;'>{formattedExpression}</pre></td>");
                        config.AppendLine($"<td style=\"border: 1px solid #ddd; padding: 8px;\"><code style='background-color: #f7f7f7; padding: 2px 4px; border-radius: 3px;'>{HttpUtility.HtmlEncode(enhancedRoute.returnValue)}</code></td>");
                        config.AppendLine($"<td style=\"border: 1px solid #ddd; padding: 8px;\"><strong>{HttpUtility.HtmlEncode(enhancedRoute.targetPath)}</strong></td>");

                        var snapNames = string.Join(", ", connectedOutput.Select(s => s.Label));
                        config.AppendLine($"<td style=\"border: 1px solid #ddd; padding: 8px;\">{HttpUtility.HtmlEncode(snapNames)}</td>");
                        config.AppendLine("</tr>");
                    }
                    // If no connected snaps, skip this row entirely
                }

                config.AppendLine("</tbody>");
                config.AppendLine("</table>");
                config.AppendLine("</div>");

                // Add routing behavior details
                var firstMatch = routerSnap.Properties.FirstOrDefault(p => p.Key.Contains("firstMatch")).Value;
                if (!string.IsNullOrEmpty(firstMatch))
                {
                    var firstMatchBool = bool.TryParse(firstMatch, out var fm) ? fm : false;
                    config.AppendLine("<div class=\"routing-behavior\">");
                    config.AppendLine("<h6>Routing Behavior</h6>");
                    if (firstMatchBool)
                    {
                        config.AppendLine("<p><strong>First Match:</strong> Documents are routed to the first matching condition and stop processing subsequent conditions.</p>");
                    }
                    else
                    {
                        config.AppendLine("<p><strong>All Matches:</strong> Documents are evaluated against all conditions and may be routed to multiple outputs if multiple conditions match.</p>");
                    }
                    config.AppendLine("</div>");
                }
            }
            else
            {
                config.AppendLine("<p><em>No routing conditions were automatically extracted. Check the snap configuration in SnapLogic for routing logic.</em></p>");
            }

            // Add pseudocode section for router logic
            var pseudocode = GenerateRouterPseudocode(routerSnap, routes);
            if (!string.IsNullOrEmpty(pseudocode))
            {
                config.AppendLine("<h6>Router Logic Pseudocode</h6>");
                config.AppendLine("<div class=\"pseudocode-container\" style=\"background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; margin: 10px 0; font-family: 'Courier New', monospace; white-space: pre-wrap;\">");
                config.AppendLine(HttpUtility.HtmlEncode(pseudocode));
                config.AppendLine("</div>");
            }

            // Add input source information
            var inputSnaps = connectedSnaps.Where(s => s.IsInput).ToList();
            if (inputSnaps.Any())
            {
                config.AppendLine("<h6>Input Sources</h6>");
                config.AppendLine("<ul>");
                foreach (var input in inputSnaps)
                {
                    config.AppendLine($"<li><strong>{HttpUtility.HtmlEncode(input.Label)}</strong> ({HttpUtility.HtmlEncode(input.Type)})</li>");
                }
                config.AppendLine("</ul>");
            }

            config.AppendLine("</div>");
            return config.ToString();
        }

        /// <summary>
        /// Generates an enhanced configuration section for Join snaps showing join logic
        /// </summary>
        public string GenerateJoinConfiguration(SnapNode joinSnap, List<SnapNode> allSnaps)
        {
            var config = new StringBuilder();
            var joinConditions = ExtractJoinConditions(joinSnap);
            var connectedSnaps = GetConnectedSnaps(joinSnap, allSnaps);
            var joinType = ExtractJoinType(joinSnap);

            config.AppendLine("<div class=\"flow-control-config\">");
            config.AppendLine("<h5>Join Configuration</h5>");

            // Join type
            if (!string.IsNullOrEmpty(joinType))
            {
                config.AppendLine($"<p><strong>Join Type:</strong> <span class=\"join-type\">{HttpUtility.HtmlEncode(joinType)}</span></p>");
            }

            // Join conditions
            if (joinConditions.Any())
            {
                config.AppendLine("<h6>Join Conditions</h6>");
                config.AppendLine("<table style=\"width: 100%; border-collapse: collapse; margin: 10px 0;\">");
                config.AppendLine("<thead>");
                config.AppendLine("<tr style=\"background-color: #f8f9fa;\">");
                config.AppendLine("<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">Join Path</th>");
                config.AppendLine("<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">Left Field</th>");
                config.AppendLine("<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">Right Field</th>");
                config.AppendLine("<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">Condition</th>");
                config.AppendLine("</tr>");
                config.AppendLine("</thead>");
                config.AppendLine("<tbody>");

                for (int i = 0; i < joinConditions.Count; i++)
                {
                    var condition = joinConditions[i];
                    var parts = condition.Split(new[] { " == " }, StringSplitOptions.None);
                    string leftField = parts.Length > 0 ? parts[0].Trim() : "";
                    string rightField = parts.Length > 1 ? parts[1].Trim() : "";

                    config.AppendLine("<tr>");
                    config.AppendLine($"<td style=\"border: 1px solid #ddd; padding: 8px;\"><strong>Path {i + 1}</strong></td>");
                    config.AppendLine($"<td style=\"border: 1px solid #ddd; padding: 8px;\"><code>{HttpUtility.HtmlEncode(leftField)}</code></td>");
                    config.AppendLine($"<td style=\"border: 1px solid #ddd; padding: 8px;\"><code>{HttpUtility.HtmlEncode(rightField)}</code></td>");
                    config.AppendLine($"<td style=\"border: 1px solid #ddd; padding: 8px;\"><code>{HttpUtility.HtmlEncode(condition)}</code></td>");
                    config.AppendLine("</tr>");
                }

                config.AppendLine("</tbody>");
                config.AppendLine("</table>");
            }
            else
            {
                config.AppendLine("<h6>Join Conditions</h6>");
                config.AppendLine("<p><em>No join conditions were automatically extracted. Check the snap configuration in SnapLogic for join criteria.</em></p>");
            }

            // Input sources with detailed information
            var inputSnaps = connectedSnaps.Where(s => s.IsInput).ToList();
            if (inputSnaps.Count >= 2)
            {
                config.AppendLine("<h6>Data Sources Being Joined</h6>");
                config.AppendLine("<table style=\"width: 100%; border-collapse: collapse; margin: 10px 0;\">");
                config.AppendLine("<thead>");
                config.AppendLine("<tr style=\"background-color: #f8f9fa;\">");
                config.AppendLine("<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">Input Stream</th>");
                config.AppendLine("<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">Source Snap</th>");
                config.AppendLine("<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">Type</th>");
                config.AppendLine("</tr>");
                config.AppendLine("</thead>");
                config.AppendLine("<tbody>");

                for (int i = 0; i < inputSnaps.Count; i++)
                {
                    var input = inputSnaps[i];
                    var streamName = i == 0 ? "Left" : i == 1 ? "Right" : $"Stream {i + 1}";

                    config.AppendLine("<tr>");
                    config.AppendLine($"<td style=\"border: 1px solid #ddd; padding: 8px;\"><strong>{streamName}</strong></td>");
                    config.AppendLine($"<td style=\"border: 1px solid #ddd; padding: 8px;\">{HttpUtility.HtmlEncode(input.Label)}</td>");
                    config.AppendLine($"<td style=\"border: 1px solid #ddd; padding: 8px;\">{HttpUtility.HtmlEncode(input.Type)}</td>");
                    config.AppendLine("</tr>");
                }

                config.AppendLine("</tbody>");
                config.AppendLine("</table>");
            }

            // Join operation explanation
            config.AppendLine("<div class=\"join-explanation\">");
            config.AppendLine("<h6>Join Operation</h6>");
            if (joinType.ToLower().Contains("inner"))
            {
                config.AppendLine("<p>This <strong>Inner Join</strong> will only output documents where matching records exist in both input streams based on the join conditions.</p>");
            }
            else if (joinType.ToLower().Contains("left"))
            {
                config.AppendLine("<p>This <strong>Left Outer Join</strong> will output all documents from the left input stream, and matching documents from the right stream (with null values where no match exists).</p>");
            }
            else if (joinType.ToLower().Contains("right"))
            {
                config.AppendLine("<p>This <strong>Right Outer Join</strong> will output all documents from the right input stream, and matching documents from the left stream (with null values where no match exists).</p>");
            }
            else if (joinType.ToLower().Contains("full") || joinType.ToLower().Contains("outer"))
            {
                config.AppendLine("<p>This <strong>Full Outer Join</strong> will output all documents from both input streams, matching them where possible and filling with null values where no match exists.</p>");
            }
            else
            {
                config.AppendLine("<p>This join will combine documents from multiple input streams based on the specified conditions.</p>");
            }
            config.AppendLine("</div>");

            config.AppendLine("</div>");
            return config.ToString();
        }

        /// <summary>
        /// Generates an enhanced configuration section for Union snaps showing merge logic
        /// </summary>
        public string GenerateUnionConfiguration(SnapNode unionSnap, List<SnapNode> allSnaps)
        {
            var config = new StringBuilder();
            var connectedSnaps = GetConnectedSnaps(unionSnap, allSnaps);

            config.AppendLine("<div class=\"flow-control-config\">");
            config.AppendLine("<h5>Union Configuration</h5>");

            // Input sources
            var inputSnaps = connectedSnaps.Where(s => s.IsInput).ToList();
            if (inputSnaps.Any())
            {
                config.AppendLine("<h6>Input Streams Being Merged</h6>");
                config.AppendLine("<table style=\"width: 100%; border-collapse: collapse; margin: 10px 0;\">");
                config.AppendLine("<thead>");
                config.AppendLine("<tr style=\"background-color: #f8f9fa;\">");
                config.AppendLine("<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">#</th>");
                config.AppendLine("<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">Source Snap</th>");
                config.AppendLine("<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">Type</th>");
                config.AppendLine("</tr>");
                config.AppendLine("</thead>");
                config.AppendLine("<tbody>");

                for (int i = 0; i < inputSnaps.Count; i++)
                {
                    var input = inputSnaps[i];
                    config.AppendLine("<tr>");
                    config.AppendLine($"<td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">{i + 1}</td>");
                    config.AppendLine($"<td style=\"border: 1px solid #ddd; padding: 8px;\">{HttpUtility.HtmlEncode(input.Label)}</td>");
                    config.AppendLine($"<td style=\"border: 1px solid #ddd; padding: 8px;\">{HttpUtility.HtmlEncode(input.Type)}</td>");
                    config.AppendLine("</tr>");
                }

                config.AppendLine("</tbody>");
                config.AppendLine("</table>");
            }

            // Union operation explanation
            config.AppendLine("<div class=\"union-explanation\">");
            config.AppendLine("<h6>Union Operation</h6>");
            config.AppendLine($"<p>This Union snap merges <strong>{inputSnaps.Count} input stream(s)</strong> into a single output stream. Documents from all input streams are combined in the order they are received, with no transformation or filtering applied.</p>");

            if (inputSnaps.Count > 1)
            {
                config.AppendLine("<p><strong>Data Flow:</strong></p>");
                config.AppendLine("<ul>");
                config.AppendLine("<li>Documents from all input streams maintain their original structure</li>");
                config.AppendLine("<li>No deduplication is performed - identical documents from different streams will both appear in the output</li>");
                config.AppendLine("<li>The order of documents in the output depends on the timing of document arrival from each input stream</li>");
                config.AppendLine("</ul>");
            }
            config.AppendLine("</div>");

            config.AppendLine("</div>");
            return config.ToString();
        }

        private List<(string condition, string outputPath)> ExtractRouterRoutes(SnapNode snap)
        {
            var routes = new List<(string condition, string outputPath)>();

            try
            {
                // First, check for SnapLogic router format in a JSON settings object
                if (snap.Properties.TryGetValue("settings", out string settingsJson))
                {
                    try
                    {
                        using var doc = JsonDocument.Parse(settingsJson);
                        if (doc.RootElement.TryGetProperty("routes", out var routesElement) &&
                            routesElement.TryGetProperty("value", out var routesArray) &&
                            routesArray.ValueKind == JsonValueKind.Array)
                        {
                            foreach (var route in routesArray.EnumerateArray())
                            {
                                if (route.TryGetProperty("expression", out var expressionObj) &&
                                    route.TryGetProperty("outputViewName", out var outputViewObj))
                                {
                                    // First check if expression has expression:true property
                                    bool isExpression = false;
                                    if (expressionObj.TryGetProperty("expression", out var exprFlag))
                                    {
                                        isExpression = exprFlag.GetBoolean();
                                    }

                                    string condition = "";
                                    if (expressionObj.TryGetProperty("value", out var valueElement))
                                    {
                                        condition = valueElement.GetString();

                                        // Add $ prefix to make it clear it's a document field if it's an expression
                                        if (isExpression && !string.IsNullOrEmpty(condition) && !condition.StartsWith("$"))
                                        {
                                            condition = "$" + condition;
                                        }
                                    }

                                    string outputView = "";
                                    if (outputViewObj.TryGetProperty("value", out var outputViewValue))
                                    {
                                        outputView = outputViewValue.GetString();
                                    }

                                    // Only add if we have both a condition and output path
                                    if (!string.IsNullOrEmpty(condition) && !string.IsNullOrEmpty(outputView))
                                    {
                                        routes.Add((condition, outputView));
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log the error and continue with other formats if JSON parsing fails
                        Console.WriteLine($"Error parsing settings JSON: {ex.Message}");
                    }
                }

                // If we still don't have routes, try to extract from flattened property structure
                if (routes.Count == 0)
                {
                    var routeEntries = snap.Properties
                        .Where(p => p.Key.StartsWith("settings.routes.value"))
                        .ToList();

                    if (routeEntries.Count > 0)
                    {
                        // Group by index number in the array
                        var routeGroups = routeEntries
                            .Select(entry => {
                                // Extract the index and property name
                                var match = System.Text.RegularExpressions.Regex.Match(entry.Key, @"settings\.routes\.value\[(\d+)\]\.(.+)");
                                if (match.Success)
                                {
                                    int index = int.Parse(match.Groups[1].Value);
                                    string propName = match.Groups[2].Value;
                                    return (Index: index, PropName: propName, Value: entry.Value);
                                }
                                return (Index: -1, PropName: "", Value: "");
                            })
                            .Where(x => x.Index >= 0)
                            .GroupBy(x => x.Index);

                        foreach (var group in routeGroups)
                        {
                            var conditionEntry = group.FirstOrDefault(x => x.PropName == "expression.value");
                            var pathEntry = group.FirstOrDefault(x => x.PropName == "outputViewName.value");

                            if (!string.IsNullOrEmpty(conditionEntry.Value) && !string.IsNullOrEmpty(pathEntry.Value))
                            {
                                routes.Add((conditionEntry.Value, pathEntry.Value));
                            }
                        }
                    }
                }

                // Check for alternative flattened format with direct route expressions
                if (routes.Count == 0)
                {
                    var routeExpEntries = snap.Properties
                        .Where(p => p.Key.Contains("routes") && p.Key.Contains("expression") && p.Key.EndsWith(".value"))
                        .ToList();

                    foreach (var expEntry in routeExpEntries)
                    {
                        // Extract the index number
                        var match = System.Text.RegularExpressions.Regex.Match(expEntry.Key, @"routes(?:\.value)?\[(\d+)\]\.expression\.value");
                        if (match.Success)
                        {
                            string index = match.Groups[1].Value;
                            string outputViewKey = expEntry.Key.Replace("expression.value", "outputViewName.value");

                            if (snap.Properties.TryGetValue(outputViewKey, out string outputView) &&
                                !string.IsNullOrEmpty(outputView) &&
                                !string.IsNullOrEmpty(expEntry.Value))
                            {
                                routes.Add((expEntry.Value, outputView));
                            }
                        }
                    }
                }

                // Check for standalone routes in JSON format
                if (routes.Count == 0)
                {
                    // Try multiple potential keys for routes
                    string[] routeKeys = { "routes", "routes.value", "settings.routes" };

                    foreach (var key in routeKeys)
                    {
                        if (snap.Properties.TryGetValue(key, out string routesJson) && !string.IsNullOrEmpty(routesJson))
                        {
                            try
                            {
                                using var doc = JsonDocument.Parse(routesJson);
                                if (doc.RootElement.ValueKind == JsonValueKind.Array)
                                {
                                    foreach (var route in doc.RootElement.EnumerateArray())
                                    {
                                        // Try multiple potential property names for paths and conditions
                                        string path = null;
                                        string condition = null;

                                        // Check various path property names
                                        foreach (var pathProp in new[] { "path", "outputPath", "outputViewName", "target", "output" })
                                        {
                                            if (route.TryGetProperty(pathProp, out var pathElement))
                                            {
                                                path = pathElement.ValueKind == JsonValueKind.String
                                                    ? pathElement.GetString()
                                                    : pathElement.TryGetProperty("value", out var pathValue)
                                                        ? pathValue.GetString()
                                                        : null;

                                                if (!string.IsNullOrEmpty(path)) break;
                                            }
                                        }

                                        // Check various condition property names
                                        foreach (var condProp in new[] { "condition", "expression", "filter", "criteria" })
                                        {
                                            if (route.TryGetProperty(condProp, out var condElement))
                                            {
                                                condition = condElement.ValueKind == JsonValueKind.String
                                                    ? condElement.GetString()
                                                    : condElement.TryGetProperty("value", out var condValue)
                                                        ? condValue.GetString()
                                                        : null;

                                                if (!string.IsNullOrEmpty(condition)) break;
                                            }
                                        }

                                        if (!string.IsNullOrEmpty(path) && !string.IsNullOrEmpty(condition))
                                        {
                                            routes.Add((condition, path));
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                // Log error and continue to next format
                                Console.WriteLine($"Error parsing routes JSON for key {key}: {ex.Message}");
                            }

                            // If we found routes, stop looking
                            if (routes.Count > 0) break;
                        }
                    }
                }

                // Check for property-based routes
                if (routes.Count == 0)
                {
                    // Look for properties matching common patterns for router conditions
                    var routeProps = snap.Properties
                        .Where(p => (p.Key.StartsWith("route.", StringComparison.OrdinalIgnoreCase) ||
                                   p.Key.EndsWith(".condition", StringComparison.OrdinalIgnoreCase) ||
                                   p.Key.Contains(".route.") ||
                                   p.Key.Contains(".expression.") ||
                                   (p.Key.Contains("output") && p.Key.Contains("condition"))) &&
                                   !string.IsNullOrEmpty(p.Value))
                        .ToList();

                    foreach (var prop in routeProps)
                    {
                        string path = prop.Key
                            .Replace(".condition", "")
                            .Replace(".expression", "");

                        if (path.StartsWith("route.", StringComparison.OrdinalIgnoreCase))
                        {
                            path = path.Substring(6);
                        }

                        // Extract output number if present (e.g., "output0", "output1")
                        var outputMatch = System.Text.RegularExpressions.Regex.Match(path, @"output(\d+)");
                        if (outputMatch.Success)
                        {
                            path = outputMatch.Value; // Use the full output name (output0, output1, etc.)
                        }

                        // Only add if we don't already have this path
                        if (!routes.Any(r => r.outputPath.Equals(path, StringComparison.OrdinalIgnoreCase)))
                        {
                            routes.Add((prop.Value, path));
                        }
                    }
                }

                // Check for numbered routes (output0, output1, etc.)
                if (routes.Count == 0)
                {
                    var outputProps = snap.Properties
                        .Where(p => System.Text.RegularExpressions.Regex.IsMatch(p.Key, @"output\d+") &&
                                  (p.Key.EndsWith(".condition") || p.Key.EndsWith(".expression")))
                        .ToList();

                    foreach (var prop in outputProps)
                    {
                        // Extract output name (output0, output1, etc.)
                        var match = System.Text.RegularExpressions.Regex.Match(prop.Key, @"(output\d+)");
                        if (match.Success)
                        {
                            string outputName = match.Groups[1].Value;
                            routes.Add((prop.Value, outputName));
                        }
                    }
                }

                // Check for conditional snap arguments/targets
                if (routes.Count == 0)
                {
                    var snapArguments = snap.Properties
                        .Where(p => p.Key.Contains("argument") || p.Key.Contains("target") || p.Key.Contains("path"))
                        .ToList();

                    foreach (var arg in snapArguments)
                    {
                        // Look for associated condition
                        var condKey = arg.Key.Replace("argument", "condition")
                                            .Replace("target", "condition")
                                            .Replace("path", "condition");

                        if (snap.Properties.TryGetValue(condKey, out string condition) &&
                            !string.IsNullOrEmpty(condition) &&
                            !string.IsNullOrEmpty(arg.Value))
                        {
                            routes.Add((condition, arg.Value));
                        }
                    }
                }

                // Add default route if present under various possible property names
                if (snap.Properties.TryGetValue("defaultRoute", out string defaultRoute) && !string.IsNullOrEmpty(defaultRoute))
                {
                    routes.Add((defaultRoute, "default"));
                }
                else if (snap.Properties.TryGetValue("defaultPath", out defaultRoute) && !string.IsNullOrEmpty(defaultRoute))
                {
                    routes.Add((defaultRoute, "default"));
                }
                else if (snap.Properties.TryGetValue("default.route", out defaultRoute) && !string.IsNullOrEmpty(defaultRoute))
                {
                    routes.Add((defaultRoute, "default"));
                }
                else if (snap.Properties.TryGetValue("settings.defaultRoute.value", out defaultRoute) && !string.IsNullOrEmpty(defaultRoute))
                {
                    routes.Add((defaultRoute, "default"));
                }
            }
            catch (Exception ex)
            {
                // Log the error and return any routes we've found so far
                Console.WriteLine($"Error extracting router routes: {ex.Message}");
            }

            return routes;
        }

        private List<string> ExtractJoinConditions(SnapNode snap)
        {
            var conditions = new List<string>();

            // Look for join expressions with equality operators
            var joinExpressions = snap.Properties.Where(p =>
                p.Key.ToLower().Contains("expression") &&
                !string.IsNullOrEmpty(p.Value) &&
                p.Value.Contains("==")).ToList();

            conditions.AddRange(joinExpressions.Select(e => e.Value));

            // Look for left/right path pairs in nested joinPaths structure (enhanced pattern matching)
            // Only get the .value properties, not .expression properties
            var leftPaths = snap.Properties.Where(p =>
                (p.Key.Contains("joinPaths.value") && p.Key.Contains("leftPath") && p.Key.EndsWith(".value")) ||
                (p.Key.ToLower().Contains("leftpath") && p.Key.EndsWith(".value")) ||
                (p.Key.ToLower().Contains("left_path") && p.Key.EndsWith(".value"))).ToList();

            var rightPaths = snap.Properties.Where(p =>
                (p.Key.Contains("joinPaths.value") && p.Key.Contains("rightPath") && p.Key.EndsWith(".value")) ||
                (p.Key.ToLower().Contains("rightpath") && p.Key.EndsWith(".value")) ||
                (p.Key.ToLower().Contains("right_path") && p.Key.EndsWith(".value"))).ToList();



            // Group left and right paths by index to handle multiple join conditions
            var pathPairs = new Dictionary<int, (string left, string right)>();

            // Extract index from path keys and group them
            foreach (var leftPath in leftPaths)
            {
                var indexMatch = System.Text.RegularExpressions.Regex.Match(leftPath.Key, @"\.(\d+)\.");
                if (indexMatch.Success && int.TryParse(indexMatch.Groups[1].Value, out int index))
                {
                    if (!pathPairs.ContainsKey(index))
                        pathPairs[index] = (leftPath.Value, "");
                    else
                        pathPairs[index] = (leftPath.Value, pathPairs[index].right);

                }
            }

            foreach (var rightPath in rightPaths)
            {
                var indexMatch = System.Text.RegularExpressions.Regex.Match(rightPath.Key, @"\.(\d+)\.");
                if (indexMatch.Success && int.TryParse(indexMatch.Groups[1].Value, out int index))
                {
                    if (!pathPairs.ContainsKey(index))
                        pathPairs[index] = ("", rightPath.Value);
                    else
                        pathPairs[index] = (pathPairs[index].left, rightPath.Value);

                }
            }

            // Create conditions from paired paths
            foreach (var pair in pathPairs.Values)
            {
                if (!string.IsNullOrEmpty(pair.left) && !string.IsNullOrEmpty(pair.right))
                {
                    string condition = $"{pair.left} == {pair.right}";
                    conditions.Add(condition);

                }
            }

            // Look for multijoin specific properties (join1, join2, etc.)
            var multiJoinProperties = snap.Properties.Where(p =>
                p.Key.ToLower().Contains("join") &&
                (p.Key.ToLower().Contains("condition") || p.Key.ToLower().Contains("field"))).ToList();

            foreach (var joinProp in multiJoinProperties)
            {
                if (!string.IsNullOrEmpty(joinProp.Value) && joinProp.Value.Contains("=="))
                {
                    conditions.Add(joinProp.Value);
                }
            }
            return conditions;
        }

        private string ExtractJoinType(SnapNode snap)
        {
            // Look for join type in properties, including nested SnapLogic structure
            var joinTypeProps = snap.Properties.Where(p =>
                p.Key.ToLower().Contains("jointype") ||
                p.Key.ToLower().Contains("join_type") ||
                p.Key.Contains("joinType.value") ||
                p.Key.Contains("settings.joinType") ||
                (p.Key.ToLower().Contains("type") && !p.Key.ToLower().Contains("datatype"))).ToList();

            foreach (var prop in joinTypeProps)
            {
                if (!string.IsNullOrEmpty(prop.Value) &&
                    (prop.Value.ToLower().Contains("inner") ||
                     prop.Value.ToLower().Contains("left") ||
                     prop.Value.ToLower().Contains("right") ||
                     prop.Value.ToLower().Contains("outer") ||
                     prop.Value.ToLower().Contains("full")))
                {
                    return prop.Value;
                }
            }

            return "Inner Join"; // Default assumption
        }

        private List<ConnectedSnap> GetConnectedSnaps(SnapNode snap, List<SnapNode> allSnaps)
        {
            var connectedSnaps = new List<ConnectedSnap>();

            // Get input snaps
            foreach (var inputConn in snap.InputConnections)
            {
                var inputSnap = allSnaps.FirstOrDefault(s => s.Id == inputConn.SourceId);
                if (inputSnap != null)
                {
                    connectedSnaps.Add(new ConnectedSnap
                    {
                        Label = inputSnap.Label,
                        Type = GetSimpleType(inputSnap.Type),
                        IsInput = true,
                        ConnectionInfo = inputConn.SourceViewId ?? ""
                    });
                }
            }

            // Get output snaps
            foreach (var outputConn in snap.OutputConnections)
            {
                var outputSnap = allSnaps.FirstOrDefault(s => s.Id == outputConn.TargetId);
                if (outputSnap != null)
                {
                    // For router snaps, if SourceViewId is empty, try to determine the output view
                    string connectionInfo = outputConn.SourceViewId ?? "";
                    
                    // If connection info is empty and this is a router snap, try to infer the output view
                    if (string.IsNullOrEmpty(connectionInfo) && snap.Type.Contains("router", StringComparison.OrdinalIgnoreCase))
                    {
                        // Try to match the connection with router output views
                        var outputViews = snap.Properties.Where(p => p.Key.Contains("outputView") || p.Key.Contains("output_view")).ToList();
                        if (outputViews.Any())
                        {
                            // Use a default pattern like "output0", "output1" based on connection order
                            var connectionIndex = snap.OutputConnections.ToList().IndexOf(outputConn);
                            connectionInfo = $"output{connectionIndex}";
                        }
                    }
                    
                    connectedSnaps.Add(new ConnectedSnap
                    {
                        Label = outputSnap.Label,
                        Type = GetSimpleType(outputSnap.Type),
                        IsInput = false,
                        ConnectionInfo = connectionInfo
                    });
                }
            }

            return connectedSnaps;
        }        private string GetSimpleType(string fullType)
        {
            // Extract simple type name from full SnapLogic type
            if (string.IsNullOrEmpty(fullType)) return "Unknown";
            
            var parts = fullType.Split('-');
            return parts.Length > 0 ? parts.Last().Replace("_", " ") : fullType;
        }        /// <summary>
        /// Gets the snaps connected to a specific router output (output0, output1, etc.)
        /// </summary>
        private List<ConnectedSnap> GetConnectedSnapsForOutput(SnapNode routerSnap, List<SnapNode> allSnaps, int outputIndex, string outputPath)
        {
            var connectedOutput = new List<ConnectedSnap>();
            
            // DEBUG: Log what we're working with
            Console.WriteLine($"[DEBUG] GetConnectedSnapsForOutput: Router '{routerSnap.Label}', OutputIndex={outputIndex}, OutputPath='{outputPath}'");
            Console.WriteLine($"[DEBUG] Router has {routerSnap.OutputConnections?.Count ?? 0} OutputConnections");
            
            // APPROACH 1: Check router's OutputConnections first
            if (routerSnap.OutputConnections != null && routerSnap.OutputConnections.Any())
            {
                Console.WriteLine($"[DEBUG] Trying Approach 1: OutputConnections");
                
                // Try explicit SourceViewId matching first
                var explicitMatches = routerSnap.OutputConnections.Where(conn => 
                    !string.IsNullOrEmpty(conn.SourceViewId) && 
                    (conn.SourceViewId.Equals(outputPath, StringComparison.OrdinalIgnoreCase) ||
                     conn.SourceViewId.Equals($"output{outputIndex}", StringComparison.OrdinalIgnoreCase))).ToList();
                
                if (explicitMatches.Any())
                {
                    Console.WriteLine($"[DEBUG] Found {explicitMatches.Count} explicit matches");
                    foreach (var conn in explicitMatches)
                    {
                        var outputSnap = allSnaps.FirstOrDefault(s => s.Id == conn.TargetId);
                        if (outputSnap != null)
                        {
                            connectedOutput.Add(new ConnectedSnap
                            {
                                Label = outputSnap.Label,
                                Type = GetSimpleType(outputSnap.Type),
                                IsInput = false,
                                ConnectionInfo = conn.SourceViewId ?? ""
                            });
                        }
                    }
                    return connectedOutput;
                }
                
                // Try connection order for empty/null SourceViewId
                var emptySourceViewConnections = routerSnap.OutputConnections.Where(conn => 
                    string.IsNullOrEmpty(conn.SourceViewId)).ToList();
                
                if (emptySourceViewConnections.Any() && outputIndex < emptySourceViewConnections.Count)
                {
                    Console.WriteLine($"[DEBUG] Using connection order, found {emptySourceViewConnections.Count} empty SourceViewId connections");
                    var conn = emptySourceViewConnections[outputIndex];
                    var outputSnap = allSnaps.FirstOrDefault(s => s.Id == conn.TargetId);
                    if (outputSnap != null)
                    {
                        Console.WriteLine($"[DEBUG] Matched output {outputIndex} to snap '{outputSnap.Label}'");
                        connectedOutput.Add(new ConnectedSnap
                        {
                            Label = outputSnap.Label,
                            Type = GetSimpleType(outputSnap.Type),
                            IsInput = false,
                            ConnectionInfo = $"output{outputIndex}" // Inferred
                        });
                        return connectedOutput;
                    }
                }
                
                // Fallback: use any output connection by index
                if (outputIndex < routerSnap.OutputConnections.Count)
                {
                    Console.WriteLine($"[DEBUG] Fallback: Using connection by index");
                    var conn = routerSnap.OutputConnections[outputIndex];
                    var outputSnap = allSnaps.FirstOrDefault(s => s.Id == conn.TargetId);
                    if (outputSnap != null)
                    {
                        connectedOutput.Add(new ConnectedSnap
                        {
                            Label = outputSnap.Label,
                            Type = GetSimpleType(outputSnap.Type),
                            IsInput = false,
                            ConnectionInfo = conn.SourceViewId ?? $"output{outputIndex}"
                        });
                        return connectedOutput;
                    }
                }
            }
            
            // APPROACH 2: Find connections by examining other snaps' InputConnections
            // This is a backup approach when OutputConnections is not populated correctly
            Console.WriteLine($"[DEBUG] Trying Approach 2: Reverse lookup via InputConnections");
            
            var targetSnaps = allSnaps.Where(snap => snap.Id != routerSnap.Id && 
                snap.InputConnections != null && 
                snap.InputConnections.Any(conn => conn.SourceId == routerSnap.Id)).ToList();
            
            Console.WriteLine($"[DEBUG] Found {targetSnaps.Count} snaps with input connections from this router");
            
            // Try to match by connection order or TargetViewId patterns
            foreach (var targetSnap in targetSnaps)
            {
                var connectionFromRouter = targetSnap.InputConnections.FirstOrDefault(conn => conn.SourceId == routerSnap.Id);
                if (connectionFromRouter != null)
                {
                    // Check if this connection matches our output index
                    bool isMatch = false;
                    
                    // Try to match by TargetViewId or SourceViewId patterns
                    if (!string.IsNullOrEmpty(connectionFromRouter.TargetViewId) && 
                        connectionFromRouter.TargetViewId.Contains($"output{outputIndex}"))
                    {
                        isMatch = true;
                    }
                    else if (!string.IsNullOrEmpty(connectionFromRouter.SourceViewId) && 
                             connectionFromRouter.SourceViewId.Contains($"output{outputIndex}"))
                    {
                        isMatch = true;
                    }
                    else
                    {                        // Use connection order as fallback
                        var allConnectionsFromRouter = allSnaps
                            .SelectMany(s => s.InputConnections?.Where(c => c.SourceId == routerSnap.Id) ?? new List<SnapLink>())
                            .OrderBy(c => c.TargetId) // Ensure consistent ordering
                            .ToList();
                        
                        var connectionIndex = allConnectionsFromRouter.IndexOf(connectionFromRouter);
                        if (connectionIndex == outputIndex)
                        {
                            isMatch = true;
                        }
                    }
                    
                    if (isMatch)
                    {
                        Console.WriteLine($"[DEBUG] Matched output {outputIndex} to snap '{targetSnap.Label}' via reverse lookup");
                        connectedOutput.Add(new ConnectedSnap
                        {
                            Label = targetSnap.Label,
                            Type = GetSimpleType(targetSnap.Type),
                            IsInput = false,
                            ConnectionInfo = connectionFromRouter.SourceViewId ?? $"output{outputIndex}"
                        });
                        return connectedOutput;
                    }
                }
            }
            
            Console.WriteLine($"[DEBUG] No connections found for output {outputIndex}");
            return connectedOutput;
        }

        /// <summary>
        /// Generates pseudocode for router snap logic
        /// </summary>
        private string GenerateRouterPseudocode(SnapNode routerSnap, List<(string condition, string outputPath)> routes)
        {
            var sb = new StringBuilder();

            sb.AppendLine($"BEGIN {routerSnap.Label.Replace(" ", "")}Router");
            sb.AppendLine("    WHILE input has more documents DO");
            sb.AppendLine("        READ document from input");
            sb.AppendLine("        SET routed = FALSE");
            sb.AppendLine();

            if (routes.Any())
            {
                sb.AppendLine("        // Evaluate routing conditions");
                foreach (var (condition, outputPath) in routes.Where(r => r.outputPath != "default"))
                {
                    sb.AppendLine($"        // Route to: {outputPath}");

                    // Format the condition for better readability
                    string formattedCondition = FormatRouterCondition(condition);

                    sb.AppendLine($"        IF {formattedCondition} THEN");
                    sb.AppendLine($"            WRITE document to {outputPath} output");
                    sb.AppendLine("            SET routed = TRUE");

                    // If firstMatch is enabled, we exit after the first match
                    if (routerSnap.Properties.TryGetValue("settings.firstMatch.value", out string firstMatchStr) &&
                        bool.TryParse(firstMatchStr, out bool firstMatch) && firstMatch)
                    {
                        sb.AppendLine("            EXIT routing (first match only)");
                    }

                    sb.AppendLine("        END IF");
                }

                // Handle default route if present
                var defaultRoute = routes.FirstOrDefault(r => r.outputPath == "default");
                if (defaultRoute != default)
                {
                    sb.AppendLine();
                    sb.AppendLine("        // Default route handling");
                    sb.AppendLine("        IF routed = FALSE THEN");
                    sb.AppendLine("            WRITE document to default output");
                    sb.AppendLine("        END IF");
                }
                else
                {
                    sb.AppendLine();
                    sb.AppendLine("        // No default route specified");
                    sb.AppendLine("        IF routed = FALSE THEN");
                    sb.AppendLine("            // Document not routed when no conditions match");
                    sb.AppendLine("        END IF");
                }
            }
            else
            {
                sb.AppendLine("        // No routing conditions defined");
                sb.AppendLine("        WRITE document to default output");
            }

            sb.AppendLine("    END WHILE");
            sb.AppendLine("END");

            return sb.ToString();
        }

        /// <summary>
        /// Formats router conditions for better readability in pseudocode
        /// </summary>
        private string FormatRouterCondition(string condition)
        {
            if (string.IsNullOrEmpty(condition))
                return "TRUE";

            // Clean up common SnapLogic expression patterns
            string formatted = condition
                .Replace("$", "")  // Remove $ prefix from variables
                .Replace("==", " equals ")
                .Replace("!=", " not equals ")
                .Replace("&&", " AND ")
                .Replace("||", " OR ")
                .Replace("!", " NOT ");

            return formatted;
        }

        public class ConnectedSnap
        {
            public string Label { get; set; } = "";
            public string Type { get; set; } = "";
            public bool IsInput { get; set; }
            public string ConnectionInfo { get; set; } = "";
        }
    }
}
