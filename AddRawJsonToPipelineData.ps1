# Script to add raw JSON storage to PipelineData class

$filePath = "SlpAnalyzer.cs"

Write-Host "Adding raw JSON storage to PipelineData..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Step 1: Add RawPipelineJson property to PipelineData class
$pipelineClassPattern = 'public List<SnapLink> Links { get; set; } = new List<SnapLink>();'
$newProperty = @'
public List<SnapLink> Links { get; set; } = new List<SnapLink>();
        public JObject RawPipelineJson { get; set; } // Store raw pipeline JSON for account extraction
'@

if ($content.Contains($pipelineClassPattern)) {
    $content = $content.Replace($pipelineClassPattern, $newProperty)
    Write-Host "Added RawPipelineJson property to PipelineData" -ForegroundColor Yellow
} else {
    Write-Host "PipelineData Links property not found" -ForegroundColor Red
}

# Step 2: Store the raw JSON in the AnalyzePipeline method
$analyzePipelinePattern = 'JObject pipelineJson = JObject.Parse(slpContent);'
$newAnalyzeCode = @'
JObject pipelineJson = JObject.Parse(slpContent);
                
                // Store raw JSON for account extraction
'@

if ($content.Contains($analyzePipelinePattern)) {
    $content = $content.Replace($analyzePipelinePattern, $newAnalyzeCode)
    Write-Host "Updated AnalyzePipeline method" -ForegroundColor Yellow
} else {
    Write-Host "AnalyzePipeline JObject.Parse not found" -ForegroundColor Red
}

# Step 3: Set the RawPipelineJson property in the pipeline object creation
$pipelineCreationPattern = @'
PipelineData pipeline = new PipelineData
                {
                    Name = GetPipelineName(pipelineJson),
                    Author = GetPipelineAuthor(pipelineJson),
                    Parameters = GetPipelineParameters(pipelineJson),
                    Snaps = GetSnaps(pipelineJson),
                    Links = GetLinks(pipelineJson)
                };
'@

$newPipelineCreation = @'
PipelineData pipeline = new PipelineData
                {
                    Name = GetPipelineName(pipelineJson),
                    Author = GetPipelineAuthor(pipelineJson),
                    Parameters = GetPipelineParameters(pipelineJson),
                    Snaps = GetSnaps(pipelineJson),
                    Links = GetLinks(pipelineJson),
                    RawPipelineJson = pipelineJson
                };
'@

if ($content.Contains($pipelineCreationPattern)) {
    $content = $content.Replace($pipelineCreationPattern, $newPipelineCreation)
    Write-Host "Updated pipeline creation to store raw JSON" -ForegroundColor Yellow
} else {
    Write-Host "Pipeline creation pattern not found" -ForegroundColor Red
}

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Enhanced SlpAnalyzer with raw JSON storage!" -ForegroundColor Green
