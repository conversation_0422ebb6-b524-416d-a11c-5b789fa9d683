using System;
using System.Collections.Generic;
using System.Linq;

namespace RouterTestMinimal
{
    // Minimal data classes for testing
    public enum SnapCategory
    {
        FlowControl,
        Transform,
        Other
    }

    public class Connection
    {
        public string SourceId { get; set; }
        public string TargetId { get; set; }
        public string SourceViewId { get; set; }
    }

    public class SnapNode
    {
        public string Id { get; set; }
        public string Label { get; set; }
        public string Type { get; set; }
        public SnapCategory Category { get; set; }
        public Dictionary<string, string> Properties { get; set; } = new Dictionary<string, string>();
        public List<Connection> OutputConnections { get; set; } = new List<Connection>();
    }

    // Simplified version of the ConnectedSnap class
    public class ConnectedSnap
    {
        public string SnapId { get; set; }
        public string SnapLabel { get; set; }
        public string ViewName { get; set; }
    }

    // Minimal FlowControlConfigurationGenerator for testing
    public class FlowControlConfigurationGenerator
    {
        public string GenerateRouterConfiguration(SnapNode routerSnap, List<SnapNode> allSnaps)
        {
            var connectedSnaps = GetConnectedSnaps(routerSnap, allSnaps);
            
            if (!connectedSnaps.Any())
            {
                return "No connected snaps found.";
            }

            var config = "Router Configuration:\n";
            config += $"Snap: {routerSnap.Label}\n";
            config += "Connected Snaps:\n";
            
            foreach (var snap in connectedSnaps)
            {
                config += $"  - {snap.SnapLabel} (via {snap.ViewName})\n";
            }
            
            return config;
        }

        private List<ConnectedSnap> GetConnectedSnaps(SnapNode routerSnap, List<SnapNode> allSnaps)
        {
            var connectedSnaps = new List<ConnectedSnap>();
            
            if (routerSnap.OutputConnections == null || !routerSnap.OutputConnections.Any())
            {
                return connectedSnaps;
            }

            // Create a dictionary for quick snap lookup
            var snapLookup = allSnaps.ToDictionary(s => s.Id);

            for (int i = 0; i < routerSnap.OutputConnections.Count; i++)
            {
                var outputConn = routerSnap.OutputConnections[i];
                
                if (snapLookup.TryGetValue(outputConn.TargetId, out var targetSnap))
                {
                    // ROUTER FIX: Handle empty SourceViewId by inferring output view names
                    string viewName = string.IsNullOrEmpty(outputConn.SourceViewId) 
                        ? $"output{i}" 
                        : outputConn.SourceViewId;
                    
                    connectedSnaps.Add(new ConnectedSnap
                    {
                        SnapId = targetSnap.Id,
                        SnapLabel = targetSnap.Label,
                        ViewName = viewName
                    });
                }
            }

            return connectedSnaps;
        }
    }    class Program
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("=== Testing Router Connection Fix ===");
            Console.WriteLine();

            // Create a mock router snap with routes
            var routerSnap = new SnapNode
            {
                Id = "router-test-001",
                Label = "Route Unit Count",
                Type = "com-snaplogic-snaps-flow-router",
                Category = SnapCategory.FlowControl,
                Properties = new Dictionary<string, string>
                {
                    ["settings.routes.value[0].expression.value"] = "$unitCount > 100",
                    ["settings.routes.value[0].outputViewName.value"] = "highValue",
                    ["settings.routes.value[1].expression.value"] = "$unitCount <= 100",
                    ["settings.routes.value[1].outputViewName.value"] = "lowValue"
                },
                OutputConnections = new List<Connection>
                {
                    new Connection { SourceId = "router-test-001", TargetId = "snap-high-001", SourceViewId = "" }, // Empty SourceViewId
                    new Connection { SourceId = "router-test-001", TargetId = "snap-low-001", SourceViewId = "" }   // Empty SourceViewId
                }
            };

            // Create connected snaps
            var allSnaps = new List<SnapNode>
            {
                routerSnap,
                new SnapNode { Id = "snap-high-001", Label = "Process High Value Orders", Type = "com-snaplogic-snaps-transform-mapper" },
                new SnapNode { Id = "snap-low-001", Label = "Process Low Value Orders", Type = "com-snaplogic-snaps-transform-mapper" }
            };

            Console.WriteLine($"Router Snap: {routerSnap.Label}");
            Console.WriteLine($"Output Connections: {routerSnap.OutputConnections.Count}");
            Console.WriteLine();

            // Test the FlowControlConfigurationGenerator
            var generator = new FlowControlConfigurationGenerator();
            
            try
            {
                Console.WriteLine("Testing router configuration generation...");
                string config = generator.GenerateRouterConfiguration(routerSnap, allSnaps);
                
                Console.WriteLine("✅ Configuration generated successfully!");
                Console.WriteLine();
                Console.WriteLine("Generated Configuration:");
                Console.WriteLine("========================");
                Console.WriteLine(config);
                
                // Check if the configuration contains proper snap connections
                if (config.Contains("Process High Value Orders") && config.Contains("Process Low Value Orders"))
                {
                    Console.WriteLine();
                    Console.WriteLine("✅ SUCCESS: Connected snaps are properly displayed in router configuration!");
                    Console.WriteLine("✅ FIX VERIFIED: Router snap connections no longer show empty quotes");
                    
                    // Check for proper output view naming
                    if (config.Contains("output0") && config.Contains("output1"))
                    {
                        Console.WriteLine("✅ OUTPUT VIEWS: Fallback output view names (output0, output1) generated correctly");
                    }
                }
                else if (config.Contains("No connected snaps"))
                {
                    Console.WriteLine();
                    Console.WriteLine("❌ ISSUE: Still showing 'No connected snaps' - fix may need refinement");
                }
                else
                {
                    Console.WriteLine();
                    Console.WriteLine("⚠️  PARTIAL: Configuration generated but connection status unclear");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
