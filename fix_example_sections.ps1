# Remove Example Usage sections for specified snap types
$filePath = "SnapBestPractices.cs"
$backupPath = "SnapBestPractices_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').cs"

# Create a backup
Copy-Item -Path $filePath -Destination $backupPath
Write-Host "Created backup at $backupPath"

# Read the file content
$content = Get-Content -Path $filePath -Raw

# These are patterns for different snap types with their respective replacements
$patterns = @(
    # Router snap - already fixed, just including for reference
    @{
        Pattern = 'details.AppendLine\("<li>Keep routing conditions simple and understandable\.</li>"\);\r?\ndetails.AppendLine\("</ul>"\);'
        Replacement = 'details.AppendLine("<li>Keep routing conditions simple and understandable.</li>");\r\ndetails.AppendLine("</ul>");\r\n// No Example Usage section for Router snap types as per requirements'
    },
    # JSON Formatter snap
    @{
        Pattern = 'details.AppendLine\("<li>Use the ''Pretty print'' option for human-readable output during development or for logging\.</li>"\);\r?\ndetails.AppendLine\("</ul>"\);(?!\r?\n// No Example)'
        Replacement = 'details.AppendLine("<li>Use the ''Pretty print'' option for human-readable output during development or for logging.</li>");\r\ndetails.AppendLine("</ul>");\r\n// No Example Usage section for JSON Formatter snap types as per requirements'
    },
    # Script snap
    @{
        Pattern = 'details.AppendLine\("<li>Be mindful of script execution time, as it can impact pipeline performance\.</li>"\);\r?\ndetails.AppendLine\("</ul>"\);\r?\ndetails.AppendLine\("<p><strong>Example Usage:</strong></p>"\);'
        Replacement = 'details.AppendLine("<li>Be mindful of script execution time, as it can impact pipeline performance.</li>");\r\ndetails.AppendLine("</ul>");\r\n// No Example Usage section for Script snap types as per requirements'
    },
    # Filter snap
    @{
        Pattern = 'details.AppendLine\("<li>Avoid overly complex expressions in a single Filter; consider multiple Filters or a Script snap\.</li>"\);\r?\ndetails.AppendLine\("</ul>"\);(?!\r?\n// No Example)'
        Replacement = 'details.AppendLine("<li>Avoid overly complex expressions in a single Filter; consider multiple Filters or a Script snap.</li>");\r\ndetails.AppendLine("</ul>");\r\n// No Example Usage section for Filter snap types as per requirements'
    },
    # Copy snap
    @{
        Pattern = 'details.AppendLine\("<ul><li>Use Copy snaps to duplicate document streams for parallel processing paths\.</li><li>Be aware that this creates full duplicates of documents, which can increase memory usage\.</li></ul>"\);\r?\ndetails.AppendLine\("<p><strong>Example Usage:</strong></p>"\);'
        Replacement = 'details.AppendLine("<ul><li>Use Copy snaps to duplicate document streams for parallel processing paths.</li><li>Be aware that this creates full duplicates of documents, which can increase memory usage.</li></ul>");\r\n// No Example Usage section for Copy snap types as per requirements'
    },
    # Pipeline Execute snap
    @{
        Pattern = 'details.AppendLine\("<ul><li>Pass only necessary parameters to the child pipeline\.</li><li>Ensure the child pipeline is designed to handle the inputs and produce expected outputs\.</li><li>Manage error handling for child pipeline failures\.</li></ul>"\);\r?\ndetails.AppendLine\("<p><strong>Example Usage:</strong></p>"\);'
        Replacement = 'details.AppendLine("<ul><li>Pass only necessary parameters to the child pipeline.</li><li>Ensure the child pipeline is designed to handle the inputs and produce expected outputs.</li><li>Manage error handling for child pipeline failures.</li></ul>");\r\n// No Example Usage section for Pipeline Execute snap types as per requirements'
    },
    # Generic Transformation snap
    @{
        Pattern = 'details.AppendLine\("<ul><li>Understand the specific transformation logic this snap applies\.</li><li>Validate output against expected schema and values\.</li></ul>"\);\r?\ndetails.AppendLine\("<p><strong>Example Usage:</strong></p>"\);'
        Replacement = 'details.AppendLine("<ul><li>Understand the specific transformation logic this snap applies.</li><li>Validate output against expected schema and values.</li></ul>");\r\n// No Example Usage section for Generic Transformation snap types as per requirements'
    }
)

# Apply all replacements
foreach ($pattern in $patterns) {
    $content = $content -replace $pattern.Pattern, $pattern.Replacement
    Write-Host "Applied replacement for pattern: $($pattern.Pattern.Substring(0, 50))..."
}

# Write the modified content back to the file
Set-Content -Path $filePath -Value $content
Write-Host "All replacements applied to $filePath"
