using System;
using System.Threading.Tasks;

namespace SnapAnalyser
{
    public class AIGeneratorTest
    {
        public static void TestConstructor()
        {
            Console.WriteLine("=== Testing AIDescriptionGenerator Constructor ===");
            Console.WriteLine($"Time: {DateTime.Now:HH:mm:ss}");
            
            try
            {
                Console.WriteLine("1. Testing ConfigManager properties...");
                string apiKey = ConfigManager.OpenAIApiKey;
                string endpoint = ConfigManager.AzureOpenAIEndpoint;
                
                Console.WriteLine($"   API Key present: {!string.IsNullOrEmpty(apiKey)}");
                Console.WriteLine($"   Endpoint: {endpoint}");
                
                Console.WriteLine("2. Creating AIDescriptionGenerator...");
                var generator = new AIDescriptionGenerator(null);
                
                Console.WriteLine("3. ✅ SUCCESS - Generator created!");
                
                Console.WriteLine("4. Testing properties...");
                Console.WriteLine($"   UseCachedDescriptions: {generator.UseCachedDescriptions}");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: {ex.Message}");
                Console.WriteLine($"Type: {ex.GetType().Name}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner: {ex.InnerException.Message}");
                }
                Console.WriteLine($"Stack: {ex.StackTrace}");
            }
        }
    }
}
