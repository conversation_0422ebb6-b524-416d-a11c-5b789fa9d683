# BatchHangingFix_ConfigOptimization.ps1
# Script to optimize configuration settings for the batch hanging fix

$configPath = "config.json"

Write-Host "=== BATCH HANGING FIX - CONFIG OPTIMIZATION ===" -ForegroundColor Green
Write-Host "Optimizing timeout settings for reliable batch processing..." -ForegroundColor Yellow

if (Test-Path $configPath) {
    try {
        # Read current config
        $config = Get-Content $configPath | ConvertFrom-Json
        
        Write-Host "`nCurrent Configuration:" -ForegroundColor Cyan
        Write-Host "  Azure OpenAI Timeout: $($config.AzureOpenAITimeoutSeconds) seconds" -ForegroundColor White
        
        # Optimize timeout settings based on our analysis
        $originalTimeout = $config.AzureOpenAITimeoutSeconds
        
        if ($config.AzureOpenAITimeoutSeconds -lt 60) {
            Write-Host "`n⚠️  WARNING: Current timeout ($($config.AzureOpenAITimeoutSeconds)s) may be too short for complex operations" -ForegroundColor Yellow
            Write-Host "   Recommended: 90-120 seconds for reliable processing" -ForegroundColor Yellow
            
            # Ask user for confirmation
            $response = Read-Host "`nWould you like to update to recommended timeout of 120 seconds? (y/n)"
            
            if ($response -eq 'y' -or $response -eq 'Y') {
                $config.AzureOpenAITimeoutSeconds = 120
                
                # Create backup
                Copy-Item $configPath "$configPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
                Write-Host "  ✅ Created backup of original config" -ForegroundColor Green
                
                # Save optimized config
                $config | ConvertTo-Json -Depth 10 | Set-Content $configPath
                Write-Host "  ✅ Updated timeout from $originalTimeout to 120 seconds" -ForegroundColor Green
            }
        } else {
            Write-Host "  ✅ Current timeout ($($config.AzureOpenAITimeoutSeconds)s) is already optimized" -ForegroundColor Green
        }
        
        Write-Host "`nOptimized Configuration Summary:" -ForegroundColor Cyan
        Write-Host "  ✅ Azure OpenAI Timeout: $($config.AzureOpenAITimeoutSeconds) seconds" -ForegroundColor Green
        Write-Host "  ✅ HttpClient Timeout: 5 minutes (hardcoded safety net)" -ForegroundColor Green
        Write-Host "  ✅ Per-file Timeout: 5 minutes (batch processing)" -ForegroundColor Green
        Write-Host "  ✅ AI Operation Timeout: 2 minutes (individual operations)" -ForegroundColor Green
        Write-Host "  ✅ Overall Batch Timeout: 30 minutes" -ForegroundColor Green
        
        Write-Host "`n🎯 CONFIGURATION OPTIMIZATION COMPLETE!" -ForegroundColor Green
        Write-Host "   The application should now handle batch processing reliably without hanging." -ForegroundColor Green
        
    } catch {
        Write-Host "❌ Error reading or updating config: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Config file not found: $configPath" -ForegroundColor Red
}

Write-Host "`n📋 Next Steps:" -ForegroundColor Cyan
Write-Host "  1. Test batch processing with AI enabled" -ForegroundColor White
Write-Host "  2. Monitor ai_processing_log files for performance metrics" -ForegroundColor White
Write-Host "  3. Verify no hanging occurs during large batch operations" -ForegroundColor White
Write-Host "  4. Check Generated Documentation folder for output" -ForegroundColor White
