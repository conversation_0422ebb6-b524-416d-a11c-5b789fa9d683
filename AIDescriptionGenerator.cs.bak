using System.Text;
using System.IO;

namespace SnapAnalyser
{
    public class AIDescriptionGenerator
    {
        private string _logFilePath;
        private bool _isLoggingEnabled = false;
        public AIDescriptionGenerator(string apiKey)
        {
            // API key parameter is kept for backward compatibility but is not used
            Console.WriteLine("AIDescriptionGenerator initialized with local implementation");
        }
        
        // Enable logging with a specific path for the log file
        public void EnableLogging(string outputFolder)
        {
            try
            {
                // Create logs directory if it doesn't exist
                Directory.CreateDirectory(outputFolder);
                
                // Set the log file path
                _logFilePath = Path.Combine(outputFolder, $"ai_processing_log_{DateTime.Now:yyyy-MM-dd_HHmmss}.log");
                _isLoggingEnabled = true;
                
                // Create the log file with a header
                File.WriteAllText(_logFilePath, $"AI Processing Log - Started at {DateTime.Now}\r\n");
                LogMessage("Logging initialized");
            }
            catch (Exception ex)
            {
                // Can't use LogMessage here since logging isn't set up yet
                Console.WriteLine($"Error setting up logging: {ex.Message}");
                _isLoggingEnabled = false;
            }
        }

        // Method to log messages both to console and file
        public void LogMessage(string message)
        {
            // Always log to console
            Console.WriteLine(message);
            
            // Log to file if enabled
            if (_isLoggingEnabled)
            {
                try
                {
                    File.AppendAllText(_logFilePath, $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}\r\n");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error writing to log file: {ex.Message}");
                }
            }
        }

        public async Task<string> GeneratePipelineDescription(PipelineData pipeline)
        {
            // Create a summary of the pipeline for the AI to work with
            var pipelineSummary = CreatePipelineSummary(pipeline);
            
            // Enhanced prompt to focus on the pipeline's business purpose and high-level functionality
            string prompt = $"Generate a comprehensive introduction for this SnapLogic pipeline based on the following information:\n\n{pipelineSummary}\n\n" +
                           "Focus on explaining:\n" +
                           "1. The business purpose of this pipeline (what business problem it solves)\n" +
                           "2. The key data sources and destinations\n" +
                           "3. The main transformations or processing steps\n" +
                           "4. The expected business outcomes or values\n\n" +
                           "Use professional, clear language suitable for technical documentation. " +
                           "Keep the description concise but informative (3-5 paragraphs maximum).";

            try
            {
                // First try OpenAI
                var result = await SendPromptToAI(prompt);
                
                // If we got an error about quota, try the fallback free service                
                if (result.StartsWith("OpenAI API quota exceeded"))
                {
                    LogMessage("OpenAI quota exceeded, trying fallback free service");
                    return await SendPromptToFreeAI(prompt);
                }
                
                return result;
            }            catch (Exception ex)
            {
                LogMessage($"Error in primary AI service: {ex.Message}");
                
                // Try fallback on any error
                try 
                {
                    return await SendPromptToFreeAI(prompt);
                }
                catch (Exception fallbackEx)
                {
                    LogMessage($"Error in fallback AI service: {fallbackEx.Message}");
                    return GetGenericDescription(pipeline);
                }
            }
        }

        public async Task<string> GenerateSnapDescription(SnapNode snap, PipelineData pipeline)
        {
            // Create a description of the snap and its context
            var snapContext = CreateSnapContext(snap, pipeline);
            
            // The prompt to send to the AI
            string prompt = $"Generate a detailed technical description of this SnapLogic snap component based on the following information:\n\n{snapContext}\n\n" +
                           "Explain what this snap does in the pipeline, its configuration, input/output relationships, and its role " +
                           "in the overall data flow. Be specific about its technical function but use clear language.";

            return await SendPromptToAI(prompt);
        }

        public async Task<string> GenerateDataFlowAnalysis(PipelineData pipeline)
        {
            // Create a representation of the data flow
            var dataFlowSummary = CreateDataFlowSummary(pipeline);
            
            // The prompt to send to the AI
            string prompt = $"Analyze the data flow in this SnapLogic pipeline based on the following information:\n\n{dataFlowSummary}\n\n" +
                           "Explain how data moves through this pipeline, identify key transformation points, potential bottlenecks, " +
                           "and important decision points. Describe the overall architecture pattern being used and suggest best practices.";

            return await SendPromptToAI(prompt);
        }

        private string CreatePipelineSummary(PipelineData pipeline)
        {
            var sb = new StringBuilder();
            
            sb.AppendLine($"Pipeline Name: {pipeline.Name}");
            sb.AppendLine($"Author: {pipeline.Author}");
            sb.AppendLine();
            
            // Parameters
            sb.AppendLine("Parameters:");
            foreach (var param in pipeline.Parameters)
            {
                sb.AppendLine($"- {param.Key} ({param.DataType}): {param.Value} {(param.Required ? "(Required)" : "")}");
            }
            sb.AppendLine();
            
            // Snap categories and counts
            var snapCategories = new Dictionary<SnapCategory, int>();
            foreach (var snap in pipeline.Snaps)
            {
                if (snapCategories.ContainsKey(snap.Category))
                    snapCategories[snap.Category]++;
                else
                    snapCategories[snap.Category] = 1;
            }
            
            sb.AppendLine("Snap Categories:");
            foreach (var category in snapCategories)
            {
                sb.AppendLine($"- {category.Key}: {category.Value} snaps");
            }
            sb.AppendLine();
            
            // Entry and exit points
            sb.AppendLine("Entry Points:");
            foreach (var snap in pipeline.GetStartPoints())
            {
                sb.AppendLine($"- {snap.Label} ({snap.Type})");
            }
            
            sb.AppendLine("Exit Points:");
            foreach (var snap in pipeline.GetEndPoints())
            {
                sb.AppendLine($"- {snap.Label} ({snap.Type})");
            }
            
            return sb.ToString();
        }

        private string CreateSnapContext(SnapNode snap, PipelineData pipeline)
        {
            var sb = new StringBuilder();
            
            sb.AppendLine($"Snap Name: {snap.Label}");
            sb.AppendLine($"Snap Type: {snap.Type}");
            sb.AppendLine($"Category: {snap.Category}");
            sb.AppendLine();
            
            // Properties
            sb.AppendLine("Properties:");
            foreach (var prop in snap.Properties)
            {
                sb.AppendLine($"- {prop.Key}: {prop.Value}");
            }
            sb.AppendLine();
            
            // Input connections
            sb.AppendLine("Input Connections:");
            foreach (var link in snap.InputConnections)
            {
                var sourceSnap = pipeline.Snaps.Find(s => s.Id == link.SourceId);
                if (sourceSnap != null)
                {
                    sb.AppendLine($"- From: {sourceSnap.Label} ({sourceSnap.Type})");
                }
            }
            sb.AppendLine();
            
            // Output connections
            sb.AppendLine("Output Connections:");
            foreach (var link in snap.OutputConnections)
            {
                var targetSnap = pipeline.Snaps.Find(s => s.Id == link.TargetId);
                if (targetSnap != null)
                {
                    sb.AppendLine($"- To: {targetSnap.Label} ({targetSnap.Type})");
                }
            }
            
            return sb.ToString();
        }

        private string CreateDataFlowSummary(PipelineData pipeline)
        {
            var sb = new StringBuilder();
            
            sb.AppendLine($"Pipeline Name: {pipeline.Name}");
            sb.AppendLine($"Total Snaps: {pipeline.Snaps.Count}");
            sb.AppendLine($"Total Links: {pipeline.Links.Count}");
            sb.AppendLine();
            
            // Find some main flow paths
            sb.AppendLine("Key Execution Paths:");
            var startPoints = pipeline.GetStartPoints();
            int pathCount = 0;
            
            foreach (var startPoint in startPoints)
            {
                var paths = FindMainPaths(pipeline, startPoint);
                foreach (var path in paths.Take(2)) // Limit to 2 paths per start point
                {
                    pathCount++;
                    sb.AppendLine($"Path {pathCount}:");
                    for (int i = 0; i < path.Count; i++)
                    {
                        var node = path[i];
                        sb.AppendLine($"  {i+1}. {node.Label} ({node.Category})");
                    }
                    sb.AppendLine();
                }
            }
            
            return sb.ToString();
        }
        
        private List<List<SnapNode>> FindMainPaths(PipelineData pipeline, SnapNode startNode, int maxDepth = 10)
        {
            var results = new List<List<SnapNode>>();
            var currentPath = new List<SnapNode> { startNode };
            var visited = new HashSet<string>();
            
            FindPathsRecursive(pipeline, startNode, currentPath, visited, results, maxDepth);
            
            return results;
        }
        
        private void FindPathsRecursive(PipelineData pipeline, SnapNode currentNode, List<SnapNode> currentPath, 
            HashSet<string> visited, List<List<SnapNode>> results, int remainingDepth)
        {
            if (remainingDepth <= 0 || visited.Contains(currentNode.Id))
            {
                return;
            }
            
            visited.Add(currentNode.Id);
            
            if (currentNode.OutputConnections.Count == 0)
            {
                results.Add(new List<SnapNode>(currentPath));
            }
            else
            {
                foreach (var link in currentNode.OutputConnections)
                {
                    var targetNode = pipeline.Snaps.Find(s => s.Id == link.TargetId);
                    if (targetNode != null)
                    {
                        currentPath.Add(targetNode);
                        FindPathsRecursive(pipeline, targetNode, currentPath, visited, results, remainingDepth - 1);
                        currentPath.RemoveAt(currentPath.Count - 1);
                    }
                }
            }
            
            visited.Remove(currentNode.Id);
        }        private async Task<string> SendPromptToFreeAI(string prompt)
        {
            LogMessage("Using fallback free AI service (Hugging Face)");
            
            try
            {
                // Create a new HttpClient instance with different headers
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Add("Authorization", "Bearer *************************************"); // Free demo token
                
                // Format request for Hugging Face
                var payload = new
                {
                    inputs = $"<s>[INST] {prompt} [/INST]"
                };
                
                var jsonContent = JsonSerializer.Serialize(payload);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                
                LogMessage("Sending request to Hugging Face API");
                var response = await client.PostAsync(_huggingFaceUrl, content);
                  if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    LogMessage($"Hugging Face API error: {response.StatusCode} - {errorContent}");
                    throw new Exception($"Hugging Face API error: {response.StatusCode}");
                }
                
                var responseBody = await response.Content.ReadAsStringAsync();
                LogMessage("Received response from Hugging Face");
                
                // Parse the response - format is different from OpenAI
                // Response is typically a JSON array of strings or a single JSON object with generated_text
                if (responseBody.StartsWith("["))
                {
                    // It's an array of responses
                    var responses = JsonSerializer.Deserialize<List<string>>(responseBody);
                    return responses != null && responses.Count > 0 ? responses[0] : "No response from AI service";
                }
                else
                {
                    // It's an object with generated_text
                    using JsonDocument doc = JsonDocument.Parse(responseBody);
                    if (doc.RootElement.TryGetProperty("generated_text", out JsonElement generatedText))
                    {
                        return generatedText.GetString() ?? "No response from AI service";
                    }
                }
                
                return "Could not parse response from fallback AI service";
            }            catch (Exception ex)
            {
                LogMessage($"Error using fallback AI service: {ex.Message}");
                throw;
            }
        }

        private string GetGenericDescription(PipelineData pipeline)
        {
            // Last resort fallback - generate based on pipeline data
            StringBuilder sb = new StringBuilder();
            
            sb.AppendLine("This SnapLogic pipeline appears to integrate data across different systems, transforming and mapping the data as needed.");
            
            // Add details based on snap types
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.ExternalSystem && s.Type.Contains("dynamics")))
            {
                sb.AppendLine("The pipeline connects to Dynamics 365 CRM to retrieve or update customer data.");
            }
            
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.Database))
            {
                sb.AppendLine("Database operations are performed to store or retrieve structured data.");
            }
            
            if (pipeline.Snaps.Any(s => s.Category == SnapCategory.FileOperation))
            {
                sb.AppendLine("File operations are used to read from or write to the file system.");
            }
            
            sb.AppendLine("The pipeline follows data integration best practices by providing clear data flow paths and proper transformation logic.");
            
            return sb.ToString();
        }

        private async Task<string> SendPromptToAI(string prompt)
        {
            try
            {                LogMessage("Preparing to send request to OpenAI API");
                LogMessage($"API Key (first 5 chars): {(_apiKey.Length > 5 ? _apiKey.Substring(0, 5) : "empty")}...");
                
                var request = new OpenAIRequest
                {
                    Model = "gpt-3.5-turbo",
                    Messages = new List<Message>
                    {
                        new Message { Role = "system", Content = "You are a technical documentation expert specializing in data integration platforms and SnapLogic." },
                        new Message { Role = "user", Content = prompt }
                    },
                    Temperature = 0.5,
                    MaxTokens = 600,
                    Presence_Penalty = 0.0,
                    Frequency_Penalty = 0.0
                };

                var jsonContent = JsonSerializer.Serialize(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                  LogMessage($"Sending request to: {_baseUrl}");
                LogMessage("Request payload size: " + jsonContent.Length + " characters");

                var response = await _httpClient.PostAsync(_baseUrl, content);
                LogMessage($"Response status code: {response.StatusCode}");
                  if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    LogMessage($"Error response: {errorContent}");
                    
                    if (errorContent.Contains("insufficient_quota") || 
                        errorContent.Contains("rate_limit_exceeded") ||
                        response.StatusCode == System.Net.HttpStatusCode.TooManyRequests)
                    {
                        return "OpenAI API quota exceeded";
                    }
                    
                    return $"API error: {response.StatusCode} - {errorContent}";
                }

                response.EnsureSuccessStatusCode();                var responseBody = await response.Content.ReadAsStringAsync();
                LogMessage("Received response from OpenAI");
                
                var aiResponse = JsonSerializer.Deserialize<OpenAIResponse>(responseBody);

                if (aiResponse?.Choices?.Count > 0)
                {
                    LogMessage("Successfully parsed API response with choices");
                    return aiResponse.Choices[0].Message.Content;
                }
                
                LogMessage("API response parsed but no choices found");
                return "Could not generate AI description.";
            }            catch (Exception ex)
            {
                LogMessage($"Exception in SendPromptToAI: {ex.GetType().Name}: {ex.Message}");
                LogMessage($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        // Classes for OpenAI API
        private class OpenAIRequest
        {
            [JsonPropertyName("model")]
            public string Model { get; set; }
            
            [JsonPropertyName("messages")]
            public List<Message> Messages { get; set; }
            
            [JsonPropertyName("temperature")]
            public double Temperature { get; set; }
            
            [JsonPropertyName("max_tokens")]
            public int MaxTokens { get; set; }
            
            [JsonPropertyName("presence_penalty")]
            public double Presence_Penalty { get; set; }
            
            [JsonPropertyName("frequency_penalty")]
            public double Frequency_Penalty { get; set; }
        }

        private class OpenAIResponse
        {
            [JsonPropertyName("choices")]
            public List<Choice> Choices { get; set; }
        }

        private class Choice
        {
            [JsonPropertyName("message")]
            public Message Message { get; set; }
        }

        private class Message
        {
            [JsonPropertyName("role")]
            public string Role { get; set; }
            
            [JsonPropertyName("content")]
            public string Content { get; set; }
        }
    }
}