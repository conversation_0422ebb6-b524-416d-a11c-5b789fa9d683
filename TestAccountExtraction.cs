using System;
using Newtonsoft.Json.Linq;
using System.IO;

namespace SnapAnalyser
{
    class TestAccountExtraction
    {
        static void Main(string[] args)
        {
            if (args.Length == 0)
            {
                Console.WriteLine("Usage: TestAccountExtraction <slp_file_path>");
                return;
            }

            try
            {
                string slpContent = File.ReadAllText(args[0]);
                JObject pipelineJson = JObject.Parse(slpContent);
                
                var snapMap = pipelineJson["snap_map"] as JObject;
                if (snapMap == null)
                {
                    Console.WriteLine("No snap_map found");
                    return;
                }

                Console.WriteLine("=== ACCOUNT EXTRACTION TEST ===");
                
                int snapCount = 0;
                foreach (var snapEntry in snapMap)
                {
                    snapCount++;
                    var snapId = snapEntry.Key;
                    var snapJson = snapEntry.Value as JObject;
                    
                    if (snapJson == null) continue;
                    
                    var propMap = snapJson["property_map"] as JObject;
                    if (propMap == null) continue;
                    
                    var account = propMap["account"] as JObject;
                    if (account == null) continue;
                    
                    Console.WriteLine($"\n--- SNAP {snapCount}: {snapId} ---");
                    
                    var accountRef = account["account_ref"];
                    if (accountRef != null)
                    {
                        Console.WriteLine("ACCOUNT_REF STRUCTURE:");
                        Console.WriteLine(accountRef.ToString(Newtonsoft.Json.Formatting.Indented));
                        
                        // Try to extract the label value using the expected path
                        var labelValue = accountRef.SelectToken("value.label.value");
                        if (labelValue != null)
                        {
                            Console.WriteLine($"EXTRACTED ACCOUNT NAME: {labelValue}");
                        }
                        else
                        {
                            Console.WriteLine("No label value found in expected path");
                        }
                    }
                    else
                    {
                        Console.WriteLine("No account_ref found");
                    }
                    
                    // Stop after first few snaps to avoid too much output
                    if (snapCount >= 3) break;
                }
                
                Console.WriteLine($"\nProcessed {snapCount} snaps");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }
    }
}
