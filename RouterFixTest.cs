using System;

namespace SnapAnalyzer
{
    public enum SnapCategory
    {
        Transformation,
        Database,
        ExternalSystem,
        FlowControl,
        Utility,
        Other
    }

    public class Snap
    {
        public string Label { get; set; }
        public string Type { get; set; }
        public SnapCategory Category { get; set; }
    }

    class RouterFixTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("Testing Router Snap Fix Logic");
            Console.WriteLine("============================");
            
            // Create a Router snap like the one in CH03
            var routerSnap = new Snap
            {
                Label = "Route Unit Count",
                Type = "com-snaplogic-snaps-flow-router",
                Category = SnapCategory.FlowControl
            };
            
            Console.WriteLine($"Testing snap: {routerSnap.Label}");
            Console.WriteLine($"Type: {routerSnap.Type}");
            Console.WriteLine($"Category: {routerSnap.Category}");
            Console.WriteLine();
            
            // Test the DocumentationGenerator logic (BEFORE FIX)
            Console.WriteLine("BEFORE FIX Logic:");
            string snapType = routerSnap.Type.ToLower();
            bool isMapperType_old = snapType.Contains("map") || snapType.Contains("datatransform") || snapType.Contains("transform");
            bool isExitOrUnionType = snapType.Contains("exit") || snapType.Contains("union");
            bool shouldUseAI_old = !isMapperType_old && !isExitOrUnionType;
            
            Console.WriteLine($"  isMapperType: {isMapperType_old}");
            Console.WriteLine($"  isExitOrUnionType: {isExitOrUnionType}");
            Console.WriteLine($"  shouldUseAI: {shouldUseAI_old}");
            Console.WriteLine($"  Result: Router snap would {(shouldUseAI_old ? "USE" : "NOT use")} AI pseudocode");
            Console.WriteLine();
            
            // Test the DocumentationGenerator logic (AFTER FIX)
            Console.WriteLine("AFTER FIX Logic:");
            bool isMapperType_new = snapType.Contains("map") || snapType.Contains("datatransform") || snapType.Contains("transform");
            bool isRouterType = routerSnap.Category == SnapCategory.FlowControl && snapType.Contains("router");
            bool shouldUseAI_new = !isMapperType_new && !isRouterType && !isExitOrUnionType;
            
            Console.WriteLine($"  isMapperType: {isMapperType_new}");
            Console.WriteLine($"  isRouterType: {isRouterType}");
            Console.WriteLine($"  isExitOrUnionType: {isExitOrUnionType}");
            Console.WriteLine($"  shouldUseAI: {shouldUseAI_new}");
            Console.WriteLine($"  Result: Router snap would {(shouldUseAI_new ? "USE" : "NOT use")} AI pseudocode");
            Console.WriteLine();
            
            // Verify the fix
            if (shouldUseAI_old && !shouldUseAI_new)
            {
                Console.WriteLine("✅ FIX SUCCESSFUL: Router snap is now excluded from AI pseudocode generation");
            }
            else if (!shouldUseAI_old && !shouldUseAI_new)
            {
                Console.WriteLine("ℹ️  Router snap was already excluded (no change needed)");
            }
            else
            {
                Console.WriteLine("❌ FIX FAILED: Router snap would still use AI pseudocode generation");
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
