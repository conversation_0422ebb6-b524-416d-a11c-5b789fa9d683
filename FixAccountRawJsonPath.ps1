# Fix the account path in the raw JSON extraction

$filePath = "DocumentationGenerator.cs"

Write-Host "Fixing account path in raw JSON extraction..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Find and replace the incorrect account path
$oldLine = 'var accountNode = rawSnapJson["account"];'
$newLine = 'var accountNode = rawSnapJson["property_map"]?["account"];'

if ($content.Contains($oldLine)) {
    $content = $content.Replace($oldLine, $newLine)
    Write-Host "Fixed account path: rawSnapJson[`"account`"] -> rawSnapJson[`"property_map`"]?[`"account`"]" -ForegroundColor Yellow
} else {
    Write-Host "Account path line not found exactly as expected" -ForegroundColor Red
    Write-Host "Looking for similar patterns..." -ForegroundColor Yellow
    
    # Let's try a broader replacement pattern
    $pattern = 'var accountNode = rawSnapJson\["account"\];'
    $replacement = 'var accountNode = rawSnapJson["property_map"]?["account"];'
    
    if ($content -match [regex]::Escape($pattern)) {
        $content = $content -replace [regex]::Escape($pattern), $replacement
        Write-Host "Fixed account path using regex pattern" -ForegroundColor Yellow
    } else {
        Write-Host "Could not find the exact pattern to replace" -ForegroundColor Red
    }
}

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Account path fix completed!" -ForegroundColor Green
