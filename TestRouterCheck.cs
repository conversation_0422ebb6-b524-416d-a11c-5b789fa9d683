using System;
using System.Collections.Generic;

namespace SnapAnalyser
{
    public class TestRouterCheck
    {
        public static void Main(string[] args)
        {
            // Create a test Router snap similar to the one in CH03 pipeline
            var routerSnap = new SnapNode
            {
                Id = "test-router",
                Label = "Route Unit Count",
                Type = "com-snaplogic-snaps-flow-router",
                Category = SnapCategory.FlowControl,
                Properties = new Dictionary<string, string>()
            };

            // Create AIDescriptionGenerator
            var generator = new AIDescriptionGenerator("", "", "", 30, false, true);
            
            // Enable logging to a test file
            var outputFolder = @"C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Generated Documentation";
            generator.EnableLogging(outputFolder);
            
            // Test the IsMapperOrConditionSnap method using reflection
            var type = typeof(AIDescriptionGenerator);
            var method = type.GetMethod("IsMapperOrConditionSnap", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (method != null)
            {
                Console.WriteLine("Testing IsMapperOrConditionSnap with Router snap:");
                Console.WriteLine($"Snap Label: {routerSnap.Label}");
                Console.WriteLine($"Snap Type: {routerSnap.Type}");
                Console.WriteLine($"Snap Category: {routerSnap.Category}");
                Console.WriteLine();
                
                bool result = (bool)method.Invoke(generator, new object[] { routerSnap });
                
                Console.WriteLine($"IsMapperOrConditionSnap result: {result}");
                Console.WriteLine();
                
                // Test what we expect
                string snapType = routerSnap.Type.ToLowerInvariant();
                bool isFlowControl = routerSnap.Category == SnapCategory.FlowControl;
                bool containsRouter = snapType.Contains("router");
                
                Console.WriteLine("Manual check:");
                Console.WriteLine($"snapType.ToLowerInvariant(): '{snapType}'");
                Console.WriteLine($"Category == SnapCategory.FlowControl: {isFlowControl}");
                Console.WriteLine($"snapType.Contains('router'): {containsRouter}");
                Console.WriteLine($"Expected result: {isFlowControl && containsRouter}");
            }
            else
            {
                Console.WriteLine("Could not find IsMapperOrConditionSnap method");
            }
            
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
