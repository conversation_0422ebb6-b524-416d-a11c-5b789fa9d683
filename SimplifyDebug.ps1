# Simplify the debug output and focus on just the first database snap

$filePath = "DocumentationGenerator.cs"

Write-Host "Simplifying debug output..." -ForegroundColor Green

# Read the file  
$content = Get-Content $filePath -Raw

# Replace the complex debug with simpler output focused on first snap only
$oldComplexDebug = @'
// DEBUG: Print the raw JSON structure to understand the layout
                                        Console.WriteLine($"[DB-CONNECTION-DEBUG] Raw JSON Keys for {snap.Label}:");
                                        foreach (var prop in rawSnapJson.Properties())
                                        {
                                            Console.WriteLine($"[DB-CONNECTION-DEBUG]   Key: {prop.Name}");
                                            if (prop.Name == "property_map" && prop.Value is JObject propMap)
                                            {
                                                Console.WriteLine($"[DB-CONNECTION-DEBUG]     property_map keys:");
                                                foreach (var subProp in propMap.Properties())
                                                {
                                                    Console.WriteLine($"[DB-CONNECTION-DEBUG]       {subProp.Name}");
                                                    if (subProp.Name == "account" && subProp.Value is JObject accountObj)
                                                    {
                                                        Console.WriteLine($"[DB-CONNECTION-DEBUG]         account structure (formatted):");
                                                        Console.WriteLine($"[DB-CONNECTION-DEBUG]         {accountObj.ToString(Newtonsoft.Json.Formatting.Indented)}");
                                                    }
                                                }
                                            }
                                        }
'@

$newSimpleDebug = @'
// DEBUG: Print account structure for first database snap only
                                        if (snap.Label.Contains("Get Property Unit Characteristics Count"))
                                        {
                                            Console.WriteLine($"[ACCOUNT-DEBUG] === ACCOUNT STRUCTURE FOR: {snap.Label} ===");
                                            var propMap = rawSnapJson["property_map"] as JObject;
                                            if (propMap != null)
                                            {
                                                var account = propMap["account"] as JObject;
                                                if (account != null)
                                                {
                                                    Console.WriteLine($"[ACCOUNT-DEBUG] Complete account JSON:");
                                                    Console.WriteLine(account.ToString(Newtonsoft.Json.Formatting.Indented));
                                                }
                                                else
                                                {
                                                    Console.WriteLine($"[ACCOUNT-DEBUG] No account object found in property_map");
                                                }
                                            }
                                            else
                                            {
                                                Console.WriteLine($"[ACCOUNT-DEBUG] No property_map found");
                                            }
                                        }
'@

if ($content.Contains($oldComplexDebug)) {
    $content = $content.Replace($oldComplexDebug, $newSimpleDebug)
    Write-Host "Simplified debug output to focus on account structure" -ForegroundColor Yellow
} else {
    Write-Host "Complex debug code not found" -ForegroundColor Red
}

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Simplified debug output!" -ForegroundColor Green
