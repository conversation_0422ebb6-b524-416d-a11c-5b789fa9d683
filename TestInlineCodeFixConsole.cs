using System;
using System.Reflection;
using System.Text.RegularExpressions;

namespace SnapAnalyser.Tests
{
    public class TestInlineCodeFixConsole
    {
        public static void RunTest()
        {
            Console.WriteLine("Simple inline code test starting...");
            
            try
            {
                // Sample markdown with inline code that would use placeholders
                var testMarkdown = "No Pass-through: With `autoCommit` set to `True`, only mapped fields are included.";
                Console.WriteLine($"Original markdown: {testMarkdown}");
                
                // Get the DocumentationGenerator instance
                var docGen = new DocumentationGenerator();
                
                // Get private method using reflection
                var docGenType = typeof(DocumentationGenerator);
                var methodInfo = docGenType.GetMethod("ConvertMarkdownToHtml", 
                    BindingFlags.NonPublic | BindingFlags.Instance);
                    
                if (methodInfo == null)
                {
                    Console.WriteLine("ERROR: Could not find ConvertMarkdownToHtml method");
                    return;
                }
                
                // Invoke the method
                var html = (string)methodInfo.Invoke(docGen, new object[] { testMarkdown });
                Console.WriteLine($"Converted HTML: {html}");
                
                // Check for placeholders in the output
                if (html.Contains("INLINE_CODE_"))
                {
                    Console.WriteLine("FAILED: Output still contains placeholders");
                    Console.WriteLine($"PLACEHOLDER FOUND: {Regex.Match(html, "##INLINE_CODE_[^#]+##").Value}");
                }
                else if (html.Contains("<code>autoCommit</code>") && html.Contains("<code>True</code>"))
                {
                    Console.WriteLine("SUCCESS: Inline code was properly converted");
                }
                else
                {
                    Console.WriteLine("ERROR: Something else went wrong. Check HTML output above.");
                }
                
                // Test a more complex example
                var complexMarkdown = "**Pass-through Properties:** With `passThrough` set to `True`, all fields from the input document are included in the output payload, even if they're not explicitly mapped.";
                html = (string)methodInfo.Invoke(docGen, new object[] { complexMarkdown });
                Console.WriteLine($"\nComplex case HTML: {html}");
                
                if (html.Contains("INLINE_CODE_"))
                {
                    Console.WriteLine("FAILED: Complex case still contains placeholders");
                }
                else if (html.Contains("<code>passThrough</code>") && html.Contains("<code>True</code>"))
                {
                    Console.WriteLine("SUCCESS: Complex case inline code was properly converted");
                }
                else
                {
                    Console.WriteLine("ERROR: Complex case has issues. Check HTML output above.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR: Test failed with exception - {ex.Message}");
                Console.WriteLine(ex.StackTrace);
            }
                
            Console.WriteLine("Test completed.");
        }
    }
}
