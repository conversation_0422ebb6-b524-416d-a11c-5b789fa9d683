using System.Text.Json;

namespace SnapAnalyser
{
    public class DescriptionCache
    {
        private readonly string _cacheFilePath;
        private Dictionary<string, CachedDescription> _cache;
        private bool _isInitialized = false;
        private readonly TimeSpan _cacheExpiry;

        public DescriptionCache(string cacheDirectoryPath = null, TimeSpan? cacheExpiry = null)
        {
            // Use the application directory or the provided path
            string directoryPath = cacheDirectoryPath ?? Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "Snap-Documenter");

            // Ensure directory exists
            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }

            _cacheFilePath = Path.Combine(directoryPath, "description_cache.json");
            _cache = new Dictionary<string, CachedDescription>();

            // Default cache expiry is 7 days, but can be customized
            _cacheExpiry = cacheExpiry ?? TimeSpan.FromDays(7);
        }
        public async Task InitializeAsync()
        {
            if (_isInitialized)
                return;

            if (File.Exists(_cacheFilePath))
            {
                try
                {
                    string jsonContent = await File.ReadAllTextAsync(_cacheFilePath);
                    var loadedCache = JsonSerializer.Deserialize<Dictionary<string, CachedDescription>>(
                        jsonContent,
                        new JsonSerializerOptions { PropertyNameCaseInsensitive = true }); if (loadedCache != null)
                    {
                        _cache = loadedCache;

                        // Migrate existing cache entries that don't have expiry dates
                        bool needsMigration = false;
                        foreach (var kvp in _cache.ToList())
                        {
                            if (kvp.Value.ExpiryDate == default(DateTime))
                            {
                                // Set expiry date based on creation date + cache expiry duration
                                kvp.Value.ExpiryDate = kvp.Value.CreatedDate.Add(_cacheExpiry);
                                needsMigration = true;
                            }
                        }

                        if (needsMigration)
                        {
                            Console.WriteLine("Migrating existing cache entries with expiry dates");
                            await SaveAsync();
                        }

                        // Clean up expired entries on initialization
                        await CleanupExpiredEntriesAsync();
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error loading description cache: {ex.Message}");
                    // If the cache file is corrupt, start with an empty cache
                    _cache = new Dictionary<string, CachedDescription>();
                }
            }

            _isInitialized = true;
        }

        public async Task SaveAsync()
        {
            try
            {
                string jsonContent = JsonSerializer.Serialize(_cache, new JsonSerializerOptions
                {
                    WriteIndented = true
                });

                await File.WriteAllTextAsync(_cacheFilePath, jsonContent);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving description cache: {ex.Message}");
            }
        }
        public bool TryGetDescription(SnapNode snap, out string description)
        {
            description = null;

            if (!_isInitialized)
            {
                InitializeAsync().ConfigureAwait(false).GetAwaiter().GetResult(); // Sync call for simplicity in this context
            }

            string cacheKey = GenerateCacheKey(snap);

            if (_cache.TryGetValue(cacheKey, out var cachedItem))
            {
                // Check if the cached item has expired
                if (IsExpired(cachedItem))
                {
                    // Remove expired item
                    _cache.Remove(cacheKey);
                    SaveAsync().ConfigureAwait(false).GetAwaiter().GetResult(); // Save changes synchronously
                    return false;
                }

                description = cachedItem.Description;
                return true;
            }

            return false;
        }
        public bool TryGetPseudocode(SnapNode snap, out string pseudocode)
        {
            pseudocode = null;

            if (!_isInitialized)
            {
                InitializeAsync().ConfigureAwait(false).GetAwaiter().GetResult(); // Sync call for simplicity in this context
            }

            string cacheKey = GenerateCacheKey(snap);

            if (_cache.TryGetValue(cacheKey, out var cachedItem) && !string.IsNullOrEmpty(cachedItem.Pseudocode))
            {                // Check if the cached item has expired
                if (IsExpired(cachedItem))
                {
                    // Remove expired item
                    _cache.Remove(cacheKey);
                    SaveAsync().ConfigureAwait(false).GetAwaiter().GetResult(); // Save changes synchronously
                    return false;
                }

                pseudocode = cachedItem.Pseudocode;
                return true;
            }

            return false;
        }


        public void StoreDescription(SnapNode snap, string description, string pseudocode = null)
        {
            if (!_isInitialized)
            {
                InitializeAsync().ConfigureAwait(false).GetAwaiter().GetResult(); // Sync call for simplicity in this context
            }

            string cacheKey = GenerateCacheKey(snap);

            Console.WriteLine($"Storing description for {snap.Label} (Type: {snap.Type})");
            Console.WriteLine($"Description length: {description?.Length ?? 0}");
            Console.WriteLine($"Pseudocode length: {pseudocode?.Length ?? 0}");
            _cache[cacheKey] = new CachedDescription
            {
                SnapType = snap.Type,
                SnapLabel = snap.Label,
                Description = description,
                Pseudocode = pseudocode,
                CreatedDate = DateTime.UtcNow,
                ExpiryDate = DateTime.UtcNow.Add(_cacheExpiry)
            };

            // Ensure we save synchronously to avoid missed saves
            SaveAsync().ConfigureAwait(false).GetAwaiter().GetResult();
        }

        private bool IsExpired(CachedDescription cachedItem)
        {
            return DateTime.UtcNow > cachedItem.ExpiryDate;
        }

        public async Task CleanupExpiredEntriesAsync()
        {
            var expiredKeys = new List<string>();

            foreach (var kvp in _cache)
            {
                if (IsExpired(kvp.Value))
                {
                    expiredKeys.Add(kvp.Key);
                }
            }

            if (expiredKeys.Count > 0)
            {
                Console.WriteLine($"Removing {expiredKeys.Count} expired cache entries");
                foreach (var key in expiredKeys)
                {
                    _cache.Remove(key);
                }

                await SaveAsync();
            }
        }
        public void ClearExpiredEntries()
        {
            CleanupExpiredEntriesAsync().ConfigureAwait(false).GetAwaiter().GetResult();
        }
        public void ClearAllEntries()
        {
            _cache.Clear();
            SaveAsync().ConfigureAwait(false).GetAwaiter().GetResult();
            Console.WriteLine("All cache entries cleared");
        }// Backwards compatibility methods
        public string GetDescription(string snapKey)
        {
            // Create a simple SnapNode from the string key
            var snap = new SnapNode { Type = snapKey, Label = snapKey };
            if (TryGetDescription(snap, out string description))
            {
                return description;
            }
            return null;
        }

        public string GetDescription(SnapNode snap)
        {
            if (TryGetDescription(snap, out string description))
            {
                return description;
            }
            return null;
        }

        public void SaveDescription(string snapKey, string description)
        {
            // Create a simple SnapNode from the string key
            var snap = new SnapNode { Type = snapKey, Label = snapKey };
            StoreDescription(snap, description);
        }

        public void SaveDescription(SnapNode snap, string description)
        {
            StoreDescription(snap, description);
        }

        public async Task SaveDescriptionAsync(string snapKey, string description)
        {
            SaveDescription(snapKey, description);
            await SaveAsync();
        }

        public async Task SaveDescriptionAsync(SnapNode snap, string description)
        {
            StoreDescription(snap, description);
            await SaveAsync();
        }

        private string GenerateCacheKey(SnapNode snap)
        {
            // Create a unique key based on snap type and configuration
            // This ensures similar snaps with different configurations get different descriptions
            string configHash = "";

            if (snap.Properties != null && snap.Properties.Count > 0)
            {
                var hashSource = string.Join("|", snap.Properties
                    .Where(p => !string.IsNullOrEmpty(p.Value))
                    .Select(p => $"{p.Key}={p.Value}"));

                // Simple hash to keep the key reasonable in length
                configHash = Convert.ToBase64String(
                    System.Security.Cryptography.SHA256.Create().ComputeHash(
                        System.Text.Encoding.UTF8.GetBytes(hashSource)
                    )
                ).Substring(0, 8);
            }

            return $"{snap.Type}_{configHash}";
        }
    }
    public class CachedDescription
    {
        public string SnapType { get; set; }
        public string SnapLabel { get; set; }
        public string Description { get; set; }
        public string Pseudocode { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ExpiryDate { get; set; }
    }
}
