using System;
using System.Windows.Forms;

namespace SnapAnalyser
{
    public partial class TestForm : Form
    {
        public TestForm()
        {
            this.Text = "Test Form";
            this.Size = new System.Drawing.Size(400, 300);
            this.StartPosition = FormStartPosition.CenterScreen;
            
            var label = new Label()
            {
                Text = "If you can see this, Windows Forms is working!",
                AutoSize = true,
                Location = new System.Drawing.Point(50, 50)
            };
            this.Controls.Add(label);
        }
    }
    
    static class MinimalTest
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new TestForm());
        }
    }
}
