using System;
using System.Threading.Tasks;
using SnapAnalyser;

namespace TestCacheExpiry
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("Testing Cache Expiry Functionality");
            Console.WriteLine("==================================");

            // Create a test cache with very short expiry (5 seconds for testing)
            var cache = new DescriptionCache(null, TimeSpan.FromSeconds(5));
            await cache.InitializeAsync();

            // Create a test snap
            var testSnap = new SnapNode
            {
                Type = "TestSnap",
                Label = "Test Label",
                Properties = new Dictionary<string, string> { { "test", "value" } }
            };

            // Store a description
            string testDescription = "This is a test description";
            cache.StoreDescription(testSnap, testDescription);
            
            // Verify it can be retrieved immediately
            if (cache.TryGetDescription(testSnap, out string retrievedDescription))
            {
                Console.WriteLine($"✅ Successfully stored and retrieved: {retrievedDescription}");
            }
            else
            {
                Console.WriteLine("❌ Failed to retrieve just-stored description");
                return;
            }

            Console.WriteLine("Waiting 6 seconds for cache to expire...");
            await Task.Delay(6000);

            // Try to retrieve after expiry
            if (cache.TryGetDescription(testSnap, out string expiredDescription))
            {
                Console.WriteLine($"❌ Retrieved expired description: {expiredDescription}");
            }
            else
            {
                Console.WriteLine("✅ Expired description was correctly removed from cache");
            }

            // Test cleanup functionality
            Console.WriteLine("\nTesting bulk cleanup functionality...");
            
            // Add several test entries
            for (int i = 1; i <= 3; i++)
            {
                var snap = new SnapNode
                {
                    Type = $"TestSnap{i}",
                    Label = $"Test Label {i}",
                    Properties = new Dictionary<string, string> { { "test", $"value{i}" } }
                };
                cache.StoreDescription(snap, $"Description {i}");
            }

            Console.WriteLine("Added 3 new entries. Waiting 6 seconds for expiry...");
            await Task.Delay(6000);

            // Clean up expired entries
            await cache.CleanupExpiredEntriesAsync();
            Console.WriteLine("Cleanup completed");

            // Test clear all functionality
            Console.WriteLine("\nTesting clear all functionality...");
            cache.StoreDescription(testSnap, "New description");
            cache.ClearAllEntries();
            
            if (cache.TryGetDescription(testSnap, out string clearedDescription))
            {
                Console.WriteLine($"❌ Retrieved description after clear all: {clearedDescription}");
            }
            else
            {
                Console.WriteLine("✅ All entries were successfully cleared");
            }

            Console.WriteLine("\nCache expiry functionality test completed!");
        }
    }
}
