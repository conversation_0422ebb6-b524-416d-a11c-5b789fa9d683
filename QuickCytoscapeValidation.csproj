<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <RootNamespace>SnapAnalyser</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="CytoscapeJsGenerator.cs" />
    <Compile Include="DiagramGenerator.cs" />
    <Compile Include="FlowControlDiagramGenerator.cs" />
    <Compile Include="SlpAnalyzer.cs" />
    <Compile Include="ProjectData.cs" />
    <Compile Include="ConfigManager.cs" />
    <Compile Include="AIDescriptionGenerator.cs" />
    <Compile Include="DescriptionCache.cs" />
    <Compile Include="QuickCytoscapeValidation.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Windows.Forms" Version="6.0.0" />
    <PackageReference Include="Azure.AI.OpenAI" Version="1.0.0-beta.17" />
  </ItemGroup>

</Project>
