using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

class QuickConnectionTest
{
    static async Task Main()
    {
        Console.WriteLine("=== Quick Azure OpenAI Connection Test ===");
        Console.WriteLine();
        
        // Your updated configuration
        string apiKey = "CSunqsaWLui2oVMEPzZwpS2Unx2m8t5N0t1nMV2vQBuvOXZDvUmuJQQJ99BEACmepeSXJ3w3AAABACOG0Qbs";
        string endpoint = "https://openai-general-assistan-resource.cognitiveservices.azure.com/";
        string deploymentName = "gpt-4.1";
        
        Console.WriteLine($"Testing connection to:");
        Console.WriteLine($"  Endpoint: {endpoint}");
        Console.WriteLine($"  Deployment: {deploymentName}");
        Console.WriteLine($"  API Key: Present (hidden)");
        Console.WriteLine();
        
        try
        {
            using (var client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("api-key", apiKey);
                client.Timeout = TimeSpan.FromSeconds(30);
                
                // Test URL
                string testUrl = $"{endpoint.TrimEnd('/')}/openai/deployments/{deploymentName}/chat/completions?api-version=2023-05-15";
                Console.WriteLine($"Request URL: {testUrl}");
                Console.WriteLine();
                
                // Simple test request
                var requestObj = new {
                    messages = new[] {
                        new { role = "user", content = "Hello, this is a connection test. Please respond with 'Connection successful'." }
                    },
                    max_tokens = 20,
                    temperature = 0.1
                };
                
                var jsonContent = JsonSerializer.Serialize(requestObj);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                
                Console.WriteLine("Sending test request...");
                var response = await client.PostAsync(testUrl, content);
                var responseBody = await response.Content.ReadAsStringAsync();
                
                Console.WriteLine($"Response Status: {response.StatusCode}");
                
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("✅ SUCCESS! Connection is working!");
                    
                    try
                    {
                        var jsonResponse = JsonSerializer.Deserialize<JsonElement>(responseBody);
                        if (jsonResponse.TryGetProperty("choices", out var choices) && choices.GetArrayLength() > 0)
                        {
                            var firstChoice = choices[0];
                            if (firstChoice.TryGetProperty("message", out var message) && 
                                message.TryGetProperty("content", out var messageContent))
                            {
                                Console.WriteLine($"AI Response: {messageContent.GetString()}");
                            }
                        }
                    }
                    catch
                    {
                        Console.WriteLine($"Raw Response: {responseBody}");
                    }
                }
                else
                {
                    Console.WriteLine("❌ FAILED!");
                    Console.WriteLine($"Error Response: {responseBody}");
                    
                    if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                    {
                        Console.WriteLine("\n🔍 DIAGNOSIS:");
                        Console.WriteLine("- The deployment name 'gpt-4.1' was not found.");
                        Console.WriteLine("- Please verify the deployment name in your Azure Portal.");
                        Console.WriteLine("- Check: Azure Portal > Your OpenAI Resource > Deployments");
                    }
                    else if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                    {
                        Console.WriteLine("\n🔍 DIAGNOSIS:");
                        Console.WriteLine("- Authentication failed. Please check your API key.");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Exception: {ex.Message}");
        }
        
        Console.WriteLine();
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}
