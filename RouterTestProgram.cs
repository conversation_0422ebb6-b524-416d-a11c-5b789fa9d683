using System;
using System.Collections.Generic;
using System.Linq;

namespace SnapAnalyser
{
    class RouterTestProgram
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Quick Router Fix Verification ===");
            Console.WriteLine();

            try
            {
                // Simulate the router snap structure from SlpAnalyzer.cs
                var routerSnap = new TestSnapNode
                {
                    Id = "router-test-001",
                    Label = "Route Unit Count",
                    Type = "com-snaplogic-snaps-flow-router",
                    Properties = new Dictionary<string, string>
                    {
                        ["settings.routes.value[0].expression.value"] = "$unitCount > 100",
                        ["settings.routes.value[0].outputViewName.value"] = "highValue",
                        ["settings.routes.value[1].expression.value"] = "$unitCount <= 100",
                        ["settings.routes.value[1].outputViewName.value"] = "lowValue"
                    },
                    OutputConnections = new List<TestSnapLink>
                    {
                        new TestSnapLink { SourceId = "router-test-001", TargetId = "snap-high-001", SourceViewId = "" }, // Empty SourceViewId
                        new TestSnapLink { SourceId = "router-test-001", TargetId = "snap-low-001", SourceViewId = "" }   // Empty SourceViewId
                    }
                };

                // Create connected snaps
                var allSnaps = new List<TestSnapNode>
                {
                    routerSnap,
                    new TestSnapNode { Id = "snap-high-001", Label = "Process High Value Orders", Type = "com-snaplogic-snaps-transform-mapper" },
                    new TestSnapNode { Id = "snap-low-001", Label = "Process Low Value Orders", Type = "com-snaplogic-snaps-transform-mapper" }
                };

                Console.WriteLine($"Router Snap: {routerSnap.Label}");
                Console.WriteLine($"Output Connections: {routerSnap.OutputConnections.Count}");
                Console.WriteLine();

                // Test the enhanced GetConnectedSnaps method logic
                Console.WriteLine("Testing enhanced connection logic...");
                var connectedSnaps = GetConnectedSnaps(routerSnap, allSnaps);
                
                Console.WriteLine($"Connected snaps found: {connectedSnaps.Count}");
                Console.WriteLine();

                foreach (var connectedSnap in connectedSnaps)
                {
                    Console.WriteLine($"- {connectedSnap.ConnectionInfo}: {connectedSnap.Snap.Label}");
                }

                Console.WriteLine();
                if (connectedSnaps.Count > 0 && !connectedSnaps.Any(c => c.ConnectionInfo.Contains("''")))
                {
                    Console.WriteLine("✅ SUCCESS: Router fix working correctly!");
                    Console.WriteLine("✅ No empty quotes in connection info");
                    Console.WriteLine("✅ Fallback output identifiers generated properly");
                }
                else
                {
                    Console.WriteLine("❌ ISSUE: Router fix may need refinement");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        // Simplified version of the enhanced GetConnectedSnaps method from FlowControlConfigurationGenerator.cs
        static List<ConnectedSnapInfo> GetConnectedSnaps(TestSnapNode snap, List<TestSnapNode> allSnaps)
        {
            var result = new List<ConnectedSnapInfo>();
            int outputIndex = 0;

            foreach (var connection in snap.OutputConnections)
            {
                var targetSnap = allSnaps.FirstOrDefault(s => s.Id == connection.TargetId);
                if (targetSnap != null)
                {
                    string connectionInfo;
                    
                    // This is the key fix: handle empty SourceViewId
                    if (string.IsNullOrEmpty(connection.SourceViewId))
                    {
                        connectionInfo = $"output{outputIndex}"; // Generate fallback identifier
                    }
                    else
                    {
                        connectionInfo = connection.SourceViewId;
                    }

                    result.Add(new ConnectedSnapInfo
                    {
                        Snap = targetSnap,
                        ConnectionInfo = connectionInfo
                    });
                }
                outputIndex++;
            }

            return result;
        }
    }

    // Test classes to simulate the actual data structures
    public class TestSnapNode
    {
        public string Id { get; set; }
        public string Label { get; set; }
        public string Type { get; set; }
        public Dictionary<string, string> Properties { get; set; } = new Dictionary<string, string>();
        public List<TestSnapLink> OutputConnections { get; set; } = new List<TestSnapLink>();
    }

    public class TestSnapLink
    {
        public string SourceId { get; set; }
        public string TargetId { get; set; }
        public string SourceViewId { get; set; }
    }

    public class ConnectedSnapInfo
    {
        public TestSnapNode Snap { get; set; }
        public string ConnectionInfo { get; set; }
    }
}
