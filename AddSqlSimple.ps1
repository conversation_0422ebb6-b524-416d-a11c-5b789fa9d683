# Simple PowerShell script to add SQL statement display

$filePath = "DocumentationGenerator.cs"

Write-Host "Adding SQL statement extraction and display..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Add GetSqlStatement method before GetDatabaseConnectionInfo
$methodToAdd = @'

        /// <summary>
        /// Extracts SQL statement from SQL execute snaps
        /// </summary>
        private string GetSqlStatement(SnapNode snap)
        {
            if (snap.Category != SnapCategory.Database)
                return null;

            // Only get SQL for execute snaps
            if (!snap.Type.Contains("execute"))
                return null;

            Console.WriteLine($"[SQL-EXTRACT] Checking SQL for snap: {snap.Label}");

            // Look for SQL statement in flattened properties
            var sqlProp = snap.Properties.FirstOrDefault(p => 
                p.Key.ToLower().Contains("sqlstatement") && 
                !string.IsNullOrEmpty(p.Value));

            if (sqlProp.Key != null)
            {
                Console.WriteLine($"[SQL-EXTRACT] Found SQL statement");
                return sqlProp.Value;
            }

            Console.WriteLine($"[SQL-EXTRACT] No SQL statement found for snap: {snap.Label}");
            return null;
        }
'@

# Find the GetDatabaseConnectionInfo method and add our method before it
$pattern = '(\s+/// <summary>\s+/// Extracts database connection information from snap properties\s+/// </summary>\s+private string GetDatabaseConnectionInfo\(SnapNode snap\))'
$replacement = $methodToAdd + "`r`n$1"
$content = $content -replace $pattern, $replacement

# Add SQL display after connection info in the main rendering
$sqlDisplay = @'

                    // Add SQL statement for execute snaps
                    if (snap.Type.Contains("execute"))
                    {
                        string sqlStatement = GetSqlStatement(snap);
                        if (!string.IsNullOrEmpty(sqlStatement))
                        {
                            html.AppendLine($"          <p><strong>SQL Statement:</strong></p>");
                            html.AppendLine($"          <pre style='background-color: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; white-space: pre-wrap;'><code>{System.Net.WebUtility.HtmlEncode(sqlStatement)}</code></pre>");
                        }
                    }
'@

# Insert after the connection info else block
$insertPattern = '(\s+else\s*\{\s*Console\.WriteLine\(\$"\[DB-CONNECTION\] Snap \{snap\.Label\} is not categorized as Database \(Category: \{snap\.Category\}\)"\);\s*\})'
$insertReplacement = "$1$sqlDisplay"
$content = $content -replace $insertPattern, $insertReplacement

# Write the updated file
$content | Set-Content $filePath -Encoding UTF8

Write-Host "SQL statement extraction and display added successfully!" -ForegroundColor Green
