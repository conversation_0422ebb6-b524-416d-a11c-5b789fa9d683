using System;
using System.Collections.Generic;
using System.IO;

namespace SnapAnalyser
{
    class CDNFixTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== CDN Fix Verification Test ===");
            Console.WriteLine("Testing flowchart.js CDN links after fix...");
            Console.WriteLine();

            try
            {
                // Test 1: Create a test pipeline
                Console.WriteLine("1. Creating test pipeline...");
                var pipeline = CreateTestPipeline();
                Console.WriteLine($"   ✓ Test pipeline created with {pipeline.Snaps.Count} snaps");

                // Test 2: Generate flowchart definition
                Console.WriteLine("2. Generating flowchart definition...");
                var generator = new FlowchartJsGenerator();
                string definition = generator.GenerateFlowchartDefinition(pipeline);
                Console.WriteLine($"   ✓ Definition generated: {definition.Length} characters");

                // Test 3: Generate HTML with updated CDN
                Console.WriteLine("3. Generating HTML with CDN fix...");
                string html = generator.GenerateFlowchartHtml(definition, "test-pipeline", "Test Pipeline Flow");
                Console.WriteLine($"   ✓ HTML generated: {html.Length} characters");

                // Test 4: Create DocumentationGenerator and test CDN
                Console.WriteLine("4. Testing DocumentationGenerator CDN fix...");
                var docGen = new DocumentationGenerator();
                string fullDoc = docGen.GenerateHtmlDocumentationAsync(pipeline, html, new ProjectData()).Result;
                
                // Check if the correct CDN is being used
                bool hasCorrectCDN = fullDoc.Contains("https://cdn.jsdelivr.net/npm/flowchart.js@1.17.1/release/flowchart.min.js");
                bool hasOldCDN = fullDoc.Contains("https://flowchart.js.org/flowchart-latest.js");
                
                Console.WriteLine($"   ✓ Uses correct CDN: {hasCorrectCDN}");
                Console.WriteLine($"   ✓ Old CDN removed: {!hasOldCDN}");

                // Test 5: Save test documentation
                Console.WriteLine("5. Saving test documentation...");
                string outputPath = "test_documentation_cdn_fix.html";
                File.WriteAllText(outputPath, fullDoc);
                Console.WriteLine($"   ✓ Documentation saved to: {outputPath}");

                Console.WriteLine();
                Console.WriteLine("🎉 CDN FIX VERIFICATION COMPLETE! 🎉");
                Console.WriteLine();
                Console.WriteLine("Results:");
                Console.WriteLine($"- Correct CDN in use: {hasCorrectCDN}");
                Console.WriteLine($"- Old CDN removed: {!hasOldCDN}");
                Console.WriteLine($"- Documentation size: {fullDoc.Length} characters");
                Console.WriteLine();
                Console.WriteLine("Next step: Open the generated documentation file in a browser to verify diagrams render correctly.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ TEST FAILED: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        static PipelineData CreateTestPipeline()
        {
            var snaps = new List<SnapNode>
            {
                new SnapNode
                {
                    Id = "start1",
                    Label = "Data Source",
                    Type = "com-snaplogic-snaps-read",
                    Category = SnapCategory.ExternalSystem,
                    Position = new Position { X = 0, Y = 0 },
                    IsStartPoint = true
                },
                new SnapNode
                {
                    Id = "transform1",
                    Label = "Transform Data",
                    Type = "com-snaplogic-snaps-transform",
                    Category = SnapCategory.Transformation,
                    Position = new Position { X = 1, Y = 0 }
                },
                new SnapNode
                {
                    Id = "router1",
                    Label = "Route Data",
                    Type = "com-snaplogic-snaps-flow-router",
                    Category = SnapCategory.FlowControl,
                    Position = new Position { X = 2, Y = 0 }
                },
                new SnapNode
                {
                    Id = "output1",
                    Label = "Data Output",
                    Type = "com-snaplogic-snaps-write",
                    Category = SnapCategory.ExternalSystem,
                    Position = new Position { X = 3, Y = 0 },
                    IsEndPoint = true
                }
            };

            var links = new List<Link>
            {
                new Link { FromSnapId = "start1", ToSnapId = "transform1" },
                new Link { FromSnapId = "transform1", ToSnapId = "router1" },
                new Link { FromSnapId = "router1", ToSnapId = "output1" }
            };

            return new PipelineData
            {
                Name = "CDN Fix Test Pipeline",
                Author = "CDN Fix Test",
                Snaps = snaps,
                Links = links
            };
        }
    }
}
