using System;
using System.Collections.Generic;
using System.Linq;

namespace SnapAnalyser
{
    /// <summary>
    /// Test version of FlowControlDiagramGenerator to troubleshoot compilation issues
    /// </summary>
    public class FlowControlDiagramGeneratorTest
    {
        public FlowControlDiagramGeneratorTest()
        {
        }

        public string GenerateRouterFlowDiagram(SnapNode routerSnap, List<SnapNode> allSnaps)
        {
            return "<div>Test Router Diagram</div>";
        }

        public string GenerateJoinFlowDiagram(SnapNode joinSnap, List<SnapNode> allSnaps)
        {
            return "<div>Test Join Diagram</div>";
        }

        public string GenerateUnionFlowDiagram(SnapNode unionSnap, List<SnapNode> allSnaps)
        {
            return "<div>Test Union Diagram</div>";
        }
    }
}
