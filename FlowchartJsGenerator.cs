using System.Text;
using System.Web;

namespace SnapAnalyser
{
    /// <summary>
    /// Generates flowchart.js syntax from SnapLogic pipeline data
    /// </summary>
    public class FlowchartJsGenerator
    {
        /// <summary>
        /// Converts a PipelineData object into flowchart.js syntax
        /// </summary>
        /// <param name="pipeline">The pipeline data to convert</param>
        /// <returns>Flowchart.js definition string</returns>
        public string GenerateFlowchartDefinition(PipelineData pipeline)
        {
            var definition = new StringBuilder();
            
            // Generate node definitions
            foreach (var snap in pipeline.Snaps)
            {
                string nodeType = GetFlowchartNodeType(snap);
                string nodeId = SanitizeId(snap.Id);
                string nodeLabel = EscapeLabel(snap.Label);
                
                definition.AppendLine($"{nodeId}=>{nodeType}: {nodeLabel}");
            }
            
            definition.AppendLine();
              // Generate flow connections
            var connections = new StringBuilder();
            foreach (var link in pipeline.Links)
            {
                string sourceId = SanitizeId(link.SourceId);
                string targetId = SanitizeId(link.TargetId);
                
                if (connections.Length > 0)
                {
                    connections.AppendLine();
                }
                connections.Append($"{sourceId}->{targetId}");
            }
            
            definition.AppendLine(connections.ToString());
            
            return definition.ToString();
        }
        
        /// <summary>
        /// Generates flowchart.js HTML container with styling options
        /// </summary>
        /// <param name="definition">The flowchart.js definition</param>
        /// <param name="containerId">The HTML container ID</param>
        /// <param name="title">Optional title for the diagram</param>
        /// <returns>Complete HTML with flowchart.js rendering</returns>
        public string GenerateFlowchartHtml(string definition, string containerId = "flowchart", string title = null)
        {
            var html = new StringBuilder();
            
            html.AppendLine($"<div class=\"flowchart-container\">");
            if (!string.IsNullOrEmpty(title))
            {
                html.AppendLine($"  <h3 class=\"flowchart-title\">{HttpUtility.HtmlEncode(title)}</h3>");
            }
            html.AppendLine($"  <div id=\"{containerId}\" class=\"flowchart-diagram\"></div>");
            html.AppendLine("</div>");            html.AppendLine("<script>");
            html.AppendLine("document.addEventListener('DOMContentLoaded', function() {");
            html.AppendLine("  // Wait for flowchart.js library to load");
            html.AppendLine("  function initializeFlowchart() {");
            html.AppendLine("    if (typeof flowchart === 'undefined') {");
            html.AppendLine("      setTimeout(initializeFlowchart, 100);");
            html.AppendLine("      return;");
            html.AppendLine("    }");
            html.AppendLine("    try {");
            string sanitizedVarName = SanitizeId($"diagram_{containerId}");
            html.AppendLine($"      var {sanitizedVarName} = flowchart.parse(`{definition.Trim()}`);");
            html.AppendLine($"      {sanitizedVarName}.drawSVG('{containerId}', {{");
            html.AppendLine("    'x': 0,");
            html.AppendLine("    'y': 0,");
            html.AppendLine("    'line-width': 2,");
            html.AppendLine("    'line-length': 50,");
            html.AppendLine("    'text-margin': 10,");
            html.AppendLine("    'font-size': 12,");
            html.AppendLine("    'font-color': 'black',");
            html.AppendLine("    'line-color': '#666',");
            html.AppendLine("    'element-color': '#333',");
            html.AppendLine("    'fill': 'white',");
            html.AppendLine("    'yes-text': 'yes',");
            html.AppendLine("    'no-text': 'no',");
            html.AppendLine("    'arrow-end': 'block',");
            html.AppendLine("    'scale': 1,");
            html.AppendLine("    'symbols': {");
            html.AppendLine("      'start': {");
            html.AppendLine("        'font-color': 'black',");
            html.AppendLine("        'element-color': '#333',");
            html.AppendLine("        'fill': '#ffffcc'");
            html.AppendLine("      },");
            html.AppendLine("      'end': {");
            html.AppendLine("        'font-color': 'black',");
            html.AppendLine("        'element-color': '#333',");
            html.AppendLine("        'fill': '#ffdddd'");
            html.AppendLine("      },");
            html.AppendLine("      'operation': {");
            html.AppendLine("        'font-color': 'black',");
            html.AppendLine("        'element-color': '#333',");
            html.AppendLine("        'fill': '#ccffcc'");
            html.AppendLine("      },");
            html.AppendLine("      'condition': {");
            html.AppendLine("        'font-color': 'black',");
            html.AppendLine("        'element-color': '#333',");
            html.AppendLine("        'fill': '#ccccff'");
            html.AppendLine("      },");
            html.AppendLine("      'inputoutput': {");
            html.AppendLine("        'font-color': 'black',");
            html.AppendLine("        'element-color': '#333',");
            html.AppendLine("        'fill': '#ffccff'");
            html.AppendLine("      },");
            html.AppendLine("      'subroutine': {");            html.AppendLine("        'font-color': 'black',");
            html.AppendLine("        'element-color': '#333',");
            html.AppendLine("        'fill': '#ffcccc'");
            html.AppendLine("          }");
            html.AppendLine("        }");
            html.AppendLine("      });");
            html.AppendLine("    } catch (error) {");
            html.AppendLine($"      console.error('Error rendering flowchart for {containerId}:', error);");
            html.AppendLine("    }");
            html.AppendLine("  }");
            html.AppendLine("  initializeFlowchart();");
            html.AppendLine("});");
            html.AppendLine("</script>");
            
            return html.ToString();
        }
        
        /// <summary>
        /// Maps SnapLogic snap categories to flowchart.js node types
        /// </summary>
        private string GetFlowchartNodeType(SnapNode snap)
        {
            // Check if it's a start node
            if (snap.IsStartPoint)
            {
                return "start";
            }
            
            // Check if it's an end node (no output connections)
            if (!snap.OutputConnections.Any())
            {
                return "end";
            }
            
            // Map based on category and type
            switch (snap.Category)
            {
                case SnapCategory.FlowControl:
                    if (snap.Type.Contains("Router", StringComparison.OrdinalIgnoreCase) ||
                        snap.Type.Contains("Filter", StringComparison.OrdinalIgnoreCase) ||
                        snap.Type.Contains("Condition", StringComparison.OrdinalIgnoreCase))
                    {
                        return "condition";
                    }
                    return "operation";
                    
                case SnapCategory.Database:
                case SnapCategory.ExternalSystem:
                    return "subroutine";
                    
                case SnapCategory.FileOperation:
                    return "inputoutput";
                    
                case SnapCategory.Transformation:
                case SnapCategory.ErrorHandling:
                default:
                    return "operation";
            }
        }
          /// <summary>
        /// Sanitizes node IDs to be valid flowchart.js identifiers
        /// </summary>
        public string SanitizeId(string id)
        {
            if (string.IsNullOrEmpty(id))
                return "node" + Guid.NewGuid().ToString("N")[..8];
                
            // Replace invalid characters with underscores
            var sanitized = System.Text.RegularExpressions.Regex.Replace(id, @"[^a-zA-Z0-9_]", "_");
            
            // Ensure it starts with a letter
            if (char.IsDigit(sanitized[0]))
            {
                sanitized = "n" + sanitized;
            }
            
            return sanitized;
        }
          /// <summary>
        /// Escapes special characters in node labels for flowchart.js
        /// </summary>
        public string EscapeLabel(string label)
        {
            if (string.IsNullOrEmpty(label))
                return "Untitled";
                
            // Escape backticks and trim whitespace
            return label.Replace("`", "\\`").Trim();
        }
    }
}
