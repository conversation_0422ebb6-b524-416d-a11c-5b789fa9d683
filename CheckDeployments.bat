@echo off
echo === Azure OpenAI Deployment Checker ===
echo.

set "endpoint=https://openai-snaplogic-documenter.openai.azure.com/"
set "apiKey=6dr7izA3Iuc1qhTiaKMZWnx1iHX0yI0MDx4Hhj1VWkSLHQ7CCPD7JQQJ99BEACmepeSXJ3w3AAABACOG0Qbs"

echo Current Configuration:
echo   Endpoint: %endpoint%
echo   API Key: Present (hidden)
echo.

echo Attempting to list deployments...
echo.

curl -H "api-key: %apiKey%" -H "Content-Type: application/json" "%endpoint%openai/deployments?api-version=2023-05-15"

echo.
echo.
echo NEXT STEPS:
echo 1. Look for "id" fields in the JSON response above
echo 2. Choose one of those deployment names
echo 3. Update the 'AzureOpenAIDeploymentName' in config.json
echo 4. Test the connection again
echo.

pause
