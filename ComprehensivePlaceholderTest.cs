using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.IO;

namespace SnapAnalyser.Test
{
    class ComprehensivePlaceholderTest
    {
        static async Task Main()
        {
            Console.WriteLine("=== Comprehensive Snap-Documenter Placeholder Test ===\n");

            try
            {
                // Create test pipeline with realistic data
                var pipeline = CreateTestPipelineWithMapper();
                
                // Create documentation generator
                var docGenerator = new DocumentationGenerator();
                
                // Create cytoscape generator for diagrams
                var cytoscapeGenerator = new CytoscapeJsGenerator();
                var diagramElements = cytoscapeGenerator.GenerateCytoscapeElements(pipeline);
                var diagramHtml = cytoscapeGenerator.GenerateCytoscapeHtml(diagramElements, "test-diagram", "Test Pipeline Flow");
                
                Console.WriteLine("1. Created test pipeline with mapper snap");
                Console.WriteLine($"   - Snaps: {pipeline.Snaps.Count}");
                Console.WriteLine($"   - Links: {pipeline.Links.Count}");
                
                // Simulate AI-generated content with problematic placeholders
                var mapperSnap = pipeline.Snaps.Find(s => s.Type.Contains("mapper"));
                if (mapperSnap != null)
                {
                    // This simulates the problematic AI-generated description with mixed placeholder formats
                    mapperSnap.Description = @"This SnapLogic snap performs data mapping, transforming input document fields. It maps the input field ##INLINECODEb20cdc1a## to ##INLINECODE85af0109## and ##INLINECODEe127afa3## to ##INLINECODE820e6bf0## in the output. Key settings include no SQL statement, no error retries (##INLINECODE68df6922##), and execution in ""Validate & Execute"" mode. ##INLINECODE01d14ea2## is disabled, so only mapped fields are output. The mapping root is set to ##INLINECODE5572589d##, processing each input document at the root level. Input is expected as a document stream; output is a transformed document containing only the mapped fields. Best practice: ensure input fields exist and consider enabling ##INLINECODEc0547d29## if original fields should be retained. Set ##INLINECODE4f9e7cd2## to ##INLINECODE30f92c1e## to halt on errors for data integrity.";
                }
                
                Console.WriteLine("\n2. Added problematic AI-generated description with mixed placeholders");
                
                // Generate HTML documentation 
                var htmlDoc = docGenerator.GenerateHtmlDocumentation(pipeline, diagramHtml);
                
                Console.WriteLine("\n3. Generated HTML documentation with placeholder processing");
                
                // Save the test output
                var outputFile = "comprehensive_placeholder_test_output.html";
                await File.WriteAllTextAsync(outputFile, htmlDoc);
                
                Console.WriteLine($"\n4. Saved output to: {outputFile}");
                
                // Analyze the results
                AnalyzePlaceholderProcessing(htmlDoc);
                
                Console.WriteLine($"\n✅ Test completed successfully!");
                Console.WriteLine($"📁 Open '{outputFile}' in your browser to see the results");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
        
        static PipelineData CreateTestPipelineWithMapper()
        {
            var snaps = new List<SnapNode>
            {
                new SnapNode
                {
                    Id = "file-reader-001",
                    Label = "Customer Data Reader",
                    Type = "com-snaplogic-snaps-file-filereader",
                    Category = SnapCategory.ExternalSystem,
                    Position = new Position { X = 0, Y = 0 },
                    Description = "Reads customer data from CSV files in the input directory. Configured to process files with comma delimiters and UTF-8 encoding."
                },
                new SnapNode
                {
                    Id = "data-mapper-002", 
                    Label = "Customer Field Mapper",
                    Type = "com-snaplogic-snaps-transform-datatransform-mapper",
                    Category = SnapCategory.Transformation,
                    Position = new Position { X = 1, Y = 0 },
                    Description = "Will be set with problematic placeholders"
                },
                new SnapNode
                {
                    Id = "validator-003",
                    Label = "Data Validator",
                    Type = "com-snaplogic-snaps-flow-gate",
                    Category = SnapCategory.FlowControl,
                    Position = new Position { X = 2, Y = 0 },
                    Description = "Validates customer data fields and routes valid records to the output stream. Invalid records are sent to the error stream for logging."
                },
                new SnapNode
                {
                    Id = "db-writer-004",
                    Label = "Database Writer",
                    Type = "com-snaplogic-snaps-database-insert",
                    Category = SnapCategory.Database,
                    Position = new Position { X = 3, Y = 0 },
                    Description = "Inserts validated customer records into the production database. Uses batch processing for optimal performance."
                }
            };

            var links = new List<SnapLink>
            {
                new SnapLink { SourceId = "file-reader-001", TargetId = "data-mapper-002", SourceViewId = "output0", TargetViewId = "input0" },
                new SnapLink { SourceId = "data-mapper-002", TargetId = "validator-003", SourceViewId = "output0", TargetViewId = "input0" },
                new SnapLink { SourceId = "validator-003", TargetId = "db-writer-004", SourceViewId = "output0", TargetViewId = "input0" }
            };

            return new PipelineData
            {
                Name = "Customer Data Processing Pipeline",
                Description = "A comprehensive pipeline for processing customer data from CSV files, transforming field mappings, validating data integrity, and storing results in the production database.",
                Snaps = snaps,
                Links = links
            };
        }
        
        static void AnalyzePlaceholderProcessing(string htmlContent)
        {
            Console.WriteLine("\n=== Placeholder Processing Analysis ===");
            
            // Count original placeholders that should have been processed
            var originalPlaceholders = System.Text.RegularExpressions.Regex.Matches(htmlContent, @"##INLINECODE[a-zA-Z0-9]+##");
            Console.WriteLine($"Remaining unprocessed placeholders: {originalPlaceholders.Count}");
            
            if (originalPlaceholders.Count > 0)
            {
                Console.WriteLine("⚠️  Found unprocessed placeholders:");
                foreach (System.Text.RegularExpressions.Match match in originalPlaceholders)
                {
                    Console.WriteLine($"   - {match.Value}");
                }
            }
            
            // Count processed placeholders
            var inferredPlaceholders = System.Text.RegularExpressions.Regex.Matches(htmlContent, @"class=""inferred-placeholder""");
            var missingPlaceholders = System.Text.RegularExpressions.Regex.Matches(htmlContent, @"class=""missing-placeholder""");
            
            Console.WriteLine($"Inferred placeholders (intelligent fallbacks): {inferredPlaceholders.Count}");
            Console.WriteLine($"Missing placeholders (no context found): {missingPlaceholders.Count}");
            
            // Check for CSS inclusion
            bool hasCss = htmlContent.Contains(".inferred-placeholder") && htmlContent.Contains(".missing-placeholder");
            Console.WriteLine($"CSS styling included: {(hasCss ? "✅ Yes" : "❌ No")}");
            
            // Check for cytoscape diagram
            bool hasDiagram = htmlContent.Contains("cytoscape") && htmlContent.Contains("cy_");
            Console.WriteLine($"Cytoscape diagram included: {(hasDiagram ? "✅ Yes" : "❌ No")}");
            
            Console.WriteLine("\n=== Summary ===");
            if (originalPlaceholders.Count == 0 && (inferredPlaceholders.Count > 0 || missingPlaceholders.Count > 0))
            {
                Console.WriteLine("✅ Placeholder processing working correctly!");
                Console.WriteLine("   All problematic placeholders have been converted to readable content.");
            }
            else if (originalPlaceholders.Count > 0)
            {
                Console.WriteLine("⚠️  Some placeholders were not processed - this may indicate an issue.");
            }
            else
            {
                Console.WriteLine("ℹ️  No placeholders found to process in this test.");
            }
        }
    }
}
