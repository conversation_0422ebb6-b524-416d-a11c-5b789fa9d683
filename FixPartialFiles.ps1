# Script to fix the ending of the partial class files

$helpersFile = "c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\DocumentationGenerator.Helpers.cs"

Write-Host "Fixing DocumentationGenerator.Helpers.cs..."
$content = Get-Content -Path $helpersFile

# Remove empty lines and extra braces at the end
$cleanContent = @()
$foundEndOfContent = $false

for ($i = 0; $i -lt $content.Length; $i++) {
    $line = $content[$i]
    
    # Stop adding content when we hit a lot of empty lines followed by closing braces
    if ($line -match '^\s*$' -and $i -gt $content.Length - 20) {
        # Check if we're near the end with just empty lines and braces
        $remainingLines = $content[$i..($content.Length - 1)]
        $nonEmptyRemaining = $remainingLines | Where-Object { $_ -notmatch '^\s*$' }
        
        if ($nonEmptyRemaining.Count -le 2 -and ($nonEmptyRemaining -join '') -match '^[\s}]*$') {
            Write-Host "Found end of meaningful content at line $($i + 1)"
            break
        }
    }
    
    $cleanContent += $line
}

# Ensure proper ending
$cleanContent += "    }"
$cleanContent += "}"

Write-Host "Writing cleaned DocumentationGenerator.Helpers.cs..."
$cleanContent | Set-Content -Path $helpersFile -Encoding UTF8

Write-Host "✅ Fixed partial class files"
