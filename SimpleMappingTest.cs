// Simple test program for the mapping table fix
using System;
using System.Collections.Generic;
using System.Reflection;
using SnapAnalyser;

namespace SnapAnalyser
{
    public class SimpleMappingTest
    {
        public static void TestMappingFix()
        {
            Console.WriteLine("Testing Duplicate Mapping Table Fix");
            Console.WriteLine("===================================\n");
            
            try
            {
                // Create a mock snap with mapping properties
                var snap = new SnapNode
                {
                    Name = "Test Mapper",
                    Type = "com.snaplogic.snaps.transform.datatransform.DataMapper",
                    Properties = new List<KeyValuePair<string, string>>
                    {
                        new KeyValuePair<string, string>("execution_mode", "Validate & Execute"),
                        new KeyValuePair<string, string>("customer.name.expression", "$customer_data.name"),
                        new KeyValuePair<string, string>("customer.email.expression", "$customer_data.email"),
                        new KeyValuePair<string, string>("order.id.targetPath", "order_id"),
                        new KeyValuePair<string, string>("order.total.expression", "$order_data.amount * 1.1"),
                        new KeyValuePair<string, string>("mapping.simple", "direct_mapping_value")
                    }
                };
                
                var allSnaps = new List<SnapNode> { snap };
                
                // Test 1: hasMappings = true (should NOT generate mapping tables)
                Console.WriteLine("Test 1: hasMappings = true (should suppress mapping tables)");
                string result1 = SnapBestPractices.GetSnapBestPractices(snap, allSnaps, hasMappings: true);
                
                bool hasFieldMappingsTable1 = result1.Contains("Field mappings defined in this snap:");
                bool hasExpressionCode1 = result1.Contains("SnapLogic Expression Language for advanced transformations:");
                
                Console.WriteLine($"  Contains field mappings table: {hasFieldMappingsTable1}");
                Console.WriteLine($"  Contains expression code: {hasExpressionCode1}");
                
                if (!hasFieldMappingsTable1 && !hasExpressionCode1)
                {
                    Console.WriteLine("  ✓ PASS: No mapping tables generated when hasMappings = true");
                }
                else
                {
                    Console.WriteLine("  ✗ FAIL: Mapping tables were generated when hasMappings = true");
                }
                
                // Test 2: hasMappings = false (should generate mapping tables)
                Console.WriteLine("\nTest 2: hasMappings = false (should generate mapping tables)");
                string result2 = SnapBestPractices.GetSnapBestPractices(snap, allSnaps, hasMappings: false);
                
                bool hasFieldMappingsTable2 = result2.Contains("Field mappings defined in this snap:");
                bool hasExpressionCode2 = result2.Contains("SnapLogic Expression Language for advanced transformations:");
                
                Console.WriteLine($"  Contains field mappings table: {hasFieldMappingsTable2}");
                Console.WriteLine($"  Contains expression code: {hasExpressionCode2}");
                
                if (hasFieldMappingsTable2 || hasExpressionCode2)
                {
                    Console.WriteLine("  ✓ PASS: Mapping tables generated when hasMappings = false");
                }
                else
                {
                    Console.WriteLine("  ✗ FAIL: No mapping tables were generated when hasMappings = false");
                }
                
                // Summary
                bool allTestsPassed = (!hasFieldMappingsTable1 && !hasExpressionCode1) && 
                                    (hasFieldMappingsTable2 || hasExpressionCode2);
                                    
                Console.WriteLine($"\nOverall Result: {(allTestsPassed ? "✓ ALL TESTS PASSED" : "✗ SOME TESTS FAILED")}");
                
                if (allTestsPassed)
                {
                    Console.WriteLine("The duplicate mapping table fix is working correctly!");
                    Console.WriteLine("Mapping tables will only appear when hasMappings = false.");
                }
                else
                {
                    Console.WriteLine("The fix may need further investigation.");
                }
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
