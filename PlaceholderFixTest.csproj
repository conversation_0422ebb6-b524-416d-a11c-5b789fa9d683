<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="PlaceholderFixTest.cs" />
    <Compile Include="DocumentationGenerator.cs" />
    <Compile Include="AIDescriptionGenerator.cs" />
    <Compile Include="SlpAnalyzer.cs" />
    <Compile Include="DescriptionCache.cs" />
    <Compile Include="ConfigManager.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>
</Project>
