using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using SnapAnalyser; // Assuming SnapNode is in this namespace
using System.Net; // For WebUtility.HtmlEncode

namespace SnapAnalyser
{
    public static class SnapBestPractices
    {
        public static string GetSnapBestPractices(SnapNode snap, List<SnapNode> allSnaps, bool hasMappings = false)
        {
            StringBuilder details = new StringBuilder();
            string snapLabel = WebUtility.HtmlEncode(snap.Label);
            // string snapDescription = WebUtility.HtmlEncode(snap.Description); // SnapNode.Description not found

            details.AppendLine($"<h4>Best Practices for {snapLabel} ({snap.Type})</h4>");
            // Common initial checks removed as we don't have Views information available

            // Add specific recommendations based on snap type
            switch (snap.Category)
            {
                case SnapCategory.Transformation:
                    if (snap.Type.Contains("datatransform") || snap.Type.Contains("map")) // Mapper
                    {
                        details.AppendLine("<p><strong>Best Practices for Mapper Snaps:</strong></p>");
                        details.AppendLine("<ul>");
                        details.AppendLine("<li>Ensure all target paths are correctly defined and match the desired output schema.</li>");
                        details.AppendLine("<li>Use preview and debug features extensively to validate mapping logic with sample data.</li>"); details.AppendLine("<li>For complex transformations, consider breaking them into multiple Mappers or using Script snaps for clarity.</li>");                        details.AppendLine("<li>Leverage built-in functions and expressions to simplify logic (e.g., <code>jsonPath()</code>, <code>match()</code>, string functions, date functions).</li>");
                        details.AppendLine("<li>Document complex expressions with comments within the expression editor if possible, or in external documentation.</li>");
                        details.AppendLine("</ul>");                        
                        // Example Usage section removed as per requirements
                            // Find all mapping-related properties
                            var mappingProps = new List<KeyValuePair<string, string>>();

                            // Look for various patterns of mapping properties
                            foreach (var prop in snap.Properties)
                            {
                                // Skip empty values
                                if (string.IsNullOrEmpty(prop.Value))
                                    continue;

                                // Skip configuration properties that aren't actual mappings
                                if (prop.Key == "execution_mode" || prop.Key == "passThrough" || prop.Key == "nullSafeAccess")
                                    continue;

                                // Skip very long JSON configurations unless they're mapping definitions
                                if (prop.Value.Length > 500 && prop.Value.Contains("{") &&
                                    !prop.Key.Equals("mappingDefinition", StringComparison.OrdinalIgnoreCase))
                                    continue;

                                // Common mapping property patterns
                                if (prop.Key.ToLower().Contains("expression") ||
                                    prop.Key.ToLower().Contains("mapping") ||
                                    prop.Key.ToLower().Contains("targetpath") ||
                                    prop.Key.ToLower().Contains("sourcepath") ||
                                    prop.Key.ToLower().Contains("target_path") ||
                                    prop.Key.ToLower().Contains("source_path") ||
                                    prop.Key.ToLower().Contains("output") ||
                                    prop.Key.ToLower().Contains("transform") ||
                                    // Include any properties that look like field paths
                                    (prop.Key.Contains(".") && !prop.Key.EndsWith(".value") && !prop.Key.EndsWith(".type")))
                                {
                                    mappingProps.Add(prop);
                                }
                            }
                            // Find advanced expressions (those using SnapLogic Expression Language)
                            var slExpressions = mappingProps.Where(p => p.Value.Contains("=>") ||
                                                                       (p.Value.Contains("$") &&
                                                                       (p.Value.Contains(".") || p.Value.Contains("[")))).ToList();

                            if (slExpressions.Any())
                            {
                                details.AppendLine("<p>This Mapper snap uses SnapLogic Expression Language for advanced transformations:</p>");
                                details.AppendLine("<pre><code class='language-javascript'>");
                                foreach (var expr in slExpressions.Take(15)) // Increased from 10 to 15 to show more expressions
                                {
                                    string targetPath = Regex.Match(expr.Key, @"([\w\.]+)(?:\.expression|$)").Groups[1].Value;
                                    if (!string.IsNullOrEmpty(targetPath) && targetPath != expr.Key)
                                    {
                                        details.AppendLine($"// Target field: {WebUtility.HtmlEncode(targetPath)}");
                                    }
                                    else
                                    {
                                        details.AppendLine($"// {WebUtility.HtmlEncode(expr.Key)}");
                                    }
                                    details.AppendLine(WebUtility.HtmlEncode(expr.Value));
                                    details.AppendLine();
                                }

                                if (slExpressions.Count > 15)
                                {
                                    details.AppendLine($"// ...and {slExpressions.Count - 15} more expressions");
                                }

                                details.AppendLine("</code></pre>");
                            }

                            // Handle simpler field mappings (direct path assignments)
                            var simpleMappings = mappingProps.Except(slExpressions).ToList();
                            if (simpleMappings.Any())
                            {
                                details.AppendLine("<p>Field mappings defined in this snap:</p>");
                                details.AppendLine("<table style='width: 100%; border-collapse: collapse;'>");
                                details.AppendLine("<tr><th style='text-align:left; border: 1px solid #ddd; padding: 8px;'>Target</th><th style='text-align:left; border: 1px solid #ddd; padding: 8px;'>Source/Expression</th></tr>");

                                // Group by path patterns to show related mappings together
                                var mappingGroups = simpleMappings
                                    .GroupBy(m => Regex.Replace(m.Key, @"[\d\.]+$", ""))
                                    .Take(10); // Limit to 10 groups

                                foreach (var group in mappingGroups)
                                {
                                    foreach (var mapping in group.Take(5)) // Limit to 5 mappings per group
                                    {
                                        // Try to extract a cleaner target path if possible
                                        string targetField = ExtractTargetFieldName(mapping.Key);
                                        details.AppendLine($"<tr><td style='border: 1px solid #ddd; padding: 8px;'>{WebUtility.HtmlEncode(targetField)}</td><td style='border: 1px solid #ddd; padding: 8px;'><code>{WebUtility.HtmlEncode(mapping.Value)}</code></td></tr>");
                                    }

                                    if (group.Count() > 5)
                                    {
                                        details.AppendLine($"<tr><td colspan='2' style='border: 1px solid #ddd; padding: 8px; font-style: italic;'>...and {group.Count() - 5} more similar mappings</td></tr>");
                                    }
                                }

                                if (mappingGroups.Count() > 10)
                                {
                                    details.AppendLine($"<tr><td colspan='2' style='border: 1px solid #ddd; padding: 8px; font-style: italic;'>...and more mapping groups not shown</td></tr>");
                                }

                                details.AppendLine("</table>");
                            }

                            // If no mappings found                            if (!mappingProps.Any())
                            {
                                details.AppendLine("<p>No specific mapping expressions were automatically extracted. This could be due to:</p>");
                                details.AppendLine("<ul>");
                                details.AppendLine("<li>Mappings stored in a non-standard format in the pipeline JSON</li>");
                                details.AppendLine("<li>Mappings defined through schema references rather than explicit expressions</li>");
                                details.AppendLine("<li>Mappings dynamically generated at runtime</li>");
                                details.AppendLine("<li>This snap using a simple pass-through configuration without transformations</li>");
                                details.AppendLine("</ul>");
                                details.AppendLine("<p>Try reopening the pipeline in SnapLogic Designer and exporting it again to ensure all mapping information is included.</p>");
                                details.AppendLine("<p>In SnapLogic, Mapper/Transform snaps typically define field-to-field transformations using expressions like <code>$input.firstName</code> to <code>$output.first_name</code>.</p>");
                            }

                            details.AppendLine("<p><em>Note: This reflects discovered properties. Refer to the SnapLogic UI for the canonical view of mappings and their evaluation context.</em></p>");                        }                        else if (snap.Type.Contains("sort")) // Sorter
                        {
                            details.AppendLine("<p><strong>Best Practices for Sorter Snaps:</strong></p>");
                            details.AppendLine("<ul>");
                            details.AppendLine("<li>Specify sort paths accurately. Incorrect paths can lead to unexpected sort orders or errors.</li>");
                            details.AppendLine("<li>Be mindful of memory usage, especially when sorting large datasets. If possible, sort data at the source (e.g., in a database query).</li>");
                            details.AppendLine("<li>Ensure data types of sorted fields are consistent for predictable results.</li>");
                            details.AppendLine("</ul>");
                            // Example Usage section removed as per requirements                        }else if (snap.Type.Contains("groupby")) // Group By
                        {
                            details.AppendLine("<p><strong>Best Practices for Group By Snaps:</strong></p>");
                            details.AppendLine("<ul>");
                            details.AppendLine("<li>Clearly define group-by fields and aggregation functions.</li>");
                            details.AppendLine("<li>Understand the impact on document structure; the output will be one document per group.</li>");
                            details.AppendLine("<li>Consider memory implications for large numbers of unique groups.</li>");
                            details.AppendLine("</ul>");
                            // Example Usage section removed as per requirements                        }else if (snap.Type.Contains("jsonparser"))
                        {
                            details.AppendLine("<p><strong>Best Practices for JSON Parser Snaps:</strong></p>");
                            details.AppendLine("<ul>");
                            details.AppendLine("<li>Ensure the input field contains valid JSON strings.</li>");
                            details.AppendLine("<li>Use the 'Ignore errors' option judiciously; it's often better to handle parsing errors explicitly.</li>");
                            details.AppendLine("</ul>");
                            // Example Usage section removed as per requirements
                        }else if (snap.Type.Contains("jsonformatter"))
                        {                        details.AppendLine("<p><strong>Best Practices for JSON Formatter Snaps:</strong></p>");
                        details.AppendLine("<ul>");
                        details.AppendLine("<li>Specify whether to format as a JSON string or binary data.</li>");
                        details.AppendLine("<li>Use the 'Pretty print' option for human-readable output during development or for logging.</li>");
                        details.AppendLine("</ul>");
                        // No Example Usage section for JSON Formatter snap types as per requirements
                        }
                        else // Generic Transformation or Script snap (since SnapCategory.Scripting is not available)
                        {
                            if (snap.Type.ToLower().Contains("script")) // Check if it's a Script snap by type
                            {
                                details.AppendLine("<p><strong>Best Practices for Script Snaps:</strong></p>");
                                details.AppendLine("<ul>");
                                details.AppendLine("<li>Use Script snaps for logic that is too complex or not possible with standard snaps.</li>");
                                details.AppendLine("<li>Write efficient and readable code (typically JavaScript). Add comments for complex sections.</li>");
                                details.AppendLine("<li>Handle errors within the script using try-catch blocks and write to the error view if necessary.</li>");
                                details.AppendLine("<li>Avoid very long scripts; consider custom snap development or breaking logic into multiple snaps.</li>");
                                details.AppendLine("<li>Be mindful of script execution time, as it can impact pipeline performance.</li>");
                                details.AppendLine("</ul>");
                                details.AppendLine("<p><strong>Example Usage:</strong></p>");

                                // Look for script content in various possible property keys
                                string scriptContent = null;

                                // Check for common script property names
                                var scriptProps = snap.Properties
                                    .Where(p => p.Key.ToLower() == "script" ||
                                               p.Key.ToLower() == "script_content" ||
                                               p.Key.ToLower() == "scriptbody" ||
                                               p.Key.ToLower() == "script_body" ||
                                               p.Key.ToLower() == "code" ||
                                               p.Key.ToLower() == "javascript" ||
                                               p.Key.ToLower().Contains("script") && p.Key.ToLower().Contains("content"))
                                    .ToList();

                                if (scriptProps.Any())
                                {
                                    // Use the first non-empty script content found
                                    scriptContent = scriptProps.FirstOrDefault(p => !string.IsNullOrWhiteSpace(p.Value)).Value;
                                }

                                if (!string.IsNullOrEmpty(scriptContent))
                                {
                                    details.AppendLine("<p>This Script snap executes the following code:</p>");
                                    details.AppendLine("<pre><code class='language-javascript'>");

                                    // Limit script length if extremely long
                                    if (scriptContent.Length > 2000)
                                    {
                                        details.AppendLine(WebUtility.HtmlEncode(scriptContent.Substring(0, 2000)));
                                        details.AppendLine("\n// ... [script continues for " + (scriptContent.Length - 2000) + " more characters] ...");
                                    }
                                    else
                                    {
                                        details.AppendLine(WebUtility.HtmlEncode(scriptContent));
                                    }

                                    details.AppendLine("</code></pre>");

                                    // Try to highlight key functionality in the script
                                    details.AppendLine("<p><strong>Script Highlights:</strong></p>");
                                    details.AppendLine("<ul>");

                                    // Check for error handling
                                    if (scriptContent.Contains("try") && scriptContent.Contains("catch"))
                                    {
                                        details.AppendLine("<li>Includes error handling with try-catch blocks</li>");
                                    }

                                    // Check for output writing
                                    if (scriptContent.Contains("output.write") || scriptContent.Contains("output.send"))
                                    {
                                        details.AppendLine("<li>Writes processed data to output view</li>");
                                    }

                                    // Check for error output 
                                    if (scriptContent.Contains("error.write") || scriptContent.Contains("error.send"))
                                    {
                                        details.AppendLine("<li>Uses error output view for exception handling</li>");
                                    }

                                    // Check for document manipulation
                                    if (scriptContent.Contains("JSON.parse") || scriptContent.Contains("JSON.stringify"))
                                    {
                                        details.AppendLine("<li>Performs JSON data manipulation</li>");
                                    }

                                    details.AppendLine("</ul>");
                                }
                                else
                                {
                                    details.AppendLine("<p>No script content was automatically extracted. A Script snap contains custom code (usually JavaScript) to process documents. Example structure:</p>");
                                    details.AppendLine("<pre><code class='language-javascript'>");
                                    details.AppendLine("function process(input, output, error) {");
                                    details.AppendLine("  // Your custom logic here, e.g.:");
                                    details.AppendLine("  // var data = input.get();");
                                    details.AppendLine("  // data.newField = data.oldField.toUpperCase();");
                                    details.AppendLine("  // output.write(data);");
                                    details.AppendLine("}");
                                    details.AppendLine("</code></pre>");
                                }
                            }
                            else // Generic Transformation
                            {
                                details.AppendLine("<p><strong>Best Practices for this Transformation Snap:</strong></p>");
                                details.AppendLine("<ul><li>Understand the specific transformation logic this snap applies.</li><li>Validate output against expected schema and values.</li></ul>");
                                details.AppendLine("<p><strong>Example Usage:</strong></p>");
                                var coreLogicProps = snap.Properties.Where(p => (p.Key.ToLower().Contains("expression") || p.Key.ToLower().Contains("config") || p.Key.ToLower().Contains("path")) && !string.IsNullOrEmpty(p.Value)).Take(5).ToList();
                                if (coreLogicProps.Any())
                                {
                                    details.AppendLine("<p>The core logic of this snap is defined by properties such as:</p>");
                                    details.AppendLine("<ul>");
                                    foreach (var prop in coreLogicProps)
                                    {
                                        details.AppendLine($"<li><strong>{WebUtility.HtmlEncode(prop.Key)}:</strong> <pre><code>{WebUtility.HtmlEncode(prop.Value)}</code></pre></li>");
                                    }
                                    details.AppendLine("</ul>");
                                }
                                else
                                {
                                    details.AppendLine("<p>This snap performs a specific transformation. Inspect its properties in SnapLogic for exact configuration. Common transformations involve data type changes, field manipulations, or structural modifications based on configured rules or expressions.</p>");
                                }
                            }
                        }
                    }
                    break;
                    
                case SnapCategory.FlowControl:                    if (snap.Type.Contains("filter"))
                    {
                        details.AppendLine("<p><strong>Best Practices for Filter Snaps:</strong></p>");
                        details.AppendLine("<ul>");
                        details.AppendLine("<li>Write clear and concise filter expressions.</li>");
                        details.AppendLine("<li>Test expressions with various input data to ensure correctness.</li>");
                        details.AppendLine("<li>Avoid overly complex expressions in a single Filter; consider multiple Filters or a Script snap.</li>");
                        details.AppendLine("</ul>");                    }else if (snap.Type.Contains("router")) // Router
                    {
                        details.AppendLine("<p><strong>Best Practices for Router Snaps:</strong></p>");
                        details.AppendLine("<ul>");
                        details.AppendLine("<li>Ensure routing expressions are mutually exclusive if documents should only go down one path (unless intentional fan-out).</li>");
                        details.AppendLine("<li>Always have a default or remainder output view for documents that don\\'t match any condition.</li>");
                        details.AppendLine("<li>Keep routing conditions simple and understandable.</li>");
                        details.AppendLine("</ul>");
                        // No Example Usage section for Router snap types as per requirements
                    }
                    else if (snap.Type.Contains("join")) // Join
                    {
                        details.AppendLine("<p><strong>Best Practices for Join Snaps (e.g., Join, Lookup Join):</strong></p>");
                        details.AppendLine("<ul>");
                        details.AppendLine("<li>Select appropriate join types (Inner, Left Outer, Full Outer) based on requirements.</li>");
                        details.AppendLine("<li>Ensure join conditions correctly match fields between input views.</li>");
                        details.AppendLine("<li>Be mindful of performance with large datasets; ensure keys are indexed if joining against a database lookup.</li>");
                        details.AppendLine("<li>Handle unmatched records appropriately (e.g., using a Left Join and checking for nulls).</li>");
                        details.AppendLine("</ul>");
                        details.AppendLine("<p><strong>Example Usage:</strong></p>");

                        // Extract join type
                        string joinType = "Unknown";
                        foreach (var prop in snap.Properties)
                        {
                            if (prop.Key.ToLower().Contains("jointype") || prop.Key.ToLower().Contains("join_type") ||
                                prop.Key.ToLower().Contains("type"))
                            {
                                if (!string.IsNullOrEmpty(prop.Value))
                                {
                                    joinType = prop.Value;
                                    break;
                                }
                            }
                        }

                        // Extract join conditions
                        var joinConditions = new List<KeyValuePair<string, string>>();
                        foreach (var prop in snap.Properties)
                        {
                            if ((prop.Key.ToLower().Contains("join") &&
                                (prop.Key.ToLower().Contains("condition") || prop.Key.ToLower().Contains("criteria"))) ||
                                prop.Key.ToLower().Contains("leftpath") || prop.Key.ToLower().Contains("left_path") ||
                                prop.Key.ToLower().Contains("rightpath") || prop.Key.ToLower().Contains("right_path") ||
                                prop.Key.ToLower().Contains("key"))
                            {
                                if (!string.IsNullOrEmpty(prop.Value))
                                {
                                    joinConditions.Add(prop);
                                }
                            }
                        }

                        if (joinConditions.Any())
                        {
                            details.AppendLine($"<p>This is a <strong>{WebUtility.HtmlEncode(joinType)}</strong> Join that merges documents based on:</p>");
                            details.AppendLine("<table style='width: 100%; border-collapse: collapse;'>");

                            // Try to match left and right path pairs
                            var leftPaths = joinConditions.Where(jc => jc.Key.ToLower().Contains("left")).ToList();
                            var rightPaths = joinConditions.Where(jc => jc.Key.ToLower().Contains("right")).ToList();

                            if (leftPaths.Any() && rightPaths.Any())
                            {
                                details.AppendLine("<tr><th style='text-align:left; border: 1px solid #ddd; padding: 8px;'>Left Path</th><th style='text-align:left; border: 1px solid #ddd; padding: 8px;'>Right Path</th></tr>");

                                int pairCount = Math.Min(leftPaths.Count, rightPaths.Count);
                                for (int i = 0; i < pairCount; i++)
                                {
                                    details.AppendLine($"<tr><td style='border: 1px solid #ddd; padding: 8px;'><code>{WebUtility.HtmlEncode(leftPaths[i].Value)}</code></td><td style='border: 1px solid #ddd; padding: 8px;'><code>{WebUtility.HtmlEncode(rightPaths[i].Value)}</code></td></tr>");
                                }
                            }
                            else
                            {
                                // Generic display for other join condition formats
                                details.AppendLine("<tr><th style='text-align:left; border: 1px solid #ddd; padding: 8px;'>Property</th><th style='text-align:left; border: 1px solid #ddd; padding: 8px;'>Value</th></tr>");

                                foreach (var condition in joinConditions)
                                {
                                    string propName = ExtractTargetFieldName(condition.Key);
                                    details.AppendLine($"<tr><td style='border: 1px solid #ddd; padding: 8px;'>{WebUtility.HtmlEncode(propName)}</td><td style='border: 1px solid #ddd; padding: 8px;'><code>{WebUtility.HtmlEncode(condition.Value)}</code></td></tr>");
                                }
                            }

                            details.AppendLine("</table>");

                            // Try to find an expression-based join condition
                            var expressionCondition = snap.Properties.FirstOrDefault(p =>
                                p.Key.ToLower().Contains("expression") &&
                                !string.IsNullOrEmpty(p.Value) &&
                                p.Value.Contains("=="));

                            if (!string.IsNullOrEmpty(expressionCondition.Value))
                            {
                                details.AppendLine("<p><strong>Join Expression:</strong></p>");
                                details.AppendLine("<pre><code class='language-javascript'>");
                                details.AppendLine(WebUtility.HtmlEncode(expressionCondition.Value));
                                details.AppendLine("</code></pre>");
                            }

                            // Show connection information if available
                            if (allSnaps != null && snap.InputConnections.Count >= 2)
                            {
                                details.AppendLine("<p><strong>Input Sources:</strong></p>");
                                details.AppendLine("<ul>");

                                foreach (var conn in snap.InputConnections.Take(5))
                                {
                                    var sourceSnap = allSnaps.FirstOrDefault(s => s.Id == conn.SourceId);
                                    if (sourceSnap != null)
                                    {
                                        details.AppendLine($"<li><code>{WebUtility.HtmlEncode(sourceSnap.Label)}</code> ({WebUtility.HtmlEncode(sourceSnap.Type)})</li>");
                                    }
                                }

                                details.AppendLine("</ul>");
                            }
                        }
                        else
                        {
                            details.AppendLine("<p>Join conditions were not specifically extracted. A Join snap requires defining how documents from its input views are matched (e.g., <code>$left.id == $right.user_id</code>).</p>");

                            if (!string.IsNullOrEmpty(joinType) && joinType != "Unknown")
                            {
                                details.AppendLine($"<p>Join type: <strong>{WebUtility.HtmlEncode(joinType)}</strong></p>");
                            }
                        }
                    }
                    else if (snap.Type.Contains("copy"))
                    {
                        details.AppendLine("<p><strong>Best Practices for Copy Snaps:</strong></p>");
                        details.AppendLine("<ul><li>Use Copy snaps to duplicate document streams for parallel processing paths.</li><li>Be aware that this creates full duplicates of documents, which can increase memory usage.</li></ul>");
                        details.AppendLine("<p><strong>Example Usage:</strong></p>");
                        details.AppendLine("<p>This Copy snap duplicates each incoming document to its multiple output views, allowing identical data to flow through different processing branches simultaneously.</p>");
                    }
                    else if (snap.Type.Contains("pipelineexecute"))
                    {
                        details.AppendLine("<p><strong>Best Practices for Pipeline Execute Snaps:</strong></p>");
                        details.AppendLine("<ul><li>Pass only necessary parameters to the child pipeline.</li><li>Ensure the child pipeline is designed to handle the inputs and produce expected outputs.</li><li>Manage error handling for child pipeline failures.</li></ul>");
                        details.AppendLine("<p><strong>Example Usage:</strong></p>");
                        var pipelineRtIdValue = snap.Properties.FirstOrDefault(p => p.Key.ToLower() == "pipeline_rt_id").Value;
                        details.AppendLine($"<p>This snap executes a child pipeline (often identified by a runtime ID or path like '<code>{WebUtility.HtmlEncode(pipelineRtIdValue ?? "N/A")}</code>').</p>");
                        var pipelineParams = snap.Properties.Where(p => p.Key.ToLower().Contains("parameter")).ToList();
                        if (pipelineParams.Any())
                        {
                            details.AppendLine("<p>Parameters passed to the child pipeline may include:</p>");
                            details.AppendLine("<ul>");
                            foreach (var param in pipelineParams)
                            {
                                details.AppendLine($"<li><strong>{WebUtility.HtmlEncode(param.Key)}:</strong> <code>{WebUtility.HtmlEncode(param.Value)}</code></li>");
                            }
                            details.AppendLine("</ul>");
                        }
                        else
                        {
                            details.AppendLine("<p>No specific parameters were extracted. Parameters can be configured to pass data from the parent to the child pipeline.</p>");
                        }
                    }
                    else // Generic FlowControl
                    {
                        details.AppendLine("<p><strong>Best Practices for this Flow Control Snap:</strong></p>");
                        details.AppendLine("<ul><li>Understand how this snap alters the document flow.</li><li>Ensure conditions or configurations are correctly set to achieve the desired pipeline behavior.</li></ul>");
                        details.AppendLine("<p><strong>Example Usage:</strong></p>");
                        var coreLogicProps = snap.Properties.Where(p => (p.Key.ToLower().Contains("condition") || p.Key.ToLower().Contains("expression") || p.Key.ToLower().Contains("route")) && !string.IsNullOrEmpty(p.Value)).Take(5).ToList();
                        if (coreLogicProps.Any())
                        {
                            details.AppendLine("<p>This snap controls pipeline flow based on configurations such as:</p>");
                            details.AppendLine("<ul>");
                            foreach (var prop in coreLogicProps)
                            {
                                details.AppendLine($"<li><strong>{WebUtility.HtmlEncode(prop.Key)}:</strong> <pre><code>{WebUtility.HtmlEncode(prop.Value)}</code></pre></li>");
                            }
                            details.AppendLine("</ul>");
                        }
                        else
                        {
                            details.AppendLine("<p>This snap directs or modifies the flow of documents. Inspect its properties in SnapLogic for specific routing conditions, join criteria, or execution control logic.</p>");
                        }
                    } 
                    break;
            
                // case SnapCategory.Scripting: // SnapCategory.Scripting not found, handled under Transformation for now
                // break;

                case SnapCategory.Database:
                    details.AppendLine("<p><strong>Best Practices for Database Snaps:</strong></p>");
                    details.AppendLine("<ul>");
                    details.AppendLine("<li>Use parameterized queries to prevent SQL injection vulnerabilities.</li>");
                    details.AppendLine("<li>Select only necessary columns (<code>SELECT col1, col2</code> instead of <code>SELECT *</code>).</li>");
                    details.AppendLine("<li>Optimize queries with WHERE clauses and JOIN conditions to minimize data transfer and processing.</li>");
                    details.AppendLine("<li>Handle database connection errors and transaction management carefully.</li>");
                    details.AppendLine("<li>For batch operations (Insert, Update, Delete), configure batch size appropriately.</li>");
                    details.AppendLine("</ul>");
                    details.AppendLine("<p><strong>Example Usage:</strong></p>");

                    // Check for SQL statements with various property names
                    string sqlStatement = null;
                    foreach (string sqlPropKey in new[] { "sqlstatement", "sql", "query", "statement", "sqlquery" })
                    {
                        var sqlProp = snap.Properties.FirstOrDefault(p =>
                            p.Key.ToLower() == sqlPropKey ||
                            p.Key.ToLower().EndsWith("." + sqlPropKey));

                        if (!string.IsNullOrEmpty(sqlProp.Value))
                        {
                            sqlStatement = sqlProp.Value;
                            break;
                        }
                    }

                    // Check for table information
                    string tableInfo = null;
                    foreach (string tablePropKey in new[] { "tablename", "table", "entity", "collection" })
                    {
                        var tableProp = snap.Properties.FirstOrDefault(p =>
                            p.Key.ToLower() == tablePropKey ||
                            p.Key.ToLower().EndsWith("." + tablePropKey));

                        if (!string.IsNullOrEmpty(tableProp.Value))
                        {
                            tableInfo = tableProp.Value;
                            break;
                        }
                    }

                    // Check for operation type (SELECT, INSERT, UPDATE, etc.)
                    string operationType = null;
                    if (snap.Type.ToLower().Contains("select"))
                        operationType = "SELECT";
                    else if (snap.Type.ToLower().Contains("insert"))
                        operationType = "INSERT";
                    else if (snap.Type.ToLower().Contains("update"))
                        operationType = "UPDATE";
                    else if (snap.Type.ToLower().Contains("delete"))
                        operationType = "DELETE";
                    else if (snap.Type.ToLower().Contains("execute"))
                        operationType = "EXECUTE";

                    // Handle SQL query display
                    if (!string.IsNullOrEmpty(sqlStatement))
                    {
                        details.AppendLine("<p>This snap executes the following SQL statement:</p>");
                        details.AppendLine("<pre><code class='language-sql'>");

                        // Format for readability if it's a long query
                        if (sqlStatement.Length > 100)
                        {
                            // Crude SQL formatting - could be improved with a proper SQL formatter
                            string formattedSql = sqlStatement
                                .Replace(" FROM ", "\nFROM ")
                                .Replace(" WHERE ", "\nWHERE ")
                                .Replace(" AND ", "\nAND ")
                                .Replace(" OR ", "\nOR ")
                                .Replace(" GROUP BY ", "\nGROUP BY ")
                                .Replace(" ORDER BY ", "\nORDER BY ")
                                .Replace(" HAVING ", "\nHAVING ")
                                .Replace(" INNER JOIN ", "\nINNER JOIN ")
                                .Replace(" LEFT JOIN ", "\nLEFT JOIN ")
                                .Replace(" RIGHT JOIN ", "\nRIGHT JOIN ")
                                .Replace(" OUTER JOIN ", "\nOUTER JOIN ");

                            details.AppendLine(WebUtility.HtmlEncode(formattedSql));
                        }
                        else
                        {
                            details.AppendLine(WebUtility.HtmlEncode(sqlStatement));
                        }

                        details.AppendLine("</code></pre>");

                        // Check for parameterized queries
                        bool usesParams = sqlStatement.Contains("?") ||
                                        sqlStatement.Contains(":") && Regex.IsMatch(sqlStatement, @":\w+");

                        if (usesParams)
                        {
                            details.AppendLine("<p><em>This query uses parameterized values, which helps prevent SQL injection.</em></p>");

                            // Try to find parameter information
                            string paramsInfo = snap.Properties.FirstOrDefault(p =>
                                p.Key.ToLower() == "parameters" ||
                                p.Key.ToLower().Contains("param")).Value;

                            if (!string.IsNullOrEmpty(paramsInfo))
                            {
                                details.AppendLine("<p><strong>Parameters:</strong></p>");
                                details.AppendLine($"<pre><code>{WebUtility.HtmlEncode(paramsInfo)}</code></pre>");
                            }
                        }
                    }
                    else if (!string.IsNullOrEmpty(tableInfo))
                    {
                        // For table-based operations without explicit SQL
                        details.AppendLine($"<p>This snap operates on table: <code>{WebUtility.HtmlEncode(tableInfo)}</code>.</p>");

                        if (!string.IsNullOrEmpty(operationType))
                        {
                            details.AppendLine($"<p>Operation: <strong>{operationType}</strong></p>");

                            // For mapped fields in INSERT/UPDATE, try to show field mappings
                            if (operationType == "INSERT" || operationType == "UPDATE")
                            {
                                var fieldMappings = snap.Properties.Where(p =>
                                    p.Key.ToLower().Contains("field") ||
                                    p.Key.ToLower().Contains("column") ||
                                    p.Key.ToLower().Contains("mapping")).ToList();

                                if (fieldMappings.Any())
                                {
                                    details.AppendLine("<p><strong>Field Mappings:</strong></p>");
                                    details.AppendLine("<ul>");

                                    foreach (var mapping in fieldMappings.Take(10))
                                    {
                                        details.AppendLine($"<li><code>{WebUtility.HtmlEncode(mapping.Key)}</code>: <code>{WebUtility.HtmlEncode(mapping.Value)}</code></li>");
                                    }

                                    if (fieldMappings.Count > 10)
                                    {
                                        details.AppendLine($"<li>...and {fieldMappings.Count - 10} more field mappings</li>");
                                    }

                                    details.AppendLine("</ul>");
                                }

                                // Check for batch size
                                var batchSizeProp = snap.Properties.FirstOrDefault(p =>
                                    p.Key.ToLower().Contains("batch") && p.Key.ToLower().Contains("size"));

                                if (!string.IsNullOrEmpty(batchSizeProp.Value))
                                {
                                    details.AppendLine($"<p><strong>Batch Size:</strong> {WebUtility.HtmlEncode(batchSizeProp.Value)}</p>");
                                }
                            }
                        }
                        else
                        {
                            details.AppendLine("<p>The specific operation (SELECT, INSERT, UPDATE, DELETE) and conditions are defined in its properties. For example, an Insert snap would map input document fields to table columns.</p>");
                        }
                    }
                    else
                    {
                        details.AppendLine("<p>No specific SQL statement or table was automatically extracted. Database snaps execute operations like SELECT, INSERT, UPDATE, DELETE against a configured database table or using a custom SQL query.</p>");
                    }

                    // Check for connection information
                    var connProp = snap.Properties.FirstOrDefault(p =>
                        p.Key.ToLower().Contains("connect") ||
                        p.Key.ToLower().Contains("account"));

                    if (!string.IsNullOrEmpty(connProp.Value))
                    {
                        details.AppendLine($"<p><strong>Connection:</strong> {WebUtility.HtmlEncode(connProp.Value)}</p>");
                    }

                    break;

                case SnapCategory.ExternalSystem: // e.g., REST, SOAP
                    details.AppendLine("<p><strong>Best Practices for External System Snaps (e.g., REST, SOAP):</strong></p>");
                    details.AppendLine("<ul>");
                    details.AppendLine("<li>Securely manage credentials using accounts. Avoid hardcoding sensitive information.</li>");
                    details.AppendLine("<li>Implement proper error handling for API call failures (e.g., HTTP 4xx/5xx errors).</li>");
                    details.AppendLine("<li>Configure timeouts and retry mechanisms for transient network issues.</li>");
                    details.AppendLine("<li>Understand and respect API rate limits.</li>");
                    details.AppendLine("<li>Validate responses and handle unexpected data structures.</li>");
                    details.AppendLine("</ul>");
                    details.AppendLine("<p><strong>Example Usage:</strong></p>");
                    string serviceUrlValue = snap.Properties.FirstOrDefault(p => p.Key.ToLower() == "serviceurl" || p.Key.ToLower() == "url" || p.Key.ToLower() == "endpoint").Value;
                    string httpMethodValue = snap.Properties.FirstOrDefault(p => p.Key.ToLower() == "httpmethod").Value; // Common for REST
                    string soapActionValue = snap.Properties.FirstOrDefault(p => p.Key.ToLower() == "soapaction").Value; // Common for SOAP

                    if (!string.IsNullOrEmpty(serviceUrlValue))
                    {
                        details.AppendLine($"<p>This snap interacts with an external service at URL/Endpoint: <code>{WebUtility.HtmlEncode(serviceUrlValue)}</code>.</p>");
                        if (!string.IsNullOrEmpty(httpMethodValue))
                        {
                            details.AppendLine($"<p><strong>HTTP Method:</strong> {WebUtility.HtmlEncode(httpMethodValue)}</p>");
                        }
                        if (!string.IsNullOrEmpty(soapActionValue))
                        {
                            details.AppendLine($"<p><strong>SOAP Action:</strong> {WebUtility.HtmlEncode(soapActionValue)}</p>");
                        }
                        string headersValue = snap.Properties.FirstOrDefault(p => p.Key.ToLower() == "httpheaders" || p.Key.ToLower() == "headers").Value;
                        if (!string.IsNullOrEmpty(headersValue))
                        {
                            string formattedHeaders = $"<pre><code class='language-json'>{WebUtility.HtmlEncode(headersValue)}</code></pre>";
                            details.AppendLine($"<p><strong>Headers (raw from properties):</strong> {formattedHeaders}</p>");
                        }
                        string requestBodyValue = snap.Properties.FirstOrDefault(p => p.Key.ToLower() == "httpentity" || p.Key.ToLower() == "requesttemplate").Value;
                        if (!string.IsNullOrEmpty(requestBodyValue))
                        {
                            details.AppendLine("<p><strong>Request Body/Template (raw from properties):</strong></p>");
                            details.AppendLine($"<pre><code class='language-json'>{WebUtility.HtmlEncode(requestBodyValue)}</code></pre>"); // Assume JSON, could be XML
                        }
                    }
                    else
                    {
                        details.AppendLine("<p>Configuration details (like Service URL) were not automatically extracted. These snaps are used to communicate with external systems via protocols like HTTP (REST, SOAP), JMS, etc., based on their specific configuration.</p>");
                    }
                    break;

                case SnapCategory.FileOperation:
                    details.AppendLine("<p><strong>Best Practices for File Operation Snaps (Reader, Writer, etc.):</strong></p>");
                    details.AppendLine("<ul>");
                    details.AppendLine("<li>Use dynamic file paths carefully; ensure they resolve correctly and have necessary permissions.</li>");
                    details.AppendLine("<li>For File Reader, specify the correct parser (CSV, JSON, XML, etc.) based on file content.</li>");
                    details.AppendLine("<li>For File Writer, choose the appropriate formatter and write mode (create, append, overwrite).</li>");
                    details.AppendLine("<li>Handle file not found, access denied, and other I/O errors.</li>");
                    details.AppendLine("<li>Consider using file locking or unique naming conventions if multiple processes might access the same files.</li>");
                    details.AppendLine("</ul>");
                    details.AppendLine("<p><strong>Example Usage:</strong></p>");
                    string filePathValue = snap.Properties.FirstOrDefault(p => p.Key.ToLower() == "filepath" || p.Key.ToLower() == "filename" || p.Key.ToLower() == "directory").Value;
                    string fileActionValue = snap.Properties.FirstOrDefault(p => p.Key.ToLower() == "fileaction" || p.Key.ToLower() == "actiononcompletion").Value; // e.g., Delete, Archive

                    if (!string.IsNullOrEmpty(filePathValue))
                    {
                        details.AppendLine($"<p>This snap operates on file(s) at path: <code>{WebUtility.HtmlEncode(filePathValue)}</code>.</p>");
                        if (snap.Type.Contains("read"))
                        {
                            var parserProp = snap.Properties.FirstOrDefault(p => p.Key.ToLower().Contains("parser"));
                            details.AppendLine($"<p>It reads the file, likely using a parser defined by properties like '<code>{WebUtility.HtmlEncode(parserProp.Key != null ? parserProp.Key : "parser_type")}</code>'.</p>");
                        }
                        else if (snap.Type.Contains("write"))
                        {
                            var formatterProp = snap.Properties.FirstOrDefault(p => p.Key.ToLower().Contains("formatter"));
                            details.AppendLine($"<p>It writes to the file, likely using a formatter defined by properties like '<code>{WebUtility.HtmlEncode(formatterProp.Key != null ? formatterProp.Key : "formatter_type")}</code>'.</p>");
                        }
                        if (!string.IsNullOrEmpty(fileActionValue))
                        {
                            details.AppendLine($"<p>Action on completion: <code>{WebUtility.HtmlEncode(fileActionValue)}</code>.</p>");
                        }
                    }
                    else
                    {
                        details.AppendLine("<p>File path or specific operation details were not automatically extracted. File operation snaps read from, write to, or manage files and directories based on their configuration (e.g., file path, parser/formatter, action on completion).</p>");
                    }
                    break;

                // ... other categories ...

                default: // Covers SnapCategory.Other and any uncategorized
                    details.AppendLine("<p><strong>General Best Practices:</strong></p>");
                    details.AppendLine("<ul>");
                    details.AppendLine("<li>Ensure the snap is correctly configured for its intended purpose.</li>");
                    details.AppendLine("<li>Review snap-specific documentation for detailed guidance.</li>");
                    details.AppendLine("<li>Use meaningful labels for snaps to improve pipeline readability.</li>");
                    details.AppendLine("</ul>");
                    details.AppendLine("<p><strong>Example Usage:</strong></p>");
                    var relevantProps = snap.Properties
                        .Where(p => !string.IsNullOrEmpty(p.Value) &&
                                    (p.Key.ToLower().Contains("config") ||
                                     p.Key.ToLower().Contains("setting") ||
                                     p.Key.ToLower().Contains("property") ||
                                     p.Key.ToLower().Contains("value") ||
                                     p.Key.ToLower().Contains("expression") ||
                                     p.Key.ToLower().Contains("path")))
                        .Take(5).ToList();
                    if (relevantProps.Any())
                    {
                        details.AppendLine("<p>Key configuration properties for this snap may include:</p>");
                        details.AppendLine("<ul>");
                        foreach (var prop in relevantProps)
                        {
                            details.AppendLine($"<li><strong>{WebUtility.HtmlEncode(prop.Key)}:</strong> <pre><code>{WebUtility.HtmlEncode(prop.Value)}</code></pre></li>");
                        }
                        details.AppendLine("</ul>");
                        details.AppendLine("<p><em>Refer to the snap's configuration in SnapLogic for its complete operational logic.</em></p>");
                    }
                    else
                    {
                        details.AppendLine("<p>The specific operational logic for this snap is determined by its configuration within SnapLogic. Please refer to the snap's properties in the pipeline designer for details.</p>");
                    }
                    break;
            }

            // Add common closing remarks or links if any
            details.AppendLine("<hr/>");
            return details.ToString();
        }  
            
        

        // Helper method to extract a clean target field name from property keys
        private static string ExtractTargetFieldName(string propertyKey)
        {
            // Try to extract a meaningful field name from property keys like "targetPaths[0].fieldName"
            // or "mappings.customer.address.expression"

            // Remove common prefixes
            string cleaned = propertyKey;
            string[] prefixesToRemove = { "targetPath", "target_path", "sourcePath", "source_path", "mapping", "expression", "output" };

            foreach (var prefix in prefixesToRemove)
            {
                if (cleaned.ToLower().StartsWith(prefix.ToLower()))
                {
                    cleaned = cleaned.Substring(prefix.Length);
                    if (cleaned.StartsWith(".") || cleaned.StartsWith("_"))
                        cleaned = cleaned.Substring(1);
                }
            }

            // Handle indexed paths like [0].something
            cleaned = Regex.Replace(cleaned, @"\[\d+\]\.", ".");

            // Handle suffix patterns
            string[] suffixesToRemove = { ".expression", ".value", ".mapping" };
            foreach (var suffix in suffixesToRemove)
            {
                if (cleaned.ToLower().EndsWith(suffix.ToLower()))
                {
                    cleaned = cleaned.Substring(0, cleaned.Length - suffix.Length);
                }
            }

            // If nothing meaningful left, return the original
            return string.IsNullOrWhiteSpace(cleaned) ? propertyKey : cleaned;
        }

        // ... any other existing methods ...
    }
}
