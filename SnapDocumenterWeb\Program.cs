using SnapDocumenterWeb.Components;
using SnapDocumenterWeb.Services;
using SnapLogic.Documentation.Shared;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

// Register our custom services
builder.Services.AddSingleton<ConfigurationService>();
builder.Services.AddScoped<SlpAnalyzer>();
builder.Services.AddScoped<DiagramGenerator>();
builder.Services.AddScoped<IAIDescriptionService, BasicAIDescriptionService>();
builder.Services.AddScoped<DocumentationGenerator>();
builder.Services.AddScoped<FileProcessingService>();
builder.Services.AddScoped<ProjectService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();


app.UseAntiforgery();

app.MapStaticAssets();
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.Run();
