[1mdiff --git a/DocumentationGenerator.cs b/DocumentationGenerator.cs[m
[1mindex dabc36b..e4e1a15 100644[m
[1m--- a/DocumentationGenerator.cs[m
[1m+++ b/DocumentationGenerator.cs[m
[36m@@ -1456,23 +1456,46 @@[m [mnamespace SnapAnalyser[m
             {[m
                 mappings.Append("// Mapping Script\n");[m
                 mappings.Append(script);[m
[31m-            }[m
[31m-            else {[m
[31m-                // Check if there are any properties that might contain mapping information[m
[31m-                foreach (var prop in snap.Properties)[m
[32m+[m[32m            }            else {[m[41m
[m
[32m+[m[32m                // Check for specific legitimate mapping properties only[m[41m
[m
[32m+[m[32m                // This replaces the overly broad search that could capture AI-generated content[m[41m
[m
[32m+[m[32m                var legitimateMappingKeys = new string[][m[41m
[m
[32m+[m[32m                {[m[41m
[m
[32m+[m[32m                    "transformations",[m[41m
[m
[32m+[m[32m                    "transformersList",[m[41m 
[m
[32m+[m[32m                    "mapDefinition",[m[41m
[m
[32m+[m[32m                    "mappingDefinition",[m[41m
[m
[32m+[m[32m                    "fieldMapping",[m[41m
[m
[32m+[m[32m                    "transformationRules"[m[41m
[m
[32m+[m[32m                };[m[41m
[m
[32m+[m[41m                
[m
[32m+[m[32m                foreach (var key in legitimateMappingKeys)[m[41m
[m
                 {[m
[31m-                    if (prop.Key.Contains("map") || prop.Key.Contains("transform") || prop.Key.Contains("expression"))[m
[32m+[m[32m                    if (snap.Properties.TryGetValue(key, out string value) && !string.IsNullOrEmpty(value))[m[41m
[m
                     {[m
[31m-                        mappings.Append($"// {prop.Key}\n");[m
[31m-                        mappings.Append(prop.Value);[m
[32m+[m[32m                        mappings.Append($"// {key}\n");[m[41m
[m
[32m+[m[32m                        mappings.Append(value);[m[41m
[m
                         mappings.Append("\n\n");[m
                     }[m
                 }[m
                 [m
[31m-                // If we still don't have any mappings, return empty string[m
[31m-                if (mappings.Length == 0)[m
[32m+[m[32m                // Also check for specific expression properties but only with proper prefixes[m[41m
[m
[32m+[m[32m                foreach (var prop in snap.Properties)[m[41m
[m
                 {[m
[31m-                    return "";[m
[32m+[m[32m                    // Only include properties that end with .expression and have a proper path structure[m[41m
[m
[32m+[m[32m                    // Exclude AI-generated content by filtering out properties with AI-related prefixes[m[41m
[m
[32m+[m[32m                    if (prop.Key.EndsWith(".expression") &&[m[41m 
[m
[32m+[m[32m                        !prop.Key.StartsWith("ai_") &&[m[41m 
[m
[32m+[m[32m                        !prop.Key.StartsWith("description") &&[m[41m
[m
[32m+[m[32m                        !prop.Key.Contains("generated") &&[m[41m
[m
[32m+[m[32m                        prop.Key.Contains(".") &&[m[41m 
[m
[32m+[m[32m                        !string.IsNullOrEmpty(prop.Value) &&[m[41m 
[m
[32m+[m[32m                        prop.Value.Length < 500) // Reasonable length limit for expressions[m[41m
[m
[32m+[m[32m                    {[m[41m
[m
[32m+[m[32m                        mappings.Append($"// {prop.Key}\n");[m[41m
[m
[32m+[m[32m                        mappings.Append(prop.Value);[m[41m
[m
[32m+[m[32m                        mappings.Append("\n\n");[m[41m
[m
[32m+[m[32m                    }[m[41m
[m
                 }[m
             }[m
             [m
