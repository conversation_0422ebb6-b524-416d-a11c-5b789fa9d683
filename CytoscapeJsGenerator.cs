using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using System.Text.Json;

namespace SnapAnalyser
{
    /// <summary>
    /// Generates cytoscape.js diagrams from SnapLogic pipeline data
    /// </summary>
    public class CytoscapeJsGenerator
    {
        /// <summary>
        /// Converts a PipelineData object into cytoscape.js format
        /// </summary>
        /// <param name="pipeline">The pipeline data to convert</param>
        /// <returns>JSON string for cytoscape.js elements</returns>
        public string GenerateCytoscapeElements(PipelineData pipeline)
        {
            var elements = new List<object>();
            
            // Add nodes
            foreach (var snap in pipeline.Snaps)
            {
                string friendlyType = GetFriendlySnapType(snap.Type);
                string displayLabel = snap.Label;

                // Add snap type to label if we have a friendly type name
                if (!string.IsNullOrEmpty(friendlyType))
                {
                    displayLabel = $"{snap.Label} ({friendlyType})";
                }

                var nodeData = new
                {
                    id = SanitizeId(snap.Id),
                    label = EscapeLabel(displayLabel),
                    type = GetCytoscapeNodeType(snap),
                    category = snap.Category.ToString(),
                    snapType = snap.Type,
                    friendlyType = friendlyType
                };

                elements.Add(new { data = nodeData });
            }
            
            // Add edges
            foreach (var link in pipeline.Links)
            {
                var edgeData = new
                {
                    id = $"edge_{SanitizeId(link.SourceId)}_{SanitizeId(link.TargetId)}",
                    source = SanitizeId(link.SourceId),
                    target = SanitizeId(link.TargetId)
                };
                
                elements.Add(new { data = edgeData });
            }
            
            return JsonSerializer.Serialize(elements, new JsonSerializerOptions { WriteIndented = true });
        }
        
        /// <summary>
        /// Generates complete HTML with cytoscape.js rendering
        /// </summary>
        /// <param name="elementsJson">The cytoscape.js elements JSON</param>
        /// <param name="containerId">The HTML container ID</param>
        /// <param name="title">Optional title for the diagram</param>
        /// <returns>Complete HTML with cytoscape.js rendering</returns>
        public string GenerateCytoscapeHtml(string elementsJson, string containerId = "cy", string title = null)
        {
            var html = new StringBuilder();
            
            html.AppendLine($"<div class=\"cytoscape-container\">");
            if (!string.IsNullOrEmpty(title))
            {
                html.AppendLine($"  <h3 class=\"cytoscape-title\">{HttpUtility.HtmlEncode(title)}</h3>");
            }
            html.AppendLine($"  <div id=\"{containerId}\" class=\"cytoscape-diagram\"></div>");
            html.AppendLine("</div>");
            
            html.AppendLine("<script>");
            html.AppendLine("document.addEventListener('DOMContentLoaded', function() {");
            html.AppendLine("  // Wait for cytoscape.js library to load");
            html.AppendLine("  function initializeCytoscape() {");
            html.AppendLine("    if (typeof cytoscape === 'undefined') {");
            html.AppendLine("      setTimeout(initializeCytoscape, 100);");
            html.AppendLine("      return;");
            html.AppendLine("    }");
            html.AppendLine("    try {");
            
            string sanitizedVarName = SanitizeId($"cy_{containerId}");
            html.AppendLine($"      var {sanitizedVarName} = cytoscape({{");
            html.AppendLine($"        container: document.getElementById('{containerId}'),");
            html.AppendLine($"        elements: {elementsJson},");
            html.AppendLine("        style: [");
            html.AppendLine("          {");
            html.AppendLine("            selector: 'node',");
            html.AppendLine("            style: {");
            html.AppendLine("              'background-color': function(ele) {");
            html.AppendLine("                switch(ele.data('type')) {");
            html.AppendLine("                  case 'start': return '#ffffcc';");
            html.AppendLine("                  case 'end': return '#ffdddd';");
            html.AppendLine("                  case 'operation': return '#ccffcc';");
            html.AppendLine("                  case 'condition': return '#ccccff';");
            html.AppendLine("                  case 'inputoutput': return '#ffccff';");
            html.AppendLine("                  case 'subroutine': return '#ffcccc';");
            html.AppendLine("                  default: return '#ffffff';");
            html.AppendLine("                }");
            html.AppendLine("              },");
            html.AppendLine("              'border-color': '#333',");
            html.AppendLine("              'border-width': 2,");
            html.AppendLine("              'label': 'data(label)',");
            html.AppendLine("              'text-valign': 'center',");
            html.AppendLine("              'text-halign': 'center',");            html.AppendLine("              'color': '#333',");
            html.AppendLine("              'font-size': '11px',");
            html.AppendLine("              'font-weight': 'bold',");
            html.AppendLine("              'width': function(ele) {");
            html.AppendLine("                var label = ele.data('label');");
            html.AppendLine("                var hasType = ele.data('friendlyType') && ele.data('friendlyType').length > 0;");
            html.AppendLine("                var baseWidth = Math.max(label.length * 6, 120);");
            html.AppendLine("                return hasType ? Math.max(baseWidth, 160) : baseWidth;");
            html.AppendLine("              },");            html.AppendLine("              'height': function(ele) {");
            html.AppendLine("                var hasType = ele.data('friendlyType') && ele.data('friendlyType').length > 0;");
            html.AppendLine("                return hasType ? 85 : 68;");
            html.AppendLine("              },");
            html.AppendLine("              'shape': function(ele) {");
            html.AppendLine("                switch(ele.data('type')) {");
            html.AppendLine("                  case 'start': return 'ellipse';");
            html.AppendLine("                  case 'end': return 'ellipse';");
            html.AppendLine("                  case 'condition': return 'diamond';");
            html.AppendLine("                  case 'inputoutput': return 'polygon';");
            html.AppendLine("                  default: return 'rectangle';");
            html.AppendLine("                }");            html.AppendLine("              },");
            html.AppendLine("              'text-wrap': 'wrap',");
            html.AppendLine("              'text-max-width': function(ele) {");
            html.AppendLine("                var hasType = ele.data('friendlyType') && ele.data('friendlyType').length > 0;");
            html.AppendLine("                return hasType ? 150 : 110;");
            html.AppendLine("              }");
            html.AppendLine("            }");
            html.AppendLine("          },");
            html.AppendLine("          {");
            html.AppendLine("            selector: 'edge',");
            html.AppendLine("            style: {");
            html.AppendLine("              'width': 2,");
            html.AppendLine("              'line-color': '#666',");
            html.AppendLine("              'target-arrow-color': '#666',");
            html.AppendLine("              'target-arrow-shape': 'triangle',");
            html.AppendLine("              'curve-style': 'bezier'");
            html.AppendLine("            }");
            html.AppendLine("          }");
            html.AppendLine("        ],");        html.AppendLine("        layout: {");
        html.AppendLine("          name: 'dagre',");
        html.AppendLine("          directed: true,");
        html.AppendLine("          padding: 20,");
        html.AppendLine("          spacingFactor: 1.2,");
        html.AppendLine("          nodeDimensionsIncludeLabels: true,");
        html.AppendLine("          rankDir: 'LR',");
        html.AppendLine("          ranker: 'network-simplex',");
        html.AppendLine("          rankSep: 60,");
        html.AppendLine("          nodeSep: 40");
        html.AppendLine("        },");
            html.AppendLine("        wheelSensitivity: 0.1,");
            html.AppendLine("        minZoom: 0.4,");
            html.AppendLine("        maxZoom: 3");
            html.AppendLine("      });");            html.AppendLine("      ");
            html.AppendLine("      // Add click handlers for interactivity");
            html.AppendLine($"      {sanitizedVarName}.on('tap', 'node', function(evt) {{");
            html.AppendLine("        var node = evt.target;");
            html.AppendLine("        console.log('Clicked node:', node.data());");
            html.AppendLine("      });");
            html.AppendLine("      ");            html.AppendLine("      // Fit diagram to container with left alignment");
            html.AppendLine($"      setTimeout(function() {{");
            html.AppendLine($"        // Force container to full width");
            html.AppendLine($"        var container = document.getElementById('{containerId}');");
            html.AppendLine($"        if (container) {{");
            html.AppendLine($"          container.style.width = '100%';");
            html.AppendLine($"          container.style.margin = '0';");
            html.AppendLine($"        }}");
            html.AppendLine($"        // Resize and fit the diagram");
            html.AppendLine($"        {sanitizedVarName}.resize();");
            html.AppendLine($"        {sanitizedVarName}.fit({sanitizedVarName}.elements(), 50);");
            html.AppendLine($"        // Position at left side instead of centering");
            html.AppendLine($"        var extent = {sanitizedVarName}.elements().boundingBox();");
            html.AppendLine($"        var containerWidth = {sanitizedVarName}.width();");
            html.AppendLine($"        {sanitizedVarName}.pan({{ x: 50, y: {sanitizedVarName}.height() / 2 - extent.h / 2 }});");
            html.AppendLine($"        // Ensure readable zoom level");
            html.AppendLine($"        var currentZoom = {sanitizedVarName}.zoom();");
            html.AppendLine($"        if (currentZoom < 0.6) {{");
            html.AppendLine($"          {sanitizedVarName}.zoom(0.6);");
            html.AppendLine($"          {sanitizedVarName}.pan({{ x: 50, y: {sanitizedVarName}.height() / 2 - extent.h / 2 }});");
            html.AppendLine($"        }}");
            html.AppendLine($"      }}, 200);");
            html.AppendLine("      ");
            html.AppendLine("    } catch (error) {");
            html.AppendLine($"      console.error('Error rendering cytoscape diagram for {containerId}:', error);");
            html.AppendLine("    }");
            html.AppendLine("  }");
            html.AppendLine("  initializeCytoscape();");
            html.AppendLine("});");
            html.AppendLine("</script>");
            
            return html.ToString();
        }
        
        /// <summary>
        /// Converts class_id to user-friendly snap type label
        /// </summary>
        private string GetFriendlySnapType(string classId)
        {
            if (string.IsNullOrEmpty(classId))
                return "";

            // Convert common SnapLogic class_ids to friendly names
            var typeMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
            {
                // Dynamics 365 snaps
                { "com-snaplogic-snaps-dynamics365forsales-search", "Dynamics 365 Search" },
                { "com-snaplogic-snaps-dynamics365forsales-create", "Dynamics 365 Create" },
                { "com-snaplogic-snaps-dynamics365forsales-update", "Dynamics 365 Update" },
                { "com-snaplogic-snaps-dynamics365forsales-delete", "Dynamics 365 Delete" },
                { "com-snaplogic-snaps-dynamics365forsales-upsert", "Dynamics 365 Upsert" },

                // Transform snaps
                { "com-snaplogic-snaps-transform-datatransform", "Mapper" },
                { "com-snaplogic-snaps-transform-jsonformatter", "JSON Formatter" },
                { "com-snaplogic-snaps-transform-xmlformatter", "XML Formatter" },
                { "com-snaplogic-snaps-transform-csvformatter", "CSV Formatter" },
                { "com-snaplogic-snaps-transform-jsonparser", "JSON Parser" },
                { "com-snaplogic-snaps-transform-xmlparser", "XML Parser" },
                { "com-snaplogic-snaps-transform-csvparser", "CSV Parser" },

                // Flow control snaps
                { "com-snaplogic-snaps-flow-router", "Router" },
                { "com-snaplogic-snaps-flow-gate", "Gate" },
                { "com-snaplogic-snaps-flow-join", "Join" },
                { "com-snaplogic-snaps-flow-union", "Union" },
                { "com-snaplogic-snaps-flow-copy", "Copy" },
                { "com-snaplogic-snaps-flow-filter", "Filter" },
                { "com-snaplogic-snaps-flow-sort", "Sort" },
                { "com-snaplogic-snaps-flow-aggregate", "Aggregate" },
                { "com-snaplogic-snaps-flow-sequence", "Sequence" },

                // Database snaps
                { "com-snaplogic-snaps-sql-sqlserver-select", "SQL Server Select" },
                { "com-snaplogic-snaps-sql-sqlserver-insert", "SQL Server Insert" },
                { "com-snaplogic-snaps-sql-sqlserver-update", "SQL Server Update" },
                { "com-snaplogic-snaps-sql-sqlserver-delete", "SQL Server Delete" },
                { "com-snaplogic-snaps-sql-sqlserver-execute", "SQL Server Execute" },
                { "com-snaplogic-snaps-sql-oracle-select", "Oracle Select" },
                { "com-snaplogic-snaps-sql-mysql-select", "MySQL Select" },

                // File snaps
                { "com-snaplogic-snaps-binary-filereader", "File Reader" },
                { "com-snaplogic-snaps-binary-filewriter", "File Writer" },
                { "com-snaplogic-snaps-binary-directory", "Directory Browser" },

                // HTTP/REST snaps
                { "com-snaplogic-snaps-rest-get", "REST Get" },
                { "com-snaplogic-snaps-rest-post", "REST Post" },
                { "com-snaplogic-snaps-rest-put", "REST Put" },
                { "com-snaplogic-snaps-rest-delete", "REST Delete" },
                { "com-snaplogic-snaps-rest-patch", "REST Patch" },

                // Utility snaps
                { "com-snaplogic-snaps-transform-structure", "Structure" },
                { "com-snaplogic-snaps-transform-jsonpath", "JSONPath" },
                { "com-snaplogic-snaps-transform-xmlpath", "XPath" },
                { "com-snaplogic-snaps-binary-base64encode", "Base64 Encode" },
                { "com-snaplogic-snaps-binary-base64decode", "Base64 Decode" },

                // Email snaps
                { "com-snaplogic-snaps-email-emailsender", "Email Sender" },
                { "com-snaplogic-snaps-email-emailreader", "Email Reader" },

                // Salesforce snaps
                { "com-snaplogic-snaps-salesforce-read", "Salesforce Read" },
                { "com-snaplogic-snaps-salesforce-write", "Salesforce Write" },
                { "com-snaplogic-snaps-salesforce-upsert", "Salesforce Upsert" },
                { "com-snaplogic-snaps-salesforce-delete", "Salesforce Delete" },

                // Document snaps
                { "com-snaplogic-snaps-document-documenttoxml", "Document to XML" },
                { "com-snaplogic-snaps-document-xmltodocument", "XML to Document" },
                { "com-snaplogic-snaps-document-documenttojson", "Document to JSON" },
                { "com-snaplogic-snaps-document-jsontodocument", "JSON to Document" }
            };

            if (typeMap.TryGetValue(classId, out string friendlyName))
            {
                return friendlyName;
            }

            // Fallback: try to extract a meaningful name from the class_id
            if (classId.StartsWith("com-snaplogic-snaps-"))
            {
                var parts = classId.Split('-');
                if (parts.Length >= 4)
                {
                    // Take the last part and capitalize it
                    string lastPart = parts[parts.Length - 1];
                    return char.ToUpper(lastPart[0]) + lastPart.Substring(1);
                }
            }

            return ""; // Return empty string if no friendly name found
        }

        /// <summary>
        /// Maps SnapLogic snap categories to cytoscape.js node types
        /// </summary>
        private string GetCytoscapeNodeType(SnapNode snap)
        {
            // Check if it's a start node
            if (snap.IsStartPoint)
            {
                return "start";
            }
            
            // Check if it's an end node (no output connections)
            if (!snap.OutputConnections.Any())
            {
                return "end";
            }
            
            // Map based on category and type
            switch (snap.Category)
            {
                case SnapCategory.FlowControl:
                    if (snap.Type.Contains("Router", StringComparison.OrdinalIgnoreCase) ||
                        snap.Type.Contains("Filter", StringComparison.OrdinalIgnoreCase) ||
                        snap.Type.Contains("Condition", StringComparison.OrdinalIgnoreCase))
                    {
                        return "condition";
                    }
                    return "operation";
                    
                case SnapCategory.Database:
                case SnapCategory.ExternalSystem:
                    return "subroutine";
                    
                case SnapCategory.FileOperation:
                    return "inputoutput";
                    
                case SnapCategory.Transformation:
                case SnapCategory.ErrorHandling:
                default:
                    return "operation";
            }
        }
        
        /// <summary>
        /// Sanitizes node IDs to be valid cytoscape.js identifiers
        /// </summary>
        public string SanitizeId(string id)
        {
            if (string.IsNullOrEmpty(id))
                return "node" + Guid.NewGuid().ToString("N")[..8];
                
            // Replace invalid characters with underscores
            var sanitized = System.Text.RegularExpressions.Regex.Replace(id, @"[^a-zA-Z0-9_]", "_");
            
            // Ensure it starts with a letter
            if (char.IsDigit(sanitized[0]))
            {
                sanitized = "n" + sanitized;
            }
            
            return sanitized;
        }
        
        /// <summary>
        /// Escapes special characters in node labels for cytoscape.js
        /// </summary>
        public string EscapeLabel(string label)
        {
            if (string.IsNullOrEmpty(label))
                return "Untitled";
                
            // Escape quotes and special characters for JSON
            return label.Replace("\"", "\\\"").Replace("\n", "\\n").Replace("\r", "").Trim();
        }
    }
}
