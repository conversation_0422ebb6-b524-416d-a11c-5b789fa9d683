# PowerShell script to fix Dynamics365 snap account display
# The issue is that connection info is only shown for Database category snaps,
# but dynamics365forsales snaps are not categorized as Database

$filePath = "DocumentationGenerator.cs"

Write-Host "Fixing Dynamics365 snap account display..." -ForegroundColor Green

$content = Get-Content $filePath -Raw

# Fix 1: Change the condition from database-only to any snap with account info
# Current: if (snap.Category == SnapCategory.Database)
# Should be: if (false) // Show accounts for any snap that has an account

$oldCondition = 'if \(snap\.Category == SnapCategory\.Database\)'
$newCondition = 'if (false) // Show accounts for any snap that has an account'

if ($content -match $oldCondition) {
    $content = $content -replace $oldCondition, $newCondition
    Write-Host "✅ Updated database-only condition to show accounts for any snap" -ForegroundColor Yellow
} else {
    Write-Host "❌ Database-only condition not found" -ForegroundColor Red
}

# Fix 2: Update the GetConnectionInfo method to handle dynamics365forsales specifically
# Add logic to look for dynamics365forsales-specific property names

$getConnectionInfoMethod = @'
    private string GetConnectionInfo(SnapNode snap, JObject rawSnapJson = null)
    {
        // Show accounts for any snap that has an account (removed database-only restriction)
        if (false) // Show accounts for any snap that has an account
        {
            Console.WriteLine($"[CONNECTION] Snap {snap.Label} is not categorized as Database (Category: {snap.Category})");
            return null;
        }

        Console.WriteLine($"[CONNECTION] Checking connection info for snap: {snap.Label}, Category: {snap.Category}, Type: {snap.Type}");

        // Handle raw JSON parsing for nested account structures
        if (rawSnapJson != null)
        {
            try
            {
                Console.WriteLine($"[CONNECTION] Raw JSON provided for account extraction");
                var accountNode = rawSnapJson["property_map"]?["account"];
                if (accountNode != null)
                {
                    Console.WriteLine($"[CONNECTION] Found account node in property_map");
                    var accountRef = accountNode["account_ref"];
                    if (accountRef != null)
                    {
                        var valueObj = accountRef["value"];
                        if (valueObj != null)
                        {
                            var labelObj = valueObj["label"];
                            if (labelObj != null)
                            {
                                var accountName = labelObj["value"]?.ToString();
                                if (!string.IsNullOrEmpty(accountName))
                                {
                                    Console.WriteLine($"[CONNECTION] Found account name from raw JSON: {accountName}");
                                    return accountName;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CONNECTION] Error parsing account from raw JSON: {ex.Message}");
            }
        }
        else
        {
            Console.WriteLine($"[CONNECTION] No raw JSON provided for account extraction");
        }
            
        // Log all properties for debugging
        foreach (var prop in snap.Properties)
        {
            Console.WriteLine($"[CONNECTION] Property: {prop.Key} = {prop.Value}");
        }

        // First, look for the nested account structure
        if (snap.Properties.ContainsKey("account"))
        {
            try
            {
                string accountJson = snap.Properties["account"];
                Console.WriteLine($"[CONNECTION] Found account property, parsing JSON (length: {accountJson.Length})");
                Console.WriteLine($"[CONNECTION] Account JSON content: {accountJson}");
                
                var accountObj = JObject.Parse(accountJson);
                
                // Navigate through the nested structure: account -> account_ref -> value -> label -> value
                var accountRef = accountObj["account_ref"];
                if (accountRef != null)
                {
                    var valueObj = accountRef["value"];
                    if (valueObj != null)
                    {
                        var labelObj = valueObj["label"];
                        if (labelObj != null)
                        {
                            var accountName = labelObj["value"]?.ToString();
                            if (!string.IsNullOrEmpty(accountName))
                            {
                                Console.WriteLine($"[CONNECTION] Found account name from nested JSON: {accountName}");
                                return accountName;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CONNECTION] Error parsing account JSON: {ex.Message}");
                Console.WriteLine($"[CONNECTION] JSON parsing failed - see details above");
            }
        }

        // Special handling for Dynamics365ForSales snaps - look for their specific properties
        if (snap.Type?.ToLower().Contains("dynamics365forsales") == true)
        {
            Console.WriteLine($"[CONNECTION] Special handling for Dynamics365ForSales snap: {snap.Label}");
            
            // Look for dynamics365-specific properties
            var dynamics365Props = snap.Properties.Where(p =>
                (p.Key.ToLower().Contains("organization") ||
                p.Key.ToLower().Contains("instance") ||      
                p.Key.ToLower().Contains("tenant") ||
                p.Key.ToLower().Contains("environment") ||
                p.Key.ToLower().Contains("service") ||
                p.Key.ToLower().Contains("url") ||
                p.Key.ToLower().Contains("connection") ||
                p.Key.ToLower().Contains("endpoint") ||
                p.Key.ToLower().Contains("crm") ||
                p.Key.ToLower().Contains("dynamics")) &&
                !string.IsNullOrEmpty(p.Value) &&
                !p.Value.Equals("true", StringComparison.OrdinalIgnoreCase) &&
                !p.Value.Equals("false", StringComparison.OrdinalIgnoreCase) &&
                !bool.TryParse(p.Value, out _))
                .ToList();

            foreach (var prop in dynamics365Props)
            {
                Console.WriteLine($"[CONNECTION] Found Dynamics365 property: {prop.Key} = {prop.Value}");
                return prop.Value;
            }
        }

        // Look for account/connection properties, but filter out boolean values
        var connectionProps = snap.Properties.Where(p =>
            (p.Key.ToLower().Contains("account") ||
            p.Key.ToLower().Contains("connect") ||
            p.Key.ToLower().Contains("datasource") ||
            p.Key.ToLower().Contains("database") ||
            p.Key.ToLower().Contains("server") ||
            p.Key.ToLower().Contains("sqlserver") ||
            p.Key.ToLower().Contains("sql_server") ||
            p.Key.ToLower().Contains("dbaccount") ||
            p.Key.ToLower().Contains("db_account") ||
            p.Key.ToLower().Contains("connectionstring") ||
            p.Key.ToLower().Contains("connection_string") ||
            p.Key.ToLower().Contains("host") ||
            p.Key.ToLower().Contains("instance") ||
            p.Key.ToLower().Contains("servername") ||
            p.Key.ToLower().Contains("server_name")) &&
            !string.IsNullOrEmpty(p.Value) &&
            !p.Value.Equals("true", StringComparison.OrdinalIgnoreCase) &&
            !p.Value.Equals("false", StringComparison.OrdinalIgnoreCase) &&
            !bool.TryParse(p.Value, out _))
            .ToList();

        foreach (var prop in connectionProps)
        {
            Console.WriteLine($"[CONNECTION] Found valid connection property: {prop.Key} = {prop.Value}");
            
            // Clean up the account name for display
            string accountName = prop.Value;
            
            // Remove common prefixes/suffixes that might not be user-friendly
            if (accountName.StartsWith("$") && accountName.Contains("."))
            {
                // Handle parameter references like "$account.database_account"
                accountName = accountName.Substring(accountName.LastIndexOf('.') + 1);
            }
            
            return accountName;
        }

        // Look for database name in properties (also filter out boolean values)
        var dbNameProps = snap.Properties.Where(p =>
            (p.Key.ToLower().Contains("dbname") ||
            p.Key.ToLower().Contains("database_name") ||
            p.Key.ToLower().Contains("schema") ||
            p.Key.ToLower().Contains("databasename") ||
            p.Key.ToLower().Contains("catalog") ||
            p.Key.ToLower().Contains("initialcatalog") ||
            p.Key.ToLower().Contains("initial_catalog") ||
            p.Key.ToLower().Contains("db") ||
            p.Key.ToLower().Contains("table") ||
            p.Key.ToLower().Contains("tablename") ||
            p.Key.ToLower().Contains("table_name")) &&
            !string.IsNullOrEmpty(p.Value) &&
            !p.Value.Equals("true", StringComparison.OrdinalIgnoreCase) &&
            !p.Value.Equals("false", StringComparison.OrdinalIgnoreCase) &&
            !bool.TryParse(p.Value, out _))
            .ToList();

        foreach (var prop in dbNameProps)
        {
            Console.WriteLine($"[CONNECTION] Found database name property: {prop.Key} = {prop.Value}");
            return prop.Value;
        }

        Console.WriteLine($"[CONNECTION] No connection info found for snap: {snap.Label}");
        return null;
    }
'@

# Replace the entire GetConnectionInfo method
$methodPattern = 'private string GetConnectionInfo\(SnapNode snap, JObject rawSnapJson = null\)[^}]*\{[^}]*(?:\{[^}]*\}[^}]*)*\}'
if ($content -match $methodPattern) {
    $content = $content -replace $methodPattern, $getConnectionInfoMethod
    Write-Host "✅ Updated GetConnectionInfo method with Dynamics365 support" -ForegroundColor Yellow
} else {
    Write-Host "❌ GetConnectionInfo method not found for replacement" -ForegroundColor Red
}

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "✅ Dynamics365 account display fix applied!" -ForegroundColor Green
Write-Host "Changes made:" -ForegroundColor Cyan
Write-Host "  1. Removed database-only restriction for account display" -ForegroundColor White
Write-Host "  2. Added special handling for Dynamics365ForSales snaps" -ForegroundColor White
Write-Host "  3. Enhanced property name detection for Dynamics365 accounts" -ForegroundColor White
