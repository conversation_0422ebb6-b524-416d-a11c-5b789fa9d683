# Fix the account extraction path in GetDatabaseConnectionInfo

$filePath = "DocumentationGenerator.cs"

Write-Host "Fixing account extraction path..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Find and replace the account extraction logic
$oldAccountExtraction = @'
// Try to extract account from raw JSON nested structure
                    var accountNode = rawSnapJson.SelectToken("account.account_ref.value.label.value");
                    if (accountNode != null && !string.IsNullOrEmpty(accountNode.ToString()))
                    {
                        var accountName = accountNode.ToString();
                        if (accountName.ToLower() != "false" && accountName.ToLower() != "true")
                        {
                            Console.WriteLine($"[DB-CONNECTION] Successfully extracted account from nested JSON: {accountName}");
                            return accountName;
                        }
                    }
                    else
                    {
                        Console.WriteLine($"[DB-CONNECTION] No account node found in raw JSON");
                    }
'@

$newAccountExtraction = @'
// Try to extract account from raw JSON nested structure
                    var accountNode = rawSnapJson.SelectToken("property_map.account.account_ref.value.label.value");
                    if (accountNode != null && !string.IsNullOrEmpty(accountNode.ToString()))
                    {
                        var accountName = accountNode.ToString();
                        if (accountName.ToLower() != "false" && accountName.ToLower() != "true")
                        {
                            Console.WriteLine($"[DB-CONNECTION] Successfully extracted account from nested JSON: {accountName}");
                            return accountName;
                        }
                    }
                    else
                    {
                        Console.WriteLine($"[DB-CONNECTION] No account found at path: property_map.account.account_ref.value.label.value");
                        // Let's debug what's actually available
                        var propMap = rawSnapJson.SelectToken("property_map");
                        if (propMap != null)
                        {
                            Console.WriteLine($"[DB-CONNECTION] property_map exists");
                            var account = propMap.SelectToken("account");
                            if (account != null)
                            {
                                Console.WriteLine($"[DB-CONNECTION] account exists: {account.ToString(Newtonsoft.Json.Formatting.None)}");
                            }
                            else
                            {
                                Console.WriteLine($"[DB-CONNECTION] No account in property_map");
                            }
                        }
                        else
                        {
                            Console.WriteLine($"[DB-CONNECTION] No property_map found");
                        }
                    }
'@

if ($content.Contains($oldAccountExtraction)) {
    $content = $content.Replace($oldAccountExtraction, $newAccountExtraction)
    Write-Host "Fixed account extraction path from 'account.' to 'property_map.account.'" -ForegroundColor Yellow
} else {
    Write-Host "Account extraction code not found" -ForegroundColor Red
}

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Fixed account extraction path!" -ForegroundColor Green
