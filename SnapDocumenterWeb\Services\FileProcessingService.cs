using System.IO.Compression;
using SnapLogic.Documentation.Shared;

namespace SnapDocumenterWeb.Services
{
    public class FileProcessingService
    {
        private readonly SlpAnalyzer _analyzer;
        private readonly DiagramGenerator _diagramGenerator;
        private readonly DocumentationGenerator _docGenerator;
        private readonly ILogger<FileProcessingService> _logger;

        public FileProcessingService(
            SlpAnalyzer analyzer,
            DiagramGenerator diagramGenerator,
            DocumentationGenerator docGenerator,
            ILogger<FileProcessingService> logger)
        {
            _analyzer = analyzer;
            _diagramGenerator = diagramGenerator;
            _docGenerator = docGenerator;
            _logger = logger;
        }

        public async Task<ProcessingResult> ProcessFilesAsync(
            List<UploadedFile> files,
            ProcessingOptions options,
            ProjectData? project = null,
            IProgress<ProcessingProgress>? progress = null,
            CancellationToken cancellationToken = default)
        {
            var result = new ProcessingResult { Success = true };
            var tempDirectory = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
            Directory.CreateDirectory(tempDirectory);
            var generatedFiles = new List<string>();

            try
            {
                _logger.LogInformation("Starting batch processing of {FileCount} files", files.Count);

                for (int i = 0; i < files.Count; i++)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    var file = files[i];
                    var fileName = Path.GetFileNameWithoutExtension(file.FileName);

                    try
                    {
                        progress?.Report(new ProcessingProgress
                        {
                            CurrentFileName = file.FileName,
                            CurrentFile = i + 1,
                            ProcessedFiles = i,
                            TotalFiles = files.Count,
                            Status = $"Processing {file.FileName}..."
                        });

                        _logger.LogInformation("Processing file: {FileName}", file.FileName);

                        // Analyze pipeline
                        var pipelineData = _analyzer.AnalyzePipeline(file.Content);
                        
                        // Generate diagram
                        var diagramSvg = _diagramGenerator.GenerateDiagram(pipelineData);

                        // Generate HTML documentation
                        if (options.GenerateHtml)
                        {
                            var htmlDoc = await _docGenerator.GenerateHtmlDocumentationAsync(
                                pipelineData, diagramSvg, project, cancellationToken);

                            var htmlPath = Path.Combine(tempDirectory, $"{fileName}_documentation.html");
                            await File.WriteAllTextAsync(htmlPath, htmlDoc, cancellationToken);
                            generatedFiles.Add(htmlPath); // Add to generated files for ZIP creation

                            result.ProcessedFiles.Add(new ProcessedFile
                            {
                                FileName = file.FileName,
                                Success = true,
                                HtmlContent = htmlDoc
                            });
                        }

                        // Generate PDF documentation (placeholder - would need actual PDF generation)
                        if (options.GeneratePdf)
                        {
                            // For now, just create a placeholder PDF file
                            var pdfPath = Path.Combine(tempDirectory, $"{fileName}_documentation.pdf");
                            await File.WriteAllTextAsync(pdfPath, "PDF generation not implemented in web version", cancellationToken);
                            generatedFiles.Add(pdfPath);
                        }

                        _logger.LogInformation("Successfully processed file: {FileName}", file.FileName);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing file: {FileName}", file.FileName);
                        result.ProcessedFiles.Add(new ProcessedFile
                        {
                            FileName = file.FileName,
                            Success = false,
                            ErrorMessage = ex.Message
                        });
                        result.Errors.Add($"Error processing {file.FileName}: {ex.Message}");
                        result.Success = false;
                    }
                }

                // Create ZIP file with all generated documentation
                if (generatedFiles.Any())
                {
                    var zipPath = Path.Combine(tempDirectory, "documentation.zip");
                    using (var zip = ZipFile.Open(zipPath, ZipArchiveMode.Create))
                    {
                        foreach (var filePath in generatedFiles)
                        {
                            var entryName = Path.GetFileName(filePath);
                            zip.CreateEntryFromFile(filePath, entryName);
                        }
                    }
                    result.DownloadUrl = zipPath;
                    result.ZipFilePath = zipPath;
                }

                progress?.Report(new ProcessingProgress
                {
                    CurrentFileName = "",
                    CurrentFile = files.Count,
                    ProcessedFiles = result.ProcessedFiles.Count,
                    TotalFiles = files.Count,
                    Status = "Processing complete!"
                });

                _logger.LogInformation("Batch processing completed. Processed {ProcessedFiles} of {TotalFiles} files",
                    result.ProcessedFiles.Count, files.Count);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during batch processing");
                result.Success = false;
                result.Message = $"Batch processing error: {ex.Message}";
                result.Errors.Add($"Batch processing error: {ex.Message}");
                return result;
            }
        }

        public async Task<byte[]> GetZipFileAsync(string zipFilePath)
        {
            if (!File.Exists(zipFilePath))
                throw new FileNotFoundException("ZIP file not found");

            return await File.ReadAllBytesAsync(zipFilePath);
        }

        public void CleanupTempFiles(string? zipFilePath)
        {
            if (string.IsNullOrEmpty(zipFilePath))
                return;

            try
            {
                var directory = Path.GetDirectoryName(zipFilePath);
                if (Directory.Exists(directory))
                {
                    Directory.Delete(directory, true);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to cleanup temp directory: {Directory}", Path.GetDirectoryName(zipFilePath));
            }
        }
    }
}
