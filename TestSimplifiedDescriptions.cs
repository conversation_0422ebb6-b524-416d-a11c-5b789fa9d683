using System;
using System.Collections.Generic;
using SnapAnalyser;

class TestSimplifiedDescriptions
{
    static void Main()
    {
        Console.WriteLine("=== Testing Simplified Descriptions for Basic Snap Types ===\n");
        
        // Test Copy snap
        var copySnap = new SnapNode 
        { 
            Type = "sl-copy", 
            Label = "Copy Documents",
            Properties = new List<SnapProperty>(),
            Category = SnapCategory.FlowControl
        };
        TestSnapDescription(copySnap, "Copy");
        
        // Test Union snap
        var unionSnap = new SnapNode 
        { 
            Type = "sl-union", 
            Label = "Union Multiple Streams",
            Properties = new List<SnapProperty>(),
            Category = SnapCategory.FlowControl
        };
        TestSnapDescription(unionSnap, "Union");
        
        // Test Exit snap
        var exitSnap = new SnapNode 
        { 
            Type = "sl-exit", 
            Label = "Exit Pipeline",
            Properties = new List<SnapProperty>(),
            Category = SnapCategory.FlowControl
        };
        TestSnapDescription(exitSnap, "Exit");
        
        Console.WriteLine("\n=== Test Completed ===");
    }
    
    static void TestSnapDescription(SnapNode snap, string snapTypeName)
    {
        Console.WriteLine($"--- Testing {snapTypeName} Snap ---");
        
        // Test ShouldSkipAIDescription
        var generator = new AIDescriptionGenerator("", "", "", 30);
        bool shouldSkip = TestShouldSkipAIDescription(generator, snap);
        Console.WriteLine($"ShouldSkipAIDescription: {shouldSkip}");
        
        // Test GetSimplifiedDescription
        string description = TestGetSimplifiedDescription(generator, snap);
        Console.WriteLine($"Simplified Description: {description}");
        
        // Test SnapBestPractices
        string bestPractices = SnapBestPractices.GetSnapBestPractices(snap, new List<SnapNode>());
        Console.WriteLine($"Best Practices (length): {bestPractices.Length} characters");
        Console.WriteLine($"Contains 'Best Practices for {snapTypeName}': {bestPractices.Contains($"Best Practices for {snapTypeName}")}");
        
        Console.WriteLine();
    }
    
    // Use reflection to test private methods
    static bool TestShouldSkipAIDescription(AIDescriptionGenerator generator, SnapNode snap)
    {
        var method = typeof(AIDescriptionGenerator).GetMethod("ShouldSkipAIDescription", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        return (bool)method.Invoke(generator, new object[] { snap });
    }
    
    static string TestGetSimplifiedDescription(AIDescriptionGenerator generator, SnapNode snap)
    {
        var method = typeof(AIDescriptionGenerator).GetMethod("GetSimplifiedDescription", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        return (string)method.Invoke(generator, new object[] { snap });
    }
}
