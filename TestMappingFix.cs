using System;
using System.Collections.Generic;

namespace SnapAnalyser
{
    class TestMappingFix
    {
        static void Main(string[] args)
        {
            Console.WriteLine("Testing Mapping Fix for AI Content Exclusion");
            Console.WriteLine("==============================================");

            var generator = new DocumentationGenerator();
            
            // Create a test snap with various properties including AI-generated content
            var testSnap = new SnapNode
            {
                Type = "Mapper",
                Label = "Test Mapper",
                Properties = new Dictionary<string, string>
                {
                    // Legitimate mapping properties that should be included
                    { "transformations", "field1 -> field2" },
                    { "fieldMapping", "source.id -> target.identifier" },
                    
                    // AI-generated content that should be excluded
                    { "ai_description", "This is AI-generated content about mapping transformations" },
                    { "description_generated", "Auto-generated mapping description with expressions" },
                    { "map_ai_summary", "AI summary containing map keywords" },
                    
                    // Properties with broad keywords that should be excluded
                    { "general_map_info", "Some general mapping information" },
                    { "transform_notes", "General transformation notes" },
                    { "expression_help", "Help text about expressions" },
                    
                    // Legitimate expression properties that should be included
                    { "field.source.expression", "$input.data" },
                    { "transform.target.expression", "$source.id + '_transformed'" },
                    
                    // AI expression properties that should be excluded
                    { "ai_generated.expression", "AI generated expression content" },
                    { "description.field.expression", "Description of field expression" }
                }
            };

            // Use reflection to call the private ExtractMapperMappings method
            var method = typeof(DocumentationGenerator).GetMethod("ExtractMapperMappings", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (method != null)
            {
                string result = (string)method.Invoke(generator, new object[] { testSnap });
                
                Console.WriteLine("Extracted Mapping Content:");
                Console.WriteLine("---------------------------");
                Console.WriteLine(result);
                Console.WriteLine("---------------------------");
                
                // Verify the fix worked
                bool hasAIContent = result.Contains("AI-generated") || 
                                  result.Contains("ai_description") ||
                                  result.Contains("description_generated") ||
                                  result.Contains("general_map_info") ||
                                  result.Contains("transform_notes") ||
                                  result.Contains("expression_help");
                
                bool hasLegitimateContent = result.Contains("transformations") ||
                                          result.Contains("fieldMapping") ||
                                          result.Contains("field.source.expression") ||
                                          result.Contains("transform.target.expression");
                
                Console.WriteLine($"\nTest Results:");
                Console.WriteLine($"Contains AI/Generated Content: {hasAIContent} (should be false)");
                Console.WriteLine($"Contains Legitimate Mapping Content: {hasLegitimateContent} (should be true)");
                
                if (!hasAIContent && hasLegitimateContent)
                {
                    Console.WriteLine("✅ TEST PASSED: Fix successfully excludes AI content while preserving legitimate mappings");
                }
                else
                {
                    Console.WriteLine("❌ TEST FAILED: Fix did not work as expected");
                }
            }
            else
            {
                Console.WriteLine("❌ Could not find ExtractMapperMappings method");
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
