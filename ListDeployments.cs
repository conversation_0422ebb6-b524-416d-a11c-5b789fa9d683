using System;
using System.Threading.Tasks;
using SnapAnalyser;

class ListDeployments
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== Azure OpenAI Deployment Lister ===");
        Console.WriteLine($"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        Console.WriteLine();
        
        try
        {
            // Load configuration
            var config = ConfigManager.LoadConfig();
            Console.WriteLine("Current Configuration:");
            Console.WriteLine($"  Endpoint: {config.AzureOpenAIEndpoint}");
            Console.WriteLine($"  Current Deployment Name: {config.AzureOpenAIDeploymentName}");
            Console.WriteLine($"  API Key: {(string.IsNullOrEmpty(config.OpenAIApiKey) ? "MISSING" : "Present")}");
            Console.WriteLine();
            
            if (string.IsNullOrEmpty(config.OpenAIApiKey) || string.IsNullOrEmpty(config.AzureOpenAIEndpoint))
            {
                Console.WriteLine("❌ Cannot list deployments - missing API key or endpoint");
                return;
            }
            
            // Create tester and get deployment info
            var tester = new AzureOpenAITester();
            Console.WriteLine("🔍 Fetching available deployments from Azure OpenAI...");
            Console.WriteLine();
            
            string deploymentInfo = await tester.GetDeploymentInfo();
            Console.WriteLine(deploymentInfo);
            
            Console.WriteLine();
            Console.WriteLine("💡 NEXT STEPS:");
            Console.WriteLine("1. Choose one of the deployment names listed above");
            Console.WriteLine("2. Update the 'AzureOpenAIDeploymentName' in config.json");
            Console.WriteLine("3. Test the connection again");
            
            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
            if (ex.InnerException != null)
            {
                Console.WriteLine($"Inner Exception: {ex.InnerException.Message}");
            }
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
}
