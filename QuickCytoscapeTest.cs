using System;
using System.Collections.Generic;

namespace SnapAnalyser
{
    class QuickCytoscapeTest
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("Quick Cytoscape.js Validation Test");
            Console.WriteLine("===================================");
            
            try
            {
                // Test basic instantiation
                var generator = new CytoscapeJsGenerator();
                Console.WriteLine("✅ CytoscapeJsGenerator created successfully");
                
                var diagramGen = new DiagramGenerator();
                Console.WriteLine("✅ DiagramGenerator created successfully");
                
                var flowControlGen = new FlowControlDiagramGenerator();
                Console.WriteLine("✅ FlowControlDiagramGenerator created successfully");
                
                // Test a simple pipeline creation
                var pipeline = new PipelineData
                {
                    name = "Test Pipeline",
                    nodes = new List<SnapNode>
                    {
                        new SnapNode { id = "node1", label = "Start", snap_class = "com.snaplogic.snap.api.InputSetting" },
                        new SnapNode { id = "node2", label = "Transform", snap_class = "com.snaplogic.snap.api.Transformer" },
                        new SnapNode { id = "node3", label = "Output", snap_class = "com.snaplogic.snap.api.OutputSetting" }
                    },
                    links = new List<SnapLink>
                    {
                        new SnapLink { from = "node1", to = "node2" },
                        new SnapLink { from = "node2", to = "node3" }
                    }
                };
                
                // Test elements generation
                string elements = generator.GenerateCytoscapeElements(pipeline);
                Console.WriteLine($"✅ Generated cytoscape elements: {elements.Length} characters");
                
                // Test HTML generation
                string html = generator.GenerateCytoscapeHtml(elements, "Test Pipeline");
                Console.WriteLine($"✅ Generated cytoscape HTML: {html.Length} characters");
                
                Console.WriteLine();
                Console.WriteLine("🎉 ALL BASIC VALIDATIONS PASSED!");
                Console.WriteLine("The cytoscape.js implementation is ready for use.");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ VALIDATION FAILED: {ex.Message}");
                Console.WriteLine($"Details: {ex.StackTrace}");
            }
            
            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
