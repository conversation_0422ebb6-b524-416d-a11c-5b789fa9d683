# PowerShell script to implement the proper Dynamics365ForSales account display fix
# This will:
# 1. Extend account display to include Dynamics365ForSales snaps 
# 2. Disable pseudocode for Database and Dynamics365ForSales snaps
# 3. Enhance GetDatabaseConnectionInfo to handle both database and Dynamics365 snaps

$filePath = "DocumentationGenerator.cs"

Write-Host "Implementing Dynamics365ForSales account display fix..." -ForegroundColor Green

$content = Get-Content $filePath -Raw

# Fix 1: Extend account display logic from database-only to include Dynamics365ForSales
$oldAccountCondition = 'if \(snap\.Category == SnapCategory\.Database\)'
$newAccountCondition = @'
// Show account info for Database snaps AND Dynamics365ForSales snaps
                bool shouldShowAccountInfo = snap.Category == SnapCategory.Database || 
                                           (snap.Type?.ToLower().Contains("dynamics365forsales") == true);
                if (shouldShowAccountInfo)
'@

if ($content -match $oldAccountCondition) {
    $content = $content -replace $oldAccountCondition, $newAccountCondition
    Write-Host "✅ Extended account display to include Dynamics365ForSales snaps" -ForegroundColor Yellow
} else {
    Write-Host "❌ Database account condition not found" -ForegroundColor Red
}

# Fix 2: Update the method call and else condition to reflect the new logic
$oldElseCondition = 'LogMessage\(\$"\[DB-CONNECTION\] Snap \{snap\.Label\} is not categorized as Database \(Category: \{snap\.Category\}\)"\);'
$newElseCondition = 'LogMessage($"[CONNECTION] Snap {snap.Label} does not qualify for account display (Category: {snap.Category}, Type: {snap.Type})");'

if ($content -match $oldElseCondition) {
    $content = $content -replace $oldElseCondition, $newElseCondition
    Write-Host "✅ Updated else condition message" -ForegroundColor Yellow
}

# Fix 3: Rename and enhance the GetDatabaseConnectionInfo method
$oldMethodSignature = 'private string GetDatabaseConnectionInfo\(SnapNode snap\)'
$newMethodSignature = 'private string GetConnectionInfo(SnapNode snap)'

if ($content -match $oldMethodSignature) {
    $content = $content -replace $oldMethodSignature, $newMethodSignature
    Write-Host "✅ Renamed GetDatabaseConnectionInfo to GetConnectionInfo" -ForegroundColor Yellow
}

# Fix 4: Update method call in main logic
$oldMethodCall = 'string connectionInfo = GetDatabaseConnectionInfo\(snap\);'
$newMethodCall = 'string connectionInfo = GetConnectionInfo(snap);'

if ($content -match $oldMethodCall) {
    $content = $content -replace $oldMethodCall, $newMethodCall
    Write-Host "✅ Updated method call to use GetConnectionInfo" -ForegroundColor Yellow
}

# Fix 5: Remove database-only restriction in GetConnectionInfo method
$oldMethodRestriction = 'if \(snap\.Category != SnapCategory\.Database\)\s*return null;'
$newMethodLogic = @'
// Show connection info for Database snaps and Dynamics365ForSales snaps
            bool isEligibleForConnectionInfo = snap.Category == SnapCategory.Database || 
                                               (snap.Type?.ToLower().Contains("dynamics365forsales") == true);
            
            if (!isEligibleForConnectionInfo)
            {
                LogMessage($"[CONNECTION] Snap {snap.Label} not eligible for connection info (Category: {snap.Category}, Type: {snap.Type})");
                return null;
            }
'@

if ($content -match $oldMethodRestriction) {
    $content = $content -replace $oldMethodRestriction, $newMethodLogic
    Write-Host "✅ Removed database-only restriction from GetConnectionInfo method" -ForegroundColor Yellow
}

# Fix 6: Add special handling for Dynamics365ForSales snaps in GetConnectionInfo
$insertionPoint = 'LogMessage\(\$"\[DB-CONNECTION\] Found account property, parsing JSON: \{accountJson\}"\);'
$dynamics365Logic = @'
LogMessage($"[CONNECTION] Found account property, parsing JSON: {accountJson}");
            
            // Special handling for Dynamics365ForSales snaps
            if (snap.Type?.ToLower().Contains("dynamics365forsales") == true)
            {
                LogMessage($"[CONNECTION] Applying Dynamics365ForSales-specific account parsing for: {snap.Label}");
                
                // Look for Dynamics365-specific account properties first
                var dynamics365AccountProps = snap.Properties.Where(p =>
                    (p.Key.ToLower().Contains("organization") ||
                    p.Key.ToLower().Contains("instance") ||
                    p.Key.ToLower().Contains("tenant") ||
                    p.Key.ToLower().Contains("environment") ||
                    p.Key.ToLower().Contains("service") ||
                    p.Key.ToLower().Contains("url") ||
                    p.Key.ToLower().Contains("endpoint") ||
                    p.Key.ToLower().Contains("crm") ||
                    p.Key.ToLower().Contains("dynamics")) &&
                    !string.IsNullOrEmpty(p.Value) &&
                    !p.Value.Equals("true", StringComparison.OrdinalIgnoreCase) &&
                    !p.Value.Equals("false", StringComparison.OrdinalIgnoreCase))
                    .FirstOrDefault();

                if (!string.IsNullOrEmpty(dynamics365AccountProps.Value))
                {
                    LogMessage($"[CONNECTION] Found Dynamics365 account property: {dynamics365AccountProps.Key} = {dynamics365AccountProps.Value}");
                    return dynamics365AccountProps.Value;
                }
            }
'@

if ($content.Contains($insertionPoint)) {
    $content = $content.Replace($insertionPoint, $dynamics365Logic)
    Write-Host "✅ Added Dynamics365ForSales-specific account parsing logic" -ForegroundColor Yellow
}

# Fix 7: Update all logging prefixes to use [CONNECTION] instead of [DB-CONNECTION]
$content = $content -replace '\[DB-CONNECTION\]', '[CONNECTION]'
Write-Host "✅ Updated logging prefixes to use [CONNECTION]" -ForegroundColor Yellow

# Fix 8: Restore pseudocode disabling for Database and Dynamics365ForSales snaps
$oldPseudocodeCondition = 'if \(!isMapperType && !isRouterType && !isExitOrUnionType && !isCopyType\)'
$newPseudocodeCondition = 'if (!isMapperType && !isRouterType && !isExitOrUnionType && !isCopyType && snap.Category != SnapCategory.Database && !snap.Type.ToLower().Contains("dynamics365forsales"))'

if ($content -match $oldPseudocodeCondition) {
    $content = $content -replace $oldPseudocodeCondition, $newPseudocodeCondition
    Write-Host "✅ Restored pseudocode disabling for Database and Dynamics365ForSales snaps" -ForegroundColor Yellow
}

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "✅ Dynamics365ForSales account display fix implemented!" -ForegroundColor Green
Write-Host ""
Write-Host "Summary of changes applied:" -ForegroundColor Cyan
Write-Host "  ✓ Extended account display to include Dynamics365ForSales snaps" -ForegroundColor White
Write-Host "  ✓ Renamed GetDatabaseConnectionInfo to GetConnectionInfo" -ForegroundColor White
Write-Host "  ✓ Removed database-only restriction from connection info method" -ForegroundColor White
Write-Host "  ✓ Added Dynamics365-specific account property detection" -ForegroundColor White
Write-Host "  ✓ Updated logging to use [CONNECTION] prefix consistently" -ForegroundColor White
Write-Host "  ✓ Disabled pseudocode generation for Database and Dynamics365ForSales snaps" -ForegroundColor White
Write-Host ""
Write-Host "The fix now supports:" -ForegroundColor Green
Write-Host "  • Database snaps (existing functionality preserved)" -ForegroundColor White
Write-Host "  • Dynamics365ForSales snaps (new functionality added)" -ForegroundColor White
Write-Host "  • Proper account name extraction for both snap types" -ForegroundColor White
Write-Host "  • No pseudocode generation for these snap types" -ForegroundColor White
