using System;
using System.Collections.Generic;
using System.Text;
using SnapAnalyser;

class TestConditionExtraction
{
    static void Main()
    {
        Console.WriteLine("Testing Condition Expression Extraction");
        
        // Create test snap nodes simulating various condition snaps with different property formats
        var testSnaps = new List<SnapNode>
        {
            new SnapNode
            {
                Id = "condition-snap-1",
                Label = "Simple Condition Snap",
                Type = "com-snaplogic-snaps-transform-conditional",
                Properties = new Dictionary<string, string>
                {
                    { "expression_0", "$value > 10" },
                    { "output_0", "True Path" },
                    { "output_1", "False Path" }
                }
            },
            new SnapNode
            {
                Id = "condition-snap-2",
                Label = "JSON Filter Condition Snap",
                Type = "com-snaplogic-snaps-flow-filter",
                Properties = new Dictionary<string, string>
                {
                    { "filterConfig", "{\"expression\": \"$data.field != null && $data.value > 100\", \"type\": \"complex\"}" },
                    { "output_true", "Has Data" },
                    { "output_false", "No Data" }
                }
            },
            new SnapNode
            {
                Id = "condition-snap-3",
                Label = "Multiple Expressions Condition",
                Type = "com-snaplogic-snaps-transform-router",
                Properties = new Dictionary<string, string>
                {
                    { "expression_0", "$type == 'order'" },
                    { "expression_1", "$type == 'invoice'" },
                    { "expression_2", "$type == 'quote'" },
                    { "output_0", "Orders" },
                    { "output_1", "Invoices" },
                    { "output_2", "Quotes" }
                }
            }
        };
        
        // Create an instance of SnapBestPractices and test the condition handling
        var bestPractices = new SnapBestPractices();
        
        foreach (var snap in testSnaps)
        {
            Console.WriteLine($"\n===== Testing Snap: {snap.Label} =====");
            var details = bestPractices.GenerateSnapDetails(snap);
            Console.WriteLine(details);
            Console.WriteLine("========================================");
        }
    }
}
