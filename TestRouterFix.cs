using System;
using System.Collections.Generic;
using System.IO;

namespace SnapAnalyser
{
    public class TestRouterFix
    {
        public static void Main(string[] args)
        {
            // Simulate the DocumentationGenerator logic for Router snap
            var routerSnap = new SnapNode
            {
                Id = "test-router",
                Label = "Route Unit Count",
                Type = "com-snaplogic-snaps-flow-router",
                Category = SnapCategory.FlowControl,
                Properties = new Dictionary<string, string>()
            };

            Console.WriteLine("Testing DocumentationGenerator logic for Router snap:");
            Console.WriteLine($"Snap Label: {routerSnap.Label}");
            Console.WriteLine($"Snap Type: {routerSnap.Type}");
            Console.WriteLine($"Snap Category: {routerSnap.Category}");
            Console.WriteLine();

            // Test OLD logic (what was causing the problem)
            string snapTypeLowerOld = routerSnap.Type.ToLower();
            bool isMapperTypeOld = snapTypeLowerOld.Contains("map") || snapTypeLowerOld.Contains("datatransform") || snapTypeLowerOld.Contains("transform");
            bool isExitOrUnionTypeOld = snapTypeLowerOld.Contains("exit") || snapTypeLowerOld.Contains("union");
            bool shouldCallAIPseudocodeOld = !isMapperTypeOld && !isExitOrUnionTypeOld;

            Console.WriteLine("OLD LOGIC:");
            Console.WriteLine($"snapTypeLower: '{snapTypeLowerOld}'");
            Console.WriteLine($"isMapperType: {isMapperTypeOld}");
            Console.WriteLine($"isExitOrUnionType: {isExitOrUnionTypeOld}");
            Console.WriteLine($"shouldCallAIPseudocode: {shouldCallAIPseudocodeOld}");
            Console.WriteLine();

            // Test NEW logic (our fix)
            string snapTypeLowerNew = routerSnap.Type.ToLower();
            bool isMapperTypeNew = snapTypeLowerNew.Contains("map") || snapTypeLowerNew.Contains("datatransform") || snapTypeLowerNew.Contains("transform");
            bool isExitOrUnionTypeNew = snapTypeLowerNew.Contains("exit") || snapTypeLowerNew.Contains("union");
            bool isRouterTypeNew = routerSnap.Category == SnapCategory.FlowControl && snapTypeLowerNew.Contains("router");
            bool shouldCallAIPseudocodeNew = !isMapperTypeNew && !isRouterTypeNew && !isExitOrUnionTypeNew;

            Console.WriteLine("NEW LOGIC (FIXED):");
            Console.WriteLine($"snapTypeLower: '{snapTypeLowerNew}'");
            Console.WriteLine($"isMapperType: {isMapperTypeNew}");
            Console.WriteLine($"isRouterType: {isRouterTypeNew}");
            Console.WriteLine($"isExitOrUnionType: {isExitOrUnionTypeNew}");
            Console.WriteLine($"shouldCallAIPseudocode: {shouldCallAIPseudocodeNew}");
            Console.WriteLine();

            Console.WriteLine("EXPECTED BEHAVIOR:");
            Console.WriteLine("- Router snaps should NOT call AI pseudocode generation");
            Console.WriteLine("- Router snaps should use explicit pseudocode generation");
            Console.WriteLine();

            if (shouldCallAIPseudocodeOld && !shouldCallAIPseudocodeNew)
            {
                Console.WriteLine("✅ FIX SUCCESSFUL: Router snap will now use explicit pseudocode instead of AI pseudocode");
            }
            else if (!shouldCallAIPseudocodeOld && !shouldCallAIPseudocodeNew)
            {
                Console.WriteLine("❓ WARNING: Both old and new logic skip AI pseudocode - verify this is correct");
            }
            else if (shouldCallAIPseudocodeOld && shouldCallAIPseudocodeNew)
            {
                Console.WriteLine("❌ FIX FAILED: Router snap still calls AI pseudocode in both cases");
            }
            else
            {
                Console.WriteLine("❓ UNEXPECTED: Old logic skipped AI but new logic doesn't - please verify");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
