<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0-windows7.0</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <EnableWindowsTargeting>true</EnableWindowsTargeting>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
    <PackageReference Include="Azure.AI.OpenAI" Version="2.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="TestRouterFix_CH03.cs" />
    <Compile Include="SlpAnalyzer.cs" />
    <Compile Include="AIDescriptionGenerator.cs" />
    <Compile Include="DocumentationGenerator.cs" />
    <Compile Include="ConfigManager.cs" />
    <Compile Include="DescriptionCache.cs" />
    <Compile Include="DiagramGenerator.cs" />
    <Compile Include="SnapLogicClient.cs" />
    <Compile Include="PathAnalysis.cs" />
    <Compile Include="PipelinePatternAnalyzer.cs" />
    <Compile Include="SnapBestPractices.cs" />
    <Compile Include="AddCategoryDescription.cs" />
  </ItemGroup>

</Project>
