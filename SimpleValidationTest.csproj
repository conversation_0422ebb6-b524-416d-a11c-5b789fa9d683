<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0-windows7.0</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="AIDescriptionGenerator.cs" />
    <Compile Include="SnapBestPractices.cs" />
    <Compile Include="SlpAnalyzer.cs" />
    <Compile Include="DescriptionCache.cs" />
    <Compile Include="ConfigManager.cs" />
    <Compile Include="SimpleValidationTest.cs" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Drawing" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

</Project>
