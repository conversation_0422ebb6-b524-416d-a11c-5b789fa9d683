# PowerShell script to enhance account extraction by looking in the raw pipeline data

$filePath = "DocumentationGenerator.cs"

Write-Host "Enhancing account extraction from raw pipeline data..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Find the GetDatabaseConnectionInfo method and enhance it to also check raw pipeline data
$enhancedAccountExtraction = @"
            // Enhanced account extraction - also check raw pipeline data
            if (_currentPipeline != null && _currentPipeline["snap_map"] != null)
            {
                try
                {
                    var snapMap = _currentPipeline["snap_map"] as JObject;
                    if (snapMap != null)
                    {
                        // Find this snap in the raw pipeline data
                        var rawSnap = snapMap.Properties()
                            .Select(p => p.Value as JObject)
                            .FirstOrDefault(s => s?["instance_id"]?.ToString() == snap.Label ||
                                                s?["view_serial"]?.ToString() == snap.Id);
                        
                        if (rawSnap != null)
                        {
                            var propertyMap = rawSnap["property_map"] as JObject;
                            if (propertyMap != null && propertyMap["account"] != null)
                            {
                                try
                                {
                                    var accountJson = propertyMap["account"].ToString();
                                    Console.WriteLine(`$"[DB-CONNECTION] Found raw account data, parsing JSON");
                                    
                                    var accountObj = JObject.Parse(accountJson);
                                    var accountRef = accountObj["account_ref"];
                                    if (accountRef != null)
                                    {
                                        var valueObj = accountRef["value"];
                                        if (valueObj != null)
                                        {
                                            var labelObj = valueObj["label"];
                                            if (labelObj != null)
                                            {
                                                var accountName = labelObj["value"]?.ToString();
                                                if (!string.IsNullOrEmpty(accountName))
                                                {
                                                    Console.WriteLine(`$"[DB-CONNECTION] Found account name from raw pipeline: {accountName}");
                                                    return accountName;
                                                }
                                            }
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine(`$"[DB-CONNECTION] Error parsing raw account JSON: {ex.Message}");
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine(`$"[DB-CONNECTION] Error accessing raw pipeline data: {ex.Message}");
                }
            }

"@

# Insert the enhanced extraction before the existing property search
$insertPoint = '(\s+// Look for account/connection properties, but filter out boolean values)'
$replacement = $enhancedAccountExtraction + "`r`n`$1"
$content = $content -replace $insertPoint, $replacement

# Write the updated file
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Account extraction enhanced successfully!" -ForegroundColor Green
Write-Host "The method will now look for account info in the raw pipeline data." -ForegroundColor Yellow
