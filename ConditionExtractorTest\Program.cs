using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json;
using System.Linq;
using System.Net;

// Standalone test for condition expression extraction improvements
class Program
{
    class ConditionInfo
    {
        public string Expression { get; set; } = "";
        public string Description { get; set; } = "";
    }
    
    class SnapNode
    {
        public string Id { get; set; } = "";
        public string Label { get; set; } = "";
        public string Type { get; set; } = "";
        public Dictionary<string, string> Properties { get; set; } = new();
    }
    
    static void Main()
    {
        Console.WriteLine("======= CONDITION SNAP EXTRACTION TEST =======");
        
        // Test 1: Simple expression conditions
        var simpleSnap = new SnapNode
        {
            Id = "simple-condition",
            Label = "Simple Condition",
            Type = "com-snaplogic-snaps-transform-conditional",
            Properties = new Dictionary<string, string>
            {
                { "expression_0", "$value > 10" },
                { "expression_1", "$type == 'order'" },
                { "output_0", "True Path" },
                { "output_1", "Orders" }
            }
        };
        
        Console.WriteLine("\n=== TEST 1: Simple Expression Conditions ===");
        var simpleConditions = ExtractConditionExpressions(simpleSnap);
        PrintConditions(simpleConditions);
        Console.WriteLine("HTML Output:");
        Console.WriteLine(GenerateConditionHtml(simpleSnap));
        
        // Test 2: JSON format conditions
        var jsonSnap = new SnapNode
        {
            Id = "json-condition",
            Label = "JSON Condition",
            Type = "com-snaplogic-snaps-flow-filter",
            Properties = new Dictionary<string, string>
            {
                { "filterConfig", "{\"expression\": \"$data.field != null && $data.value > 100\", \"type\": \"complex\"}" },
                { "output_true", "Has Data" },
                { "output_false", "No Data" }
            }
        };
        
        Console.WriteLine("\n=== TEST 2: JSON Format Conditions ===");
        var jsonConditions = ExtractConditionExpressions(jsonSnap);
        PrintConditions(jsonConditions);
        Console.WriteLine("HTML Output:");
        Console.WriteLine(GenerateConditionHtml(jsonSnap));
        
        // Test 3: Mixed format conditions
        var mixedSnap = new SnapNode
        {
            Id = "mixed-condition",
            Label = "Mixed Condition",
            Type = "com-snaplogic-snaps-transform-router",
            Properties = new Dictionary<string, string>
            {
                { "expression_0", "$type == 'invoice'" },
                { "condition", "$amount > 1000" },
                { "filterConfig", "{\"expression\": \"$customer != null\", \"type\": \"simple\"}" },
                { "output_0", "Invoices" },
                { "output_1", "Large Orders" },
                { "output_2", "Valid Customers" }
            }
        };
        
        Console.WriteLine("\n=== TEST 3: Mixed Format Conditions ===");
        var mixedConditions = ExtractConditionExpressions(mixedSnap);
        PrintConditions(mixedConditions);
        Console.WriteLine("HTML Output:");
        Console.WriteLine(GenerateConditionHtml(mixedSnap));
        
        Console.WriteLine("\n=== TEST COMPLETED ===");
    }
    
    static void PrintConditions(List<ConditionInfo> conditions)
    {
        Console.WriteLine($"Found {conditions.Count} condition expressions:");
        foreach (var cond in conditions)
        {
            Console.WriteLine($"- Expression: {cond.Expression}");
            Console.WriteLine($"  Description: {cond.Description}");
        }
    }
    
    // Extract condition expressions from snap properties
    static List<ConditionInfo> ExtractConditionExpressions(SnapNode snap)
    {
        var conditions = new List<ConditionInfo>();
        
        // Look for properties with expression in the key
        foreach (var prop in snap.Properties)
        {
            if (prop.Key.Contains("expression_") && !string.IsNullOrEmpty(prop.Value))
            {
                var condition = new ConditionInfo
                {
                    Expression = prop.Value,
                    Description = AnalyzeConditionPattern(prop.Value)
                };
                conditions.Add(condition);
            }
        }
        
        // Check for direct condition properties
        foreach (var prop in snap.Properties)
        {
            if ((prop.Key.Contains("condition") || prop.Key.Contains("evaluate")) && 
                !string.IsNullOrEmpty(prop.Value) && 
                !conditions.Any(c => c.Expression == prop.Value))
            {
                var condition = new ConditionInfo
                {
                    Expression = prop.Value,
                    Description = AnalyzeConditionPattern(prop.Value)
                };
                conditions.Add(condition);
            }
        }
        
        // Check for JSON format conditions
        foreach (var prop in snap.Properties)
        {
            if (prop.Key.Contains("filter") || 
                prop.Key.Contains("config") || 
                prop.Key.Contains("json"))
            {
                try
                {
                    // Try to parse as JSON
                    if (prop.Value.StartsWith("{"))
                    {
                        var jsonDoc = JsonDocument.Parse(prop.Value);
                        if (jsonDoc.RootElement.TryGetProperty("expression", out var expr))
                        {
                            var expressionValue = expr.GetString();
                            if (!string.IsNullOrEmpty(expressionValue) && 
                                !conditions.Any(c => c.Expression == expressionValue))
                            {
                                var condition = new ConditionInfo
                                {
                                    Expression = expressionValue,
                                    Description = AnalyzeConditionPattern(expressionValue)
                                };
                                conditions.Add(condition);
                            }
                        }
                    }
                }
                catch (JsonException)
                {
                    // Not valid JSON, ignore
                    Console.WriteLine($"Note: Could not parse JSON in property {prop.Key}");
                }
            }
        }
        
        return conditions;
    }
    
    // Analyze condition pattern to generate description
    static string AnalyzeConditionPattern(string expression)
    {
        // Check for common patterns
        if (expression.Contains("==") || expression.Contains("!=") || 
            expression.Contains(">") || expression.Contains("<"))
        {
            return "Comparison: Tests if values are equal, not equal, greater than, or less than.";
        }
        else if (expression.Contains("null"))
        {
            return "Null check: Tests if a value exists or is null.";
        }
        else if (expression.Contains("&&") || expression.Contains("||"))
        {
            return "Logical operation: Combines multiple conditions with AND/OR logic.";
        }
        else if (expression.Contains("$."))
        {
            return "JSONPath: Extracts values from a JSON structure.";
        }
        else
        {
            return "Custom expression";
        }
    }
    
    // Generate HTML for condition details
    static string GenerateConditionHtml(SnapNode snap)
    {
        var details = new StringBuilder();
        
        details.AppendLine("<p><strong>Condition Configuration:</strong></p>");
        
        var conditions = ExtractConditionExpressions(snap);
        
        if (conditions.Any())
        {
            details.AppendLine("<p>This condition snap evaluates the following expression(s):</p>");
            details.AppendLine("<table style='width: 100%; border-collapse: collapse;'>");
            details.AppendLine("<tr><th style='text-align:left; border: 1px solid #ddd; padding: 8px;'>Expression</th><th style='text-align:left; border: 1px solid #ddd; padding: 8px;'>Description</th></tr>");
            
            foreach (var condition in conditions)
            {
                details.AppendLine($"<tr><td style='border: 1px solid #ddd; padding: 8px;'><code>{WebUtility.HtmlEncode(condition.Expression)}</code></td><td style='border: 1px solid #ddd; padding: 8px;'>{WebUtility.HtmlEncode(condition.Description)}</td></tr>");
            }
            
            details.AppendLine("</table>");
        }
        else
        {
            details.AppendLine("<p>No specific condition expressions were found.</p>");
        }
        
        // Extract output routing properties
        var outputProps = snap.Properties.Where(p => 
            p.Key.Contains("output") || 
            p.Key.Contains("true") || 
            p.Key.Contains("false") || 
            p.Key.Contains("path") || 
            p.Key.Contains("route")).ToList();
            
        if (outputProps.Any())
        {
            details.AppendLine("<p><strong>Output Routing:</strong></p>");
            details.AppendLine("<ul>");
            foreach (var output in outputProps)
            {
                details.AppendLine($"<li><strong>{WebUtility.HtmlEncode(output.Key)}:</strong> <code>{WebUtility.HtmlEncode(output.Value)}</code></li>");
            }
            details.AppendLine("</ul>");
        }
        
        return details.ToString();
    }
}
