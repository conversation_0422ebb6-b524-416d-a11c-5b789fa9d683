Checking for config file at: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
Read config file, content length: 857 characters
Config successfully loaded with values:
  OpenAIApiKey: Set (hidden)
  LastSlpFolderLocation: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Snap-Pipelines\PTR\Legionella\P03 - Legionella Unknown Attribute CRM_2025_07_03.slp
  LastOutputFolderLocation: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Generated Documentation
  SnapLogicUrl: https://cdn.elastic.snaplogic.com
  SnapLogicUsername: <EMAIL>
  SnapLogicPassword: Not set
  SnapLogicOrgId: 5fd9a86c47060ece477be9b6
Successfully loaded config from: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
Attempting to save config to the following locations:
  C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
  C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
  C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\config.json
  C:\Users\<USER>\AppData\Roaming\SnapAnalyser\config.json
Successfully saved config to: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
Checking for config file at: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
Read config file, content length: 857 characters
Config successfully loaded with values:
  OpenAIApiKey: Set (hidden)
  LastSlpFolderLocation: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Snap-Pipelines\PTR\Legionella\P03 - Legionella Unknown Attribute CRM_2025_07_03.slp
  LastOutputFolderLocation: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Generated Documentation
  SnapLogicUrl: https://cdn.elastic.snaplogic.com
  SnapLogicUsername: <EMAIL>
  SnapLogicPassword: Not set
  SnapLogicOrgId: 5fd9a86c47060ece477be9b6
Successfully loaded config from: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
Initializing logging...
Base directory: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\
Solution directory: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin
Using documentation directory: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Generated Documentation
Documentation directory already exists
Attempting to write to: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Generated Documentation\SnapDocumenter_MapperDebug.log
Successfully initialized logging to: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Generated Documentation\SnapDocumenter_MapperDebug.log
Log location written to: C:\Users\<USER>\OneDrive - Newport City Homes\Desktop\SnapDocumenter_LogLocation.txt
Checking for config file at: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
Read config file, content length: 857 characters
Config successfully loaded with values:
  OpenAIApiKey: Set (hidden)
  LastSlpFolderLocation: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Snap-Pipelines\PTR\Legionella\P03 - Legionella Unknown Attribute CRM_2025_07_03.slp
  LastOutputFolderLocation: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Generated Documentation
  SnapLogicUrl: https://cdn.elastic.snaplogic.com
  SnapLogicUsername: <EMAIL>
  SnapLogicPassword: Not set
  SnapLogicOrgId: 5fd9a86c47060ece477be9b6
Successfully loaded config from: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
Attempting to save config to the following locations:
  C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
  C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
  C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\config.json
  C:\Users\<USER>\AppData\Roaming\SnapAnalyser\config.json
Successfully saved config to: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\config.json
Extracting transformation properties from prefix: settings.transformations
Processing property: settings.transformations.mappingRoot
  Simple value: settings.transformations.mappingRoot.value = $
Processing property: settings.transformations.mappingTable
  Complex value at settings.transformations.mappingTable.value (length: 556)
  Found mappingTable, doing deeper extraction
Processing mapper expressions from settings.transformations.mappingTable, token type: Object
Processing mapping table entry 0
Found mapping: $nch_propertytechnicalrecordsid = $nch_propertytechnicalrecordsid
Processing mapping table entry 1
Found mapping: $nch_legionellainspectionrequired = 803030000
Processing mapping table entry 2
Found mapping: $nch_legionellainspectiondate = '01/01/1753 00:00:00'
  Found mapping pair: {
  "value": "$nch_propertytechnicalrecordsid"
} = {
  "value": "$nch_propertytechnicalrecordsid",
  "expression": true
}
  Found mapping pair: {
  "value": "$nch_legionellainspectionrequired"
} = {
  "value": "803030000",
  "expression": true
}
  Found mapping pair: {
  "value": "$nch_legionellainspectiondate"
} = {
  "value": "'01/01/1753 00:00:00'",
  "expression": true
}
Extracting transformation properties from prefix: settings.transformations
Processing property: settings.transformations.mappingRoot
  Simple value: settings.transformations.mappingRoot.value = $
Processing property: settings.transformations.mappingTable
  Complex value at settings.transformations.mappingTable.value (length: 404)
  Found mappingTable, doing deeper extraction
Processing mapper expressions from settings.transformations.mappingTable, token type: Object
Processing mapping table entry 0
Found mapping: $nch_uprn = $nch_unitcode>=11000000 ? $nch_unitcode : $nch_unitcode-********
Processing mapping table entry 1
Found mapping: $nch_propertytechnicalrecordsid = $nch_propertytechnicalrecordsid
  Found mapping pair: {
  "value": "$nch_uprn"
} = {
  "expression": true,
  "value": "$nch_unitcode>=11000000 ? $nch_unitcode : $nch_unitcode-********"
}
  Found mapping pair: {
  "value": "$nch_propertytechnicalrecordsid"
} = {
  "value": "$nch_propertytechnicalrecordsid",
  "expression": true
}
[DEBUG] === SNAP CATEGORIZATION ===
[DEBUG] Processing 12 snaps for categorization
[DEBUG] Processing snap: 'Join on UPRN' (Type: 'com-snaplogic-snaps-transform-multijoin')
[DEBUG] CATEGORIZED as FlowControl: 'Join on UPRN' (Type: com-snaplogic-snaps-transform-multijoin)
[DEBUG] Processing snap: 'Get LEG Unknown Properties' (Type: 'com-snaplogic-snaps-sqlserver-execute')
[DEBUG] CATEGORIZED as Database: 'Get LEG Unknown Properties' (Type: com-snaplogic-snaps-sqlserver-execute)
[DEBUG] Processing snap: 'Map legionella inspection required' (Type: 'com-snaplogic-snaps-transform-datatransform')
[DEBUG] CATEGORIZED as Transformation: 'Map legionella inspection required' (Type: com-snaplogic-snaps-transform-datatransform)
[DEBUG] Processing snap: 'Update CRM PTR' (Type: 'com-snaplogic-snaps-dynamics365forsales-update')
[DEBUG] CATEGORIZED as Database: 'Update CRM PTR' (Type: com-snaplogic-snaps-dynamics365forsales-update)
[DEBUG] Processing snap: 'Get CRM PTRs' (Type: 'com-snaplogic-snaps-dynamics365forsales-search')
[DEBUG] CATEGORIZED as ExternalSystem: 'Get CRM PTRs' (Type: com-snaplogic-snaps-dynamics365forsales-search)
[DEBUG] Processing snap: 'Get CRM Properties' (Type: 'com-snaplogic-snaps-dynamics365forsales-read')
[DEBUG] CATEGORIZED as ExternalSystem: 'Get CRM Properties' (Type: com-snaplogic-snaps-dynamics365forsales-read)
[DEBUG] Processing snap: 'Join propertyid' (Type: 'com-snaplogic-snaps-transform-multijoin')
[DEBUG] CATEGORIZED as FlowControl: 'Join propertyid' (Type: com-snaplogic-snaps-transform-multijoin)
[DEBUG] Processing snap: 'Map propertytechnicalrecordsid' (Type: 'com-snaplogic-snaps-transform-datatransform')
[DEBUG] CATEGORIZED as Transformation: 'Map propertytechnicalrecordsid' (Type: com-snaplogic-snaps-transform-datatransform)
[DEBUG] Processing snap: 'Exit' (Type: 'com-snaplogic-snaps-flow-exit')
[DEBUG] CATEGORIZED as FlowControl: 'Exit' (Type: com-snaplogic-snaps-flow-exit)
[DEBUG] Processing snap: 'Save to NCHVINT01' (Type: 'com-snaplogic-snaps-binary-write')
[DEBUG] CATEGORIZED as FileOperation: 'Save to NCHVINT01' (Type: com-snaplogic-snaps-binary-write)
[DEBUG] Processing snap: 'Document to Binary' (Type: 'com-snaplogic-snaps-transform-documenttobinary')
[DEBUG] CATEGORIZED as Transformation: 'Document to Binary' (Type: com-snaplogic-snaps-transform-documenttobinary)
[DEBUG] Processing snap: 'Copy' (Type: 'com-snaplogic-snaps-flow-copy')
[DEBUG] CATEGORIZED as FlowControl: 'Copy' (Type: com-snaplogic-snaps-flow-copy)
[DEBUG] === END SNAP CATEGORIZATION ===
----- Azure OpenAI Configuration -----
API Key provided: True
API Key length: 84
Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
Deployment Name: gpt-4.1
Timeout: 30 seconds
Azure OpenAI will be used: True
Using cached descriptions: True
AIDescriptionGenerator initialized with Azure OpenAI
Using API key starting with: CSunqsaW...
?? STARTING Azure OpenAI connection test...
?? FINAL Azure AI status after connection test: True
?? Testing Azure OpenAI connection...
?? Testing connection to Azure OpenAI...
?? API Key length: 84
?? Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
?? Deployment: gpt-4.1
? All configuration values are present
?? Sending test request to Azure OpenAI...
Logging initialized
Initializing logging...
Base directory: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Debug\net9.0-windows7.0\
Solution directory: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin
Using documentation directory: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Generated Documentation
Documentation directory already exists
Attempting to write to: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Generated Documentation\SnapDocumenter_MapperDebug.log
Successfully initialized logging to: C:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\bin\Generated Documentation\SnapDocumenter_MapperDebug.log
Log location written to: C:\Users\<USER>\OneDrive - Newport City Homes\Desktop\SnapDocumenter_LogLocation.txt
[AI-REQ-4e1a4183] Starting Azure OpenAI request
[AI-REQ-4e1a4183] Prompt length: 42 characters
[AI-REQ-4e1a4183] CancellationToken: IsCancelled=False, CanBeCancelled=False
[AI-REQ-4e1a4183] Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
[AI-REQ-4e1a4183] Deployment: gpt-4.1
[AI-REQ-4e1a4183] HttpClient timeout: 00:05:00
[AI-REQ-4e1a4183] Request URI: https://openai-general-assistan-resource.cognitiveservices.azure.com//openai/deployments/gpt-4.1/chat/completions?api-version=2024-02-01
[AI-REQ-4e1a4183] Creating HTTP request message
Resetting processed snap types for new pipeline documentation
[PIPELINE-DESC-55c1f44c] Starting pipeline description generation for 'P03 - Legionella Unknown Attribute CRM'
[PIPELINE-DESC-55c1f44c] Pipeline contains 12 snaps
[PIPELINE-DESC-55c1f44c] Using Azure AI: True
[PIPELINE-DESC-55c1f44c] Creating pipeline summary
[AI-REQ-4e1a4183] About to send HTTP request to Azure OpenAI
[PIPELINE-DESC-55c1f44c] Pipeline summary created, length: 853 characters
[PIPELINE-DESC-55c1f44c] Sending to Azure OpenAI, prompt length: 1148 characters
[AI-REQ-dae73732] Starting Azure OpenAI request
[AI-REQ-dae73732] Prompt length: 1148 characters
[AI-REQ-dae73732] CancellationToken: IsCancelled=False, CanBeCancelled=False
[AI-REQ-dae73732] Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
[AI-REQ-dae73732] Deployment: gpt-4.1
[AI-REQ-dae73732] HttpClient timeout: 00:05:00
[AI-REQ-dae73732] Request URI: https://openai-general-assistan-resource.cognitiveservices.azure.com//openai/deployments/gpt-4.1/chat/completions?api-version=2024-02-01
[AI-REQ-dae73732] Creating HTTP request message
[AI-REQ-dae73732] About to send HTTP request to Azure OpenAI
[AI-REQ-4e1a4183] HTTP request completed in 553ms, Status: OK
[AI-REQ-4e1a4183] About to read response content
[AI-REQ-4e1a4183] Response content read in 1ms, Length: 1208 characters
[AI-REQ-4e1a4183] Parsing JSON response
[AI-REQ-4e1a4183] SUCCESS: Request completed in 580ms, Result length: 2 characters
?? Test connection response: 'ok'
?? Connection test result: SUCCESS
? Successfully connected to Azure OpenAI API
[AI-REQ-dae73732] HTTP request completed in 1488ms, Status: OK
[AI-REQ-dae73732] About to read response content
[AI-REQ-dae73732] Response content read in 0ms, Length: 1867 characters
[AI-REQ-dae73732] Parsing JSON response
[AI-REQ-dae73732] SUCCESS: Request completed in 1495ms, Result length: 655 characters
[PIPELINE-DESC-55c1f44c] SUCCESS: Azure OpenAI completed in 1501ms, result length: 655 characters
[DEBUG-FILTER] Total snaps in pipeline: 12
[DEBUG-FILTER] Snap 'Join on UPRN' has Category: FlowControl (Type: com-snaplogic-snaps-transform-multijoin)
[DEBUG-FILTER] Snap 'Get LEG Unknown Properties' has Category: Database (Type: com-snaplogic-snaps-sqlserver-execute)
[DEBUG-FILTER] Snap 'Map legionella inspection required' has Category: Transformation (Type: com-snaplogic-snaps-transform-datatransform)
[DEBUG-FILTER] Snap 'Update CRM PTR' has Category: Database (Type: com-snaplogic-snaps-dynamics365forsales-update)
[DEBUG-FILTER] Snap 'Get CRM PTRs' has Category: ExternalSystem (Type: com-snaplogic-snaps-dynamics365forsales-search)
[DEBUG-FILTER] Snap 'Get CRM Properties' has Category: ExternalSystem (Type: com-snaplogic-snaps-dynamics365forsales-read)
[DEBUG-FILTER] Snap 'Join propertyid' has Category: FlowControl (Type: com-snaplogic-snaps-transform-multijoin)
[DEBUG-FILTER] Snap 'Map propertytechnicalrecordsid' has Category: Transformation (Type: com-snaplogic-snaps-transform-datatransform)
[DEBUG-FILTER] Snap 'Exit' has Category: FlowControl (Type: com-snaplogic-snaps-flow-exit)
[DEBUG-FILTER] Snap 'Save to NCHVINT01' has Category: FileOperation (Type: com-snaplogic-snaps-binary-write)
[DEBUG-FILTER] Snap 'Document to Binary' has Category: Transformation (Type: com-snaplogic-snaps-transform-documenttobinary)
[DEBUG-FILTER] Snap 'Copy' has Category: FlowControl (Type: com-snaplogic-snaps-flow-copy)
[DEBUG-FILTER-RESULT] Database snaps filtered: 2
[DEBUG-FILTER-RESULT] Database snap: 'Get LEG Unknown Properties' (Category: Database)
[DEBUG-FILTER-RESULT] Database snap: 'Update CRM PTR' (Category: Database)
[DEBUG] ===== ALL SNAPS IN CATEGORY 'Flow Control' =====
[DEBUG] Snap 1: 'Join on UPRN' (Type: 'com-snaplogic-snaps-transform-multijoin', ID: 22ada40c-10ce-4c23-b57a-09ba37c81262)
[DEBUG] Snap 2: 'Join propertyid' (Type: 'com-snaplogic-snaps-transform-multijoin', ID: ba9ceedf-4b5e-47f1-bfdf-33e331a37191)
[DEBUG] Snap 3: 'Exit' (Type: 'com-snaplogic-snaps-flow-exit', ID: 714d4edd-65bd-439f-9502-2174458261cb)
[DEBUG] *** POTENTIAL CONDITION SNAP FOUND: 'Exit' (Type: 'com-snaplogic-snaps-flow-exit') ***
[DEBUG] Snap 4: 'Copy' (Type: 'com-snaplogic-snaps-flow-copy', ID: 1e6c6db0-709e-45c9-aa8c-9fb6b38724a7)
[DEBUG] *** POTENTIAL CONDITION SNAP FOUND: 'Copy' (Type: 'com-snaplogic-snaps-flow-copy') ***
[DEBUG] ===== END CATEGORY 'Flow Control' =====
[DEBUG-SECTION-START] AddSnapCategorySection called for 'Flow Control' with 4 snaps
[DEBUG-SECTION-START] Snap in Flow Control: 'Join on UPRN' (Category: FlowControl)
[DEBUG-SECTION-START] Snap in Flow Control: 'Join propertyid' (Category: FlowControl)
[DEBUG-SECTION-START] Snap in Flow Control: 'Exit' (Category: FlowControl)
[DEBUG-SECTION-START] Snap in Flow Control: 'Copy' (Category: FlowControl)
[DEBUG] ===== ALL SNAPS IN CATEGORY 'Flow Control' =====
[DEBUG] Snap 1: 'Join on UPRN' (Type: 'com-snaplogic-snaps-transform-multijoin', ID: 22ada40c-10ce-4c23-b57a-09ba37c81262)
[DEBUG] Snap 2: 'Join propertyid' (Type: 'com-snaplogic-snaps-transform-multijoin', ID: ba9ceedf-4b5e-47f1-bfdf-33e331a37191)
[DEBUG] Snap 3: 'Exit' (Type: 'com-snaplogic-snaps-flow-exit', ID: 714d4edd-65bd-439f-9502-2174458261cb)
[DEBUG] *** POTENTIAL CONDITION SNAP FOUND: 'Exit' (Type: 'com-snaplogic-snaps-flow-exit') ***
[DEBUG] Snap 4: 'Copy' (Type: 'com-snaplogic-snaps-flow-copy', ID: 1e6c6db0-709e-45c9-aa8c-9fb6b38724a7)
[DEBUG] *** POTENTIAL CONDITION SNAP FOUND: 'Copy' (Type: 'com-snaplogic-snaps-flow-copy') ***
[DEBUG] ===== END CATEGORY 'Flow Control' =====
[DEBUG-BEFORE-LOOP] About to process 4 snaps in Flow Control
[DEBUG-IN-LOOP] Processing snap: 'Join on UPRN' in Flow Control
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Join on UPRN'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Join on UPRN'. Category: FlowControl, Type: com-snaplogic-snaps-transform-multijoin
[DEBUG-SKIP-CONNECTION] Skipping connection info for mapper/transform snap: 'Join on UPRN'
[ENHANCED-DESC-b4994891] Starting enhanced snap description for 'Join on UPRN' (com-snaplogic-snaps-transform-multijoin)
[ENHANCED-DESC-b4994891] Using cached descriptions: False
[ENHANCED-DESC-b4994891] Using Azure AI: True
[ENHANCED-DESC-b4994891] CACHE MISS: No cached enhanced description found
[ENHANCED-DESC-b4994891] Creating snap context for Azure OpenAI
[ENHANCED-DESC-b4994891] Context created, length: 506 characters
[ENHANCED-DESC-b4994891] Sending to Azure OpenAI, prompt length: 783 characters
[AI-REQ-c2be27c8] Starting Azure OpenAI request
[AI-REQ-c2be27c8] Prompt length: 783 characters
[AI-REQ-c2be27c8] CancellationToken: IsCancelled=False, CanBeCancelled=False
[AI-REQ-c2be27c8] Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
[AI-REQ-c2be27c8] Deployment: gpt-4.1
[AI-REQ-c2be27c8] HttpClient timeout: 00:05:00
[AI-REQ-c2be27c8] Request URI: https://openai-general-assistan-resource.cognitiveservices.azure.com//openai/deployments/gpt-4.1/chat/completions?api-version=2024-02-01
[AI-REQ-c2be27c8] Creating HTTP request message
[AI-REQ-c2be27c8] About to send HTTP request to Azure OpenAI
[AI-REQ-c2be27c8] HTTP request completed in 1352ms, Status: OK
[AI-REQ-c2be27c8] About to read response content
[AI-REQ-c2be27c8] Response content read in 0ms, Length: 1846 characters
[AI-REQ-c2be27c8] Parsing JSON response
[AI-REQ-c2be27c8] SUCCESS: Request completed in 1358ms, Result length: 629 characters
[ENHANCED-DESC-b4994891] Azure OpenAI response received, length: 629 characters
[ENHANCED-DESC-b4994891] Storing enhanced description in cache
Storing description for Join on UPRN (Type: com-snaplogic-snaps-transform-multijoin)
Description length: 629
Pseudocode length: 0
[ENHANCED-DESC-b4994891] SUCCESS: Enhanced description completed in 1385ms
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Join on UPRN' is flow control snap
[DEBUG-FLOW-CONTROL] IsFlowControlSnap called for: 'Join on UPRN'
[DEBUG-FLOW-CONTROL] Snap Type: 'com-snaplogic-snaps-transform-multijoin' -> Lower: 'com-snaplogic-snaps-transform-multijoin'
[DEBUG-FLOW-CONTROL] Snap Category: 'FlowControl'
[DEBUG-FLOW-CONTROL] Contains router: False
[DEBUG-FLOW-CONTROL] Contains join: True
[DEBUG-FLOW-CONTROL] Contains union: False
[DEBUG-FLOW-CONTROL] Contains switch: False
[DEBUG-FLOW-CONTROL] Contains branch: False
[DEBUG-FLOW-CONTROL] Contains conditional: False
[DEBUG-FLOW-CONTROL] Contains if: False
[DEBUG-FLOW-CONTROL] Contains case: False
[DEBUG-FLOW-CONTROL] Is FlowControl category: True
[DEBUG-FLOW-CONTROL] FINAL RESULT for 'Join on UPRN': True
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Join on UPRN' isFlowControlSnap: True
[DEBUG-JOIN-DETECTION] Found Join snap: 'Join on UPRN' (Type: com-snaplogic-snaps-transform-multijoin), isFlowControlSnap: True
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Join on UPRN'
[DEBUG-FLOW-CONTROL-ENTERED] *** ENTERED FLOW CONTROL CONFIGURATION SECTION FOR 'Join on UPRN' ***
[DEBUG-FLOW-CONTROL-TRY] *** ENTERED TRY BLOCK FOR FLOW CONTROL CONFIG: 'Join on UPRN' ***
[DEBUG-FLOW-CONTROL-GENERATOR] FlowControlConfigurationGenerator created successfully for: 'Join on UPRN'
[DEBUG-SETUP-CONNECTIONS] About to call SetupSnapConnections for: 'Join on UPRN'
[DEBUG-SETUP-CONNECTIONS] SetupSnapConnections completed for: 'Join on UPRN'
[DEBUG-FLOW-CONTROL] About to start snap type detection for: 'Join on UPRN'
[DEBUG-FLOW-CONTROL] flowControlConfig variable initialized for: 'Join on UPRN'
[DEBUG-FLOW-CONTROL] snapTypeLower = 'com-snaplogic-snaps-transform-multijoin' for: 'Join on UPRN'
[DEBUG-FLOW-CONTROL] About to call LogMessage for snap type detection: 'Join on UPRN'
[DEBUG-FLOW-CONTROL] Computed boolean values - router: False, join: True, union: False
[DEBUG-FLOW-CONTROL] About to check configuration type for: 'Join on UPRN'
[DEBUG-FLOW-CONTROL] Detected JOIN snap: 'Join on UPRN'
[FLOW-CONTROL-CONFIG] *** GENERATING JOIN CONFIGURATION *** for: Join on UPRN
[DEBUG-JOIN-CALL] About to call GenerateJoinConfiguration for: 'Join on UPRN'
[DEBUG-JOIN-RESULT] GenerateJoinConfiguration returned 1303 characters for: 'Join on UPRN'
[DEBUG-IN-LOOP] Processing snap: 'Join propertyid' in Flow Control
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Join propertyid'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Join propertyid'. Category: FlowControl, Type: com-snaplogic-snaps-transform-multijoin
[DEBUG-SKIP-CONNECTION] Skipping connection info for mapper/transform snap: 'Join propertyid'
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Join propertyid' is flow control snap
[DEBUG-FLOW-CONTROL] IsFlowControlSnap called for: 'Join propertyid'
[DEBUG-FLOW-CONTROL] Snap Type: 'com-snaplogic-snaps-transform-multijoin' -> Lower: 'com-snaplogic-snaps-transform-multijoin'
[DEBUG-FLOW-CONTROL] Snap Category: 'FlowControl'
[DEBUG-FLOW-CONTROL] Contains router: False
[DEBUG-FLOW-CONTROL] Contains join: True
[DEBUG-FLOW-CONTROL] Contains union: False
[DEBUG-FLOW-CONTROL] Contains switch: False
[DEBUG-FLOW-CONTROL] Contains branch: False
[DEBUG-FLOW-CONTROL] Contains conditional: False
[DEBUG-FLOW-CONTROL] Contains if: False
[DEBUG-FLOW-CONTROL] Contains case: False
[DEBUG-FLOW-CONTROL] Is FlowControl category: True
[DEBUG-FLOW-CONTROL] FINAL RESULT for 'Join propertyid': True
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Join propertyid' isFlowControlSnap: True
[DEBUG-JOIN-DETECTION] Found Join snap: 'Join propertyid' (Type: com-snaplogic-snaps-transform-multijoin), isFlowControlSnap: True
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Join propertyid'
[DEBUG-FLOW-CONTROL-ENTERED] *** ENTERED FLOW CONTROL CONFIGURATION SECTION FOR 'Join propertyid' ***
[DEBUG-FLOW-CONTROL-TRY] *** ENTERED TRY BLOCK FOR FLOW CONTROL CONFIG: 'Join propertyid' ***
[DEBUG-FLOW-CONTROL-GENERATOR] FlowControlConfigurationGenerator created successfully for: 'Join propertyid'
[DEBUG-SETUP-CONNECTIONS] About to call SetupSnapConnections for: 'Join propertyid'
[DEBUG-SETUP-CONNECTIONS] SetupSnapConnections completed for: 'Join propertyid'
[DEBUG-FLOW-CONTROL] About to start snap type detection for: 'Join propertyid'
[DEBUG-FLOW-CONTROL] flowControlConfig variable initialized for: 'Join propertyid'
[DEBUG-FLOW-CONTROL] snapTypeLower = 'com-snaplogic-snaps-transform-multijoin' for: 'Join propertyid'
[DEBUG-FLOW-CONTROL] About to call LogMessage for snap type detection: 'Join propertyid'
[DEBUG-FLOW-CONTROL] Computed boolean values - router: False, join: True, union: False
[DEBUG-FLOW-CONTROL] About to check configuration type for: 'Join propertyid'
[DEBUG-FLOW-CONTROL] Detected JOIN snap: 'Join propertyid'
[FLOW-CONTROL-CONFIG] *** GENERATING JOIN CONFIGURATION *** for: Join propertyid
[DEBUG-JOIN-CALL] About to call GenerateJoinConfiguration for: 'Join propertyid'
[DEBUG-JOIN-RESULT] GenerateJoinConfiguration returned 1267 characters for: 'Join propertyid'
[DEBUG-IN-LOOP] Processing snap: 'Exit' in Flow Control
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Exit'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Exit'. Category: FlowControl, Type: com-snaplogic-snaps-flow-exit
[DEBUG-BEFORE-GET-INFO] About to call GetConnectionInfo for: 'Exit'
[DEBUG-RAW-JSON] RawPipelineJson is null: False
[DEBUG-RAW-JSON] RawPipelineJson has snaps: False
[DEBUG-CONNECTION-START] GetConnectionInfo called for: Exit
[DEBUG-CONNECTION-PROPS] Snap has 3 properties
[DEBUG-CONNECTION-PROP] settings.execution_mode.value = Validate & Execute...
[DEBUG-CONNECTION-PROP] settings.KeyThresholdLimit.value = ********...
[DEBUG-CONNECTION-PROP] settings.KeyErrorMsg.value = Exceeded threshold limit...
[DEBUG-JSON-KEYS] Raw JSON top-level keys: class_fqid, snode_id, instance_id, instance_version, link_map, link_serial, property_map, render_map, snap_map, path_id, path_snode
[DEBUG-SNAPS] snap_map collection exists: True
[DEBUG-SNAP-JSON] Found snap JSON for ID 714d4edd-65bd-439f-9502-2174458261cb: True
[DEBUG-PROPERTY-MAP] property_map exists: True
[DEBUG-ACCOUNT] account property exists: False
[DEBUG-CONNECTION-NO-ACCOUNT] No account info found - returning NULL
[DEBUG-AFTER-GET-INFO] GetConnectionInfo returned: 'NULL'
[ENHANCED-DESC-0f2b0566] Starting enhanced snap description for 'Exit' (com-snaplogic-snaps-flow-exit)
[ENHANCED-DESC-0f2b0566] Using cached descriptions: False
[ENHANCED-DESC-0f2b0566] Using Azure AI: True
[ENHANCED-DESC-0f2b0566] CACHE MISS: No cached enhanced description found
[ENHANCED-DESC-0f2b0566] Creating snap context for Azure OpenAI
[ENHANCED-DESC-0f2b0566] Context created, length: 229 characters
[ENHANCED-DESC-0f2b0566] Sending to Azure OpenAI, prompt length: 506 characters
[AI-REQ-fe45c107] Starting Azure OpenAI request
[AI-REQ-fe45c107] Prompt length: 506 characters
[AI-REQ-fe45c107] CancellationToken: IsCancelled=False, CanBeCancelled=False
[AI-REQ-fe45c107] Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
[AI-REQ-fe45c107] Deployment: gpt-4.1
[AI-REQ-fe45c107] HttpClient timeout: 00:05:00
[AI-REQ-fe45c107] Request URI: https://openai-general-assistan-resource.cognitiveservices.azure.com//openai/deployments/gpt-4.1/chat/completions?api-version=2024-02-01
[AI-REQ-fe45c107] Creating HTTP request message
[AI-REQ-fe45c107] About to send HTTP request to Azure OpenAI
[AI-REQ-fe45c107] HTTP request completed in 1325ms, Status: OK
[AI-REQ-fe45c107] About to read response content
[AI-REQ-fe45c107] Response content read in 0ms, Length: 1850 characters
[AI-REQ-fe45c107] Parsing JSON response
[AI-REQ-fe45c107] SUCCESS: Request completed in 1330ms, Result length: 631 characters
[ENHANCED-DESC-0f2b0566] Azure OpenAI response received, length: 631 characters
[ENHANCED-DESC-0f2b0566] Storing enhanced description in cache
Storing description for Exit (Type: com-snaplogic-snaps-flow-exit)
Description length: 631
Pseudocode length: 0
[ENHANCED-DESC-0f2b0566] SUCCESS: Enhanced description completed in 1336ms
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Exit' is flow control snap
[DEBUG-FLOW-CONTROL] IsFlowControlSnap called for: 'Exit'
[DEBUG-FLOW-CONTROL] Snap Type: 'com-snaplogic-snaps-flow-exit' -> Lower: 'com-snaplogic-snaps-flow-exit'
[DEBUG-FLOW-CONTROL] Snap Category: 'FlowControl'
[DEBUG-FLOW-CONTROL] Contains router: False
[DEBUG-FLOW-CONTROL] Contains join: False
[DEBUG-FLOW-CONTROL] Contains union: False
[DEBUG-FLOW-CONTROL] Contains switch: False
[DEBUG-FLOW-CONTROL] Contains branch: False
[DEBUG-FLOW-CONTROL] Contains conditional: False
[DEBUG-FLOW-CONTROL] Contains if: False
[DEBUG-FLOW-CONTROL] Contains case: False
[DEBUG-FLOW-CONTROL] Is FlowControl category: True
[DEBUG-FLOW-CONTROL] FINAL RESULT for 'Exit': True
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Exit' isFlowControlSnap: True
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Exit'
[DEBUG-FLOW-CONTROL-ENTERED] *** ENTERED FLOW CONTROL CONFIGURATION SECTION FOR 'Exit' ***
[DEBUG-FLOW-CONTROL-TRY] *** ENTERED TRY BLOCK FOR FLOW CONTROL CONFIG: 'Exit' ***
[DEBUG-FLOW-CONTROL-GENERATOR] FlowControlConfigurationGenerator created successfully for: 'Exit'
[DEBUG-SETUP-CONNECTIONS] About to call SetupSnapConnections for: 'Exit'
[DEBUG-SETUP-CONNECTIONS] SetupSnapConnections completed for: 'Exit'
[DEBUG-FLOW-CONTROL] About to start snap type detection for: 'Exit'
[DEBUG-FLOW-CONTROL] flowControlConfig variable initialized for: 'Exit'
[DEBUG-FLOW-CONTROL] snapTypeLower = 'com-snaplogic-snaps-flow-exit' for: 'Exit'
[DEBUG-FLOW-CONTROL] About to call LogMessage for snap type detection: 'Exit'
[DEBUG-FLOW-CONTROL] Computed boolean values - router: False, join: False, union: False
[DEBUG-FLOW-CONTROL] About to check configuration type for: 'Exit'
[DEBUG-IN-LOOP] Processing snap: 'Copy' in Flow Control
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Copy'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Copy'. Category: FlowControl, Type: com-snaplogic-snaps-flow-copy
[DEBUG-BEFORE-GET-INFO] About to call GetConnectionInfo for: 'Copy'
[DEBUG-RAW-JSON] RawPipelineJson is null: False
[DEBUG-RAW-JSON] RawPipelineJson has snaps: False
[DEBUG-CONNECTION-START] GetConnectionInfo called for: Copy
[DEBUG-CONNECTION-PROPS] Snap has 1 properties
[DEBUG-CONNECTION-PROP] settings.execution_mode.value = Validate & Execute...
[DEBUG-JSON-KEYS] Raw JSON top-level keys: class_fqid, snode_id, instance_id, instance_version, link_map, link_serial, property_map, render_map, snap_map, path_id, path_snode
[DEBUG-SNAPS] snap_map collection exists: True
[DEBUG-SNAP-JSON] Found snap JSON for ID 1e6c6db0-709e-45c9-aa8c-9fb6b38724a7: True
[DEBUG-PROPERTY-MAP] property_map exists: True
[DEBUG-ACCOUNT] account property exists: False
[DEBUG-CONNECTION-NO-ACCOUNT] No account info found - returning NULL
[DEBUG-AFTER-GET-INFO] GetConnectionInfo returned: 'NULL'
[ENHANCED-DESC-bcf9981a] Starting enhanced snap description for 'Copy' (com-snaplogic-snaps-flow-copy)
[ENHANCED-DESC-bcf9981a] Using cached descriptions: False
[ENHANCED-DESC-bcf9981a] Using Azure AI: True
[ENHANCED-DESC-bcf9981a] CACHE MISS: No cached enhanced description found
[ENHANCED-DESC-bcf9981a] Creating snap context for Azure OpenAI
[ENHANCED-DESC-bcf9981a] Context created, length: 127 characters
[ENHANCED-DESC-bcf9981a] Sending to Azure OpenAI, prompt length: 404 characters
[AI-REQ-f1d48417] Starting Azure OpenAI request
[AI-REQ-f1d48417] Prompt length: 404 characters
[AI-REQ-f1d48417] CancellationToken: IsCancelled=False, CanBeCancelled=False
[AI-REQ-f1d48417] Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
[AI-REQ-f1d48417] Deployment: gpt-4.1
[AI-REQ-f1d48417] HttpClient timeout: 00:05:00
[AI-REQ-f1d48417] Request URI: https://openai-general-assistan-resource.cognitiveservices.azure.com//openai/deployments/gpt-4.1/chat/completions?api-version=2024-02-01
[AI-REQ-f1d48417] Creating HTTP request message
[AI-REQ-f1d48417] About to send HTTP request to Azure OpenAI
[AI-REQ-f1d48417] HTTP request completed in 1474ms, Status: OK
[AI-REQ-f1d48417] About to read response content
[AI-REQ-f1d48417] Response content read in 0ms, Length: 1849 characters
[AI-REQ-f1d48417] Parsing JSON response
[AI-REQ-f1d48417] SUCCESS: Request completed in 1479ms, Result length: 633 characters
[ENHANCED-DESC-bcf9981a] Azure OpenAI response received, length: 633 characters
[ENHANCED-DESC-bcf9981a] Storing enhanced description in cache
Storing description for Copy (Type: com-snaplogic-snaps-flow-copy)
Description length: 633
Pseudocode length: 0
[ENHANCED-DESC-bcf9981a] SUCCESS: Enhanced description completed in 1483ms
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Copy' is flow control snap
[DEBUG-FLOW-CONTROL] IsFlowControlSnap called for: 'Copy'
[DEBUG-FLOW-CONTROL] Snap Type: 'com-snaplogic-snaps-flow-copy' -> Lower: 'com-snaplogic-snaps-flow-copy'
[DEBUG-FLOW-CONTROL] Snap Category: 'FlowControl'
[DEBUG-FLOW-CONTROL] Contains router: False
[DEBUG-FLOW-CONTROL] Contains join: False
[DEBUG-FLOW-CONTROL] Contains union: False
[DEBUG-FLOW-CONTROL] Contains switch: False
[DEBUG-FLOW-CONTROL] Contains branch: False
[DEBUG-FLOW-CONTROL] Contains conditional: False
[DEBUG-FLOW-CONTROL] Contains if: False
[DEBUG-FLOW-CONTROL] Contains case: False
[DEBUG-FLOW-CONTROL] Is FlowControl category: True
[DEBUG-FLOW-CONTROL] FINAL RESULT for 'Copy': True
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Copy' isFlowControlSnap: True
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Copy'
[DEBUG-FLOW-CONTROL-ENTERED] *** ENTERED FLOW CONTROL CONFIGURATION SECTION FOR 'Copy' ***
[DEBUG-FLOW-CONTROL-TRY] *** ENTERED TRY BLOCK FOR FLOW CONTROL CONFIG: 'Copy' ***
[DEBUG-FLOW-CONTROL-GENERATOR] FlowControlConfigurationGenerator created successfully for: 'Copy'
[DEBUG-SETUP-CONNECTIONS] About to call SetupSnapConnections for: 'Copy'
[DEBUG-SETUP-CONNECTIONS] SetupSnapConnections completed for: 'Copy'
[DEBUG-FLOW-CONTROL] About to start snap type detection for: 'Copy'
[DEBUG-FLOW-CONTROL] flowControlConfig variable initialized for: 'Copy'
[DEBUG-FLOW-CONTROL] snapTypeLower = 'com-snaplogic-snaps-flow-copy' for: 'Copy'
[DEBUG-FLOW-CONTROL] About to call LogMessage for snap type detection: 'Copy'
[DEBUG-FLOW-CONTROL] Computed boolean values - router: False, join: False, union: False
[DEBUG-FLOW-CONTROL] About to check configuration type for: 'Copy'
[DEBUG-SECTION-START] AddSnapCategorySection called for 'Transformations' with 3 snaps
[DEBUG-SECTION-START] Snap in Transformations: 'Map legionella inspection required' (Category: Transformation)
[DEBUG-SECTION-START] Snap in Transformations: 'Map propertytechnicalrecordsid' (Category: Transformation)
[DEBUG-SECTION-START] Snap in Transformations: 'Document to Binary' (Category: Transformation)
[DEBUG] ===== ALL SNAPS IN CATEGORY 'Transformations' =====
[DEBUG] Snap 1: 'Map legionella inspection required' (Type: 'com-snaplogic-snaps-transform-datatransform', ID: 08684fba-9c1b-41a8-9e92-aba2eea8433c)
[DEBUG] Snap 2: 'Map propertytechnicalrecordsid' (Type: 'com-snaplogic-snaps-transform-datatransform', ID: c17e720d-6c35-4741-bda9-28663d0895b7)
[DEBUG] Snap 3: 'Document to Binary' (Type: 'com-snaplogic-snaps-transform-documenttobinary', ID: cb4edbd0-6f49-406c-b23e-62c8d0bc62ef)
[DEBUG] ===== END CATEGORY 'Transformations' =====
[DEBUG-BEFORE-LOOP] About to process 3 snaps in Transformations
[DEBUG-IN-LOOP] Processing snap: 'Map legionella inspection required' in Transformations
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Map legionella inspection required'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Map legionella inspection required'. Category: Transformation, Type: com-snaplogic-snaps-transform-datatransform
[DEBUG-SKIP-CONNECTION] Skipping connection info for mapper/transform snap: 'Map legionella inspection required'
[ENHANCED-DESC-d4466590] Starting enhanced snap description for 'Map legionella inspection required' (com-snaplogic-snaps-transform-datatransform)
[ENHANCED-DESC-d4466590] Using cached descriptions: False
[ENHANCED-DESC-d4466590] Using Azure AI: True
[ENHANCED-DESC-d4466590] CACHE MISS: No cached enhanced description found
[ENHANCED-DESC-d4466590] Creating snap context for Azure OpenAI
[ENHANCED-DESC-d4466590] Context created, length: 1915 characters
[ENHANCED-DESC-d4466590] Sending to Azure OpenAI, prompt length: 2192 characters
[AI-REQ-e35d377b] Starting Azure OpenAI request
[AI-REQ-e35d377b] Prompt length: 2192 characters
[AI-REQ-e35d377b] CancellationToken: IsCancelled=False, CanBeCancelled=False
[AI-REQ-e35d377b] Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
[AI-REQ-e35d377b] Deployment: gpt-4.1
[AI-REQ-e35d377b] HttpClient timeout: 00:05:00
[AI-REQ-e35d377b] Request URI: https://openai-general-assistan-resource.cognitiveservices.azure.com//openai/deployments/gpt-4.1/chat/completions?api-version=2024-02-01
[AI-REQ-e35d377b] Creating HTTP request message
[AI-REQ-e35d377b] About to send HTTP request to Azure OpenAI
[AI-REQ-e35d377b] HTTP request completed in 1914ms, Status: OK
[AI-REQ-e35d377b] About to read response content
[AI-REQ-e35d377b] Response content read in 0ms, Length: 1982 characters
[AI-REQ-e35d377b] Parsing JSON response
[AI-REQ-e35d377b] SUCCESS: Request completed in 1920ms, Result length: 764 characters
[ENHANCED-DESC-d4466590] Azure OpenAI response received, length: 764 characters
[ENHANCED-DESC-d4466590] Storing enhanced description in cache
Storing description for Map legionella inspection required (Type: com-snaplogic-snaps-transform-datatransform)
Description length: 764
Pseudocode length: 0
[ENHANCED-DESC-d4466590] SUCCESS: Enhanced description completed in 1926ms
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Map legionella inspection required' is flow control snap
[DEBUG-FLOW-CONTROL] IsFlowControlSnap called for: 'Map legionella inspection required'
[DEBUG-FLOW-CONTROL] Snap Type: 'com-snaplogic-snaps-transform-datatransform' -> Lower: 'com-snaplogic-snaps-transform-datatransform'
[DEBUG-FLOW-CONTROL] Snap Category: 'Transformation'
[DEBUG-FLOW-CONTROL] Contains router: False
[DEBUG-FLOW-CONTROL] Contains join: False
[DEBUG-FLOW-CONTROL] Contains union: False
[DEBUG-FLOW-CONTROL] Contains switch: False
[DEBUG-FLOW-CONTROL] Contains branch: False
[DEBUG-FLOW-CONTROL] Contains conditional: False
[DEBUG-FLOW-CONTROL] Contains if: False
[DEBUG-FLOW-CONTROL] Contains case: False
[DEBUG-FLOW-CONTROL] Is FlowControl category: False
[DEBUG-FLOW-CONTROL] FINAL RESULT for 'Map legionella inspection required': False
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Map legionella inspection required' isFlowControlSnap: False
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Map legionella inspection required'
[DEBUG-IN-LOOP] Processing snap: 'Map propertytechnicalrecordsid' in Transformations
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Map propertytechnicalrecordsid'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Map propertytechnicalrecordsid'. Category: Transformation, Type: com-snaplogic-snaps-transform-datatransform
[DEBUG-SKIP-CONNECTION] Skipping connection info for mapper/transform snap: 'Map propertytechnicalrecordsid'
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Map propertytechnicalrecordsid' is flow control snap
[DEBUG-FLOW-CONTROL] IsFlowControlSnap called for: 'Map propertytechnicalrecordsid'
[DEBUG-FLOW-CONTROL] Snap Type: 'com-snaplogic-snaps-transform-datatransform' -> Lower: 'com-snaplogic-snaps-transform-datatransform'
[DEBUG-FLOW-CONTROL] Snap Category: 'Transformation'
[DEBUG-FLOW-CONTROL] Contains router: False
[DEBUG-FLOW-CONTROL] Contains join: False
[DEBUG-FLOW-CONTROL] Contains union: False
[DEBUG-FLOW-CONTROL] Contains switch: False
[DEBUG-FLOW-CONTROL] Contains branch: False
[DEBUG-FLOW-CONTROL] Contains conditional: False
[DEBUG-FLOW-CONTROL] Contains if: False
[DEBUG-FLOW-CONTROL] Contains case: False
[DEBUG-FLOW-CONTROL] Is FlowControl category: False
[DEBUG-FLOW-CONTROL] FINAL RESULT for 'Map propertytechnicalrecordsid': False
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Map propertytechnicalrecordsid' isFlowControlSnap: False
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Map propertytechnicalrecordsid'
[DEBUG-IN-LOOP] Processing snap: 'Document to Binary' in Transformations
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Document to Binary'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Document to Binary'. Category: Transformation, Type: com-snaplogic-snaps-transform-documenttobinary
[DEBUG-SKIP-CONNECTION] Skipping connection info for mapper/transform snap: 'Document to Binary'
[ENHANCED-DESC-9f56e276] Starting enhanced snap description for 'Document to Binary' (com-snaplogic-snaps-transform-documenttobinary)
[ENHANCED-DESC-9f56e276] Using cached descriptions: False
[ENHANCED-DESC-9f56e276] Using Azure AI: True
[ENHANCED-DESC-9f56e276] CACHE MISS: No cached enhanced description found
[ENHANCED-DESC-9f56e276] Creating snap context for Azure OpenAI
[ENHANCED-DESC-9f56e276] Context created, length: 192 characters
[ENHANCED-DESC-9f56e276] Sending to Azure OpenAI, prompt length: 469 characters
[AI-REQ-355b9e31] Starting Azure OpenAI request
[AI-REQ-355b9e31] Prompt length: 469 characters
[AI-REQ-355b9e31] CancellationToken: IsCancelled=False, CanBeCancelled=False
[AI-REQ-355b9e31] Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
[AI-REQ-355b9e31] Deployment: gpt-4.1
[AI-REQ-355b9e31] HttpClient timeout: 00:05:00
[AI-REQ-355b9e31] Request URI: https://openai-general-assistan-resource.cognitiveservices.azure.com//openai/deployments/gpt-4.1/chat/completions?api-version=2024-02-01
[AI-REQ-355b9e31] Creating HTTP request message
[AI-REQ-355b9e31] About to send HTTP request to Azure OpenAI
[AI-REQ-355b9e31] HTTP request completed in 1123ms, Status: OK
[AI-REQ-355b9e31] About to read response content
[AI-REQ-355b9e31] Response content read in 0ms, Length: 1856 characters
[AI-REQ-355b9e31] Parsing JSON response
[AI-REQ-355b9e31] SUCCESS: Request completed in 1129ms, Result length: 637 characters
[ENHANCED-DESC-9f56e276] Azure OpenAI response received, length: 637 characters
[ENHANCED-DESC-9f56e276] Storing enhanced description in cache
Storing description for Document to Binary (Type: com-snaplogic-snaps-transform-documenttobinary)
Description length: 637
Pseudocode length: 0
[ENHANCED-DESC-9f56e276] SUCCESS: Enhanced description completed in 1135ms
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Document to Binary' is flow control snap
[DEBUG-FLOW-CONTROL] IsFlowControlSnap called for: 'Document to Binary'
[DEBUG-FLOW-CONTROL] Snap Type: 'com-snaplogic-snaps-transform-documenttobinary' -> Lower: 'com-snaplogic-snaps-transform-documenttobinary'
[DEBUG-FLOW-CONTROL] Snap Category: 'Transformation'
[DEBUG-FLOW-CONTROL] Contains router: False
[DEBUG-FLOW-CONTROL] Contains join: False
[DEBUG-FLOW-CONTROL] Contains union: False
[DEBUG-FLOW-CONTROL] Contains switch: False
[DEBUG-FLOW-CONTROL] Contains branch: False
[DEBUG-FLOW-CONTROL] Contains conditional: False
[DEBUG-FLOW-CONTROL] Contains if: False
[DEBUG-FLOW-CONTROL] Contains case: False
[DEBUG-FLOW-CONTROL] Is FlowControl category: False
[DEBUG-FLOW-CONTROL] FINAL RESULT for 'Document to Binary': False
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Document to Binary' isFlowControlSnap: False
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Document to Binary'
[DEBUG-FLOW-CONTROL] IsFlowControlSnap called for: 'Document to Binary'
[DEBUG-FLOW-CONTROL] Snap Type: 'com-snaplogic-snaps-transform-documenttobinary' -> Lower: 'com-snaplogic-snaps-transform-documenttobinary'
[DEBUG-FLOW-CONTROL] Snap Category: 'Transformation'
[DEBUG-FLOW-CONTROL] Contains router: False
[DEBUG-FLOW-CONTROL] Contains join: False
[DEBUG-FLOW-CONTROL] Contains union: False
[DEBUG-FLOW-CONTROL] Contains switch: False
[DEBUG-FLOW-CONTROL] Contains branch: False
[DEBUG-FLOW-CONTROL] Contains conditional: False
[DEBUG-FLOW-CONTROL] Contains if: False
[DEBUG-FLOW-CONTROL] Contains case: False
[DEBUG-FLOW-CONTROL] Is FlowControl category: False
[DEBUG-FLOW-CONTROL] FINAL RESULT for 'Document to Binary': False
[DEBUG-SECTION-START] AddSnapCategorySection called for 'Database Operations' with 2 snaps
[DEBUG-SECTION-START] Snap in Database Operations: 'Get LEG Unknown Properties' (Category: Database)
[DEBUG-SECTION-START] Snap in Database Operations: 'Update CRM PTR' (Category: Database)
[DEBUG] ===== ALL SNAPS IN CATEGORY 'Database Operations' =====
[DEBUG] Snap 1: 'Get LEG Unknown Properties' (Type: 'com-snaplogic-snaps-sqlserver-execute', ID: eba9da03-aa03-4a15-b5a8-3f774fbb0abe)
[DEBUG] Snap 2: 'Update CRM PTR' (Type: 'com-snaplogic-snaps-dynamics365forsales-update', ID: 47c52b57-6a3f-4c0e-8169-8a17f1987b48)
[DEBUG] ===== END CATEGORY 'Database Operations' =====
[DEBUG-BEFORE-LOOP] About to process 2 snaps in Database Operations
[DEBUG-IN-LOOP] Processing snap: 'Get LEG Unknown Properties' in Database Operations
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Get LEG Unknown Properties'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Get LEG Unknown Properties'. Category: Database, Type: com-snaplogic-snaps-sqlserver-execute
[DEBUG-BEFORE-GET-INFO] About to call GetConnectionInfo for: 'Get LEG Unknown Properties'
[DEBUG-RAW-JSON] RawPipelineJson is null: False
[DEBUG-RAW-JSON] RawPipelineJson has snaps: False
[DEBUG-CONNECTION-START] GetConnectionInfo called for: Get LEG Unknown Properties
[DEBUG-CONNECTION-PROPS] Snap has 8 properties
[DEBUG-CONNECTION-PROP] settings.execution_mode.value = Validate & Execute...
[DEBUG-CONNECTION-PROP] settings.executable_during_suggest.value = False...
[DEBUG-CONNECTION-PROP] settings.passThrough.value = False...
[DEBUG-CONNECTION-PROP] settings.maxRetryProp.value = 0...
[DEBUG-CONNECTION-PROP] settings.retryIntervalProp.value = 1...
[DEBUG-JSON-KEYS] Raw JSON top-level keys: class_fqid, snode_id, instance_id, instance_version, link_map, link_serial, property_map, render_map, snap_map, path_id, path_snode
[DEBUG-SNAPS] snap_map collection exists: True
[DEBUG-SNAP-JSON] Found snap JSON for ID eba9da03-aa03-4a15-b5a8-3f774fbb0abe: True
[DEBUG-PROPERTY-MAP] property_map exists: True
[DEBUG-ACCOUNT] account property exists: True
[DEBUG-AFTER-GET-INFO] GetConnectionInfo returned: 'URM (Live)'
[ENHANCED-DESC-3ddc8298] Starting enhanced snap description for 'Get LEG Unknown Properties' (com-snaplogic-snaps-sqlserver-execute)
[ENHANCED-DESC-3ddc8298] Using cached descriptions: False
[ENHANCED-DESC-3ddc8298] Using Azure AI: True
[ENHANCED-DESC-3ddc8298] CACHE MISS: No cached enhanced description found
[ENHANCED-DESC-3ddc8298] Creating snap context for Azure OpenAI
[ENHANCED-DESC-3ddc8298] Context created, length: 411 characters
[ENHANCED-DESC-3ddc8298] Sending to Azure OpenAI, prompt length: 688 characters
[AI-REQ-b306341b] Starting Azure OpenAI request
[AI-REQ-b306341b] Prompt length: 688 characters
[AI-REQ-b306341b] CancellationToken: IsCancelled=False, CanBeCancelled=False
[AI-REQ-b306341b] Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
[AI-REQ-b306341b] Deployment: gpt-4.1
[AI-REQ-b306341b] HttpClient timeout: 00:05:00
[AI-REQ-b306341b] Request URI: https://openai-general-assistan-resource.cognitiveservices.azure.com//openai/deployments/gpt-4.1/chat/completions?api-version=2024-02-01
[AI-REQ-b306341b] Creating HTTP request message
[AI-REQ-b306341b] About to send HTTP request to Azure OpenAI
[AI-REQ-b306341b] HTTP request completed in 1709ms, Status: OK
[AI-REQ-b306341b] About to read response content
[AI-REQ-b306341b] Response content read in 0ms, Length: 1922 characters
[AI-REQ-b306341b] Parsing JSON response
[AI-REQ-b306341b] SUCCESS: Request completed in 1714ms, Result length: 703 characters
[ENHANCED-DESC-3ddc8298] Azure OpenAI response received, length: 703 characters
[ENHANCED-DESC-3ddc8298] Storing enhanced description in cache
Storing description for Get LEG Unknown Properties (Type: com-snaplogic-snaps-sqlserver-execute)
Description length: 703
Pseudocode length: 0
[ENHANCED-DESC-3ddc8298] SUCCESS: Enhanced description completed in 1719ms
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Get LEG Unknown Properties' is flow control snap
[DEBUG-FLOW-CONTROL] IsFlowControlSnap called for: 'Get LEG Unknown Properties'
[DEBUG-FLOW-CONTROL] Snap Type: 'com-snaplogic-snaps-sqlserver-execute' -> Lower: 'com-snaplogic-snaps-sqlserver-execute'
[DEBUG-FLOW-CONTROL] Snap Category: 'Database'
[DEBUG-FLOW-CONTROL] Contains router: False
[DEBUG-FLOW-CONTROL] Contains join: False
[DEBUG-FLOW-CONTROL] Contains union: False
[DEBUG-FLOW-CONTROL] Contains switch: False
[DEBUG-FLOW-CONTROL] Contains branch: False
[DEBUG-FLOW-CONTROL] Contains conditional: False
[DEBUG-FLOW-CONTROL] Contains if: False
[DEBUG-FLOW-CONTROL] Contains case: False
[DEBUG-FLOW-CONTROL] Is FlowControl category: False
[DEBUG-FLOW-CONTROL] FINAL RESULT for 'Get LEG Unknown Properties': False
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Get LEG Unknown Properties' isFlowControlSnap: False
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Get LEG Unknown Properties'
[DEBUG-FLOW-CONTROL] IsFlowControlSnap called for: 'Get LEG Unknown Properties'
[DEBUG-FLOW-CONTROL] Snap Type: 'com-snaplogic-snaps-sqlserver-execute' -> Lower: 'com-snaplogic-snaps-sqlserver-execute'
[DEBUG-FLOW-CONTROL] Snap Category: 'Database'
[DEBUG-FLOW-CONTROL] Contains router: False
[DEBUG-FLOW-CONTROL] Contains join: False
[DEBUG-FLOW-CONTROL] Contains union: False
[DEBUG-FLOW-CONTROL] Contains switch: False
[DEBUG-FLOW-CONTROL] Contains branch: False
[DEBUG-FLOW-CONTROL] Contains conditional: False
[DEBUG-FLOW-CONTROL] Contains if: False
[DEBUG-FLOW-CONTROL] Contains case: False
[DEBUG-FLOW-CONTROL] Is FlowControl category: False
[DEBUG-FLOW-CONTROL] FINAL RESULT for 'Get LEG Unknown Properties': False
[DEBUG-IN-LOOP] Processing snap: 'Update CRM PTR' in Database Operations
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Update CRM PTR'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Update CRM PTR'. Category: Database, Type: com-snaplogic-snaps-dynamics365forsales-update
[DEBUG-BEFORE-GET-INFO] About to call GetConnectionInfo for: 'Update CRM PTR'
[DEBUG-RAW-JSON] RawPipelineJson is null: False
[DEBUG-RAW-JSON] RawPipelineJson has snaps: False
[DEBUG-CONNECTION-START] GetConnectionInfo called for: Update CRM PTR
[DEBUG-CONNECTION-PROPS] Snap has 12 properties
[DEBUG-CONNECTION-PROP] settings.continueOnError.value = False...
[DEBUG-CONNECTION-PROP] settings.relatedObjectRelationship.value = NULL/EMPTY
[DEBUG-CONNECTION-PROP] settings.relatedObjectType.value = NULL/EMPTY
[DEBUG-CONNECTION-PROP] settings.retries.value = 0...
[DEBUG-CONNECTION-PROP] settings.execution_mode.value = Execute only...
[DEBUG-JSON-KEYS] Raw JSON top-level keys: class_fqid, snode_id, instance_id, instance_version, link_map, link_serial, property_map, render_map, snap_map, path_id, path_snode
[DEBUG-SNAPS] snap_map collection exists: True
[DEBUG-SNAP-JSON] Found snap JSON for ID 47c52b57-6a3f-4c0e-8169-8a17f1987b48: True
[DEBUG-PROPERTY-MAP] property_map exists: True
[DEBUG-ACCOUNT] account property exists: True
[DEBUG-AFTER-GET-INFO] GetConnectionInfo returned: 'NCH 365 PROD'
[DEBUG-DYNAMICS365] Adding Dynamics365ForSales details for: 'Update CRM PTR'
[ENHANCED-DESC-b86949db] Starting enhanced snap description for 'Update CRM PTR' (com-snaplogic-snaps-dynamics365forsales-update)
[ENHANCED-DESC-b86949db] Using cached descriptions: False
[ENHANCED-DESC-b86949db] Using Azure AI: True
[ENHANCED-DESC-b86949db] CACHE MISS: No cached enhanced description found
[ENHANCED-DESC-b86949db] Creating snap context for Azure OpenAI
[ENHANCED-DESC-b86949db] Context created, length: 609 characters
[ENHANCED-DESC-b86949db] Sending to Azure OpenAI, prompt length: 886 characters
[AI-REQ-02e5978d] Starting Azure OpenAI request
[AI-REQ-02e5978d] Prompt length: 886 characters
[AI-REQ-02e5978d] CancellationToken: IsCancelled=False, CanBeCancelled=False
[AI-REQ-02e5978d] Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
[AI-REQ-02e5978d] Deployment: gpt-4.1
[AI-REQ-02e5978d] HttpClient timeout: 00:05:00
[AI-REQ-02e5978d] Request URI: https://openai-general-assistan-resource.cognitiveservices.azure.com//openai/deployments/gpt-4.1/chat/completions?api-version=2024-02-01
[AI-REQ-02e5978d] Creating HTTP request message
[AI-REQ-02e5978d] About to send HTTP request to Azure OpenAI
[AI-REQ-02e5978d] HTTP request completed in 2444ms, Status: OK
[AI-REQ-02e5978d] About to read response content
[AI-REQ-02e5978d] Response content read in 0ms, Length: 1932 characters
[AI-REQ-02e5978d] Parsing JSON response
[AI-REQ-02e5978d] SUCCESS: Request completed in 2448ms, Result length: 718 characters
[ENHANCED-DESC-b86949db] Azure OpenAI response received, length: 718 characters
[ENHANCED-DESC-b86949db] Storing enhanced description in cache
Storing description for Update CRM PTR (Type: com-snaplogic-snaps-dynamics365forsales-update)
Description length: 718
Pseudocode length: 0
[ENHANCED-DESC-b86949db] SUCCESS: Enhanced description completed in 2453ms
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Update CRM PTR' is flow control snap
[DEBUG-FLOW-CONTROL] IsFlowControlSnap called for: 'Update CRM PTR'
[DEBUG-FLOW-CONTROL] Snap Type: 'com-snaplogic-snaps-dynamics365forsales-update' -> Lower: 'com-snaplogic-snaps-dynamics365forsales-update'
[DEBUG-FLOW-CONTROL] Snap Category: 'Database'
[DEBUG-FLOW-CONTROL] Contains router: False
[DEBUG-FLOW-CONTROL] Contains join: False
[DEBUG-FLOW-CONTROL] Contains union: False
[DEBUG-FLOW-CONTROL] Contains switch: False
[DEBUG-FLOW-CONTROL] Contains branch: False
[DEBUG-FLOW-CONTROL] Contains conditional: False
[DEBUG-FLOW-CONTROL] Contains if: False
[DEBUG-FLOW-CONTROL] Contains case: False
[DEBUG-FLOW-CONTROL] Is FlowControl category: False
[DEBUG-FLOW-CONTROL] FINAL RESULT for 'Update CRM PTR': False
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Update CRM PTR' isFlowControlSnap: False
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Update CRM PTR'
[DEBUG-FLOW-CONTROL] IsFlowControlSnap called for: 'Update CRM PTR'
[DEBUG-FLOW-CONTROL] Snap Type: 'com-snaplogic-snaps-dynamics365forsales-update' -> Lower: 'com-snaplogic-snaps-dynamics365forsales-update'
[DEBUG-FLOW-CONTROL] Snap Category: 'Database'
[DEBUG-FLOW-CONTROL] Contains router: False
[DEBUG-FLOW-CONTROL] Contains join: False
[DEBUG-FLOW-CONTROL] Contains union: False
[DEBUG-FLOW-CONTROL] Contains switch: False
[DEBUG-FLOW-CONTROL] Contains branch: False
[DEBUG-FLOW-CONTROL] Contains conditional: False
[DEBUG-FLOW-CONTROL] Contains if: False
[DEBUG-FLOW-CONTROL] Contains case: False
[DEBUG-FLOW-CONTROL] Is FlowControl category: False
[DEBUG-FLOW-CONTROL] FINAL RESULT for 'Update CRM PTR': False
[DEBUG-SECTION-START] AddSnapCategorySection called for 'External Systems' with 2 snaps
[DEBUG-SECTION-START] Snap in External Systems: 'Get CRM PTRs' (Category: ExternalSystem)
[DEBUG-SECTION-START] Snap in External Systems: 'Get CRM Properties' (Category: ExternalSystem)
[DEBUG] ===== ALL SNAPS IN CATEGORY 'External Systems' =====
[DEBUG] Snap 1: 'Get CRM PTRs' (Type: 'com-snaplogic-snaps-dynamics365forsales-search', ID: a75c73ce-c8fc-46f2-a10e-ee923b3d95cb)
[DEBUG] Snap 2: 'Get CRM Properties' (Type: 'com-snaplogic-snaps-dynamics365forsales-read', ID: 475ac7d8-fc9e-4b34-9bca-e6e7309b08c8)
[DEBUG] ===== END CATEGORY 'External Systems' =====
[DEBUG-BEFORE-LOOP] About to process 2 snaps in External Systems
[DEBUG-IN-LOOP] Processing snap: 'Get CRM PTRs' in External Systems
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Get CRM PTRs'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Get CRM PTRs'. Category: ExternalSystem, Type: com-snaplogic-snaps-dynamics365forsales-search
[DEBUG-BEFORE-GET-INFO] About to call GetConnectionInfo for: 'Get CRM PTRs'
[DEBUG-RAW-JSON] RawPipelineJson is null: False
[DEBUG-RAW-JSON] RawPipelineJson has snaps: False
[DEBUG-CONNECTION-START] GetConnectionInfo called for: Get CRM PTRs
[DEBUG-CONNECTION-PROPS] Snap has 8 properties
[DEBUG-CONNECTION-PROP] settings.continueOnError.value = False...
[DEBUG-CONNECTION-PROP] settings.retries.value = 0...
[DEBUG-CONNECTION-PROP] settings.execution_mode.value = Validate & Execute...
[DEBUG-CONNECTION-PROP] settings.pageSize.value = 1000...
[DEBUG-CONNECTION-PROP] settings.executeDuringPreview.value = True...
[DEBUG-JSON-KEYS] Raw JSON top-level keys: class_fqid, snode_id, instance_id, instance_version, link_map, link_serial, property_map, render_map, snap_map, path_id, path_snode
[DEBUG-SNAPS] snap_map collection exists: True
[DEBUG-SNAP-JSON] Found snap JSON for ID a75c73ce-c8fc-46f2-a10e-ee923b3d95cb: True
[DEBUG-PROPERTY-MAP] property_map exists: True
[DEBUG-ACCOUNT] account property exists: True
[DEBUG-AFTER-GET-INFO] GetConnectionInfo returned: 'NCH 365 PROD'
[DEBUG-DYNAMICS365] Adding Dynamics365ForSales details for: 'Get CRM PTRs'
[ENHANCED-DESC-********] Starting enhanced snap description for 'Get CRM PTRs' (com-snaplogic-snaps-dynamics365forsales-search)
[ENHANCED-DESC-********] Using cached descriptions: False
[ENHANCED-DESC-********] Using Azure AI: True
[ENHANCED-DESC-********] CACHE MISS: No cached enhanced description found
[ENHANCED-DESC-********] Creating snap context for Azure OpenAI
[ENHANCED-DESC-********] Context created, length: 420 characters
[ENHANCED-DESC-********] Sending to Azure OpenAI, prompt length: 697 characters
[AI-REQ-9d0052f5] Starting Azure OpenAI request
[AI-REQ-9d0052f5] Prompt length: 697 characters
[AI-REQ-9d0052f5] CancellationToken: IsCancelled=False, CanBeCancelled=False
[AI-REQ-9d0052f5] Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
[AI-REQ-9d0052f5] Deployment: gpt-4.1
[AI-REQ-9d0052f5] HttpClient timeout: 00:05:00
[AI-REQ-9d0052f5] Request URI: https://openai-general-assistan-resource.cognitiveservices.azure.com//openai/deployments/gpt-4.1/chat/completions?api-version=2024-02-01
[AI-REQ-9d0052f5] Creating HTTP request message
[AI-REQ-9d0052f5] About to send HTTP request to Azure OpenAI
[AI-REQ-9d0052f5] HTTP request completed in 1563ms, Status: OK
[AI-REQ-9d0052f5] About to read response content
[AI-REQ-9d0052f5] Response content read in 0ms, Length: 1873 characters
[AI-REQ-9d0052f5] Parsing JSON response
[AI-REQ-9d0052f5] SUCCESS: Request completed in 1569ms, Result length: 663 characters
[ENHANCED-DESC-********] Azure OpenAI response received, length: 663 characters
[ENHANCED-DESC-********] Storing enhanced description in cache
Storing description for Get CRM PTRs (Type: com-snaplogic-snaps-dynamics365forsales-search)
Description length: 663
Pseudocode length: 0
[ENHANCED-DESC-********] SUCCESS: Enhanced description completed in 1576ms
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Get CRM PTRs' is flow control snap
[DEBUG-FLOW-CONTROL] IsFlowControlSnap called for: 'Get CRM PTRs'
[DEBUG-FLOW-CONTROL] Snap Type: 'com-snaplogic-snaps-dynamics365forsales-search' -> Lower: 'com-snaplogic-snaps-dynamics365forsales-search'
[DEBUG-FLOW-CONTROL] Snap Category: 'ExternalSystem'
[DEBUG-FLOW-CONTROL] Contains router: False
[DEBUG-FLOW-CONTROL] Contains join: False
[DEBUG-FLOW-CONTROL] Contains union: False
[DEBUG-FLOW-CONTROL] Contains switch: False
[DEBUG-FLOW-CONTROL] Contains branch: False
[DEBUG-FLOW-CONTROL] Contains conditional: False
[DEBUG-FLOW-CONTROL] Contains if: False
[DEBUG-FLOW-CONTROL] Contains case: False
[DEBUG-FLOW-CONTROL] Is FlowControl category: False
[DEBUG-FLOW-CONTROL] FINAL RESULT for 'Get CRM PTRs': False
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Get CRM PTRs' isFlowControlSnap: False
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Get CRM PTRs'
[DEBUG-FLOW-CONTROL] IsFlowControlSnap called for: 'Get CRM PTRs'
[DEBUG-FLOW-CONTROL] Snap Type: 'com-snaplogic-snaps-dynamics365forsales-search' -> Lower: 'com-snaplogic-snaps-dynamics365forsales-search'
[DEBUG-FLOW-CONTROL] Snap Category: 'ExternalSystem'
[DEBUG-FLOW-CONTROL] Contains router: False
[DEBUG-FLOW-CONTROL] Contains join: False
[DEBUG-FLOW-CONTROL] Contains union: False
[DEBUG-FLOW-CONTROL] Contains switch: False
[DEBUG-FLOW-CONTROL] Contains branch: False
[DEBUG-FLOW-CONTROL] Contains conditional: False
[DEBUG-FLOW-CONTROL] Contains if: False
[DEBUG-FLOW-CONTROL] Contains case: False
[DEBUG-FLOW-CONTROL] Is FlowControl category: False
[DEBUG-FLOW-CONTROL] FINAL RESULT for 'Get CRM PTRs': False
[DEBUG-IN-LOOP] Processing snap: 'Get CRM Properties' in External Systems
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Get CRM Properties'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Get CRM Properties'. Category: ExternalSystem, Type: com-snaplogic-snaps-dynamics365forsales-read
[DEBUG-BEFORE-GET-INFO] About to call GetConnectionInfo for: 'Get CRM Properties'
[DEBUG-RAW-JSON] RawPipelineJson is null: False
[DEBUG-RAW-JSON] RawPipelineJson has snaps: False
[DEBUG-CONNECTION-START] GetConnectionInfo called for: Get CRM Properties
[DEBUG-CONNECTION-PROPS] Snap has 10 properties
[DEBUG-CONNECTION-PROP] settings.continueOnError.value = False...
[DEBUG-CONNECTION-PROP] settings.retries.value = 0...
[DEBUG-CONNECTION-PROP] settings.execution_mode.value = Validate & Execute...
[DEBUG-CONNECTION-PROP] settings.pageSize.value = 1000...
[DEBUG-CONNECTION-PROP] settings.startPage.value = 1...
[DEBUG-JSON-KEYS] Raw JSON top-level keys: class_fqid, snode_id, instance_id, instance_version, link_map, link_serial, property_map, render_map, snap_map, path_id, path_snode
[DEBUG-SNAPS] snap_map collection exists: True
[DEBUG-SNAP-JSON] Found snap JSON for ID 475ac7d8-fc9e-4b34-9bca-e6e7309b08c8: True
[DEBUG-PROPERTY-MAP] property_map exists: True
[DEBUG-ACCOUNT] account property exists: True
[DEBUG-AFTER-GET-INFO] GetConnectionInfo returned: 'NCH 365 PROD'
[DEBUG-DYNAMICS365] Adding Dynamics365ForSales details for: 'Get CRM Properties'
[ENHANCED-DESC-ad55708b] Starting enhanced snap description for 'Get CRM Properties' (com-snaplogic-snaps-dynamics365forsales-read)
[ENHANCED-DESC-ad55708b] Using cached descriptions: False
[ENHANCED-DESC-ad55708b] Using Azure AI: True
[ENHANCED-DESC-ad55708b] CACHE MISS: No cached enhanced description found
[ENHANCED-DESC-ad55708b] Creating snap context for Azure OpenAI
[ENHANCED-DESC-ad55708b] Context created, length: 462 characters
[ENHANCED-DESC-ad55708b] Sending to Azure OpenAI, prompt length: 739 characters
[AI-REQ-b6f309a4] Starting Azure OpenAI request
[AI-REQ-b6f309a4] Prompt length: 739 characters
[AI-REQ-b6f309a4] CancellationToken: IsCancelled=False, CanBeCancelled=False
[AI-REQ-b6f309a4] Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
[AI-REQ-b6f309a4] Deployment: gpt-4.1
[AI-REQ-b6f309a4] HttpClient timeout: 00:05:00
[AI-REQ-b6f309a4] Request URI: https://openai-general-assistan-resource.cognitiveservices.azure.com//openai/deployments/gpt-4.1/chat/completions?api-version=2024-02-01
[AI-REQ-b6f309a4] Creating HTTP request message
[AI-REQ-b6f309a4] About to send HTTP request to Azure OpenAI
[AI-REQ-b6f309a4] HTTP request completed in 1603ms, Status: OK
[AI-REQ-b6f309a4] About to read response content
[AI-REQ-b6f309a4] Response content read in 0ms, Length: 1911 characters
[AI-REQ-b6f309a4] Parsing JSON response
[AI-REQ-b6f309a4] SUCCESS: Request completed in 1607ms, Result length: 701 characters
[ENHANCED-DESC-ad55708b] Azure OpenAI response received, length: 701 characters
[ENHANCED-DESC-ad55708b] Storing enhanced description in cache
Storing description for Get CRM Properties (Type: com-snaplogic-snaps-dynamics365forsales-read)
Description length: 701
Pseudocode length: 0
[ENHANCED-DESC-ad55708b] SUCCESS: Enhanced description completed in 1611ms
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Get CRM Properties' is flow control snap
[DEBUG-FLOW-CONTROL] IsFlowControlSnap called for: 'Get CRM Properties'
[DEBUG-FLOW-CONTROL] Snap Type: 'com-snaplogic-snaps-dynamics365forsales-read' -> Lower: 'com-snaplogic-snaps-dynamics365forsales-read'
[DEBUG-FLOW-CONTROL] Snap Category: 'ExternalSystem'
[DEBUG-FLOW-CONTROL] Contains router: False
[DEBUG-FLOW-CONTROL] Contains join: False
[DEBUG-FLOW-CONTROL] Contains union: False
[DEBUG-FLOW-CONTROL] Contains switch: False
[DEBUG-FLOW-CONTROL] Contains branch: False
[DEBUG-FLOW-CONTROL] Contains conditional: False
[DEBUG-FLOW-CONTROL] Contains if: False
[DEBUG-FLOW-CONTROL] Contains case: False
[DEBUG-FLOW-CONTROL] Is FlowControl category: False
[DEBUG-FLOW-CONTROL] FINAL RESULT for 'Get CRM Properties': False
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Get CRM Properties' isFlowControlSnap: False
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Get CRM Properties'
[DEBUG-FLOW-CONTROL] IsFlowControlSnap called for: 'Get CRM Properties'
[DEBUG-FLOW-CONTROL] Snap Type: 'com-snaplogic-snaps-dynamics365forsales-read' -> Lower: 'com-snaplogic-snaps-dynamics365forsales-read'
[DEBUG-FLOW-CONTROL] Snap Category: 'ExternalSystem'
[DEBUG-FLOW-CONTROL] Contains router: False
[DEBUG-FLOW-CONTROL] Contains join: False
[DEBUG-FLOW-CONTROL] Contains union: False
[DEBUG-FLOW-CONTROL] Contains switch: False
[DEBUG-FLOW-CONTROL] Contains branch: False
[DEBUG-FLOW-CONTROL] Contains conditional: False
[DEBUG-FLOW-CONTROL] Contains if: False
[DEBUG-FLOW-CONTROL] Contains case: False
[DEBUG-FLOW-CONTROL] Is FlowControl category: False
[DEBUG-FLOW-CONTROL] FINAL RESULT for 'Get CRM Properties': False
[DEBUG-SECTION-START] AddSnapCategorySection called for 'File Operations' with 1 snaps
[DEBUG-SECTION-START] Snap in File Operations: 'Save to NCHVINT01' (Category: FileOperation)
[DEBUG] ===== ALL SNAPS IN CATEGORY 'File Operations' =====
[DEBUG] Snap 1: 'Save to NCHVINT01' (Type: 'com-snaplogic-snaps-binary-write', ID: 550b2d53-16be-40c4-b16f-157ea043a388)
[DEBUG] ===== END CATEGORY 'File Operations' =====
[DEBUG-BEFORE-LOOP] About to process 1 snaps in File Operations
[DEBUG-IN-LOOP] Processing snap: 'Save to NCHVINT01' in File Operations
[DEBUG-AFTER-LOG] After LogMessage for snap: 'Save to NCHVINT01'
[DEBUG-CONNECTION-CHECK] Checking connection info for snap 'Save to NCHVINT01'. Category: FileOperation, Type: com-snaplogic-snaps-binary-write
[DEBUG-BEFORE-GET-INFO] About to call GetConnectionInfo for: 'Save to NCHVINT01'
[DEBUG-RAW-JSON] RawPipelineJson is null: False
[DEBUG-RAW-JSON] RawPipelineJson has snaps: False
[DEBUG-CONNECTION-START] GetConnectionInfo called for: Save to NCHVINT01
[DEBUG-CONNECTION-PROPS] Snap has 13 properties
[DEBUG-CONNECTION-PROP] settings.flushIntervalKb.value = -1...
[DEBUG-CONNECTION-PROP] settings.retries.value = 0...
[DEBUG-CONNECTION-PROP] settings.cannedAcls.value = None...
[DEBUG-CONNECTION-PROP] settings.filename.value = "file:///SnapFileDownloads/PTR_Debug Files/legione...
[DEBUG-CONNECTION-PROP] settings.execution_mode.value = Execute only...
[DEBUG-JSON-KEYS] Raw JSON top-level keys: class_fqid, snode_id, instance_id, instance_version, link_map, link_serial, property_map, render_map, snap_map, path_id, path_snode
[DEBUG-SNAPS] snap_map collection exists: True
[DEBUG-SNAP-JSON] Found snap JSON for ID 550b2d53-16be-40c4-b16f-157ea043a388: True
[DEBUG-PROPERTY-MAP] property_map exists: True
[DEBUG-ACCOUNT] account property exists: True
[DEBUG-CONNECTION-NO-ACCOUNT] No account info found - returning NULL
[DEBUG-AFTER-GET-INFO] GetConnectionInfo returned: 'NULL'
[ENHANCED-DESC-c5b94ddd] Starting enhanced snap description for 'Save to NCHVINT01' (com-snaplogic-snaps-binary-write)
[ENHANCED-DESC-c5b94ddd] Using cached descriptions: False
[ENHANCED-DESC-c5b94ddd] Using Azure AI: True
[ENHANCED-DESC-c5b94ddd] CACHE MISS: No cached enhanced description found
[ENHANCED-DESC-c5b94ddd] Creating snap context for Azure OpenAI
[ENHANCED-DESC-c5b94ddd] Context created, length: 727 characters
[ENHANCED-DESC-c5b94ddd] Sending to Azure OpenAI, prompt length: 1004 characters
[AI-REQ-0a88dca5] Starting Azure OpenAI request
[AI-REQ-0a88dca5] Prompt length: 1004 characters
[AI-REQ-0a88dca5] CancellationToken: IsCancelled=False, CanBeCancelled=False
[AI-REQ-0a88dca5] Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
[AI-REQ-0a88dca5] Deployment: gpt-4.1
[AI-REQ-0a88dca5] HttpClient timeout: 00:05:00
[AI-REQ-0a88dca5] Request URI: https://openai-general-assistan-resource.cognitiveservices.azure.com//openai/deployments/gpt-4.1/chat/completions?api-version=2024-02-01
[AI-REQ-0a88dca5] Creating HTTP request message
[AI-REQ-0a88dca5] About to send HTTP request to Azure OpenAI
[AI-REQ-0a88dca5] HTTP request completed in 1649ms, Status: OK
[AI-REQ-0a88dca5] About to read response content
[AI-REQ-0a88dca5] Response content read in 0ms, Length: 2024 characters
[AI-REQ-0a88dca5] Parsing JSON response
[AI-REQ-0a88dca5] SUCCESS: Request completed in 1656ms, Result length: 798 characters
[ENHANCED-DESC-c5b94ddd] Azure OpenAI response received, length: 798 characters
[ENHANCED-DESC-c5b94ddd] Storing enhanced description in cache
Storing description for Save to NCHVINT01 (Type: com-snaplogic-snaps-binary-write)
Description length: 798
Pseudocode length: 0
[ENHANCED-DESC-c5b94ddd] SUCCESS: Enhanced description completed in 1663ms
[PSEUDOCODE-e7750285] Starting pseudocode generation for 'Save to NCHVINT01' (com-snaplogic-snaps-binary-write)
[PSEUDOCODE-e7750285] Using Azure AI: True
[PSEUDOCODE-e7750285] Checking if snap is mapper or condition type
[MAPPER-CHECK] Checking snap: Save to NCHVINT01
[MAPPER-CHECK] Snap Type: 'com-snaplogic-snaps-binary-write' -> Lower: 'com-snaplogic-snaps-binary-write'
[MAPPER-CHECK] Snap Category: FileOperation
[MAPPER-CHECK] IsTransformation: False
[MAPPER-CHECK] IsFlowControl: False (Category=False, Contains router=False)
[MAPPER-CHECK] Final result: False
[PSEUDOCODE-e7750285] Snap is not mapper/condition type, will try AI-based generation
[PSEUDOCODE-e7750285] Creating context for AI-based pseudocode generation
[PSEUDOCODE-e7750285] Context created, length: 727 characters
[PSEUDOCODE-e7750285] Sending to Azure OpenAI, prompt length: 1370 characters
[AI-REQ-d25021b0] Starting Azure OpenAI request
[AI-REQ-d25021b0] Prompt length: 1370 characters
[AI-REQ-d25021b0] CancellationToken: IsCancelled=False, CanBeCancelled=False
[AI-REQ-d25021b0] Endpoint: https://openai-general-assistan-resource.cognitiveservices.azure.com/
[AI-REQ-d25021b0] Deployment: gpt-4.1
[AI-REQ-d25021b0] HttpClient timeout: 00:05:00
[AI-REQ-d25021b0] Request URI: https://openai-general-assistan-resource.cognitiveservices.azure.com//openai/deployments/gpt-4.1/chat/completions?api-version=2024-02-01
[AI-REQ-d25021b0] Creating HTTP request message
[AI-REQ-d25021b0] About to send HTTP request to Azure OpenAI
[AI-REQ-d25021b0] HTTP request completed in 1670ms, Status: OK
[AI-REQ-d25021b0] About to read response content
[AI-REQ-d25021b0] Response content read in 0ms, Length: 2048 characters
[AI-REQ-d25021b0] Parsing JSON response
[AI-REQ-d25021b0] SUCCESS: Request completed in 1675ms, Result length: 810 characters
[PSEUDOCODE-e7750285] SUCCESS: AI-based pseudocode generated in 1680ms, length: 810 characters
[PSEUDOCODE-e7750285] Storing AI-based pseudocode in cache
Storing description for Save to NCHVINT01 (Type: com-snaplogic-snaps-binary-write)
Description length: 0
Pseudocode length: 810
[DEBUG-BEFORE-FLOW-CONTROL-CHECK] About to check if 'Save to NCHVINT01' is flow control snap
[DEBUG-FLOW-CONTROL] IsFlowControlSnap called for: 'Save to NCHVINT01'
[DEBUG-FLOW-CONTROL] Snap Type: 'com-snaplogic-snaps-binary-write' -> Lower: 'com-snaplogic-snaps-binary-write'
[DEBUG-FLOW-CONTROL] Snap Category: 'FileOperation'
[DEBUG-FLOW-CONTROL] Contains router: False
[DEBUG-FLOW-CONTROL] Contains join: False
[DEBUG-FLOW-CONTROL] Contains union: False
[DEBUG-FLOW-CONTROL] Contains switch: False
[DEBUG-FLOW-CONTROL] Contains branch: False
[DEBUG-FLOW-CONTROL] Contains conditional: False
[DEBUG-FLOW-CONTROL] Contains if: False
[DEBUG-FLOW-CONTROL] Contains case: False
[DEBUG-FLOW-CONTROL] Is FlowControl category: False
[DEBUG-FLOW-CONTROL] FINAL RESULT for 'Save to NCHVINT01': False
[DEBUG-AFTER-FLOW-CONTROL-CHECK] 'Save to NCHVINT01' isFlowControlSnap: False
[DEBUG-BEFORE-FLOW-CONTROL-IF] About to check isFlowControlSnap condition for 'Save to NCHVINT01'
[DEBUG-FLOW-CONTROL] IsFlowControlSnap called for: 'Save to NCHVINT01'
[DEBUG-FLOW-CONTROL] Snap Type: 'com-snaplogic-snaps-binary-write' -> Lower: 'com-snaplogic-snaps-binary-write'
[DEBUG-FLOW-CONTROL] Snap Category: 'FileOperation'
[DEBUG-FLOW-CONTROL] Contains router: False
[DEBUG-FLOW-CONTROL] Contains join: False
[DEBUG-FLOW-CONTROL] Contains union: False
[DEBUG-FLOW-CONTROL] Contains switch: False
[DEBUG-FLOW-CONTROL] Contains branch: False
[DEBUG-FLOW-CONTROL] Contains conditional: False
[DEBUG-FLOW-CONTROL] Contains if: False
[DEBUG-FLOW-CONTROL] Contains case: False
[DEBUG-FLOW-CONTROL] Is FlowControl category: False
[DEBUG-FLOW-CONTROL] FINAL RESULT for 'Save to NCHVINT01': False
[DEBUG-SECTION-START] AddSnapCategorySection called for 'Error Handling' with 0 snaps
[DEBUG] ===== ALL SNAPS IN CATEGORY 'Error Handling' =====
[DEBUG] ===== END CATEGORY 'Error Handling' =====
[DEBUG-SECTION-START] AddSnapCategorySection called for 'Other Snaps' with 0 snaps
[DEBUG] ===== ALL SNAPS IN CATEGORY 'Other Snaps' =====
[DEBUG] ===== END CATEGORY 'Other Snaps' =====
