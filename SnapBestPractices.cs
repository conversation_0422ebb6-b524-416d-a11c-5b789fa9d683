using System.Net; // For WebUtility.HtmlEncode
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace SnapAnalyser
{
    // Class to store condition expression information
    public class ConditionInfo
    {
        public string Expression { get; set; }
        public string Description { get; set; }
        public string ReturnValue { get; set; }
        public string TargetPath { get; set; }

        public ConditionInfo(string expression, string description, string returnValue = null, string targetPath = null)
        {
            Expression = expression;
            Description = description;
            ReturnValue = returnValue;
            TargetPath = targetPath;
        }
    }

    public static class SnapBestPractices
    {
        // Helper method to extract condition expressions from various property formats
        public static List<ConditionInfo> ExtractConditionExpressions(SnapNode snap)
        {
            Console.WriteLine($"[DEBUG] Extracting condition expressions for snap: {snap.Label} (ID: {snap.Id}, Type: {snap.Type})");
            var conditions = new List<ConditionInfo>();

            // COMPREHENSIVE DEBUG: Show all properties to understand the actual structure
            Console.WriteLine($"[DEBUG] === ALL PROPERTIES FOR {snap.Label} ===");
            foreach (var prop in snap.Properties)
            {
                Console.WriteLine($"[DEBUG]   {prop.Key} = {(prop.Value.Length > 100 ? prop.Value.Substring(0, 100) + "..." : prop.Value)}");
            }
            Console.WriteLine($"[DEBUG] === END PROPERTIES ===");

            // Look for conditionalTable property
            string conditionalTableJson = null;
            string conditionalTableKey = null;

            // Look for any property key containing 'conditionalTable'
            foreach (var prop in snap.Properties)
            {
                if (prop.Key.ToLower().Contains("conditionaltable") ||
                    (prop.Key.ToLower().Contains("conditional") && prop.Key.ToLower().Contains("table")) ||
                    (prop.Key.ToLower().Contains("condition") && prop.Key.ToLower().Contains("table")) ||
                    (prop.Key.ToLower().Contains("branch") && prop.Key.ToLower().Contains("table")))
                {
                    conditionalTableJson = prop.Value;
                    conditionalTableKey = prop.Key;
                    Console.WriteLine($"[DEBUG] Found conditionalTable property: {prop.Key}");
                    break;
                }
            }

            // Helper method to parse a conditionalTable entry
            void ParseConditionalTableEntry(JsonElement element, List<ConditionInfo> conditionList)
            {
                string conditionalExpression = null;
                string returnValue = null;
                string targetPath = null;

                // Helper method to extract value from nested object structure
                string ExtractValue(JsonElement element, string propertyName)
                {
                    if (element.TryGetProperty(propertyName, out var propElement))
                    {
                        // Check if it's a nested object with a "value" property
                        if (propElement.ValueKind == JsonValueKind.Object && propElement.TryGetProperty("value", out var valueElement))
                        {
                            return valueElement.GetString();
                        }
                        // Otherwise try to get it as a direct string
                        else if (propElement.ValueKind == JsonValueKind.String)
                        {
                            return propElement.GetString();
                        }
                    }
                    return null;
                }

                // Try to get conditionalExpression with fallbacks to similar property names
                conditionalExpression = ExtractValue(element, "conditionalExpression") ??
                                      ExtractValue(element, "expression") ??
                                      ExtractValue(element, "condition");

                // Try to get returnValue
                returnValue = ExtractValue(element, "returnValue") ??
                             ExtractValue(element, "return") ??
                             ExtractValue(element, "result");

                // Try to get targetPath
                targetPath = ExtractValue(element, "targetPath") ??
                            ExtractValue(element, "path") ??
                            ExtractValue(element, "output");

                string description = ExtractValue(element, "description") ?? conditionalTableKey;

                if (!string.IsNullOrEmpty(conditionalExpression))
                {
                    Console.WriteLine($"[DEBUG] Extracted conditionalTable entry: Expression={conditionalExpression?.Substring(0, Math.Min(30, conditionalExpression?.Length ?? 0))}, ReturnValue={returnValue}, TargetPath={targetPath}");
                    conditionList.Add(new ConditionInfo(conditionalExpression, description, returnValue, targetPath));
                }
                else
                {
                    Console.WriteLine($"[DEBUG] No conditionalExpression found in element, available properties: {string.Join(", ", element.EnumerateObject().Select(p => p.Name))}");
                }
            }

            // Try to process conditionalTable if found
            if (!string.IsNullOrEmpty(conditionalTableJson))
            {
                Console.WriteLine($"[DEBUG] Processing conditionalTable for snap {snap.Label}: {conditionalTableJson.Substring(0, Math.Min(50, conditionalTableJson.Length))}...");
                try
                {
                    // First try to clean up the JSON string in case it's improperly escaped
                    string cleanedJson = conditionalTableJson;
                    if (cleanedJson.StartsWith("\"") && cleanedJson.EndsWith("\""))
                    {
                        cleanedJson = cleanedJson.Substring(1, cleanedJson.Length - 2);
                    }
                    // Replace escaped quotes that might interfere with parsing
                    cleanedJson = cleanedJson.Replace("\\\"", "\"");

                    using var doc = JsonDocument.Parse(cleanedJson);
                    try
                    {
                        // Try parsing as array first
                        foreach (var element in doc.RootElement.EnumerateArray())
                        {
                            ParseConditionalTableEntry(element, conditions);
                        }
                    }
                    catch (InvalidOperationException)
                    {
                        // If not an array, try as a single object
                        ParseConditionalTableEntry(doc.RootElement, conditions);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[DEBUG] Error parsing conditionalTable JSON: {ex.Message}");
                    // Try to extract JSON components even if overall parsing failed
                    TryParseConditionJSON(conditionalTableJson, conditionalTableKey, conditions);
                }
            }
            else
            {
                // Check for direct condition property with various naming patterns
                foreach (var prop in snap.Properties)
                {
                    string key = prop.Key.ToLower();

                    if (key == "condition" || key == "filter" || key == "conditionalexpression" || key == "filtercondition" ||
                        key == "conditional" || key == "conditionproperty")
                    {
                        Console.WriteLine($"[DEBUG] Found direct condition property: {prop.Key} = {prop.Value.Substring(0, Math.Min(50, prop.Value.Length))}...");

                        // Try to parse as JSON first
                        if (TryParseConditionJSON(prop.Value, prop.Key, conditions) == false)
                        {
                            try
                            {
                                // Try parsing the value as a simple string that might be JSON but doesn't begin with { or [
                                // This handles cases where the JSON might be stored as a string literal
                                string rawValue = prop.Value.Trim().Trim('"');

                                // Check if this might be an escaped JSON string
                                if (rawValue.Contains("{\"condition\"") ||
                                    rawValue.Contains("{\"expression\"") ||
                                    rawValue.Contains("\"returnValue\"") ||
                                    rawValue.Contains("\"targetPath\""))
                                {
                                    Console.WriteLine($"[DEBUG] Found potential escaped JSON inside property: {prop.Key}");
                                    // Try to unescape the JSON string - this handles different escape patterns
                                    string unescaped = rawValue.Replace("\\\"", "\"").Replace("\\", "");
                                    if (TryParseConditionJSON(unescaped, prop.Key, conditions))
                                    {
                                        // Successfully parsed the unescaped JSON
                                        continue;
                                    }

                                    // If that didn't work, try more aggressive cleaning
                                    unescaped = rawValue.Replace("\\", "").Replace("\"\"", "\"");
                                    if (TryParseConditionJSON(unescaped, prop.Key, conditions))
                                    {
                                        continue;
                                    }
                                }

                                // If still no success, add as plain expression
                                Console.WriteLine($"[DEBUG] Adding as plain expression: {prop.Value.Substring(0, Math.Min(30, prop.Value.Length))}");
                                conditions.Add(new ConditionInfo(prop.Value, prop.Key));
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"[DEBUG] Error parsing condition property as JSON: {ex.Message}");
                                // Fallback to adding as plain expression
                                conditions.Add(new ConditionInfo(prop.Value, prop.Key));
                            }
                        }
                    }
                    // Check for expression_N pattern (most common in condition snaps)
                    else if (key.StartsWith("expression_") && char.IsDigit(key.Last()))
                    {
                        var indexStr = key.Substring("expression_".Length);
                        string description = $"Expression #{indexStr}";

                        Console.WriteLine($"[DEBUG] Found expression_N property: {prop.Key} = {prop.Value}");

                        // Look for corresponding output_N property
                        string targetPath = null;
                        string returnValue = null;

                        var outputKey = $"output_{indexStr}";
                        var outputProp = snap.Properties.FirstOrDefault(p => p.Key.ToLower() == outputKey);
                        if (outputProp.Key != null)
                        {
                            targetPath = outputProp.Value;
                            Console.WriteLine($"[DEBUG] Found paired output property: {outputProp.Key} = {outputProp.Value}");
                        }
                        else
                        {
                            Console.WriteLine($"[DEBUG] No output property found for key: {outputKey}");
                        }

                        // Look for return_N property if it exists
                        var returnKey = $"return_{indexStr}";
                        var returnProp = snap.Properties.FirstOrDefault(p => p.Key.ToLower() == returnKey);
                        if (returnProp.Key != null)
                        {
                            returnValue = returnProp.Value;
                            Console.WriteLine($"[DEBUG] Found paired return property: {returnProp.Key} = {returnProp.Value}");
                        }
                        else
                        {
                            Console.WriteLine($"[DEBUG] No return property found for key: {returnKey}");
                        }

                        conditions.Add(new ConditionInfo(prop.Value, description, returnValue, targetPath));
                    }
                    // Check for properties that might contain expression patterns with settings prefix
                    else if (key.Contains("expression") && (key.Contains("settings") || key.Contains("value")))
                    {
                        Console.WriteLine($"[DEBUG] Found potential nested expression property: {prop.Key} = {prop.Value}");
                        
                        // Try to extract index from nested property names like "settings.expression_0.value"
                        var match = Regex.Match(key, @"expression_(\d+)");
                        if (match.Success)
                        {
                            var indexStr = match.Groups[1].Value;
                            string description = $"Nested Expression #{indexStr}";
                            
                            // Look for corresponding output property with similar pattern
                            string targetPath = null;
                            string returnValue = null;
                            
                            var outputPattern = key.Replace($"expression_{indexStr}", $"output_{indexStr}");
                            var outputProp = snap.Properties.FirstOrDefault(p => p.Key.ToLower() == outputPattern.ToLower());
                            if (outputProp.Key != null)
                            {
                                targetPath = outputProp.Value;
                                Console.WriteLine($"[DEBUG] Found paired nested output property: {outputProp.Key} = {outputProp.Value}");
                            }
                            else
                            {
                                Console.WriteLine($"[DEBUG] No paired nested output property found for pattern: {outputPattern}");
                            }
                            
                            conditions.Add(new ConditionInfo(prop.Value, description, returnValue, targetPath));
                        }
                        else
                        {
                            // Add as generic expression if no index pattern found
                            conditions.Add(new ConditionInfo(prop.Value, $"Expression from {prop.Key}"));
                        }
                    }
                    // Check for indexed condition properties (like condition[0], condition[1], etc.)
                    else if ((key.Contains("condition") || key.Contains("filter") || key.Contains("branch")) &&
                              Regex.IsMatch(key, @"\[\d+\]"))
                    {
                        var match = Regex.Match(key, @"\[(?<index>\d+)\]");
                        string description = match.Success ? $"Condition #{match.Groups["index"].Value}" : key;
                        Console.WriteLine($"[DEBUG] Found indexed condition property: {prop.Key} = {prop.Value.Substring(0, Math.Min(50, prop.Value.Length))}...");

                        // Try to parse as JSON first
                        if (!TryParseConditionJSON(prop.Value, description, conditions))
                        {
                            // Declare variables outside try block so they're accessible later
                            string targetPath = null;
                            string returnValue = null;

                            try
                            {
                                // Try to parse as escaped JSON first
                                string rawValue = prop.Value.Trim().Trim('"');
                                if (rawValue.Contains("{\"condition\"") ||
                                    rawValue.Contains("{\"expression\"") ||
                                    rawValue.Contains("\"returnValue\"") ||
                                    rawValue.Contains("\"targetPath\""))
                                {
                                    // Try to unescape the JSON string - this handles different escape patterns
                                    string unescaped = rawValue.Replace("\\\"", "\"").Replace("\\", "");
                                    if (TryParseConditionJSON(unescaped, description, conditions))
                                    {
                                        continue;
                                    }
                                }

                                // If not valid JSON, check if it's a paired format with specific output indexes
                                var outputKey = key.Replace("condition", "output").Replace("filter", "targetPath");

                                // Look for paired output or target property with same index
                                var pairedProp = snap.Properties.FirstOrDefault(p => p.Key.ToLower() == outputKey);
                                if (pairedProp.Key != null)
                                {
                                    targetPath = pairedProp.Value;
                                    Console.WriteLine($"[DEBUG] Found paired output property: {pairedProp.Key} = {pairedProp.Value}");
                                }
                                else
                                {
                                    Console.WriteLine($"[DEBUG] No paired output property found for key: {outputKey}");
                                }

                                // Try to find any property containing 'output' and the same index number
                                if (targetPath == null)
                                {
                                    var indexStr = match.Groups["index"].Value;
                                    var outputProps = snap.Properties.Where(p => p.Key.ToLower().Contains("output") &&
                                                                       p.Key.ToLower().Contains(indexStr)).ToList();
                                    if (outputProps.Any())
                                    {
                                        targetPath = outputProps.First().Value;
                                        Console.WriteLine($"[DEBUG] Found output property with matching index: {outputProps.First().Key} = {targetPath}");
                                    }
                                    else
                                    {
                                        Console.WriteLine($"[DEBUG] No output property found with matching index: {indexStr}");
                                    }
                                }

                                // Check for return value in a similar pattern
                                var returnKey = key.Replace("condition", "return").Replace("filter", "returnValue");

                                var returnProp = snap.Properties.FirstOrDefault(p => p.Key.ToLower() == returnKey);
                                if (returnProp.Key != null)
                                {
                                    returnValue = returnProp.Value;
                                    Console.WriteLine($"[DEBUG] Found paired return value property: {returnProp.Key} = {returnProp.Value}");
                                }
                                else
                                {
                                    Console.WriteLine($"[DEBUG] No return value property found for key: {returnKey}");
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"[DEBUG] Error while processing indexed condition: {ex.Message}");
                            }

                            // Return value is now handled inside the try block

                            // Add with any paired values we found
                            conditions.Add(new ConditionInfo(prop.Value, description, returnValue, targetPath));
                        }
                    }
                }
            }

            return conditions;
        }

        /// <summary>
        /// Try to parse a JSON string into condition components (expression, return value, target path)
        /// </summary>
        /// <returns>True if parsing was successful, false otherwise</returns>
        private static bool TryParseConditionJSON(string jsonString, string propertyKey, List<ConditionInfo> conditions)
        {
            if (string.IsNullOrWhiteSpace(jsonString))
                return false;

            // Skip if it doesn't look like JSON after trimming whitespace
            string trimmedJson = jsonString.Trim();
            if (!(trimmedJson.StartsWith("{") || trimmedJson.StartsWith("[")))
                return false;

            Console.WriteLine($"[DEBUG] Attempting to parse potential JSON condition: {jsonString.Substring(0, Math.Min(50, jsonString.Length))}...");

            try
            {
                // Handle potentially escaped quotes and backslashes in the JSON
                string cleanedJson = jsonString;
                // Replace doubly-escaped quotes if found
                if (cleanedJson.Contains("\\\""))
                {
                    cleanedJson = cleanedJson.Replace("\\\"", "\"");
                }

                using var doc = JsonDocument.Parse(cleanedJson);
                var root = doc.RootElement;

                // Case 1: Array of condition objects
                if (root.ValueKind == JsonValueKind.Array)
                {
                    Console.WriteLine("[DEBUG] Parsing JSON array of conditions");
                    bool anyParsed = false;

                    foreach (var element in root.EnumerateArray())
                    {
                        if (ParseSingleConditionObject(element, propertyKey, conditions))
                            anyParsed = true;
                    }

                    return anyParsed;
                }
                // Case 2: Single condition object
                else if (root.ValueKind == JsonValueKind.Object)
                {
                    Console.WriteLine("[DEBUG] Parsing single JSON condition object");
                    return ParseSingleConditionObject(root, propertyKey, conditions);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] Failed to parse condition JSON: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// Parse a single JSON object that may contain condition info
        /// </summary>
        private static bool ParseSingleConditionObject(JsonElement element, string propertyKey, List<ConditionInfo> conditions)
        {
            string expression = null;
            string returnValue = null;
            string targetPath = null;
            string description = propertyKey;

            // Check for various property name patterns for the expression
            foreach (string exprProp in new[] { "conditionalExpression", "expression", "condition", "filter" })
            {
                if (element.TryGetProperty(exprProp, out var exprElement))
                {
                    expression = exprElement.GetString();
                    break;
                }
            }

            // Check for various property name patterns for the return value
            foreach (string retProp in new[] { "returnValue", "return", "value", "outputValue" })
            {
                if (element.TryGetProperty(retProp, out var retElement))
                {
                    returnValue = retElement.GetString();
                    break;
                }
            }

            // Check for various property name patterns for the target path
            foreach (string pathProp in new[] { "targetPath", "path", "output", "destination", "target" })
            {
                if (element.TryGetProperty(pathProp, out var pathElement))
                {
                    targetPath = pathElement.GetString();
                    break;
                }
            }

            // Try to get description if available
            if (element.TryGetProperty("description", out var descElement))
            {
                var tempDesc = descElement.GetString();
                if (!string.IsNullOrWhiteSpace(tempDesc))
                    description = tempDesc;
            }

            // Only add if we found at least an expression
            if (!string.IsNullOrEmpty(expression))
            {
                // Clean up any remaining escape sequences in the expression
                expression = expression.Replace("\\\"", "\"").Replace("\\\\", "\\");
                if (returnValue != null) returnValue = returnValue.Replace("\\\"", "\"").Replace("\\\\", "\\");
                if (targetPath != null) targetPath = targetPath.Replace("\\\"", "\"").Replace("\\\\", "\\");

                Console.WriteLine($"[DEBUG] Extracted JSON condition: Expression={expression?.Substring(0, Math.Min(30, expression?.Length ?? 0))}, " +
                                  $"ReturnValue={returnValue}, TargetPath={targetPath}");
                conditions.Add(new ConditionInfo(expression, description, returnValue, targetPath));
                return true;
            }

            return false;
        }

        /// <summary>
        /// Gets the best practices analysis for a snap, particularly focusing on condition expressions
        /// </summary>
        /// <param name="snap">The snap node to analyze</param>
        /// <param name="allSnaps">All snaps in the pipeline</param>
        /// <param name="hasMappings">Whether the pipeline has mappings</param>
        /// <returns>HTML-formatted best practices information</returns>
        public static string GetSnapBestPractices(SnapNode snap, List<SnapNode> allSnaps, bool hasMappings)
        {
            Console.WriteLine($"[DEBUG] Generating best practices for snap: {snap.Label} (ID: {snap.Id}, Type: {snap.Type}");
            var details = new StringBuilder();

            // DEBUG: Show all snap types to help identify condition snaps
            Console.WriteLine($"[DEBUG] SNAP TYPE ANALYSIS: '{snap.Type}' for snap '{snap.Label}'");

            // Extract condition expressions if this is a conditional transform snap (NOT router snaps)
            // Router snaps are pure flow control and shouldn't show condition configuration
            bool isConditionalTransformSnap = (snap.Type?.ToLower().Contains("condition") == true ||
                snap.Type?.ToLower().Contains("conditional") == true ||
                snap.Type?.ToLower().Contains("branch") == true ||
                snap.Type?.ToLower().Contains("switch") == true ||
                snap.Type?.ToLower().Contains("if") == true ||
                snap.Type?.ToLower().Contains("filter") == true ||
                snap.Type?.ToLower().Contains("decision") == true ||
                snap.Type?.ToLower().Contains("choice") == true ||
                snap.Type?.ToLower().Contains("gate") == true ||
                snap.Type?.ToLower().Contains("-gate") == true ||
                snap.Type?.ToLower().Contains("-branch") == true ||
                snap.Type?.ToLower().Contains("-switch") == true ||
                snap.Type?.ToLower().Contains("-condition") == true ||
                snap.Type?.ToLower().Contains("snaps-transform-gate") == true ||
                snap.Type?.ToLower().Contains("snaps-control-") == true) &&
                // Exclude router snaps - they are flow control, not conditional transforms
                !(snap.Type?.ToLower().Contains("router") == true ||
                  snap.Type?.ToLower().Contains("-router") == true ||
                  snap.Type?.ToLower().Contains("flow") == true ||
                  snap.Type?.ToLower().Contains("snaps-flow-") == true);

            Console.WriteLine($"[DEBUG] Is conditional transform snap (not router): {isConditionalTransformSnap} (Type: {snap.Type})");

            if (isConditionalTransformSnap)
            {
                Console.WriteLine($"[DEBUG] This is a conditional transform snap, extracting expressions");
                var conditions = ExtractConditionExpressions(snap);
                Console.WriteLine($"[DEBUG] Found {conditions.Count} condition expressions");
                foreach (var c in conditions)
                {
                    Console.WriteLine($"[DEBUG]   - Expression: {(c.Expression != null ? c.Expression.Substring(0, Math.Min(30, c.Expression.Length)) : "null")}..., ReturnValue: {c.ReturnValue ?? "null"}, TargetPath: {c.TargetPath ?? "null"}");
                }

                if (conditions.Any())
                {
                    details.AppendLine("<p><strong>Condition Configuration:</strong></p>");
                    details.AppendLine("<p>This snap evaluates the following expressions:</p>");
                    details.AppendLine("<table class='condition-table' style='width: 100%; border-collapse: collapse; margin-bottom: 15px;'>");
                    details.AppendLine("<tr class='header-row' style='background-color: #f8f9fa;'>" +
                                       "<th style='text-align:left; border: 1px solid #ddd; padding: 8px; width: 50%;'>Expression</th>" +
                                       "<th style='text-align:left; border: 1px solid #ddd; padding: 8px; width: 25%;'>Return Value</th>" +
                                       "<th style='text-align:left; border: 1px solid #ddd; padding: 8px; width: 25%;'>Target Path</th></tr>");

                    foreach (var condition in conditions)
                    {
                        // Format the expression for better readability
                        string formattedExpression = condition.Expression;

                        // Replace common operators with highlighted versions for better readability
                        formattedExpression = WebUtility.HtmlEncode(formattedExpression)
                            .Replace("&amp;&amp;", "<span style='color:#0066cc; font-weight:bold;'>&amp;&amp;</span>")
                            .Replace("|||", "<span style='color:#0066cc; font-weight:bold;'>||</span>")
                            .Replace("==", "<span style='color:#cc6600; font-weight:bold;'>==</span>")
                            .Replace("!=", "<span style='color:#cc6600; font-weight:bold;'>!=</span>")
                            .Replace("&gt;", "<span style='color:#cc6600; font-weight:bold;'>&gt;</span>")
                            .Replace("&lt;", "<span style='color:#cc6600; font-weight:bold;'>&lt;</span>");

                        // Replace pre tag with a div using the same styling to avoid HTML nesting warnings
                        details.AppendLine($"<tr class='condition-row'>" +
                                        $"<td style='border: 1px solid #ddd; padding: 8px; font-family: monospace;'><div style='margin: 0; white-space: pre-wrap; word-break: break-word;'>{formattedExpression}</div></td>" +
                                        $"<td style='border: 1px solid #ddd; padding: 8px;'><code style='background-color: #f7f7f7; padding: 2px 4px; border-radius: 3px;'>{WebUtility.HtmlEncode(condition.ReturnValue ?? "")}</code></td>" +
                                        $"<td style='border: 1px solid #ddd; padding: 8px;'><strong>{WebUtility.HtmlEncode(condition.TargetPath ?? "")}</strong></td>" +
                                        $"</tr>");
                    }

                    details.AppendLine("</table>");
                }
                else
                {
                    details.AppendLine("<p>No specific condition expressions were found.</p>");
                }
            }

            // Add input connections if this is a condition/router snap
            if ((snap.Type.ToLower().Contains("condition") ||
                 snap.Type.ToLower().Contains("router") ||
                 snap.Type.ToLower().Contains("branch")) &&
                 snap.InputConnections.Any())
            {
                details.AppendLine("<p><strong>Input Connections:</strong></p>");
                details.AppendLine("<ul>");
                foreach (var inputConn in snap.InputConnections)
                {
                    var inputSnap = allSnaps.FirstOrDefault(s => s.Id == inputConn.SourceId);
                    if (inputSnap != null)
                    {
                        details.AppendLine($"<li>{WebUtility.HtmlEncode(inputSnap.Label)} ({WebUtility.HtmlEncode(inputSnap.Type)})</li>");
                    }
                }
                details.AppendLine("</ul>");
            }

            string result = details.ToString();
            Console.WriteLine($"[DEBUG] Final HTML content length: {result.Length}");
            if (result.Length > 0)
            {
                Console.WriteLine($"[DEBUG] Final HTML content: {result.Substring(0, Math.Min(100, result.Length))}...");
            }
            else
            {
                Console.WriteLine($"[DEBUG] WARNING: Empty HTML content generated for snap {snap.Label}");
            }
            return result;
        }

        // Helper method to extract a clean target field name from property keys
        private static string ExtractTargetFieldName(string propertyKey)
        {
            // Try to extract a meaningful field name from property keys like "targetPaths[0].fieldName"
            // or "mappings.customer.address.expression" or "settings.transformations.mappingTable.0.targetPath.value"

            if (string.IsNullOrWhiteSpace(propertyKey))
                return propertyKey;

            // First, handle common complex path patterns
            string cleaned = propertyKey;

            // Handle common nested path patterns
            string[] commonPrefixPatterns = {
                "settings.transformations.mappingTable.",
                "settings.transformations.value.mappingTable.",
                "settings.mappingTable.",
                "transformations.value.mappingTable.",
                "transformations.mappingTable."
            };

            foreach (var pattern in commonPrefixPatterns)
            {
                if (cleaned.Contains(pattern))
                {
                    cleaned = cleaned.Substring(cleaned.IndexOf(pattern) + pattern.Length);
                    break;
                }
            }

            // Remove array indices anywhere in the path
            cleaned = Regex.Replace(cleaned, @"\[\d+\]\.?", ".");

            // Remove common prefixes
            string[] prefixesToRemove = {
                "targetPath", "target_path", "sourcePath", "source_path",
                "mapping", "expression", "output", "target", "source"
            };

            foreach (var prefix in prefixesToRemove)
            {
                if (cleaned.ToLower().StartsWith(prefix.ToLower()))
                {
                    cleaned = cleaned.Substring(prefix.Length);
                    if (cleaned.StartsWith(".") || cleaned.StartsWith("_"))
                        cleaned = cleaned.Substring(1);
                }
            }

            // Handle common suffixes
            string[] suffixesToRemove = { ".expression", ".value", ".mapping", ".targetPath", ".sourcePath" };
            foreach (var suffix in suffixesToRemove)
            {
                if (cleaned.ToLower().EndsWith(suffix.ToLower()))
                {
                    cleaned = cleaned.Substring(0, cleaned.Length - suffix.Length);
                }
            }

            // If we're left with a path (contains dots), take the last part as the field name
            if (cleaned.Contains("."))
            {
                string[] parts = cleaned.Split('.');
                cleaned = parts.LastOrDefault(p => !string.IsNullOrWhiteSpace(p)) ?? cleaned;
            }

            // Clean up any remaining dots at start/end
            cleaned = cleaned.Trim('.');

            // If nothing meaningful left, return the original
            return string.IsNullOrWhiteSpace(cleaned) ? propertyKey : cleaned;
        }

        // Helper method to extract actual target variable values from SnapLogic mapping structures
        private static string ExtractActualTargetVariable(string propertyKey, string propertyValue, SnapNode snap)
        {
            // Try to find the actual target variable value from targetPath.value properties

            // First, check if this property key is a targetPath.value itself
            if (propertyKey.EndsWith(".targetPath.value"))
            {
                return propertyValue; // This IS the target variable name (like "$unitcode")
            }

            // If this is an expression property, try to find its corresponding targetPath
            if (propertyKey.EndsWith(".expression.value") || propertyKey.EndsWith(".expression"))
            {
                // Extract the base path by removing the expression part
                string basePath = propertyKey.Replace(".expression.value", "").Replace(".expression", "");

                // Look for the corresponding targetPath.value
                string targetPathKey = basePath + ".targetPath.value";
                if (snap.Properties.TryGetValue(targetPathKey, out string targetValue))
                {
                    return targetValue; // Found the actual target variable name
                }

                // Try alternative targetPath key
                string altTargetPathKey = basePath + ".targetPath";
                if (snap.Properties.TryGetValue(altTargetPathKey, out string altTargetValue))
                {
                    try
                    {
                        // Parse JSON to extract the value
                        using (var doc = JsonDocument.Parse(altTargetValue))
                        {
                            if (doc.RootElement.TryGetProperty("value", out var valueElement))
                            {
                                return valueElement.GetString();
                            }
                        }
                    }
                    catch
                    {
                        // If JSON parsing fails, return the raw value
                        return altTargetValue;
                    }
                }
            }

            // For other property types, try to extract from the property structure
            if (propertyKey.Contains("mappingTable"))
            {
                // Extract mapping index pattern like "mappingTable.0" 
                var match = Regex.Match(propertyKey, @"mappingTable\.(\d+)");
                if (match.Success)
                {
                    string index = match.Groups[1].Value;

                    // Construct possible targetPath keys
                    string[] possibleKeys = {
                        $"settings.transformations.mappingTable.{index}.targetPath.value",
                        $"settings.transformations.value.mappingTable.{index}.targetPath.value",
                        $"transformations.mappingTable.{index}.targetPath.value",
                        $"mappingTable.{index}.targetPath.value"
                    };

                    foreach (string key in possibleKeys)
                    {
                        if (snap.Properties.TryGetValue(key, out string targetVar))
                        {
                            return targetVar;
                        }
                    }
                }
            }

            // Fall back to the original field name extraction if we can't find the actual target
            return ExtractTargetFieldName(propertyKey);
        }

        // ... any other existing methods ...
    }
}
