# PowerShell script to restore the working account extraction logic
# Based on the successful memory that was extracting accounts correctly

$filePath = "DocumentationGenerator.cs"

Write-Host "Restoring working account extraction logic..." -ForegroundColor Green

$content = Get-Content $filePath -Raw

# Fix 1: Update GetConnectionInfo method signature to accept raw JSON
$oldMethodSignature = 'private string GetConnectionInfo\(SnapNode snap\)'
$newMethodSignature = 'private string GetConnectionInfo(SnapNode snap, JObject rawSnapJson = null)'

if ($content -match $oldMethodSignature) {
    $content = $content -replace $oldMethodSignature, $newMethodSignature
    Write-Host "✅ Updated GetConnectionInfo method signature to accept raw JSON" -ForegroundColor Yellow
}

# Fix 2: Add raw JSON extraction before calling GetConnectionInfo
$oldMethodCall = 'string connectionInfo = GetConnectionInfo\(snap\);'
$newMethodCallWithRawJson = @'
// Get raw JSON for this snap from the pipeline
                    JObject rawSnapJson = null;
                    if (_currentPipeline?.RawPipelineJson != null)
                    {
                        try
                        {
                            var pipelineObj = JObject.Parse(_currentPipeline.RawPipelineJson);
                            var snapsArray = pipelineObj["snaps"] as JArray;
                            rawSnapJson = snapsArray?.FirstOrDefault(s => s["snap_id"]?.ToString() == snap.Id) as JObject;
                            LogMessage($"[CONNECTION] Raw JSON for snap {snap.Label}: {(rawSnapJson != null ? "Found" : "Not found")}");
                        }
                        catch (Exception ex)
                        {
                            LogMessage($"[CONNECTION] Error getting raw JSON for snap {snap.Label}: {ex.Message}");
                        }
                    }
                    
                    string connectionInfo = GetConnectionInfo(snap, rawSnapJson);
'@

if ($content.Contains('string connectionInfo = GetConnectionInfo(snap);')) {
    $content = $content.Replace('string connectionInfo = GetConnectionInfo(snap);', $newMethodCallWithRawJson)
    Write-Host "✅ Updated method call to extract and pass raw JSON" -ForegroundColor Yellow
}

# Fix 3: Add the critical raw JSON parsing logic at the start of GetConnectionInfo method
$insertionPoint = 'LogMessage\(\$"\[CONNECTION\] Checking connection info for snap: \{snap\.Label\}, Properties count: \{snap\.Properties\.Count\}"\);'
$rawJsonLogic = @'
LogMessage($"[CONNECTION] Checking connection info for snap: {snap.Label}, Properties count: {snap.Properties.Count}");
            
            // **PRIORITY 1: Extract from raw JSON using the correct path that was working**
            if (rawSnapJson != null)
            {
                try
                {
                    // Use the correct path: property_map.account.account_ref.value.label.value
                    var accountNode = rawSnapJson["property_map"]?["account"];
                    if (accountNode != null)
                    {
                        LogMessage($"[CONNECTION] Found property_map.account in raw JSON for: {snap.Label}");
                        
                        var accountRef = accountNode["account_ref"];
                        if (accountRef != null)
                        {
                            var valueObj = accountRef["value"];
                            if (valueObj != null)
                            {
                                var labelObj = valueObj["label"];
                                if (labelObj != null)
                                {
                                    var accountName = labelObj["value"]?.ToString();
                                    if (!string.IsNullOrEmpty(accountName))
                                    {
                                        LogMessage($"[CONNECTION] ✅ SUCCESS: Extracted account name from raw JSON: {accountName}");
                                        return accountName;
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        LogMessage($"[CONNECTION] No property_map.account found in raw JSON for: {snap.Label}");
                    }
                }
                catch (Exception ex)
                {
                    LogMessage($"[CONNECTION] Error parsing raw JSON for account info: {ex.Message}");
                }
            }
            else
            {
                LogMessage($"[CONNECTION] ⚠️  No raw JSON provided for account extraction for: {snap.Label}");
            }
'@

if ($content.Contains($insertionPoint)) {
    $content = $content.Replace($insertionPoint, $rawJsonLogic)
    Write-Host "✅ Added priority raw JSON extraction logic" -ForegroundColor Yellow
}

# Write the updated content
$content | Set-Content $filePath -Encoding UTF8

Write-Host "✅ Account extraction logic restored!" -ForegroundColor Green
Write-Host ""
Write-Host "Key changes applied:" -ForegroundColor Cyan
Write-Host "  ✓ GetConnectionInfo now accepts raw JSON parameter" -ForegroundColor White
Write-Host "  ✓ Raw JSON is extracted from pipeline and passed to method" -ForegroundColor White
Write-Host "  ✓ Priority given to raw JSON path: property_map.account.account_ref.value.label.value" -ForegroundColor White
Write-Host "  ✓ Enhanced logging to show extraction success/failure" -ForegroundColor White
Write-Host ""
Write-Host "This should restore the working account extraction that was successfully extracting" -ForegroundColor Green
Write-Host "account names like 'KYP Live' and 'KYPERA (Live)' from the pipeline raw JSON." -ForegroundColor Green
