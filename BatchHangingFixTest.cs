using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using SnapAnalyser;

namespace SnapAnalyser
{
    class BatchHangingFixTest
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== BATCH PROCESSING HANGING FIX VERIFICATION ===");
            Console.WriteLine($"Test started at: {DateTime.Now}");
            Console.WriteLine();

            try
            {
                await TestConfigureAwaitFix();
                await TestTimeoutConfiguration();
                await TestDescriptionCacheDeadlockFix();
                await TestAIProcessingChain();
                
                Console.WriteLine();
                Console.WriteLine("✅ ALL TESTS PASSED - Hanging fix verification complete!");
                Console.WriteLine($"Test completed at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ TEST FAILED: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return;
            }
        }

        static async Task TestConfigureAwaitFix()
        {
            Console.WriteLine("🔧 Testing ConfigureAwait(false) fix...");
            
            // Test AIDescriptionGenerator initialization (this tests HttpClient timeout fix)
            try
            {
                var aiGenerator = new AIDescriptionGenerator();
                Console.WriteLine("  ✅ AIDescriptionGenerator initialized without hanging");
                
                // Test that the cache initializes properly without deadlock
                var cache = new DescriptionCache();
                await cache.InitializeAsync();
                Console.WriteLine("  ✅ DescriptionCache initialized without deadlock");
            }
            catch (Exception ex)
            {
                throw new Exception($"ConfigureAwait fix test failed: {ex.Message}", ex);
            }
        }

        static async Task TestTimeoutConfiguration()
        {
            Console.WriteLine("⏱️  Testing timeout configuration...");
            
            try
            {
                var aiGenerator = new AIDescriptionGenerator();
                var deploymentInfo = aiGenerator.GetDeploymentInfo();
                
                Console.WriteLine($"  📊 Current timeout: {deploymentInfo.TimeoutSeconds} seconds");
                Console.WriteLine($"  📊 Endpoint configured: {!string.IsNullOrEmpty(deploymentInfo.Endpoint)}");
                Console.WriteLine($"  📊 API key configured: {!string.IsNullOrEmpty(deploymentInfo.ApiKey)}");
                Console.WriteLine("  ✅ Timeout configuration verified");
            }
            catch (Exception ex)
            {
                throw new Exception($"Timeout configuration test failed: {ex.Message}", ex);
            }
        }

        static async Task TestDescriptionCacheDeadlockFix()
        {
            Console.WriteLine("🔒 Testing DescriptionCache deadlock fix...");
            
            try
            {
                var cache = new DescriptionCache();
                
                // Test async initialization
                await cache.InitializeAsync();
                Console.WriteLine("  ✅ Async initialization completed");
                
                // Test synchronous operations that previously caused deadlocks
                var testSnap = new SnapNode 
                { 
                    Id = "test", 
                    Type = "TestSnap", 
                    Label = "Test Snap",
                    Properties = new Dictionary<string, string>()
                };
                
                // This should not hang (previously would deadlock in UI context)
                bool hasDescription = cache.TryGetDescription(testSnap, out string description);
                Console.WriteLine("  ✅ TryGetDescription completed without hanging");
                
                // Test storing description (should not block)
                cache.StoreDescription(testSnap, "Test description", "Test pseudocode");
                Console.WriteLine("  ✅ StoreDescription completed without blocking");
                
                // Verify it was stored
                bool retrieved = cache.TryGetDescription(testSnap, out string retrievedDesc);
                if (retrieved && !string.IsNullOrEmpty(retrievedDesc))
                {
                    Console.WriteLine("  ✅ Description storage and retrieval working");
                }
                else
                {
                    throw new Exception("Description was not properly stored or retrieved");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"DescriptionCache deadlock fix test failed: {ex.Message}", ex);
            }
        }

        static async Task TestAIProcessingChain()
        {
            Console.WriteLine("🤖 Testing AI processing chain fixes...");
            
            try
            {
                var aiGenerator = new AIDescriptionGenerator();
                
                // Create a test snap
                var testSnap = new SnapNode 
                { 
                    Id = "test-snap", 
                    Type = "File Reader", 
                    Label = "Test File Reader",
                    Properties = new Dictionary<string, string>
                    {
                        {"settings.filename", "test.csv"},
                        {"settings.encoding", "UTF-8"}
                    }
                };
                
                var testPipeline = new PipelineData
                {
                    Name = "Test Pipeline",
                    Snaps = new List<SnapNode> { testSnap }
                };
                
                // Test with short timeout to verify proper cancellation
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                
                try
                {
                    // This should either complete quickly or cancel properly (not hang)
                    var description = await aiGenerator.GenerateSnapDescription(testSnap, testPipeline, cts.Token);
                    Console.WriteLine("  ✅ GenerateSnapDescription completed or fell back gracefully");
                }
                catch (OperationCanceledException)
                {
                    Console.WriteLine("  ✅ GenerateSnapDescription cancelled properly (no hanging)");
                }
                catch (Exception ex) when (ex.Message.Contains("not configured") || ex.Message.Contains("connection"))
                {
                    Console.WriteLine("  ✅ GenerateSnapDescription failed gracefully (expected for test)");
                }
                
                // Test pipeline description generation
                try
                {
                    using var cts2 = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                    var pipelineDesc = await aiGenerator.GeneratePipelineDescription(testPipeline, cts2.Token);
                    Console.WriteLine("  ✅ GeneratePipelineDescription completed or fell back gracefully");
                }
                catch (OperationCanceledException)
                {
                    Console.WriteLine("  ✅ GeneratePipelineDescription cancelled properly (no hanging)");
                }
                catch (Exception ex) when (ex.Message.Contains("not configured") || ex.Message.Contains("connection"))
                {
                    Console.WriteLine("  ✅ GeneratePipelineDescription failed gracefully (expected for test)");
                }
                
                Console.WriteLine("  ✅ AI processing chain operates without hanging");
            }
            catch (Exception ex)
            {
                throw new Exception($"AI processing chain test failed: {ex.Message}", ex);
            }
        }
    }
}
