namespace SnapLogic.Documentation.Shared
{
    public interface IAIDescriptionService
    {
        Task<string> GenerateSnapDescriptionAsync(SnapNode snap, PipelineData pipeline, CancellationToken cancellationToken = default);
        Task<string> GeneratePipelineDescriptionAsync(PipelineData pipeline, CancellationToken cancellationToken = default);
        bool IsAIEnabled { get; }
    }

    public class BasicAIDescriptionService : IAIDescriptionService
    {
        public bool IsAIEnabled => false;

        public Task<string> GenerateSnapDescriptionAsync(SnapNode snap, PipelineData pipeline, CancellationToken cancellationToken = default)
        {
            // Fallback to enhanced local descriptions
            return Task.FromResult(GetEnhancedSnapDescription(snap, pipeline));
        }

        public Task<string> GeneratePipelineDescriptionAsync(PipelineData pipeline, CancellationToken cancellationToken = default)
        {
            // Fallback to basic pipeline description
            return Task.FromResult(GetBasicPipelineDescription(pipeline));
        }

        private string GetEnhancedSnapDescription(SnapNode snap, PipelineData pipeline)
        {
            var type = snap.Type?.ToLower() ?? "";
            var label = snap.Label ?? "Unknown Snap";

            // Debug logging (reduced)
            Console.WriteLine($"[AI-DESC] Processing: {label} (Type: {type})");

            // Enhanced descriptions based on snap type and properties
            if (type.Contains("datatransform") || type.Contains("mapper") || type.Contains("transform"))
            {
                var mappings = ExtractMappings(snap);
                Console.WriteLine($"[AI-DESC] Found {mappings.Count} mappings for transform snap");

                if (mappings.Any())
                {
                    var mappingCount = mappings.Count;
                    var sampleMappings = mappings.Take(3).Select(m => $"{m.Key} = {m.Value}").ToList();

                    return $"Transforms data by mapping {mappingCount} field(s). " +
                           $"Key mappings include: {string.Join(", ", sampleMappings)}" +
                           (mappingCount > 3 ? $" and {mappingCount - 3} more." : ".");
                }
                return "Transforms and maps data fields according to configured expressions and rules.";
            }
            else if (type.Contains("sqlserver") || type.Contains("database") || type.Contains("lookup"))
            {
                var operation = GetPropertyValue(snap, "operation") ??
                               (type.Contains("lookup") ? "lookup" : "database operation");
                var table = GetPropertyValue(snap, "table") ?? GetPropertyValue(snap, "tableName");
                var schema = GetPropertyValue(snap, "schema") ?? GetPropertyValue(snap, "schemaName");

                Console.WriteLine($"[AI-DESC] SQL Server snap - Operation: {operation}, Table: {table}, Schema: {schema}");

                var tableInfo = "";
                if (!string.IsNullOrEmpty(table))
                {
                    tableInfo = !string.IsNullOrEmpty(schema) ? $" on {schema}.{table}" : $" on {table}";
                }

                // Get SQL query if available
                var sqlQuery = GetPropertyValue(snap, "sql") ?? GetPropertyValue(snap, "query") ?? GetPropertyValue(snap, "statement");
                if (!string.IsNullOrEmpty(sqlQuery) && sqlQuery.Length > 20)
                {
                    var shortQuery = sqlQuery.Length > 100 ? sqlQuery.Substring(0, 100) + "..." : sqlQuery;
                    return $"Performs {operation}{tableInfo} using SQL query: {shortQuery}";
                }

                return $"🚀 ENHANCED: Performs {operation}{tableInfo}. Connects to SQL Server database to read, write, or execute database operations.";
            }
            else if (type.Contains("dynamics"))
            {
                var entity = GetPropertyValue(snap, "entity") ?? "entity";
                var operation = GetPropertyValue(snap, "operation") ?? "operation";
                
                return $"Performs {operation} operations on {entity} in Dynamics365. Integrates with Microsoft Dynamics CRM for data synchronization.";
            }
            else if (type.Contains("file") || type.Contains("csv") || type.Contains("excel"))
            {
                var filename = GetPropertyValue(snap, "filename") ?? GetPropertyValue(snap, "path");
                var fileInfo = !string.IsNullOrEmpty(filename) ? $" with file: {filename}" : "";
                
                return $"Handles file operations{fileInfo}. Reads from or writes to files in various formats.";
            }
            else if (type.Contains("router"))
            {
                return "Routes data to different output paths based on configured conditions and expressions.";
            }
            else if (type.Contains("filter"))
            {
                var condition = GetPropertyValue(snap, "condition") ?? GetPropertyValue(snap, "expression");
                var conditionInfo = !string.IsNullOrEmpty(condition) ? $" using condition: {condition}" : "";
                
                return $"Filters data records{conditionInfo}. Only passes records that meet the specified criteria.";
            }
            else if (type.Contains("join"))
            {
                return "Joins data from multiple input streams based on configured join conditions and key fields.";
            }
            else if (type.Contains("aggregate"))
            {
                return "Aggregates data by grouping records and performing calculations like sum, count, average, etc.";
            }
            else if (type.Contains("sort"))
            {
                return "Sorts data records based on specified field(s) and sort order (ascending/descending).";
            }

            // Generic fallback
            return $"Processes data according to its {GetSnapCategory(type)} configuration. Part of the pipeline's data processing workflow.";
        }

        private string GetBasicPipelineDescription(PipelineData pipeline)
        {
            var snapCount = pipeline.Snaps.Count;
            var categories = pipeline.Snaps.GroupBy(s => s.Category).ToDictionary(g => g.Key, g => g.Count());
            
            var description = $"This pipeline contains {snapCount} snaps processing data through multiple stages. ";
            
            if (categories.ContainsKey(SnapCategory.Database))
            {
                description += $"Includes {categories[SnapCategory.Database]} database operation(s) for data persistence. ";
            }
            
            if (categories.ContainsKey(SnapCategory.Transformation))
            {
                description += $"Performs {categories[SnapCategory.Transformation]} data transformation(s). ";
            }
            
            if (categories.ContainsKey(SnapCategory.ExternalSystem))
            {
                description += $"Integrates with {categories[SnapCategory.ExternalSystem]} external system(s). ";
            }

            return description.Trim();
        }

        private Dictionary<string, string> ExtractMappings(SnapNode snap)
        {
            var mappings = new Dictionary<string, string>();

            // Use the same logic as the Windows app
            Console.WriteLine($"[AI-DESC] Extracting mappings for snap: {snap.Label} ({snap.Type})");

            // First check for mappingTable in different potential locations (from Windows app)
            List<string> mappingTableKeys = new List<string> {
                "settings.transformations.value.mappingTable",
                "settings.transformations.mappingTable.value",
                "settings.mappingTable.value",
                "settings.mappingTable",
                "mappingTable.value",
                "mappingTable",
                "transformations.value.mappingTable.value"
            };

            foreach (string key in mappingTableKeys)
            {
                if (snap.Properties.ContainsKey(key))
                {
                    try
                    {
                        string mappingData = snap.Properties[key];
                        Console.WriteLine($"[AI-DESC] Found mappingTable at key: {key}, data length: {mappingData.Length}");

                        using var doc = System.Text.Json.JsonDocument.Parse(mappingData);
                        if (doc.RootElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                        {
                            foreach (var mapping in doc.RootElement.EnumerateArray())
                            {
                                if (mapping.TryGetProperty("targetPath", out var targetProp) &&
                                    mapping.TryGetProperty("expression", out var exprProp))
                                {
                                    var targetPath = targetProp.TryGetProperty("value", out var tVal) ? tVal.GetString() : targetProp.GetString();
                                    var expression = exprProp.TryGetProperty("value", out var eVal) ? eVal.GetString() : exprProp.GetString();

                                    if (!string.IsNullOrEmpty(targetPath) && !string.IsNullOrEmpty(expression))
                                    {
                                        mappings[targetPath] = expression;
                                        Console.WriteLine($"[AI-DESC] Found mapping: {targetPath} = {expression}");
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"[AI-DESC] Error parsing mappingTable at key {key}: {ex.Message}");
                    }
                }
            }

            // Extract mappings from SnapLogic mapper specific format (from Windows app)
            try
            {
                var potentialMappingRoots = snap.Properties.Where(p =>
                    p.Key.Contains("mappingRoot") && p.Key.EndsWith(".value")).ToList();

                foreach (var rootProp in potentialMappingRoots)
                {
                    string rootPath = rootProp.Value;
                    string rootPrefix = rootProp.Key.Substring(0, rootProp.Key.LastIndexOf(".mappingRoot"));
                    string mappingKeyBase = $"{rootPrefix}.mappingTable.value";

                    Console.WriteLine($"[AI-DESC] Found potential mapping root at {rootPrefix} with value {rootPath}");

                    if (snap.Properties.TryGetValue(mappingKeyBase, out string mappingData) &&
                        !string.IsNullOrEmpty(mappingData))
                    {
                        try
                        {
                            Console.WriteLine($"[AI-DESC] Found mappingTable data at {mappingKeyBase}, parsing...");
                            using var doc = System.Text.Json.JsonDocument.Parse(mappingData);
                            if (doc.RootElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                            {
                                foreach (var mapping in doc.RootElement.EnumerateArray())
                                {
                                    if (mapping.TryGetProperty("targetPath", out var targetProp) &&
                                        mapping.TryGetProperty("expression", out var exprProp))
                                    {
                                        string targetPath = targetProp.GetProperty("value").GetString();
                                        string expression = exprProp.GetProperty("value").GetString();
                                        Console.WriteLine($"[AI-DESC] Found SnapLogic mapping: {targetPath} = {expression}");
                                        mappings[targetPath] = expression;
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"[AI-DESC] Error parsing SnapLogic mapping format: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[AI-DESC] Error in SnapLogic specific mapping extraction: {ex.Message}");
            }

            Console.WriteLine($"[AI-DESC] Total mappings extracted: {mappings.Count}");
            return mappings;
        }

        private string? GetPropertyValue(SnapNode snap, string propertyName)
        {
            // Try exact match first
            if (snap.Properties.ContainsKey(propertyName))
                return snap.Properties[propertyName];

            // Try with .value suffix
            var valueKey = $"{propertyName}.value";
            if (snap.Properties.ContainsKey(valueKey))
                return snap.Properties[valueKey];

            // Try with settings prefix
            var settingsKey = $"settings.{propertyName}.value";
            if (snap.Properties.ContainsKey(settingsKey))
                return snap.Properties[settingsKey];

            return null;
        }

        private string GetSnapCategory(string snapType)
        {
            if (snapType.Contains("transform") || snapType.Contains("map"))
                return "transformation";
            else if (snapType.Contains("database") || snapType.Contains("sql"))
                return "database";
            else if (snapType.Contains("file"))
                return "file processing";
            else if (snapType.Contains("flow") || snapType.Contains("router"))
                return "flow control";
            else
                return "data processing";
        }
    }
}
