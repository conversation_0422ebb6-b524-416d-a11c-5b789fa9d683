# PowerShell script to fix build errors in the enhanced methods

$filePath = "DocumentationGenerator.cs"

Write-Host "Fixing build errors..." -ForegroundColor Green

# Read the file
$content = Get-Content $filePath -Raw

# Fix SnapType -> Type
Write-Host "Fixing SnapType references..." -ForegroundColor Yellow
$content = $content -replace 'snap\.SnapType', 'snap.Type'

# Fix _currentPipeline indexing - need to access the PipelineJson property
Write-Host "Fixing _currentPipeline access..." -ForegroundColor Yellow
$content = $content -replace '_currentPipeline\["snap_map"\]', '_currentPipeline.PipelineJson["snap_map"]'
$content = $content -replace 'var snapMap = _currentPipeline\["snap_map"\] as JObject;', 'var snapMap = _currentPipeline.PipelineJson["snap_map"] as JObject;'

# Write the updated file
$content | Set-Content $filePath -Encoding UTF8

Write-Host "Build errors fixed successfully!" -ForegroundColor Green
