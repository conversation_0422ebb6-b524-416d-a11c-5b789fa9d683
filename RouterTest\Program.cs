﻿using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;

Console.WriteLine("=== Router Connection Analysis ===");

// Test the connection parsing logic
string pipelineFile = @"c:\Users\<USER>\OneDrive - Newport City Homes\Documents\Snap-Documenter\Snap-Documenter\Snap-Pipelines\P01 - Smoke Alarm Attribute CRM_2025_05_21.json";

if (!File.Exists(pipelineFile))
{
    Console.WriteLine($"Pipeline file not found: {pipelineFile}");
    return;
}

try
{
    string jsonContent = File.ReadAllText(pipelineFile);
    JObject pipeline = JObject.Parse(jsonContent);
    
    Console.WriteLine("Pipeline loaded successfully");
      // Find all snaps and their connections
    var snaps = pipeline["snap_map"]?.Children<JProperty>()?.ToList();
    if (snaps == null)
    {
        Console.WriteLine("No snaps found in pipeline");
        return;
    }
    
    Console.WriteLine($"Found {snaps.Count} snaps");
    
    // Look for flow control snaps (copy, router, etc.)
    foreach (JProperty snapProp in snaps)
    {
        var snap = snapProp.Value;
        string classId = snap?["class_id"]?.ToString() ?? "";
        
        if (classId.Contains("flow") || classId.Contains("router") || classId.Contains("copy"))
        {
            Console.WriteLine($"\n--- Flow Control Snap: {snapProp.Name} ---");
            Console.WriteLine($"Class ID: {classId}");
            Console.WriteLine($"Label: {snap?["instance_label"]}");
            
            // Check output views
            var outputViews = snap?["output_views"];
            if (outputViews != null)
            {
                Console.WriteLine("Output Views:");
                foreach (JProperty view in outputViews.Children<JProperty>())
                {
                    Console.WriteLine($"  - {view.Name}");
                }
            }
            
            // Check properties for router routes
            var properties = snap?["properties"];
            if (properties != null)
            {
                Console.WriteLine("Properties:");
                foreach (JProperty prop in properties.Children<JProperty>())
                {
                    if (prop.Name.ToLower().Contains("route") || prop.Name.ToLower().Contains("path"))
                    {
                        Console.WriteLine($"  {prop.Name}: {prop.Value}");
                    }
                }
            }
        }
    }
    
    // Check all links for connections to flow control snaps
    var links = pipeline["link_map"]?.Children<JProperty>()?.ToList();
    if (links != null)
    {
        Console.WriteLine($"\n=== Found {links.Count} links ===");
        
        foreach (JProperty linkProp in links)
        {
            var link = linkProp.Value;
            string srcId = link?["src_id"]?.ToString() ?? "";
            string srcViewId = link?["src_view_id"]?.ToString() ?? "";
            string dstId = link?["dst_id"]?.ToString() ?? "";
            string dstViewId = link?["dst_view_id"]?.ToString() ?? "";
              // Find the source snap
            JProperty? srcSnapProp = snaps.Where(s => s.Name == srcId).FirstOrDefault();
            if (srcSnapProp != null)
            {
                var srcSnap = srcSnapProp.Value;
                string srcClassId = srcSnap?["class_id"]?.ToString() ?? "";
                if (srcClassId.Contains("flow") || srcClassId.Contains("router") || srcClassId.Contains("copy"))
                {
                    Console.WriteLine($"\nFlow Control Connection:");
                    Console.WriteLine($"  Source: {srcSnap?["instance_label"]} ({srcClassId})");
                    Console.WriteLine($"  Source View: '{srcViewId}'");
                    Console.WriteLine($"  Target: {dstId}");
                    Console.WriteLine($"  Target View: '{dstViewId}'");
                }
            }
        }
    }
    
}
catch (Exception ex)
{
    Console.WriteLine($"Error: {ex.Message}");
}

Console.WriteLine("\n=== Analysis Complete ===");
