using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;

namespace SnapAnalyser
{
    class QuickJoinTest
    {
        static void Main()
        {
            Console.WriteLine("Quick Join Snap Test");
            Console.WriteLine("===================");

            try
            {
                // Load real pipeline data
                var parser = new PipelineJsonParser();
                var pipelineFile = @"Snap-Pipelines\P01 - Smoke Alarm Attribute CRM_2025_05_21.json";
                
                if (File.Exists(pipelineFile))
                {
                    Console.WriteLine($"Loading pipeline: {pipelineFile}");
                    var snaps = parser.ParsePipelineJson(pipelineFile);
                    Console.WriteLine($"Total snaps loaded: {snaps.Count}");
                    
                    // Find Join snaps
                    var joinSnaps = snaps.Where(s => s.Type.ToLower().Contains("join")).ToList();
                    Console.WriteLine($"Join snaps found: {joinSnaps.Count}");
                    
                    foreach (var joinSnap in joinSnaps)
                    {
                        Console.WriteLine($"\nTesting Join snap: {joinSnap.Label} ({joinSnap.Type})");
                        Console.WriteLine($"Properties count: {joinSnap.Properties.Count}");
                        
                        // Show first few properties for debugging
                        Console.WriteLine("Key properties:");
                        foreach (var prop in joinSnap.Properties.Take(10))
                        {
                            var value = prop.Value.Length > 100 ? prop.Value.Substring(0, 100) + "..." : prop.Value;
                            Console.WriteLine($"  {prop.Key}: {value}");
                        }
                        
                        // Test FlowControlConfigurationGenerator
                        Console.WriteLine("\n--- Testing Enhanced Configuration Generator ---");
                        try
                        {
                            var flowConfigGenerator = new FlowControlConfigurationGenerator();
                            string config = flowConfigGenerator.GenerateJoinConfiguration(joinSnap, snaps);
                            
                            if (!string.IsNullOrEmpty(config))
                            {
                                Console.WriteLine("✓ Enhanced configuration generated successfully");
                                Console.WriteLine($"Config length: {config.Length} characters");
                                Console.WriteLine("Preview:");
                                var preview = config.Length > 200 ? config.Substring(0, 200) + "..." : config;
                                Console.WriteLine(preview);
                            }
                            else
                            {
                                Console.WriteLine("⚠ Enhanced configuration returned empty");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"✗ Error in enhanced configuration: {ex.Message}");
                        }
                        
                        // Test SnapBestPractices integration
                        Console.WriteLine("\n--- Testing SnapBestPractices Integration ---");
                        try
                        {
                            string bestPractices = SnapBestPractices.GetSnapBestPractices(joinSnap, snaps, false);
                            
                            if (!string.IsNullOrEmpty(bestPractices))
                            {
                                Console.WriteLine("✓ Best practices generated successfully");
                                Console.WriteLine($"Content length: {bestPractices.Length} characters");
                                
                                // Check what type of configuration is included
                                if (bestPractices.Contains("Join Configuration:") && bestPractices.Contains("Join Type:"))
                                {
                                    Console.WriteLine("✓ Contains enhanced Join configuration");
                                }
                                else if (bestPractices.Contains("Configuration:") && bestPractices.Contains("property_map"))
                                {
                                    Console.WriteLine("⚠ Contains raw configuration (fallback)");
                                }
                                else
                                {
                                    Console.WriteLine("? Unknown configuration type");
                                }
                                
                                // Show a preview
                                var lines = bestPractices.Split('\n');
                                Console.WriteLine("Preview (first 10 lines):");
                                foreach (var line in lines.Take(10))
                                {
                                    Console.WriteLine($"  {line}");
                                }
                            }
                            else
                            {
                                Console.WriteLine("✗ Best practices returned empty");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"✗ Error in SnapBestPractices: {ex.Message}");
                        }
                    }
                }
                else
                {
                    Console.WriteLine($"Pipeline file not found: {pipelineFile}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
