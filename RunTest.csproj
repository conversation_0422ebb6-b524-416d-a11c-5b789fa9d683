<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0-windows7.0</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <StartupObject>TestProgram</StartupObject>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="Azure.AI.OpenAI" Version="1.0.0-beta.17" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>
  
  <ItemGroup>
    <Compile Include="ConfigManager.cs" />
    <Compile Include="AzureOpenAITester.cs" />
    <Compile Include="TestConnectionProgram.cs" />
  </ItemGroup>
</Project>
